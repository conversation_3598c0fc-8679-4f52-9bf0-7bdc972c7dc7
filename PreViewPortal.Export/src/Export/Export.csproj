<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <GenerateRuntimeConfigurationFiles>true</GenerateRuntimeConfigurationFiles>
        <AWSProjectType>Lambda</AWSProjectType>
        <!-- This property makes the build directory similar to a publish directory and helps the AWS .NET Lambda Mock Test Tool find project dependencies. -->
        <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
        <!-- Generate ready to run images during publishing to improve cold start time. -->
        <PublishReadyToRun>true</PublishReadyToRun>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="Amazon.Lambda.Core" Version="2.2.0"/>
        <PackageReference Include="Amazon.Lambda.Serialization.SystemTextJson" Version="2.4.3"/>
        <PackageReference Include="Amazon.Lambda.S3Events" Version="3.1.0"/>
        <PackageReference Include="AWS.Lambda.Powertools.Logging" Version="1.6.2"/>
        <PackageReference Include="AWSSDK.S3" Version="3.7.305.28"/>
        <PackageReference Include="ClosedXML" Version="0.102.2"/>
        <PackageReference Include="Amazon.Extensions.Configuration.SystemsManager" Version="6.0.0"/>
        <PackageReference Include="CsvHelper" Version="33.0.1"/>
        <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="8.0.0"/>
        <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.1"/>
        <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.1"/>
        <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="8.0.1"/>
        <PackageReference Include="Realeyesit.Extensions.Configuration" Version="3.9.0"/>
        <ProjectReference Include="..\..\..\PreView.Common\src\BusinessLayer\BusinessLayer.csproj"/>
        <ProjectReference Include="..\..\..\PreView.Common\src\DataLayer\DataLayer.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <None Update="appsettings.dev.json">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="appsettings.json">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="appsettings.live.json">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="appsettings.stage.json">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
    </ItemGroup>
</Project>