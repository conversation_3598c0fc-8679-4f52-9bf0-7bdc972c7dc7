#nullable enable
using Amazon.Lambda.Core;
using Amazon.Lambda.S3Events;
using Amazon.Lambda.Serialization.SystemTextJson;
using AWS.Lambda.Powertools.Logging;
using BusinessLayer.Collector;
using BusinessLayer.Model.Response;
using DataLayer.Mapper;
using Export.Services.Excel;
using Export.Services.Payload;
using Export.Services.Result;
using Microsoft.Extensions.DependencyInjection;

[assembly: LambdaSerializer(typeof(DefaultLambdaJsonSerializer))]

namespace Export;

public class LambdaEntryPoint
{
    private readonly IExcelService _excelService;
    private readonly IPayloadService<S3Event> _payloadService;
    private readonly IResultService _resultService;
    private readonly IEnumerable<IExcelSheetClient> _sheetClients;
    private readonly IEnumerable<IExportDataCollector> _exportDataCollectors;

    public LambdaEntryPoint()
    {
        var serviceProvider = ServiceConfiguration.ConfigureServices();
        _excelService = serviceProvider.GetRequiredService<IExcelService>();
        _payloadService = serviceProvider.GetRequiredService<IPayloadService<S3Event>>();
        _sheetClients = serviceProvider.GetServices<IExcelSheetClient>();
        _resultService = serviceProvider.GetRequiredService<IResultService>();
        _exportDataCollectors = serviceProvider.GetServices<IExportDataCollector>();
    }

    [Logging(LogEvent = true)]
    public async Task Export(S3Event evnt, ILambdaContext context)
    {
        var payload = await _payloadService.GetPayload(evnt);

        var request = payload.ToRequest();

        var exportDataCollector = _exportDataCollectors
            .FirstOrDefault(collector => collector.CanHandleProductType(payload.ProductType));

        if (exportDataCollector == null)
        {
            Logger.LogError($"No ExportDataCollector found for ProductType: {payload.ProductType}");
            return;
        }

        ExportData exportData;
        
        try
        {
            exportData = await exportDataCollector.GetExportData(request);
        }
        catch (Exception ex)
        {
            Logger.LogError($"Failed to get export data: {ex.Message}");
            await _resultService.ExportFailed(payload);
            return;
        }

        var sheetClient = _sheetClients
            .FirstOrDefault(client => client.CanHandleProductType(payload.ProductType));

        if (sheetClient == null)
        {
            Logger.LogError($"No SheetClient found for ProductType: {payload.ProductType}");
            await _resultService.ExportFailed(payload);
            return;
        }

        var sheets = sheetClient.GetExcelSheets(exportData);
        if (sheets == null || !sheets.Any())
        {
            Logger.LogError("No sheets found.");
            await _resultService.ExportFailed(payload);
            return;
        }

        try
        {
            var persistResult = await _excelService.GenerateAndPersistExcel(payload, sheets);
            await _resultService.ExportCompleted(payload, persistResult.SignedUrl, persistResult.FileName);
        }
        catch (Exception e)
        {
            Logger.LogError($"Export failed: {e.Message}");
            await _resultService.ExportFailed(payload);
        }
    }
}