using Amazon;
using Amazon.Extensions.NETCore.Setup;
using Amazon.Lambda.S3Events;
using Amazon.S3;
using BusinessLayer.Configuration;
using DataLayer.Configuration;
using Export.Excel;
using Export.Forcedexposure;
using Export.Incontext;
using Export.Services.Excel;
using Export.Services.File;
using Export.Services.Payload;
using Export.Services.Payload.S3;
using Export.Services.Result;
using Export.Services.Result.S3;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Realeyesit.Extensions.Configuration;

namespace Export;

public static class ServiceConfiguration
{
    public static ServiceProvider ConfigureServices()
    {
        var services = new ServiceCollection();

        var configuration = BuildConfiguration().ResolveSecrets();

        services.AddAWSService<IAmazonS3>(
            new AWSOptions { Region = RegionEndpoint.GetBySystemName("eu-west-1") });

        services.AddTransient<IPayloadService<S3Event>, S3PayloadService>();
        services.AddTransient<IS3FileService, S3FileService>();
        services.AddTransient<IResultService, S3ResultService>();
        services.AddTransient<IExcelService, ExcelService>();
        services.AddTransient<IExcelFileBuilder, ExcelFileBuilder>();
        services.AddTransient<IExcelDataConverter, DataTableConverter>();
        services.AddTransient<IExcelSheetClient, ForcedExposureExcelSheetClient>();
        services.AddTransient<IExcelSheetClient, InContextExcelSheetClient>();

        services.AddBusinessConfiguration();
        services.AddDataConfiguration(configuration);

        return services.BuildServiceProvider();
    }

    public static IConfigurationRoot BuildConfiguration()
    {
        var basePath = Directory.GetCurrentDirectory();
        var environmentName = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")?.ToLower();

        return new ConfigurationBuilder()
            .SetBasePath(basePath)
            .AddJsonFile("appsettings.json", false, false)
            .AddJsonFile($"appsettings.{environmentName}.json", true)
            .AddEnvironmentVariables()
            .Build();
    }
}