{"profiles": {"Mock Lambda Test Tool - dev": {"commandName": "Executable", "executablePath": "C:\\Users\\<USER>\\.dotnet\\tools\\dotnet-lambda-test-tool-8.0.exe", "commandLineArgs": "--port 5050", "workingDirectory": "G:\\Realeyes\\PreViewPortal\\PreViewPortal.Export\\src\\Export\\bin\\Debug\\net8.0", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "dev"}}, "Mock Lambda Test Tool - live": {"commandName": "Executable", "executablePath": "C:\\Users\\<USER>\\.dotnet\\tools\\dotnet-lambda-test-tool-8.0.exe", "commandLineArgs": "--port 5050", "workingDirectory": "G:\\Realeyes\\PreViewPortal\\PreViewPortal.Export\\src\\Export\\bin\\Debug\\net8.0", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "live"}}, "Mock Lambda Test Tool - stage": {"commandName": "Executable", "executablePath": "C:\\Users\\<USER>\\.dotnet\\tools\\dotnet-lambda-test-tool-8.0.exe", "commandLineArgs": "--port 5050", "workingDirectory": "G:\\Realeyes\\PreViewPortal\\PreViewPortal.Export\\src\\Export\\bin\\Debug\\net8.0", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "stage"}}}}