using ClosedXML.Excel;
using Export.Excel;
using Export.Services.Excel;

namespace Export.Forcedexposure;

public class CreativePerformanceForcedExposureSummaryExcelWorkSheet : IExcelCustomWorksheet
{
    public string Name { get; init; } = "Summary";

    public List<CustomExcelCell> Cells { get; init; }
        = new()
        {
            new CustomExcelCell { Row = 1, Column = 1, Value = "Product Type" },
            new CustomExcelCell { Row = 1, Column = 2, Value = "PreView Focused Exposure" },
            new CustomExcelCell
            {
                Row = 2, Column = 2,
                Value =
                    $"Viewers complete an ad exposure task in a neutral video player{Environment.NewLine}without options for interaction, no survey questions are asked."
            },
            new CustomExcelCell { Row = 3, Column = 1, Value = "Date and time of the export" },
            new CustomExcelCell { Row = 3, Column = 2, Value = DateTimeOffset.Now.ToString("MM'/'dd'/'yyyy HH:mm") },
            new CustomExcelCell { Row = 4, Column = 1, Value = "Number of creatives included" },
            new CustomExcelCell { Row = 6, Column = 1, Value = "Reports:", Bold = true },
            new CustomExcelCell
                { Row = 7, Column = 1, Value = "Ad-level data", Hyperlink = new XLHyperlink("'Ad-level data'!A1") },
            new CustomExcelCell
            {
                Row = 8, Column = 1, Value = "Time Series data", Hyperlink = new XLHyperlink("'Time Series data'!A1")
            }
        };

    public void AddCell(CustomExcelCell cell)
    {
        Cells.Add(cell);
    }
}