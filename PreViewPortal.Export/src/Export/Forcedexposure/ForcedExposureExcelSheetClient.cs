using BusinessLayer.Model;
using BusinessLayer.Model.Response;
using Export.Services.Excel;

namespace Export.Forcedexposure;

public class ForcedExposureExcelSheetClient : IExcelSheetClient
{
    private readonly IExcelDataConverter _datatableConverter;

    public ForcedExposureExcelSheetClient(IExcelDataConverter datatableConverter)
    {
        _datatableConverter = datatableConverter;
    }
    
    public bool CanHandleProductType(ProductType type)
    {
        return type == ProductType.NewForcedExposure;
    }

    public IEnumerable<IExcelSheet> GetExcelSheets(ExportData exportData)
    {
        var excelSheets = new List<IExcelSheet>();

        var summarySheet = new CreativePerformanceForcedExposureSummaryExcelWorkSheet();
        summarySheet.AddCell(new CustomExcelCell { Row = 4, Column = 2, Value = exportData.MediaCount });
        excelSheets.Add(summarySheet);

        var creativePerformanceSheet = new CreativePerformanceForcedExposureExcelWorkSheet(
            exportData.CreativesPayload,
            exportData.VisibleColumnsOrder,
            _datatableConverter
        );
        excelSheets.Add(creativePerformanceSheet);

        var curvesWorksheet = new CreativePerformanceForcedExposureCurvesExcelWorksheet(exportData.CurvesPayload, _datatableConverter);
        excelSheets.Add(curvesWorksheet);

        return excelSheets;
    }
}