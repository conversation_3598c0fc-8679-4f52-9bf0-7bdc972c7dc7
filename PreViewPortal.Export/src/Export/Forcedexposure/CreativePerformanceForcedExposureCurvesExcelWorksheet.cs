using BusinessLayer.Model.Response;
using ClosedXML.Excel;
using Export.Excel;
using Export.Services.Excel;
using System.Reflection;

namespace Export.Forcedexposure;

public class CreativePerformanceForcedExposureCurvesExcelWorksheet : IExcelWorksheet<IXLWorksheet, IXLTable>
{
    private readonly IExcelDataConverter _dataConverter;

    public CreativePerformanceForcedExposureCurvesExcelWorksheet(IEnumerable<object> data,
        IExcelDataConverter dataConverter)
    {
        Data = data;
        _dataConverter = dataConverter;

        ColumnsSetup = FilterEmptyColumns(data, AllColumnsSetup);
    }

    public IEnumerable<object> Data { get; init; }

    public IXLTable GetTable(IXLWorksheet sheet)
    {
        var visibleColumns = ColumnsSetup.Keys.ToList();

        var dataTable = _dataConverter.ToDataTable(Data, visibleColumns);

        var ixlTable = sheet.FirstCell().InsertTable(dataTable);

        return ixlTable;
    }

    public string Name { get; init; } = "Time Series data";

    public Dictionary<string, ExcelColumnSetup> ColumnsSetup { get; init; } = new();

    private static Dictionary<string, ExcelColumnSetup> AllColumnsSetup { get; }
        = new()
        {
            { nameof(ForcedExposureCreativeWithCurve.CreationDate), new ExcelColumnSetup { Name = "Date Tested" } },
            { nameof(ForcedExposureCreativeWithCurve.Brand), new ExcelColumnSetup { Name = "Brand" } },
            { nameof(ForcedExposureCreativeWithCurve.SourceMedia), new ExcelColumnSetup { Name = "Creative" } },
            {
                nameof(ForcedExposureCreativeWithCurve.Duration),
                new ExcelColumnSetup { Name = "Duration", NumberFormat = "0\"s\"" }
            },
            { nameof(ForcedExposureCreativeWithCurve.Device), new ExcelColumnSetup { Name = "Device" } },
            { nameof(ForcedExposureCreativeWithCurve.Country), new ExcelColumnSetup { Name = "Country" } },
            { nameof(ForcedExposureCreativeWithCurve.GeographicRegion), new ExcelColumnSetup { Name = "Region" } },
            {
                nameof(ForcedExposureCreativeWithCurve.SubCategory),
                new ExcelColumnSetup { Name = "Industry (sub level)" }
            },
            {
                nameof(ForcedExposureCreativeWithCurve.MidCategory),
                new ExcelColumnSetup { Name = "Industry (mid level)" }
            },
            {
                nameof(ForcedExposureCreativeWithCurve.TopCategory),
                new ExcelColumnSetup { Name = "Industry (top level)" }
            },
            { nameof(ForcedExposureCreativeWithCurve.Views), new ExcelColumnSetup { Name = "Views" } },
            { nameof(ForcedExposureCreativeWithCurve.Second), new ExcelColumnSetup { Name = "Second" } },
            { nameof(ForcedExposureCreativeWithCurve.Audience), new ExcelColumnSetup { Name = "Audience" } },
            {
                nameof(ForcedExposureCreativeWithCurve.AdformatTextIF),
                new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Ad Format" }
            },
            {
                nameof(ForcedExposureCreativeWithCurve.Attention),
                new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Attention", NumberFormat = "0.00%" }
            },
            {
                nameof(ForcedExposureCreativeWithCurve.AttentionNorm),
                new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Attention (Norm)", NumberFormat = "0.00%" }
            },
            {
                nameof(ForcedExposureCreativeWithCurve.AllReactions),
                new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Reactions", NumberFormat = "0.00%" }
            },
            {
                nameof(ForcedExposureCreativeWithCurve.AllReactionsNorm),
                new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Reactions (Norm)", NumberFormat = "0.00%" }
            },
            {
                nameof(ForcedExposureCreativeWithCurve.Negativity),
                new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Negativity", NumberFormat = "0.00%" }
            },
            {
                nameof(ForcedExposureCreativeWithCurve.NegativityNorm),
                new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Negativity (Norm)", NumberFormat = "0.00%" }
            },
            {
                nameof(ForcedExposureCreativeWithCurve.Happiness),
                new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Happiness", NumberFormat = "0.00%" }
            },
            {
                nameof(ForcedExposureCreativeWithCurve.HappinessNorm),
                new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Happiness (Norm)", NumberFormat = "0.00%" }
            },
            {
                nameof(ForcedExposureCreativeWithCurve.Contempt),
                new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Contempt", NumberFormat = "0.00%" }
            },
            {
                nameof(ForcedExposureCreativeWithCurve.ContemptNorm),
                new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Contempt (Norm)", NumberFormat = "0.00%" }
            },
            {
                nameof(ForcedExposureCreativeWithCurve.Surprise),
                new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Surprise", NumberFormat = "0.00%" }
            },
            {
                nameof(ForcedExposureCreativeWithCurve.SurpriseNorm),
                new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Surprise (Norm)", NumberFormat = "0.00%" }
            },
            {
                nameof(ForcedExposureCreativeWithCurve.Confusion),
                new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Confusion", NumberFormat = "0.00%" }
            },
            {
                nameof(ForcedExposureCreativeWithCurve.ConfusionNorm),
                new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Confusion (Norm)", NumberFormat = "0.00%" }
            },
            {
                nameof(ForcedExposureCreativeWithCurve.Disgust),
                new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Disgust", NumberFormat = "0.00%" }
            },
            {
                nameof(ForcedExposureCreativeWithCurve.DisgustNorm),
                new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Disgust (Norm)", NumberFormat = "0.00%" }
            },
            {
                nameof(ForcedExposureCreativeWithCurve.Playback),
                new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Playback", NumberFormat = "0.00%" }
            },
            {
                nameof(ForcedExposureCreativeWithCurve.PlaybackNorm),
                new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Playback (Norm)", NumberFormat = "0.00%" }
            },
            {
                nameof(ForcedExposureCreativeWithCurve.Distraction),
                new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Distraction", NumberFormat = "0.00%" }
            },
            {
                nameof(ForcedExposureCreativeWithCurve.DistractionNorm),
                new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Distraction (Norm)", NumberFormat = "0.00%" }
            },
            {
                nameof(ForcedExposureCreativeWithCurve.NeutralAttention),
                new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Neutral Attention", NumberFormat = "0.00%" }
            },
            {
                nameof(ForcedExposureCreativeWithCurve.NeutralAttentionNorm),
                new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Neutral Attention (Norm)", NumberFormat = "0.00%" }
            }
        };

    private static Dictionary<string, ExcelColumnSetup> FilterEmptyColumns(
        IEnumerable<object> data,
        Dictionary<string, ExcelColumnSetup> allColumns)
    {
        if (!data.Any())
            return allColumns;

        var filteredColumns = new Dictionary<string, ExcelColumnSetup>();
        var dataList = data.ToList();
        var firstItem = dataList.First();
        var properties = firstItem.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance);

        foreach (var kvp in allColumns)
        {
            var propertyName = kvp.Key;
            var columnSetup = kvp.Value;

            var property = properties.FirstOrDefault(p => p.Name == propertyName);
            if (property == null)
            {
                continue;
            }

            var hasNonNullValue = dataList.Any(item =>
            {
                var value = property.GetValue(item);
                return value != null;
            });

            if (hasNonNullValue)
            {
                filteredColumns[propertyName] = columnSetup;
            }
        }

        return filteredColumns;
    }
}