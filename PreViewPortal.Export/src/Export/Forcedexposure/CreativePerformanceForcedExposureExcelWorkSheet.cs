using BusinessLayer.Model.Response;
using ClosedXML.Excel;
using Export.Services.Excel;

namespace Export.Forcedexposure;

public class CreativePerformanceForcedExposureExcelWorkSheet : IExcelWorksheet<IXLWorksheet, IXLTable>
{
    private readonly IExcelDataConverter _datatableConverter;

    public CreativePerformanceForcedExposureExcelWorkSheet(IEnumerable<object> data,
        List<string> visibleColumnsOrder, IExcelDataConverter datatableConverter)
    {
        Data = data;
        VisibleColumnsOrder = visibleColumnsOrder;
        _datatableConverter = datatableConverter;
    }

    public List<string> VisibleColumnsOrder { get; init; }

    public IEnumerable<object> Data { get; init; }

    public IXLTable GetTable(IXLWorksheet sheet)
    {
        var dataTable = _datatableConverter.ToDataTable(Data, VisibleColumnsOrder);

        var ixlTable = sheet.FirstCell().InsertTable(dataTable);

        return ixlTable;
    }

    public string Name { get; init; } = "Ad-level data";

    public Dictionary<string, ExcelColumnSetup> ColumnsSetup { get; init; }
        = new()
        {
            { nameof(ForcedExposureCreative.CreationDate), new ExcelColumnSetup { Name = "Date Tested" } },
            { nameof(ForcedExposureCreative.SubCategory), new ExcelColumnSetup { Name = "Industry (sub level)" } },
            { nameof(ForcedExposureCreative.MidCategory), new ExcelColumnSetup { Name = "Industry (mid level)" } },
            { nameof(ForcedExposureCreative.TopCategory), new ExcelColumnSetup { Name = "Industry (top level)" } },
            { nameof(ForcedExposureCreative.Brand), new ExcelColumnSetup { Name = "Brand" } },
            { nameof(ForcedExposureCreative.SourceMedia), new ExcelColumnSetup { Name = "Creative" } },
            { nameof(ForcedExposureCreative.Device), new ExcelColumnSetup { Name = "Device" } },
            { nameof(ForcedExposureCreative.Duration), new ExcelColumnSetup { Name = "Duration", NumberFormat = "0\"s\"" } },
            { nameof(ForcedExposureCreative.Audience), new ExcelColumnSetup { Name = "Audience" } },
            { nameof(ForcedExposureCreative.GeographicRegion), new ExcelColumnSetup { Name = "Region" } },
            { nameof(ForcedExposureCreative.Country), new ExcelColumnSetup { Name = "Country" } },
            { nameof(ForcedExposureCreative.Views), new ExcelColumnSetup { Name = "Views" } },
            { nameof(ForcedExposureCreative.QualityScore), new ExcelColumnSetup { Name = "Quality Score", NumberFormat = "0.0" } },
            { nameof(ForcedExposureCreative.AdformatTextIF), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Ad Format" } },
            { nameof(ForcedExposureCreative.VTR), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}VTR", NumberFormat = "0.00%" } },
            { nameof(ForcedExposureCreative.VTRMedian), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}VTR (Norm)", NumberFormat = "0.00%" } },
            { nameof(ForcedExposureCreative.VTRRank), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}VTR (Rank)" } },
            { nameof(ForcedExposureCreative.VTRDiff), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}VTR{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
            { nameof(ForcedExposureCreative.PlaybackSeconds), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Playback Seconds", NumberFormat = "0.00\"s\"" } },
            { nameof(ForcedExposureCreative.PlaybackSecondsMedian), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Playback Seconds (Norm)", NumberFormat = "0.00\"s\"" } },
            { nameof(ForcedExposureCreative.PlaybackSecondsRank), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Playback Seconds (Rank)" } },
            { nameof(ForcedExposureCreative.PlaybackSecondsDiff), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Playback Seconds{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
            { nameof(ForcedExposureCreative.AttentiveSeconds), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Attentive Seconds", NumberFormat = "0.00\"s\"" } },
            { nameof(ForcedExposureCreative.AttentiveSecondsMedian), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Attentive Seconds (Norm)", NumberFormat = "0.00\"s\"" } },
            { nameof(ForcedExposureCreative.AttentiveSecondsRank), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Attentive Seconds (Rank)" } },
            { nameof(ForcedExposureCreative.AttentiveSecondsDiff), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Attentive Seconds{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
            { nameof(ForcedExposureCreative.AttentionAvg), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Attention Avg.", NumberFormat = "0.00%" } },
            { nameof(ForcedExposureCreative.AttentionAvgMedian), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Attention Avg. (Norm)", NumberFormat = "0.00%" } },
            { nameof(ForcedExposureCreative.AttentionAvgRank), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Attention Avg. (Rank)" } },
            { nameof(ForcedExposureCreative.AttentionAvgDiff), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Attention Avg.{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
            { nameof(ForcedExposureCreative.AttentiveSecondsVTR), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Attentive VTR", NumberFormat = "0.00%" } },
            { nameof(ForcedExposureCreative.AttentiveSecondsVTRMedian), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Attentive VTR (Norm)", NumberFormat = "0.00%" } },
            { nameof(ForcedExposureCreative.AttentiveSecondsVTRRank), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Attentive VTR (Rank)" } },
            { nameof(ForcedExposureCreative.AttentiveSecondsVTRDiff), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Attentive VTR{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
            { nameof(ForcedExposureCreative.Reactions), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Reactions Avg.", NumberFormat = "0.00%" } },
            { nameof(ForcedExposureCreative.ReactionsMedian), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Reactions Avg. (Norm)", NumberFormat = "0.00%" } },
            { nameof(ForcedExposureCreative.ReactionsRank), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Reactions Avg. (Rank)" } },
            { nameof(ForcedExposureCreative.ReactionsDiff), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Reactions Avg.{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
            { nameof(ForcedExposureCreative.NegativityAvg), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Negativity Avg.", NumberFormat = "0.00%" } },
            { nameof(ForcedExposureCreative.NegativityAvgMedian), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Negativity Avg. (Norm)", NumberFormat = "0.00%" } },
            { nameof(ForcedExposureCreative.NegativityAvgRank), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Negativity Avg. (Rank)" } },
            { nameof(ForcedExposureCreative.NegativityAvgDiff), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Negativity Avg.{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
            { nameof(ForcedExposureCreative.HappyPeak), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Happy Peak", NumberFormat = "0.00%" } },
            { nameof(ForcedExposureCreative.HappyPeakMedian), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Happy Peak (Norm)", NumberFormat = "0.00%" } },
            { nameof(ForcedExposureCreative.HappyPeakRank), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Happy Peak (Rank)" } },
            { nameof(ForcedExposureCreative.HappyPeakDiff), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Happy Peak{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
            { nameof(ForcedExposureCreative.SurprisePeak), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Surprise Peak", NumberFormat = "0.00%" } },
            { nameof(ForcedExposureCreative.SurprisePeakMedian), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Surprise Peak (Norm)", NumberFormat = "0.00%" } },
            { nameof(ForcedExposureCreative.SurprisePeakRank), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Surprise Peak (Rank)" } },
            { nameof(ForcedExposureCreative.SurprisePeakDiff), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Surprise Peak{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
            { nameof(ForcedExposureCreative.ConfusionPeak), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Confusion Peak", NumberFormat = "0.00%" } },
            { nameof(ForcedExposureCreative.ConfusionPeakMedian), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Confusion Peak (Norm)", NumberFormat = "0.00%" } },
            { nameof(ForcedExposureCreative.ConfusionPeakRank), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Confusion Peak (Rank)" } },
            { nameof(ForcedExposureCreative.ConfusionPeakDiff), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Confusion Peak{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
            { nameof(ForcedExposureCreative.ContemptPeak), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Contempt Peak", NumberFormat = "0.00%" } },
            { nameof(ForcedExposureCreative.ContemptPeakMedian), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Contempt Peak (Norm)", NumberFormat = "0.00%" } },
            { nameof(ForcedExposureCreative.ContemptPeakRank), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Contempt Peak (Rank)" } },
            { nameof(ForcedExposureCreative.ContemptPeakDiff), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Contempt Peak{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
            { nameof(ForcedExposureCreative.DisgustPeak), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Disgust Peak", NumberFormat = "0.00%" } },
            { nameof(ForcedExposureCreative.DisgustPeakMedian), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Disgust Peak (Norm)", NumberFormat = "0.00%" } },
            { nameof(ForcedExposureCreative.DisgustPeakRank), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Disgust Peak (Rank)" } },
            { nameof(ForcedExposureCreative.DisgustPeakDiff), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Disgust Peak{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
            { nameof(ForcedExposureCreative.DistractionAvg), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Distraction Avg.", NumberFormat = "0.00%" } },
            { nameof(ForcedExposureCreative.DistractionAvgMedian), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Distraction Avg. (Norm)", NumberFormat = "0.00%" } },
            { nameof(ForcedExposureCreative.DistractionAvgRank), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Distraction Avg. (Rank)" } },
            { nameof(ForcedExposureCreative.DistractionAvgDiff), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Distraction Avg.{ExportLabel.VsNorm}", NumberFormat = "0.00%" } }
        };
}