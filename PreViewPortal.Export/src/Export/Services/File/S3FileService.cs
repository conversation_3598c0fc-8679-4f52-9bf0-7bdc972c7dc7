using Amazon.S3;
using Amazon.S3.Model;

namespace Export.Services.File;

public class S3FileService : IS3FileService
{
    private readonly IAmazonS3 _s3Client;

    public S3FileService(IAmazonS3 s3Client)
    {
        _s3Client = s3Client;
    }

    public async Task<string> GetFileContentAsync(string bucketName, string key)
    {
        var response = await _s3Client.GetObjectAsync(new GetObjectRequest
        {
            BucketName = bucketName,
            Key = key
        });

        using var reader = new StreamReader(response.ResponseStream);
        var content = await reader.ReadToEndAsync();
        return content;
    }

    public async Task<string> GeneratePreSignedUrl(string bucketName, string key)
    {
        var expiration = DateTime.UtcNow.Add(TimeSpan.FromMinutes(10));

        return await Task.FromResult(_s3Client.GeneratePreSignedURL(bucketName, key, expiration, null));
    }

    public async Task PersistFile(PutObjectRequest putObjectRequest)
    {
        await _s3Client.PutObjectAsync(putObjectRequest);
    }
}