using System.Data;
using Export.Services.Excel;

namespace Export.Excel;

public class DataTableConverter : IExcelDataConverter
{
    //In DataTable we can skip columns and set their order dynamically
    public DataTable ToDataTable(IEnumerable<object> enumerable, List<string> visibleColumnsOrder)
    {
        var dataTable = new DataTable();

        // Get the actual type from the first item in the enumerable instead of generic type arguments
        var enumerableList = enumerable.ToList();
        if (!enumerableList.Any())
        {
            return dataTable; // Return empty DataTable if no data
        }

        var actualType = enumerableList.First().GetType();
        var allproperties = actualType.GetProperties();

        var filteredSortedProperties = allproperties
            .Where(p => visibleColumnsOrder.Contains(p.Name, StringComparer.OrdinalIgnoreCase))
            .OrderBy(p => visibleColumnsOrder.FindIndex(c => c.Equals(p.Name, StringComparison.OrdinalIgnoreCase)));

        foreach (var property in filteredSortedProperties)
        {
            var type = Nullable.GetUnderlyingType(property.PropertyType) ?? property.PropertyType;

            //To get the precise value in excel without adding extra decimals
            if (type == typeof(float))
                type = typeof(decimal);

            dataTable.Columns.Add(new DataColumn { ColumnName = property.Name, DataType = type, AllowDBNull = true });
        }

        foreach (var rowData in enumerableList)
        {
            var newRow = dataTable.NewRow();

            foreach (var property in filteredSortedProperties)
            {
                var value = property.GetValue(rowData, null) ?? DBNull.Value;
                newRow[property.Name] = value;
            }

            dataTable.Rows.Add(newRow);
        }

        return dataTable;
    }
}