using Amazon.S3.Model;
using BusinessLayer.Model.Response;
using Export.Services.Excel;
using Export.Services.File;

namespace Export.Excel;

public class ExcelService : IExcelService
{
    private readonly IExcelFileBuilder _excelFileBuilder;
    private readonly IS3FileService _fileService;

    public ExcelService(
        IExcelFileBuilder excelFileBuilder,
        IS3FileService fileService)
    {
        _excelFileBuilder = excelFileBuilder;
        _fileService = fileService;
    }

    public async Task<ExcelResult> GenerateAndPersistExcel(ExportPayload payload, IEnumerable<IExcelSheet> sheets)
    {
        _excelFileBuilder.SetSheets(sheets);

        var fileName = $"{payload.ExportFileName}.xlsx";
        var excelPath = $"{payload.ExportFolderPath}/{fileName}";

        var fileResult = _excelFileBuilder.CreateExcelFile();

        var putRequest = new PutObjectRequest
        {
            BucketName = payload.BucketName,
            Key = excelPath,
            InputStream = fileResult,
            ContentType = "application/octet-stream"
        };

        await _fileService.PersistFile(putRequest);

        var signedUrl = await _fileService.GeneratePreSignedUrl(payload.BucketName, excelPath);

        return new ExcelResult
        {
            FileName = fileName,
            SignedUrl = signedUrl
        };
    }
}