using ClosedXML.Excel;
using Export.Excel;

namespace Export.Services.Excel;

public class ExcelFileBuilder : IExcelFileBuilder
{
    public List<IExcelWorksheet<IXLWorksheet, IXLTable>> Worksheets { get; set; } = new();
    public List<IExcelCustomWorksheet> CustomWorksheets { get; set; } = new();

    public void SetSheets(IEnumerable<IExcelSheet> sheets)
    {
        Worksheets.Clear();
        CustomWorksheets.Clear();

        foreach (var sheet in sheets)
            switch (sheet)
            {
                case IExcelWorksheet<IXLWorksheet, IXLTable> worksheet:
                    Worksheets.Add(worksheet);
                    break;
                case IExcelCustomWorksheet customWorksheet:
                    CustomWorksheets.Add(customWorksheet);
                    break;
            }
    }

    public MemoryStream CreateExcelFile()
    {
        var mem = new MemoryStream();
        using (var workbook = new XLWorkbook())
        {
            foreach (var customWorksheet in CustomWorksheets)
            {
                var newCustomSheet = workbook.Worksheets.Add(customWorksheet.Name);
                foreach (var cell in customWorksheet.Cells)
                {
                    var newCell = newCustomSheet.Cell(cell.Row, cell.Column);

                    newCell.Value = cell.Value;

                    newCell.Style.Font.Bold = cell.Bold;

                    if (cell.Hyperlink != null)
                        newCell.SetHyperlink(cell.Hyperlink);
                }

                newCustomSheet.Rows().AdjustToContents();
                newCustomSheet.Columns().AdjustToContents();
            }

            foreach (var worksheet in Worksheets)
            {
                var newSheet = workbook.Worksheets.Add(worksheet.Name);

                var newTable = worksheet.GetTable(newSheet);

                //Setting columns' name and formatting based on setup  
                var columns = newTable.Fields.Select(f => f.Name).ToList();
                foreach (var column in columns)
                    if (worksheet.ColumnsSetup.TryGetValue(column, out var columnSetup))
                    {
                        if (!string.IsNullOrEmpty(columnSetup.NumberFormat))
                            newTable.Field(column).Column.Style.NumberFormat.Format = columnSetup.NumberFormat;

                        newTable.Field(column).Name = columnSetup.Name;
                    }

                //Disable default table style
                newTable.Theme = XLTableTheme.None;
                newTable.ShowAutoFilter = false;
                newTable.FirstRow().Style.Font.Bold = true;

                newSheet.Columns().AdjustToContents();
            }

            workbook.SaveAs(mem);

            return mem;
        }
    }
}