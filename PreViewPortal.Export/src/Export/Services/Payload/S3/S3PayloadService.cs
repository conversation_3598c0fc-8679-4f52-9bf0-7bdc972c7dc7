using System.Text.Json;
using Amazon.Lambda.S3Events;
using AWS.Lambda.Powertools.Logging;
using BusinessLayer.Model.Response;
using Export.Services.File;

namespace Export.Services.Payload.S3;

public class S3PayloadService : IPayloadService<S3Event>
{
    private readonly IS3FileService _fileService;

    public S3PayloadService(IS3FileService fileService)
    {
        _fileService = fileService;
    }

    public async Task<ExportPayload> GetPayload(S3Event evnt)
    {
        var bucketName = "";
        var objectKey = "";
        try
        {
            foreach (var record in evnt.Records)
            {
                bucketName = record.S3.Bucket.Name;
                objectKey = record.S3.Object.Key;

                var content = await _fileService.GetFileContentAsync(bucketName, objectKey);

                return GetPayload(content);
            }

            return null;
        }
        catch (Exception e)
        {
            Logger.LogError(
                $"Get payload content failed, bucket: {bucketName}, filePath: {objectKey}, error: {e.Message}");
            return null;
        }
    }


    private ExportPayload GetPayload(string content)
    {
        try
        {
            return JsonSerializer.Deserialize<ExportPayload>(content);
        }
        catch (Exception)
        {
            Logger.LogError("Payload failed to deserialize.");
            return null;
        }
    }
}