using System.Text.Json;
using Amazon.S3.Model;
using AWS.Lambda.Powertools.Logging;
using BusinessLayer.Model.Response;
using Export.Services.File;

namespace Export.Services.Result.S3;

public class S3ResultService : IResultService
{
    private readonly IS3FileService _s3Service;

    public S3ResultService(IS3FileService s3Service)
    {
        _s3Service = s3Service;
    }

    public async Task ExportCompleted(ExportPayload payload, string signedUrl, string fileName)
    {
        var exportResult = await GetExportResult(payload);
        exportResult.ExportUrl = signedUrl;
        exportResult.ExportFileName = fileName;
        await UpdateExportResult(exportResult, "Completed");
    }

    public async Task ExportFailed(ExportPayload payload)
    {
        var exportResult = await GetExportResult(payload);
        await UpdateExportResult(exportResult, "Failed");
    }

    private async Task<ExportResult> GetExportResult(ExportPayload payload)
    {
        try
        {
            var jsonContent = await _s3Service.GetFileContentAsync(
                payload.BucketName,
                $"{payload.ExportFolderPath}/result.json"
            );

            var exportResult = JsonSerializer.Deserialize<ExportResult>(jsonContent);
            return exportResult ?? throw new InvalidOperationException();
        }
        catch (Exception e)
        {
            Logger.LogError($"Get export result failed: {e.Message}");
            throw;
        }
    }

    private async Task UpdateExportResult(ExportResult exportResult, string newStatus)
    {
        try
        {
            exportResult.JobStatus = newStatus;

            var configJson = JsonSerializer.Serialize(exportResult);

            var putRequest = new PutObjectRequest
            {
                BucketName = exportResult.BucketName,
                Key = $"{exportResult.ExportFolderPath}/result.json",
                ContentBody = configJson,
                ContentType = "application/json"
            };

            await _s3Service.PersistFile(putRequest);
        }
        catch (Exception e)
        {
            Logger.LogError($"Update export result failed: {e.Message}");
            throw;
        }
    }
}