using BusinessLayer.Model.Response;
using ClosedXML.Excel;
using Export.Services.Excel;
using System.Reflection;

namespace Export.Incontext;

public class CreativePerformanceInContextCurvesExcelWorksheet : IExcelWorksheet<IXLWorksheet, IXLTable>
{
    private readonly IExcelDataConverter _dataConverter;

    public CreativePerformanceInContextCurvesExcelWorksheet(IEnumerable<object> data, IExcelDataConverter dataConverter)
    {
        Data = data;
        _dataConverter = dataConverter;

        // Filter out columns that have all null values
        ColumnsSetup = FilterEmptyColumns(data, AllColumnsSetup);
    }

    public IEnumerable<object> Data { get; init; }

    public IXLTable GetTable(IXLWorksheet sheet)
    {
        // Get the visible columns (non-empty columns) in the order they appear in ColumnsSetup
        var visibleColumns = ColumnsSetup.Keys.ToList();

        // Convert data to DataTable with only the visible columns
        var dataTable = _dataConverter.ToDataTable(Data, visibleColumns);

        var ixlTable = sheet.FirstCell().InsertTable(dataTable);

        return ixlTable;
    }

    public string Name { get; init; } = "Time Series data";

    public Dictionary<string, ExcelColumnSetup> ColumnsSetup { get; init; } = new();

    /// <summary>
    /// All possible columns setup - used as the base for filtering
    /// </summary>
    private static Dictionary<string, ExcelColumnSetup> AllColumnsSetup { get; }
        = new()
        {
            { nameof(InContextCreativeWithCurve.CreationDate), new ExcelColumnSetup { Name = "Date Tested" } },
            { nameof(InContextCreativeWithCurve.Brand), new ExcelColumnSetup { Name = "Brand" } },
            { nameof(InContextCreativeWithCurve.SourceMedia), new ExcelColumnSetup { Name = "Creative" } },
            { nameof(InContextCreativeWithCurve.SubCategory), new ExcelColumnSetup { Name = "Industry (sub level)" } },
            { nameof(InContextCreativeWithCurve.MidCategory), new ExcelColumnSetup { Name = "Industry (mid level)" } },
            { nameof(InContextCreativeWithCurve.TopCategory), new ExcelColumnSetup { Name = "Industry (top level)" } },
            { nameof(InContextCreativeWithCurve.GeographicRegion), new ExcelColumnSetup { Name = "Region" } },
            { nameof(InContextCreativeWithCurve.Duration), new ExcelColumnSetup { Name = "Duration", NumberFormat = "0\"s\"" } },
            { nameof(InContextCreativeWithCurve.Device), new ExcelColumnSetup { Name = "Device" } },
            { nameof(InContextCreativeWithCurve.Country), new ExcelColumnSetup { Name = "Country" } },
            { nameof(InContextCreativeWithCurve.Audience), new ExcelColumnSetup { Name = "Audience" } },
            { nameof(InContextCreativeWithCurve.Views), new ExcelColumnSetup { Name = "Views" } },
            { nameof(InContextCreativeWithCurve.Second), new ExcelColumnSetup { Name = "Second" } },
            { nameof(InContextCreativeWithCurve.AdformatText), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Ad Format" } },
            { nameof(InContextCreativeWithCurve.Attention), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Attention", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.AttentionNorm), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Attention (Norm)", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.AllReactionsIC), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Reactions", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.AllReactionsICNorm), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Reactions (Norm)", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.NegativityIC), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Negativity", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.NegativityICNorm), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Negativity (Norm)", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.HappinessIC), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Happiness", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.HappinessICNorm), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Happiness (Norm)", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.ContemptIC), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Contempt", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.ContemptICNorm), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Contempt (Norm)", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.SurpriseIC), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Surprise", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.SurpriseICNorm), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Surprise (Norm)", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.ConfusionIC), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Confusion", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.ConfusionICNorm), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Confusion (Norm)", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.DisgustIC), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Disgust", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.DisgustICNorm), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Disgust (Norm)", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.AdformatTextIF), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Ad Format" } },
            { nameof(InContextCreativeWithCurve.AttentionIF), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Attention", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.AttentionIFNorm), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Attention (Norm)", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.AllReactions), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Reactions", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.AllReactionsNorm), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Reactions (Norm)", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.Negativity), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Negativity", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.NegativityNorm), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Negativity (Norm)", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.Happiness), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Happiness", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.HappinessNorm), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Happiness (Norm)", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.Contempt), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Contempt", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.ContemptNorm), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Contempt (Norm)", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.Surprise), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Surprise", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.SurpriseNorm), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Surprise (Norm)", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.Confusion), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Confusion", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.ConfusionNorm), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Confusion (Norm)", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.Disgust), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Disgust", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.DisgustNorm), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Disgust (Norm)", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.PlaybackIC), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Playback", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.PlaybackICNorm), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Playback (Norm)", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.PlaybackIF), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Playback", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.PlaybackIFNorm), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Playback (Norm)", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.DistractionIC), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Distraction", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.DistractionICNorm), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Distraction (Norm)", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.DistractionIF), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Distraction", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.DistractionIFNorm), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Distraction (Norm)", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.NeutralAttention), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Neutral Attention", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.NeutralAttentionNorm), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Neutral Attention (Norm)", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.NeutralAttentionIF), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Neutral Attention", NumberFormat = "0.00%" } },
            { nameof(InContextCreativeWithCurve.NeutralAttentionIFNorm), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Neutral Attention (Norm)", NumberFormat = "0.00%" } }
        };

    /// <summary>
    /// Filters out columns that have all null values in the provided data
    /// </summary>
    /// <param name="data">The data to analyze</param>
    /// <param name="allColumns">All possible column setups</param>
    /// <returns>Filtered column setups containing only columns with non-null data</returns>
    private static Dictionary<string, ExcelColumnSetup> FilterEmptyColumns(
        IEnumerable<object> data,
        Dictionary<string, ExcelColumnSetup> allColumns)
    {
        if (!data.Any())
            return allColumns;

        var filteredColumns = new Dictionary<string, ExcelColumnSetup>();
        var dataList = data.ToList();
        var firstItem = dataList.First();
        var properties = firstItem.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance);

        foreach (var kvp in allColumns)
        {
            var propertyName = kvp.Key;
            var columnSetup = kvp.Value;

            // Find the property by name
            var property = properties.FirstOrDefault(p => p.Name == propertyName);
            if (property == null)
            {
                // If property doesn't exist, skip it
                continue;
            }

            // Check if any item in the data has a non-null value for this property
            var hasNonNullValue = dataList.Any(item =>
            {
                var value = property.GetValue(item);
                return value != null;
            });

            // Only include columns that have at least one non-null value
            if (hasNonNullValue)
            {
                filteredColumns[propertyName] = columnSetup;
            }
        }

        return filteredColumns;
    }
}
