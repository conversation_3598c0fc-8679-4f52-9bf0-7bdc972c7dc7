using BusinessLayer.Model;
using BusinessLayer.Model.Response;
using Export.Services.Excel;

namespace Export.Incontext;

public class InContextExcelSheetClient : IExcelSheetClient
{
    private readonly IExcelDataConverter _datatableConverter;

    public InContextExcelSheetClient(
        IExcelDataConverter datatableConverter)
    {
        _datatableConverter = datatableConverter;
    }

    public bool CanHandleProductType(ProductType type)
    {
        return type == ProductType.InContext;
    }

    public IEnumerable<IExcelSheet> GetExcelSheets(ExportData exportData)
    {
        var excelSheets = new List<IExcelSheet>();

        var summarySheet = new CreativePerformanceInContextSummaryExcelWorkSheet();
        summarySheet.AddCell(new CustomExcelCell { Row = 4, Column = 2, Value = exportData.MediaCount });
        excelSheets.Add(summarySheet);

        var creativePerformanceSheet = new CreativePerformanceInContextExcelWorkSheet(
            exportData.CreativesPayload,
            exportData.VisibleColumnsOrder,
            _datatableConverter
        );
        excelSheets.Add(creativePerformanceSheet);

        var curvesWorksheet = new CreativePerformanceInContextCurvesExcelWorksheet(exportData.CurvesPayload, _datatableConverter);
        excelSheets.Add(curvesWorksheet);

        return excelSheets;
    }
}