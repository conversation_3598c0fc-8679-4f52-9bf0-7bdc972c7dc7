using BusinessLayer.Model.Response;
using ClosedXML.Excel;
using Export.Services.Excel;

namespace Export.Incontext;

public class CreativePerformanceInContextExcelWorkSheet : IExcelWorksheet<IXLWorksheet, IXLTable>
{
    private readonly IExcelDataConverter datatableConverter;

    public CreativePerformanceInContextExcelWorkSheet(IEnumerable<object> data, List<string> visibleColumnsOrder,
        IExcelDataConverter datatableConverter)
    {
        Data = data;
        VisibleColumnsOrder = visibleColumnsOrder;
        this.datatableConverter = datatableConverter;
    }

    public List<string> VisibleColumnsOrder { get; init; }

    public IEnumerable<object> Data { get; init; }

    public IXLTable GetTable(IXLWorksheet sheet)
    {
        var dataTable = datatableConverter.ToDataTable(Data, VisibleColumnsOrder);

        var ixlTable = sheet.FirstCell().InsertTable(dataTable);

        return ixlTable;
    }

    public string Name { get; init; } = "Ad-level data";

    public Dictionary<string, ExcelColumnSetup> ColumnsSetup { get; init; }
        = new()
        {
        { nameof(InContextCreative.CreationDate), new ExcelColumnSetup { Name = "Date Tested" } },
        { nameof(InContextCreative.SubCategory), new ExcelColumnSetup { Name = "Industry (sub level)" } },
        { nameof(InContextCreative.MidCategory), new ExcelColumnSetup { Name = "Industry (mid level)" } },
        { nameof(InContextCreative.TopCategory), new ExcelColumnSetup { Name = "Industry (top level)" } },
        { nameof(InContextCreative.Brand), new ExcelColumnSetup { Name = "Brand" } },
        { nameof(InContextCreative.SourceMedia), new ExcelColumnSetup { Name = "Creative" } },
        { nameof(InContextCreative.Device), new ExcelColumnSetup { Name = "Device" } },
        { nameof(InContextCreative.Duration), new ExcelColumnSetup { Name = "Duration", NumberFormat = "0\"s\"" } },
        { nameof(InContextCreative.AdformatText), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Ad Format" } },
        { nameof(InContextCreative.AdformatTextIF), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Ad Format" } },
        { nameof(InContextCreative.GeographicRegion), new ExcelColumnSetup { Name = "Region" } },
        { nameof(InContextCreative.Country), new ExcelColumnSetup { Name = "Country" } },
        { nameof(InContextCreative.Audience), new ExcelColumnSetup { Name = "Audience" } },
        { nameof(InContextCreative.Views), new ExcelColumnSetup { Name = "Views" } },
        { nameof(InContextCreative.QualityScore), new ExcelColumnSetup { Name = "Quality Score", NumberFormat = "0.0" } },
        { nameof(InContextCreative.AttentiveSeconds), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Attentive Seconds", NumberFormat = "0.00\"s\"" } },
        { nameof(InContextCreative.AttentiveSecondsMedian), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Attentive Seconds (Norm)", NumberFormat = "0.00\"s\"" } },
        { nameof(InContextCreative.AttentiveSecondsRank), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Attentive Seconds (Rank)" } },
        { nameof(InContextCreative.AttentiveSecondsDiff), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Attentive Seconds{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.AttentiveSecondsIF), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Attentive Seconds", NumberFormat = "0.00\"s\"" } },
        { nameof(InContextCreative.AttentiveSecondsIFMedian), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Attentive Seconds (Norm)", NumberFormat = "0.00\"s\"" } },
        { nameof(InContextCreative.AttentiveSecondsIFRank), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Attentive Seconds (Rank)" } },
        { nameof(InContextCreative.AttentiveSecondsIFDiff), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Attentive Seconds{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.AttentiveSecondsVTR), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Attentive VTR", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.AttentiveSecondsVTRMedian), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Attentive VTR (Norm)", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.AttentiveSecondsVTRRank), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Attentive VTR (Rank)" } },
        { nameof(InContextCreative.AttentiveSecondsVTRDiff), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Attentive VTR{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.AttentiveSecondsVTRIF), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Attentive VTR", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.AttentiveSecondsVTRIFMedian), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Attentive VTR (Norm)", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.AttentiveSecondsVTRIFRank), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Attentive VTR (Rank)" } },
        { nameof(InContextCreative.AttentiveSecondsVTRIFDiff), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}{ExportLabel.VsNorm}Attentive VTR", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.VTR), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}VTR", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.VTRMedian), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}VTR (Norm)", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.VTRRank), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}VTR (Rank)" } },
        { nameof(InContextCreative.VTRDiff), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}VTR{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.VTRIF), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}VTR", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.VTRIFMedian), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}VTR (Norm)", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.VTRIFRank), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}VTR (Rank)" } },
        { nameof(InContextCreative.VTRIFDiff), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}VTR{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.PlaybackSeconds), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Playback Seconds", NumberFormat = "0.00\"s\"" } },
        { nameof(InContextCreative.PlaybackSecondsMedian), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Playback Seconds (Norm)", NumberFormat = "0.00\"s\"" } },
        { nameof(InContextCreative.PlaybackSecondsRank), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Playback Seconds (Rank)" } },
        { nameof(InContextCreative.PlaybackSecondsDiff), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Playback Seconds{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.PlaybackSecondsIF), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Playback Seconds", NumberFormat = "0.00\"s\"" } },
        { nameof(InContextCreative.PlaybackSecondsIFMedian), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Playback Seconds (Norm)", NumberFormat = "0.00\"s\"" } },
        { nameof(InContextCreative.PlaybackSecondsIFRank), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Playback Seconds (Rank)" } },
        { nameof(InContextCreative.PlaybackSecondsIFDiff), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Playback Seconds{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.BrandRecognition), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Brand Recognition", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.BrandRecognitionMedian), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Brand Recognition (Norm)", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.BrandRecognitionRank), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Brand Recognition (Rank)" } },
        { nameof(InContextCreative.BrandRecognitionDiff), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Brand Recognition{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.AdRecognition), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Ad Recognition", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.AdRecognitionMedian), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Ad Recognition (Norm)", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.AdRecognitionRank), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Ad Recognition (Rank)" } },
        { nameof(InContextCreative.AdRecognitionDiff), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Ad Recognitionn{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.ReactionsIC), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Reactions Avg.", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.ReactionsICMedian), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Reactions Avg. (Norm)", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.ReactionsICRank), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Reactions Avg. (Rank)" } },
        { nameof(InContextCreative.ReactionsICDiff), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Reactions Avg.{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.Reactions), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Reactions Avg.", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.ReactionsMedian), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Reactions Avg. (Norm)", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.ReactionsRank), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Reactions Avg. (Rank)" } },
        { nameof(InContextCreative.ReactionsDiff), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Reactions Avg.{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.NegativityAvgIC), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Negativity Avg.", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.NegativityAvgICMedian), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Negativity Avg. (Norm)", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.NegativityAvgICRank), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Negativity Avg. (Rank)" } },
        { nameof(InContextCreative.NegativityAvgICDiff), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Negativity Avg.{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.NegativityAvgIF), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Negativity Avg.", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.NegativityAvgIFMedian), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Negativity Avg. (Norm)", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.NegativityAvgIFRank), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Negativity Avg. (Rank)" } },
        { nameof(InContextCreative.NegativityAvgIFDiff), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Negativity Avg.{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.AttentionAvgIC), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Attention Avg.", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.AttentionAvgICMedian), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Attention Avg. (Norm)", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.AttentionAvgICRank), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Attention Avg. (Rank)" } },
        { nameof(InContextCreative.AttentionAvgICDiff), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Attention Avg.{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.AttentionAvgIF), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Attention Avg.", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.AttentionAvgIFMedian), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Attention Avg. (Norm)", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.AttentionAvgIFRank), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Attention Avg. (Rank)" } },
        { nameof(InContextCreative.AttentionAvgIFDiff), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Attention Avg.{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.HappyPeakIC), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Happy Peak", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.HappyPeakICMedian), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Happy Peak (Norm)", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.HappyPeakICRank), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Happy Peak (Rank)" } },
        { nameof(InContextCreative.HappyPeakICDiff), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Happy Peak{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.HappyPeak), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Happy Peak", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.HappyPeakMedian), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Happy Peak (Norm)", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.HappyPeakRank), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Happy Peak (Rank)" } },
        { nameof(InContextCreative.HappyPeakDiff), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Happy Peak{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.SurprisePeakIC), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Surprise Peak", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.SurprisePeakICMedian), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Surprise Peak (Norm)", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.SurprisePeakICRank), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Surprise Peak (Rank)" } },
        { nameof(InContextCreative.SurprisePeakICDiff), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Surprise Peak{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.SurprisePeak), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Surprise Peak", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.SurprisePeakMedian), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Surprise Peak (Norm)", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.SurprisePeakRank), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Surprise Peak (Rank)" } },
        { nameof(InContextCreative.SurprisePeakDiff), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Surprise Peak{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.ConfusionPeakIC), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Confusion Peak", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.ConfusionPeakICMedian), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Confusion Peak (Norm)", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.ConfusionPeakICRank), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Confusion Peak (Rank)" } },
        { nameof(InContextCreative.ConfusionPeakICDiff), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Confusion Peak{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.ConfusionPeak), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Confusion Peak", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.ConfusionPeakMedian), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Confusion Peak (Norm)", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.ConfusionPeakRank), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Confusion Peak (Rank)" } },
        { nameof(InContextCreative.ConfusionPeakDiff), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Confusion Peak{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.ContemptPeakIC), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Contempt Peak", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.ContemptPeakICMedian), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Contempt Peak (Norm)", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.ContemptPeakICRank), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Contempt Peak (Rank)" } },
        { nameof(InContextCreative.ContemptPeakICDiff), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Contempt Peak{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.ContemptPeak), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Contempt Peak", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.ContemptPeakMedian), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Contempt Peak (Norm)", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.ContemptPeakRank), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Contempt Peak (Rank)" } },
        { nameof(InContextCreative.ContemptPeakDiff), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Contempt Peak{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.DisgustPeakIC), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Disgust Peak", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.DisgustPeakICMedian), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Disgust Peak (Norm)", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.DisgustPeakICRank), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Disgust Peak (Rank)" } },
        { nameof(InContextCreative.DisgustPeakICDiff), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Disgust Peak{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.DisgustPeak), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Disgust Peak", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.DisgustPeakMedian), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Disgust Peak (Norm)", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.DisgustPeakRank), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Disgust Peak (Rank)" } },
        { nameof(InContextCreative.DisgustPeakDiff), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Disgust Peak{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.AdLikeability), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Ad Likeability", NumberFormat = "0.00" } },
        { nameof(InContextCreative.AdLikeabilityMedian), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Ad Likeability (Norm)", NumberFormat = "0.00" } },
        { nameof(InContextCreative.AdLikeabilityRank), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Ad Likeability (Rank)" } },
        { nameof(InContextCreative.AdLikeabilityDiff), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Ad Likeability{ExportLabel.VsNorm}", NumberFormat = "0.00" } },
        { nameof(InContextCreative.BrandTrust), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Brand Trust", NumberFormat = "0.00" } },
        { nameof(InContextCreative.BrandTrustMedian), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Brand Trust (Norm)", NumberFormat = "0.00" } },
        { nameof(InContextCreative.BrandTrustRank), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Brand Trust (Rank)" } },
        { nameof(InContextCreative.BrandTrustDiff), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Brand Trust{ExportLabel.VsNorm}", NumberFormat = "0.00" } },
        { nameof(InContextCreative.Persuasion), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Persuasion", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.PersuasionMedian), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Persuasion (Norm)", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.PersuasionRank), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Persuasion (Rank)" } },
        { nameof(InContextCreative.PersuasionDiff), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Persuasion{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.DistractionAvgIC), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Distraction Avg.", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.DistractionAvgICMedian), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Distraction Avg. (Norm)", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.DistractionAvgICRank), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Distraction Avg. (Rank)" } },
        { nameof(InContextCreative.DistractionAvgICDiff), new ExcelColumnSetup { Name = $"{ExportLabel.InContext}Distraction Avg.{ExportLabel.VsNorm}", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.DistractionAvgIF), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Distraction Avg.", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.DistractionAvgIFMedian), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Distraction Avg. (Norm)", NumberFormat = "0.00%" } },
        { nameof(InContextCreative.DistractionAvgIFRank), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Distraction Avg. (Rank)" } },
        { nameof(InContextCreative.DistractionAvgIFDiff), new ExcelColumnSetup { Name = $"{ExportLabel.Focused}Distraction Avg.{ExportLabel.VsNorm}", NumberFormat = "0.00%" } }

        };
}