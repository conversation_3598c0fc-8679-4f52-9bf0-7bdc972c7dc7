using BusinessLayer.Collector;
using BusinessLayer.Model;
using BusinessLayer.Model.Filter;
using BusinessLayer.Model.Request;
using BusinessLayer.Model.Response;
using BusinessLayer.Repository;
using Moq;
using NUnit.Framework;

namespace Export.tests.Collector;

[TestFixture]
public class ForcedExposureExportDataCollectorTests
{
    private Mock<IForcedExposureCollector> _mockForcedExposureCollector;
    private Mock<ISegmentRepository> _mockSegmentRepository;
    private ForcedExposureExportDataCollector _collector;

    [SetUp]
    public void Setup()
    {
        _mockForcedExposureCollector = new Mock<IForcedExposureCollector>();
        _mockSegmentRepository = new Mock<ISegmentRepository>();
        _collector = new ForcedExposureExportDataCollector(_mockForcedExposureCollector.Object, _mockSegmentRepository.Object);
    }

    [Test]
    public async Task GetExportData_WithCurveFilter_ShouldFilterCurvesCorrectly()
    {
        // Arrange
        var request = new GetExportDataRequest
        {
            AccountId = 1,
            IsAdmin = true,
            SegmentKeys = new[] { "segment1" },
            Media = new List<MediaModelRequest>
            {
                new MediaModelRequest { SourceMediaID = 1, TestID = 1 }
            },
            CurveFilter = new List<CurveFilterItem>
            {
                new CurveFilterItem { Type = CurveType.Happiness }
            },
            VisibleColumnsOrder = new List<string> { "views", "brand" }
        };

        var mockCreatives = new List<ForcedExposureCreative>
        {
            new ForcedExposureCreative
            {
                SourceMediaID = 1,
                TestID = 1,
                SegmentKey = "segment1",
                Brand = "TestBrand",
                Views = 100,
                Audience = "Test Audience"
            }
        };

        var mockCurves = new List<ForcedExposureCurve>
        {
            new ForcedExposureCurve
            {
                SourceMediaID = 1,
                TestID = 1,
                SegmentKey = "segment1",
                Second = 1,
                Happiness = 0.8f,
                HappinessNorm = 0.7f,
                Attention = 0.9f,
                AttentionNorm = 0.8f,
                Negativity = 0.3f,
                NegativityNorm = 0.2f
            }
        };

        var mockSegments = new List<SegmentKeyLabel>
        {
            new SegmentKeyLabel
            {
                SegmentKey = "segment1",
                Question = "Test Question",
                Answer = "Test Answer"
            }
        };

        _mockForcedExposureCollector
            .Setup(x => x.GetCreativesAsync(It.IsAny<GetCreativesRequest>()))
            .ReturnsAsync(mockCreatives);

        _mockForcedExposureCollector
            .Setup(x => x.GetCurvesAsync(It.IsAny<GetCurvesRequest>()))
            .ReturnsAsync(mockCurves);

        _mockSegmentRepository
            .Setup(x => x.GetSegmentKeys(It.IsAny<GetSegmentKeysRequest>()))
            .ReturnsAsync(mockSegments);

        // Act
        var result = await _collector.GetExportData(request);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.CurvesPayload, Is.Not.Null);
        
        var curvesWithCreatives = result.CurvesPayload.Cast<ForcedExposureCreativeWithCurve>().ToList();
        Assert.That(curvesWithCreatives, Has.Count.EqualTo(1));
        
        var curveWithCreative = curvesWithCreatives.First();
        
        // Should have happiness data (filtered curve type)
        Assert.That(curveWithCreative.Happiness, Is.EqualTo(0.8m));
        Assert.That(curveWithCreative.HappinessNorm, Is.EqualTo(0.7m));
        
        // Should NOT have attention data (filtered out)
        Assert.That(curveWithCreative.Attention, Is.Null);
        Assert.That(curveWithCreative.AttentionNorm, Is.Null);
        
        // Should NOT have negativity data (filtered out)
        Assert.That(curveWithCreative.Negativity, Is.Null);
        Assert.That(curveWithCreative.NegativityNorm, Is.Null);
    }

    [Test]
    public async Task GetExportData_WithoutCurveFilter_ShouldKeepAllCurveData()
    {
        // Arrange
        var request = new GetExportDataRequest
        {
            AccountId = 1,
            IsAdmin = true,
            SegmentKeys = new[] { "segment1" },
            Media = new List<MediaModelRequest>
            {
                new MediaModelRequest { SourceMediaID = 1, TestID = 1 }
            },
            CurveFilter = new List<CurveFilterItem>(), // Empty filter
            VisibleColumnsOrder = new List<string> { "views", "brand" }
        };

        var mockCreatives = new List<ForcedExposureCreative>
        {
            new ForcedExposureCreative
            {
                SourceMediaID = 1,
                TestID = 1,
                SegmentKey = "segment1",
                Brand = "TestBrand",
                Views = 100,
                Audience = "Test Audience"
            }
        };

        var mockCurves = new List<ForcedExposureCurve>
        {
            new ForcedExposureCurve
            {
                SourceMediaID = 1,
                TestID = 1,
                SegmentKey = "segment1",
                Second = 1,
                Happiness = 0.8f,
                Attention = 0.9f,
                Negativity = 0.3f
            }
        };

        var mockSegments = new List<SegmentKeyLabel>
        {
            new SegmentKeyLabel
            {
                SegmentKey = "segment1",
                Question = "Test Question",
                Answer = "Test Answer"
            }
        };

        _mockForcedExposureCollector
            .Setup(x => x.GetCreativesAsync(It.IsAny<GetCreativesRequest>()))
            .ReturnsAsync(mockCreatives);

        _mockForcedExposureCollector
            .Setup(x => x.GetCurvesAsync(It.IsAny<GetCurvesRequest>()))
            .ReturnsAsync(mockCurves);

        _mockSegmentRepository
            .Setup(x => x.GetSegmentKeys(It.IsAny<GetSegmentKeysRequest>()))
            .ReturnsAsync(mockSegments);

        // Act
        var result = await _collector.GetExportData(request);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.CurvesPayload, Is.Not.Null);
        
        var curvesWithCreatives = result.CurvesPayload.Cast<ForcedExposureCreativeWithCurve>().ToList();
        Assert.That(curvesWithCreatives, Has.Count.EqualTo(1));
        
        var curveWithCreative = curvesWithCreatives.First();
        
        // Should have all curve data (no filtering applied)
        Assert.That(curveWithCreative.Happiness, Is.EqualTo(0.8m));
        Assert.That(curveWithCreative.Attention, Is.EqualTo(0.9m));
        Assert.That(curveWithCreative.Negativity, Is.EqualTo(0.3m));
    }

    [Test]
    public void CanHandleProductType_WithNewForcedExposureType_ShouldReturnTrue()
    {
        // Act
        var result = _collector.CanHandleProductType(ProductType.NewForcedExposure);

        // Assert
        Assert.That(result, Is.True);
    }

    [Test]
    public void CanHandleProductType_WithInContextType_ShouldReturnFalse()
    {
        // Act
        var result = _collector.CanHandleProductType(ProductType.InContext);

        // Assert
        Assert.That(result, Is.False);
    }
}
