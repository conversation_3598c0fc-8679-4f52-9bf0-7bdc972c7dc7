using System.Text;
using Amazon.S3;
using Amazon.S3.Model;
using Export.Services.File;
using Moq;

namespace Export.tests.Services.File
{
    [TestFixture]
    public class S3FileServiceTests
    {
        private Mock<IAmazonS3> _mockS3Client;
        private S3FileService _s3FileService;

        [SetUp]
        public void Setup()
        {
            _mockS3Client = new Mock<IAmazonS3>();
            _s3FileService = new S3FileService(_mockS3Client.Object);
        }

        [Test]
        public async Task GetFileContentAsync_ShouldReturnFileContent_WhenFileExists()
        {
            // Arrange
            const string bucketName = "test-bucket";
            const string key = "test-file.txt";
            const string expectedContent = "File content here";

            var s3Response = new GetObjectResponse
            {
                ResponseStream = new MemoryStream(Encoding.UTF8.GetBytes(expectedContent))
            };
            _mockS3Client.Setup(x => x.GetObjectAsync(It.IsAny<GetObjectRequest>(), default))
                .ReturnsAsync(s3Response);

            // Act
            var result = await _s3FileService.GetFileContentAsync(bucketName, key);

            // Assert
            Assert.AreEqual(expectedContent, result);
            _mockS3Client.Verify(x => x.GetObjectAsync(It.IsAny<GetObjectRequest>(), default), Times.Once);
        }

        [Test]
        public async Task GeneratePreSignedUrl_ShouldReturnUrl_WhenCalled()
        {
            // Arrange
            const string bucketName = "test-bucket";
            const string key = "test-file.txt";
            const string expectedUrl = "https://example.com/signed-url";

            _mockS3Client.Setup(x => x.GeneratePreSignedURL(bucketName, key, It.IsAny<DateTime>(), null))
                .Returns(expectedUrl);

            // Act
            var result = await _s3FileService.GeneratePreSignedUrl(bucketName, key);

            // Assert
            Assert.AreEqual(expectedUrl, result);
            _mockS3Client.Verify(x => x.GeneratePreSignedURL(bucketName, key, It.IsAny<DateTime>(), null), Times.Once);
        }

        [Test]
        public async Task PersistFile_ShouldInvokePutObjectAsync_WhenCalled()
        {
            // Arrange
            const string bucketName = "test-bucket";
            const string key = "test-file.txt";
            const string content = "Test content";
            var putObjectRequest = new PutObjectRequest
            {
                BucketName = bucketName,
                Key = key,
                ContentBody = content
            };

            var putObjectResponse = new PutObjectResponse();
            _mockS3Client.Setup(x => x.PutObjectAsync(It.IsAny<PutObjectRequest>(), default))
                .ReturnsAsync(putObjectResponse);
            
            // Act
            await _s3FileService.PersistFile(putObjectRequest);

            // Assert
            _mockS3Client.Verify(x => x.PutObjectAsync(It.IsAny<PutObjectRequest>(), default), Times.Once);
        }
    }
}