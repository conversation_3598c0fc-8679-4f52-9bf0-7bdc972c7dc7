using System.Text.Json;
using Amazon.Lambda.S3Events;
using BusinessLayer.Model;
using BusinessLayer.Model.Filter;
using BusinessLayer.Model.Response;
using Export.Services.File;
using Export.Services.Payload.S3;
using Moq;

namespace Export.tests.Services.Payload.S3
{
    [TestFixture]
    public class S3PayloadServiceTests
    {
        private Mock<IS3FileService> _mockFileService;
        private S3PayloadService _s3PayloadService;

        [SetUp]
        public void Setup()
        {
            _mockFileService = new Mock<IS3FileService>();
            _s3PayloadService = new S3PayloadService(_mockFileService.Object);
        }

        [Test]
        public async Task GetPayload_ShouldReturnExportPayload_WhenValidData()
        {
            // Arrange
            const string bucketName = "test-bucket";
            const string objectKey = "test-key";

            var validPayload = new ExportPayload
            {
                AccountId = 123,
                ProductType = ProductType.NewForcedExposure,
                IsAdmin = true,
                BrandId = 456,
                StudyId = 789,
                Audience = "CustomAudience",
                ProduceEmptyAudiences = false,
                MinViewThresholdForScores = 100,
                ExportFolderPath = "/export/path/",
                BucketName = "test-bucket",
                ExportFileName = "export-file.json",
                Filters = new List<GridFilterItem>
                {
                    new() { ColumnField = "Region", OrQueryGroupId = 1, Value = "USA" },
                    new() { ColumnField = "AgeGroup", OrQueryGroupId = 2, Value = "18" }
                },
                VisibleColumnsOrder = new List<string> { "ColumnA", "ColumnB", "ColumnC" },
                Sorting = new List<GridSortingItem>
                {
                    new() { Field = "Date", Direction = "desc" }
                },
                Media = new List<Media>
                {
                    new() { TaskID = "1", SourceMediaID = 1 },
                    new() { TaskID = "2", SourceMediaID = 2 }
                },
                SegmentKeys = new List<string> { "Segment1", "Segment2" }
            };

            var s3Event = CreateS3Event(bucketName, objectKey);
            var content = JsonSerializer.Serialize(validPayload);

            _mockFileService.Setup(fs => fs.GetFileContentAsync(bucketName, objectKey))
                .ReturnsAsync(content);

            // Act
            var result = await _s3PayloadService.GetPayload(s3Event);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(123, result.AccountId);
            Assert.AreEqual(ProductType.NewForcedExposure, result.ProductType);
            Assert.IsTrue(result.IsAdmin);
            Assert.AreEqual(456, result.BrandId);
            Assert.AreEqual(789, result.StudyId);
            Assert.AreEqual("CustomAudience", result.Audience);
            Assert.IsFalse(result.ProduceEmptyAudiences);
            Assert.AreEqual(100, result.MinViewThresholdForScores);
            Assert.AreEqual("/export/path/", result.ExportFolderPath);
            Assert.AreEqual("test-bucket", result.BucketName);
            Assert.AreEqual("export-file.json", result.ExportFileName);

            Assert.AreEqual(2, result.Filters.Count);
            Assert.AreEqual("Region", result.Filters[0].ColumnField);
            Assert.AreEqual(1, result.Filters[0].OrQueryGroupId);
            Assert.AreEqual("USA", result.Filters[0].Value);
            Assert.AreEqual("AgeGroup", result.Filters[1].ColumnField);
            Assert.AreEqual(2, result.Filters[1].OrQueryGroupId);
            Assert.AreEqual("18", result.Filters[1].Value);

            Assert.AreEqual(3, result.VisibleColumnsOrder.Count);
            Assert.AreEqual("ColumnA", result.VisibleColumnsOrder[0]);
            Assert.AreEqual("ColumnB", result.VisibleColumnsOrder[1]);
            Assert.AreEqual("ColumnC", result.VisibleColumnsOrder[2]);

            Assert.AreEqual(1, result.Sorting.Count);
            Assert.AreEqual("Date", result.Sorting[0].Field);
            Assert.AreEqual("desc", result.Sorting[0].Direction);

            Assert.AreEqual(2, result.Media.Count);
            Assert.AreEqual("1", result.Media[0].TaskID);
            Assert.AreEqual(1, result.Media[0].SourceMediaID);
            Assert.AreEqual("2", result.Media[1].TaskID);
            Assert.AreEqual(2, result.Media[1].SourceMediaID);

            Assert.AreEqual(2, result.SegmentKeys.Count);
            Assert.AreEqual("Segment1", result.SegmentKeys[0]);
            Assert.AreEqual("Segment2", result.SegmentKeys[1]);
        }

        [Test]
        public async Task GetPayload_ShouldReturnNull_WhenFileFetchFails()
        {
            // Arrange
            const string bucketName = "test-bucket";
            const string objectKey = "test-key";
            var s3Event = CreateS3Event(bucketName, objectKey);

            _mockFileService.Setup(fs => fs.GetFileContentAsync(bucketName, objectKey))
                .ThrowsAsync(new Exception("File fetch error"));

            // Act
            var result = await _s3PayloadService.GetPayload(s3Event);

            // Assert
            Assert.IsNull(result);
            _mockFileService.Verify(fs => fs.GetFileContentAsync(bucketName, objectKey), Times.Once);
        }

        [Test]
        public async Task GetPayload_ShouldReturnNull_WhenNoRecordsInEvent()
        {
            // Arrange
            var s3Event = new S3Event { Records = new List<S3Event.S3EventNotificationRecord>() };

            // Act
            var result = await _s3PayloadService.GetPayload(s3Event);

            // Assert
            Assert.IsNull(result);
            _mockFileService.Verify(fs => fs.GetFileContentAsync(It.IsAny<string>(), It.IsAny<string>()), Times.Never);
        }

        private S3Event CreateS3Event(string bucketName, string objectKey)
        {
            return new S3Event
            {
                Records = new List<S3Event.S3EventNotificationRecord>
                {
                    new()
                    {
                        S3 = new S3Event.S3Entity
                        {
                            Bucket = new S3Event.S3BucketEntity { Name = bucketName },
                            Object = new S3Event.S3ObjectEntity { Key = objectKey }
                        }
                    }
                }
            };
        }
    }
}