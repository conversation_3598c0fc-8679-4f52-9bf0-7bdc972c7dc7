using BusinessLayer.Model.Response;
using Export.Excel;
using NUnit.Framework;
using System.Data;

namespace Export.tests.Services;

[TestFixture]
public class DataTableConverterTests
{
    private DataTableConverter _converter;

    [SetUp]
    public void Setup()
    {
        _converter = new DataTableConverter();
    }

    [Test]
    public void ToDataTable_WithInContextCreativeWithCurve_ShouldCreateCorrectDataTable()
    {
        // Arrange
        var data = new List<InContextCreativeWithCurve>
        {
            new InContextCreativeWithCurve
            {
                Brand = "TestBrand",
                SourceMedia = "TestMedia",
                Second = 1,
                Happiness = 0.8m,
                HappinessNorm = 0.7m,
                Attention = null // This should be handled correctly
            }
        };

        var visibleColumns = new List<string>
        {
            nameof(InContextCreativeWithCurve.Brand),
            nameof(InContextCreativeWithCurve.SourceMedia),
            nameof(InContextCreativeWithCurve.Second),
            nameof(InContextCreativeWithCurve.Happiness),
            nameof(InContextCreativeWithCurve.HappinessNorm),
            nameof(InContextCreativeWithCurve.Attention)
        };

        // Act
        var result = _converter.ToDataTable(data.Cast<object>(), visibleColumns);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Rows.Count, Is.EqualTo(1));
        Assert.That(result.Columns.Count, Is.EqualTo(6));
        
        // Check column names
        Assert.That(result.Columns.Contains(nameof(InContextCreativeWithCurve.Brand)), Is.True);
        Assert.That(result.Columns.Contains(nameof(InContextCreativeWithCurve.SourceMedia)), Is.True);
        Assert.That(result.Columns.Contains(nameof(InContextCreativeWithCurve.Second)), Is.True);
        Assert.That(result.Columns.Contains(nameof(InContextCreativeWithCurve.Happiness)), Is.True);
        Assert.That(result.Columns.Contains(nameof(InContextCreativeWithCurve.HappinessNorm)), Is.True);
        Assert.That(result.Columns.Contains(nameof(InContextCreativeWithCurve.Attention)), Is.True);
        
        // Check data values
        var row = result.Rows[0];
        Assert.That(row[nameof(InContextCreativeWithCurve.Brand)], Is.EqualTo("TestBrand"));
        Assert.That(row[nameof(InContextCreativeWithCurve.SourceMedia)], Is.EqualTo("TestMedia"));
        Assert.That(row[nameof(InContextCreativeWithCurve.Second)], Is.EqualTo(1));
        Assert.That(row[nameof(InContextCreativeWithCurve.Happiness)], Is.EqualTo(0.8m));
        Assert.That(row[nameof(InContextCreativeWithCurve.HappinessNorm)], Is.EqualTo(0.7m));
        Assert.That(row[nameof(InContextCreativeWithCurve.Attention)], Is.EqualTo(DBNull.Value));
    }

    [Test]
    public void ToDataTable_WithForcedExposureCreativeWithCurve_ShouldCreateCorrectDataTable()
    {
        // Arrange
        var data = new List<ForcedExposureCreativeWithCurve>
        {
            new ForcedExposureCreativeWithCurve
            {
                Brand = "TestBrand2",
                SourceMedia = "TestMedia2",
                Second = 2,
                Happiness = 0.9m,
                Attention = 0.85m
            }
        };

        var visibleColumns = new List<string>
        {
            nameof(ForcedExposureCreativeWithCurve.Brand),
            nameof(ForcedExposureCreativeWithCurve.SourceMedia),
            nameof(ForcedExposureCreativeWithCurve.Second),
            nameof(ForcedExposureCreativeWithCurve.Happiness),
            nameof(ForcedExposureCreativeWithCurve.Attention)
        };

        // Act
        var result = _converter.ToDataTable(data.Cast<object>(), visibleColumns);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Rows.Count, Is.EqualTo(1));
        Assert.That(result.Columns.Count, Is.EqualTo(5));
        
        // Check data values
        var row = result.Rows[0];
        Assert.That(row[nameof(ForcedExposureCreativeWithCurve.Brand)], Is.EqualTo("TestBrand2"));
        Assert.That(row[nameof(ForcedExposureCreativeWithCurve.SourceMedia)], Is.EqualTo("TestMedia2"));
        Assert.That(row[nameof(ForcedExposureCreativeWithCurve.Second)], Is.EqualTo(2));
        Assert.That(row[nameof(ForcedExposureCreativeWithCurve.Happiness)], Is.EqualTo(0.9m));
        Assert.That(row[nameof(ForcedExposureCreativeWithCurve.Attention)], Is.EqualTo(0.85m));
    }

    [Test]
    public void ToDataTable_WithEmptyData_ShouldReturnEmptyDataTable()
    {
        // Arrange
        var data = new List<InContextCreativeWithCurve>();
        var visibleColumns = new List<string> { "Brand", "SourceMedia" };

        // Act
        var result = _converter.ToDataTable(data.Cast<object>(), visibleColumns);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Rows.Count, Is.EqualTo(0));
        Assert.That(result.Columns.Count, Is.EqualTo(0));
    }

    [Test]
    public void ToDataTable_WithFilteredColumns_ShouldOnlyIncludeSpecifiedColumns()
    {
        // Arrange
        var data = new List<InContextCreativeWithCurve>
        {
            new InContextCreativeWithCurve
            {
                Brand = "TestBrand",
                SourceMedia = "TestMedia",
                Second = 1,
                Happiness = 0.8m,
                Attention = 0.9m,
                Negativity = 0.2m // This should not be included
            }
        };

        var visibleColumns = new List<string>
        {
            nameof(InContextCreativeWithCurve.Brand),
            nameof(InContextCreativeWithCurve.Happiness)
            // Note: Negativity is not included in visible columns
        };

        // Act
        var result = _converter.ToDataTable(data.Cast<object>(), visibleColumns);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Rows.Count, Is.EqualTo(1));
        Assert.That(result.Columns.Count, Is.EqualTo(2));
        
        // Should only have the specified columns
        Assert.That(result.Columns.Contains(nameof(InContextCreativeWithCurve.Brand)), Is.True);
        Assert.That(result.Columns.Contains(nameof(InContextCreativeWithCurve.Happiness)), Is.True);
        Assert.That(result.Columns.Contains(nameof(InContextCreativeWithCurve.Negativity)), Is.False);
        
        // Check data values
        var row = result.Rows[0];
        Assert.That(row[nameof(InContextCreativeWithCurve.Brand)], Is.EqualTo("TestBrand"));
        Assert.That(row[nameof(InContextCreativeWithCurve.Happiness)], Is.EqualTo(0.8m));
    }

    [Test]
    public void ToDataTable_WithFloatValues_ShouldConvertToDecimal()
    {
        // This test verifies that float values are converted to decimal for Excel precision
        // The conversion happens in the column type setup, so we're testing the type conversion logic
        
        // Arrange
        var data = new List<InContextCreativeWithCurve>
        {
            new InContextCreativeWithCurve
            {
                Brand = "TestBrand",
                // Note: The actual properties are already decimal, but this tests the type conversion logic
                Happiness = 0.123456789m
            }
        };

        var visibleColumns = new List<string>
        {
            nameof(InContextCreativeWithCurve.Brand),
            nameof(InContextCreativeWithCurve.Happiness)
        };

        // Act
        var result = _converter.ToDataTable(data.Cast<object>(), visibleColumns);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Columns[nameof(InContextCreativeWithCurve.Happiness)].DataType, Is.EqualTo(typeof(decimal)));
        
        var row = result.Rows[0];
        Assert.That(row[nameof(InContextCreativeWithCurve.Happiness)], Is.EqualTo(0.123456789m));
    }
}
