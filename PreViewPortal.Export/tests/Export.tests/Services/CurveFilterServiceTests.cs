using BusinessLayer.Model;
using BusinessLayer.Model.Filter;
using BusinessLayer.Model.Response;
using BusinessLayer.Services;
using NUnit.Framework;

namespace Export.tests.Services;

[TestFixture]
public class CurveFilterServiceTests
{
    [Test]
    public void FilterCurves_WithHappinessFilter_ShouldKeepOnlyHappinessData()
    {
        // Arrange
        var curves = new List<InContextCurve>
        {
            new InContextCurve
            {
                SourceMediaID = 1,
                TestID = 1,
                TaskID = "task1",
                Second = 1,
                SegmentKey = "segment1",
                Happiness = 0.8f,
                HappinessNorm = 0.7f,
                HappinessIC = 0.6f,
                HappinessICNorm = 0.5f,
                Attention = 0.9f,
                AttentionNorm = 0.8f,
                Negativity = 0.3f,
                NegativityNorm = 0.2f
            }
        };

        var filters = new List<CurveFilterItem>
        {
            new CurveFilterItem { Type = CurveType.Happiness }
        };

        // Act
        var result = CurveFilterService.FilterCurves(curves, filters);

        // Assert
        Assert.That(result, Has.Count.EqualTo(1));
        var filteredCurve = result.First();
        
        // Should keep identifying properties
        Assert.That(filteredCurve.SourceMediaID, Is.EqualTo(1));
        Assert.That(filteredCurve.TestID, Is.EqualTo(1));
        Assert.That(filteredCurve.TaskID, Is.EqualTo("task1"));
        Assert.That(filteredCurve.Second, Is.EqualTo(1));
        Assert.That(filteredCurve.SegmentKey, Is.EqualTo("segment1"));
        
        // Should keep happiness data
        Assert.That(filteredCurve.Happiness, Is.EqualTo(0.8f));
        Assert.That(filteredCurve.HappinessNorm, Is.EqualTo(0.7f));
        Assert.That(filteredCurve.HappinessIC, Is.EqualTo(0.6f));
        Assert.That(filteredCurve.HappinessICNorm, Is.EqualTo(0.5f));
        
        // Should remove other curve data
        Assert.That(filteredCurve.Attention, Is.Null);
        Assert.That(filteredCurve.AttentionNorm, Is.Null);
        Assert.That(filteredCurve.Negativity, Is.Null);
        Assert.That(filteredCurve.NegativityNorm, Is.Null);
    }

    [Test]
    public void FilterForcedExposureCurves_WithHappinessFilter_ShouldKeepOnlyHappinessData()
    {
        // Arrange
        var curves = new List<ForcedExposureCurve>
        {
            new ForcedExposureCurve
            {
                SourceMediaID = 1,
                TestID = 1,
                Second = 1,
                SegmentKey = "segment1",
                Happiness = 0.8f,
                HappinessNorm = 0.7f,
                Attention = 0.9f,
                AttentionNorm = 0.8f,
                Negativity = 0.3f,
                NegativityNorm = 0.2f
            }
        };

        var filters = new List<CurveFilterItem>
        {
            new CurveFilterItem { Type = CurveType.Happiness }
        };

        // Act
        var result = CurveFilterService.FilterForcedExposureCurves(curves, filters);

        // Assert
        Assert.That(result, Has.Count.EqualTo(1));
        var filteredCurve = result.First();
        
        // Should keep identifying properties
        Assert.That(filteredCurve.SourceMediaID, Is.EqualTo(1));
        Assert.That(filteredCurve.TestID, Is.EqualTo(1));
        Assert.That(filteredCurve.Second, Is.EqualTo(1));
        Assert.That(filteredCurve.SegmentKey, Is.EqualTo("segment1"));
        
        // Should keep happiness data
        Assert.That(filteredCurve.Happiness, Is.EqualTo(0.8f));
        Assert.That(filteredCurve.HappinessNorm, Is.EqualTo(0.7f));
        
        // Should remove other curve data
        Assert.That(filteredCurve.Attention, Is.Null);
        Assert.That(filteredCurve.AttentionNorm, Is.Null);
        Assert.That(filteredCurve.Negativity, Is.Null);
        Assert.That(filteredCurve.NegativityNorm, Is.Null);
    }

    [Test]
    public void FilterForcedExposureCurves_WithMultipleFilters_ShouldKeepMultipleCurveTypes()
    {
        // Arrange
        var curves = new List<ForcedExposureCurve>
        {
            new ForcedExposureCurve
            {
                SourceMediaID = 1,
                TestID = 1,
                Second = 1,
                SegmentKey = "segment1",
                Happiness = 0.8f,
                HappinessNorm = 0.7f,
                Attention = 0.9f,
                AttentionNorm = 0.8f,
                Negativity = 0.3f,
                NegativityNorm = 0.2f,
                Contempt = 0.1f,
                ContemptNorm = 0.05f
            }
        };

        var filters = new List<CurveFilterItem>
        {
            new CurveFilterItem { Type = CurveType.Happiness },
            new CurveFilterItem { Type = CurveType.Attention }
        };

        // Act
        var result = CurveFilterService.FilterForcedExposureCurves(curves, filters);

        // Assert
        Assert.That(result, Has.Count.EqualTo(1));
        var filteredCurve = result.First();
        
        // Should keep happiness and attention data
        Assert.That(filteredCurve.Happiness, Is.EqualTo(0.8f));
        Assert.That(filteredCurve.HappinessNorm, Is.EqualTo(0.7f));
        Assert.That(filteredCurve.Attention, Is.EqualTo(0.9f));
        Assert.That(filteredCurve.AttentionNorm, Is.EqualTo(0.8f));
        
        // Should remove other curve data
        Assert.That(filteredCurve.Negativity, Is.Null);
        Assert.That(filteredCurve.NegativityNorm, Is.Null);
        Assert.That(filteredCurve.Contempt, Is.Null);
        Assert.That(filteredCurve.ContemptNorm, Is.Null);
    }

    [Test]
    public void FilterForcedExposureCurves_WithEmptyFilters_ShouldReturnOriginalCurves()
    {
        // Arrange
        var curves = new List<ForcedExposureCurve>
        {
            new ForcedExposureCurve
            {
                SourceMediaID = 1,
                TestID = 1,
                Second = 1,
                SegmentKey = "segment1",
                Happiness = 0.8f,
                Attention = 0.9f
            }
        };

        var filters = new List<CurveFilterItem>();

        // Act
        var result = CurveFilterService.FilterForcedExposureCurves(curves, filters);

        // Assert
        Assert.That(result, Has.Count.EqualTo(1));
        var resultCurve = result.First();
        Assert.That(resultCurve.Happiness, Is.EqualTo(0.8f));
        Assert.That(resultCurve.Attention, Is.EqualTo(0.9f));
    }

    [Test]
    public void FilterForcedExposureCurves_WithNullCurves_ShouldReturnEmptyList()
    {
        // Arrange
        List<ForcedExposureCurve> curves = null;
        var filters = new List<CurveFilterItem>
        {
            new CurveFilterItem { Type = CurveType.Happiness }
        };

        // Act
        var result = CurveFilterService.FilterForcedExposureCurves(curves, filters);

        // Assert
        Assert.That(result, Is.Empty);
    }

    [Test]
    public void FilterForcedExposureCurves_WithNullFilters_ShouldReturnOriginalCurves()
    {
        // Arrange
        var curves = new List<ForcedExposureCurve>
        {
            new ForcedExposureCurve
            {
                SourceMediaID = 1,
                Happiness = 0.8f,
                Attention = 0.9f
            }
        };

        List<CurveFilterItem> filters = null;

        // Act
        var result = CurveFilterService.FilterForcedExposureCurves(curves, filters);

        // Assert
        Assert.That(result, Has.Count.EqualTo(1));
        var resultCurve = result.First();
        Assert.That(resultCurve.Happiness, Is.EqualTo(0.8f));
        Assert.That(resultCurve.Attention, Is.EqualTo(0.9f));
    }
}
