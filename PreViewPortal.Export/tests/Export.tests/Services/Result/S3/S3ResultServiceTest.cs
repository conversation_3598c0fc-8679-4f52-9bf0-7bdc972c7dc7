using System.Text.Json;
using Amazon.S3.Model;
using BusinessLayer.Model;
using Moq;
using NUnit.Framework;
using Export.Services.File;
using Export.Services.Result.S3;
using BusinessLayer.Model.Response;

namespace Export.Tests.Services.Result.S3;

[TestFixture]
public class S3ResultServiceTests
{
    private Mock<IS3FileService> _mockS3FileService;
    private S3ResultService _s3ResultService;

    [SetUp]
    public void Setup()
    {
        _mockS3FileService = new Mock<IS3FileService>();
        _s3ResultService = new S3ResultService(_mockS3FileService.Object);
    }

    [Test]
    public async Task ExportCompleted_ShouldUpdateExportResultWithCompletedStatus()
    {
        // Arrange
        var payload = CreateExportPayload();
        const string signedUrl = "https://s3.amazonaws.com/test-bucket/export/result.json";
        const string fileName = "result.json";

        var existingResult = new ExportResult
        {
            JobStatus = "InProgress",
            BucketName = payload.BucketName,
            ExportFolderPath = payload.ExportFolderPath
        };

        var resultJson = JsonSerializer.Serialize(existingResult);

        _mockS3FileService.Setup(s => s.GetFileContentAsync(payload.BucketName, $"{payload.ExportFolderPath}/result.json"))
            .ReturnsAsync(resultJson);

        _mockS3FileService.Setup(s => s.PersistFile(It.IsAny<PutObjectRequest>()))
            .Returns(Task.CompletedTask);

        // Act
        await _s3ResultService.ExportCompleted(payload, signedUrl, fileName);

        // Assert
        _mockS3FileService.Verify(s => s.PersistFile(It.Is<PutObjectRequest>(r =>
            r.BucketName == payload.BucketName &&
            r.Key == $"{payload.ExportFolderPath}/result.json" &&
            r.ContentBody.Contains("\"JobStatus\":\"Completed\"") &&
            r.ContentBody.Contains("\"ExportUrl\":\"https://s3.amazonaws.com/test-bucket/export/result.json\"") &&
            r.ContentBody.Contains("\"ExportFileName\":\"result.json\"")
        )), Times.Once);
    }

    [Test]
    public async Task ExportFailed_ShouldUpdateExportResultWithFailedStatus()
    {
        // Arrange
        var payload = CreateExportPayload();

        var existingResult = new ExportResult
        {
            JobStatus = "InProgress",
            BucketName = payload.BucketName,
            ExportFolderPath = payload.ExportFolderPath
        };

        var resultJson = JsonSerializer.Serialize(existingResult);

        _mockS3FileService.Setup(s => s.GetFileContentAsync(payload.BucketName, $"{payload.ExportFolderPath}/result.json"))
            .ReturnsAsync(resultJson);

        _mockS3FileService.Setup(s => s.PersistFile(It.IsAny<PutObjectRequest>()))
            .Returns(Task.CompletedTask);

        // Act
        await _s3ResultService.ExportFailed(payload);

        // Assert
        _mockS3FileService.Verify(s => s.PersistFile(It.Is<PutObjectRequest>(r =>
            r.BucketName == payload.BucketName &&
            r.Key == $"{payload.ExportFolderPath}/result.json" &&
            r.ContentBody.Contains("\"JobStatus\":\"Failed\"")
        )), Times.Once);
    }

    [Test]
    public void GetExportResult_ShouldThrowException_WhenFileContentIsInvalid()
    {
        // Arrange
        var payload = CreateExportPayload();
        _mockS3FileService.Setup(s => s.GetFileContentAsync(payload.BucketName, $"{payload.ExportFolderPath}/result.json"))
            .ThrowsAsync(new InvalidOperationException("Invalid file content"));

        // Act & Assert
        Assert.ThrowsAsync<InvalidOperationException>(async () =>
            await _s3ResultService.ExportFailed(payload)
        );

        _mockS3FileService.Verify(s => s.GetFileContentAsync(payload.BucketName, $"{payload.ExportFolderPath}/result.json"), Times.Once);
    }

    [Test]
    public void UpdateExportResult_ShouldThrowException_WhenPersistFileFails()
    {
        // Arrange
        var payload = CreateExportPayload();

        var existingResult = new ExportResult
        {
            JobStatus = "InProgress",
            BucketName = payload.BucketName,
            ExportFolderPath = payload.ExportFolderPath
        };

        var resultJson = JsonSerializer.Serialize(existingResult);

        _mockS3FileService.Setup(s => s.GetFileContentAsync(payload.BucketName, $"{payload.ExportFolderPath}/result.json"))
            .ReturnsAsync(resultJson);

        _mockS3FileService.Setup(s => s.PersistFile(It.IsAny<PutObjectRequest>()))
            .ThrowsAsync(new InvalidOperationException("Failed to persist file"));

        // Act & Assert
        Assert.ThrowsAsync<InvalidOperationException>(async () =>
            await _s3ResultService.ExportFailed(payload)
        );

        _mockS3FileService.Verify(s => s.PersistFile(It.IsAny<PutObjectRequest>()), Times.Once);
    }

    private ExportPayload CreateExportPayload()
    {
        return new ExportPayload
        {
            AccountId = 123,
            ProductType = ProductType.NewForcedExposure,
            BucketName = "test-bucket",
            ExportFolderPath = "export/path",
            ExportFileName = "result.json"
        };
    }
}
