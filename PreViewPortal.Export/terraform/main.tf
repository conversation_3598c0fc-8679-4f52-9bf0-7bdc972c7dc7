terraform {
  backend "s3" {
    bucket = "preview-terraform-workspaces"
    key = "preview-portal-export"
	  region = "eu-west-1"
  }
}

provider "aws" {
  region = var.region
  # This is needed only for local deployment
  # ignore_tags {
  #   keys = ["AutoTag_CreateTime", "AutoTag_Creator"]
  # }
  default_tags {
    tags = {
      Costgroup   = var.cost_group
      product     = "preview"
      stage       = var.stage
    }
  }
}

locals {
  service_prefix_with_stage = "${var.stage}-${var.service_prefix}"
  dremio_environment = var.stage == "live" || var.stage == "beta" ? "live" : "stage"
}

data "aws_subnet" "subnet_1" {
  id = "subnet-5312240a"  
}

data "aws_subnet" "subnet_2" {
  id = "subnet-1dae5e79" 
}

data "aws_security_group" "sec_group" {
  id = "sg-8b383cef" 
}

resource "aws_iam_role" "lambda_role" {
  name = "${local.service_prefix_with_stage}-lambda-execution-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action    = "sts:AssumeRole"
        Effect    = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "lambda_execution_policy" {
  role       = aws_iam_role.lambda_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

resource "aws_iam_role_policy_attachment" "AWSLambdaVPCAccessExecutionRole" {
    role       = aws_iam_role.lambda_role.name
    policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole"
}

resource "aws_iam_role_policy" "lambda_custom_policy" {
  name   = "${aws_iam_role.lambda_role.name}-custom-policy"
  role   =  aws_iam_role.lambda_role.name

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:*",
          "ssm:GetParametersByPath",
          "ssm:GetParameters",
          "ssm:GetParameter"
        ]
        Resource = [
          "${aws_s3_bucket.s3_export_bucket.arn}",
          "${aws_s3_bucket.s3_export_bucket.arn}/*",
          "arn:aws:ssm:eu-west-1:249265253269:parameter/${local.dremio_environment}/administration/dremio/preview/*"
        ]
      }
    ]
  })
}

resource "aws_lambda_function" "export_lambda" {
  function_name = "${local.service_prefix_with_stage}-lambda"
  role          = aws_iam_role.lambda_role.arn
  handler       = "Export::Export.LambdaEntryPoint::Export"
  runtime       = "dotnet8"
  memory_size      = 4096
  timeout          = 300

  filename        = "../src/Export/publish/export.zip"
  source_code_hash = filebase64sha256("../src/Export/publish/export.zip")

  environment {
    variables = {
      ASPNETCORE_ENVIRONMENT                 = var.dotnet_enviroment
      Dremio__BaseUrl                        = "$${ssm:/${local.dremio_environment}/administration/dremio/preview/baseUrl}"
      Dremio__Username                       = "$${ssm:/${local.dremio_environment}/administration/dremio/preview/username}"
      Dremio__Password                       = "$${ssm:/${local.dremio_environment}/administration/dremio/preview/password}"
    }
  }

  vpc_config {
    subnet_ids          = [data.aws_subnet.subnet_1.id, data.aws_subnet.subnet_2.id]
    security_group_ids  = [data.aws_security_group.sec_group.id]
  }
}

resource "aws_cloudwatch_log_group" "lambda_log_group" {
  name = "/aws/lambda/${aws_lambda_function.export_lambda.function_name}"
  retention_in_days = 30 
}

resource "aws_s3_bucket" "s3_export_bucket" {
  bucket = "${local.service_prefix_with_stage}-bucket"
}

resource "aws_s3_bucket_lifecycle_configuration" "bucket_lifecycle" {
  bucket = aws_s3_bucket.s3_export_bucket.bucket

  rule {
    id     = "expire-objects-after-30-days"
    status = "Enabled"
    
    filter {
      prefix = ""
    }

    expiration {
      days = 30
    }
  }
}

resource "aws_lambda_permission" "s3_permission" {
  statement_id  = "AllowS3Invoke"
  action        = "lambda:InvokeFunction" 
  function_name = aws_lambda_function.export_lambda.function_name  
  principal     = "s3.amazonaws.com"
  source_arn    = aws_s3_bucket.s3_export_bucket.arn
}

resource "aws_s3_bucket_notification" "s3_event" {
  bucket = aws_s3_bucket.s3_export_bucket.bucket

  depends_on = [aws_lambda_function.export_lambda, aws_s3_bucket.s3_export_bucket, aws_s3_bucket_lifecycle_configuration.bucket_lifecycle]

  lambda_function {
    events = ["s3:ObjectCreated:Put"]
    filter_suffix = "configuration.json"
    lambda_function_arn = aws_lambda_function.export_lambda.arn
  }
}