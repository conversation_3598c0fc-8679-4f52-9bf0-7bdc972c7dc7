#!/bin/bash

bot_token=${1}
channel_id=${2}
before_or_after=${3}
job_status=${4}
github_repository=${5}
github_sha=${6}
github_ref=${7}
github_run_id=${8}
github_actor=${9}
env_name=${10}

repo_name=${github_repository#*/}
branch_name=${github_ref#refs/*/}

repo_url="https://github.com/$github_repository"
commit_url="$repo_url/commit/$github_sha"
env_url="$repo_url/deployments?environment=$env_name#activity-log"
action_url="$repo_url/actions/runs/$github_run_id"
actor_url="https://github.com/$github_actor"
slack_api_url="https://slack.com/api/chat.postMessage"

if [ $before_or_after == before ]
then
  color_value="warning" 
  message="has started deploying"
elif [ $job_status == success ]
then
  color_value="good" 
  message="was successfully deployed"
else
  color_value="danger" 
  message="failed deploying"
fi

curl -X POST -H "Content-Type:application/json" -H "Authorization:Bearer $bot_token" -d '{"channel":"'"$channel_id"'","attachments":[{"color":"'"$color_value"'","text":"<'"$repo_url"'|*'"$repo_name"'*> \n <'"$commit_url"'|'"$branch_name"'> '"$message"' to <'"$env_url"'|'"$env_name"'>. <'"$action_url"'|See details>. \n Responsible User: <'"$actor_url"'|'"$github_actor"'>."}]}' $slack_api_url
