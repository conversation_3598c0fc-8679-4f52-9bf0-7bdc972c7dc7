terraform {
  backend "s3" {
    bucket = "preview-terraform-workspaces"
    key = "preview-portal"
	  region = "eu-west-1"
  }
}

provider "aws" {
  region = var.region
  # This is needed only for local deployment
  # ignore_tags {
  #   keys = ["AutoTag_CreateTime", "AutoTag_Creator"]
  # }
  default_tags {
    tags = {
      Costgroup   = var.cost_group
      product     = "preview"
      stage       = var.stage
    }
  }
}

provider "aws" {
  region = var.region
  alias  = "aws-without-default-tags"
}

locals {
  api_gateway_url = "${aws_api_gateway_rest_api.api.id}.execute-api.${var.region}.amazonaws.com"
  service_prefix_with_stage = "${var.stage}-${var.service_prefix}"
}


data "aws_subnet" "subnet_1" {
  id = "subnet-5312240a"  
}

data "aws_subnet" "subnet_2" {
  id = "subnet-1dae5e79" 
}

data "aws_security_group" "sec_group" {
  id = "sg-8b383cef" 
}

resource "aws_iam_role" "lambda_role" {
  name = "${local.service_prefix_with_stage}-lambda-execution-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action    = "sts:AssumeRole"
        Effect    = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "lambda_execution_policy" {
  role       = aws_iam_role.lambda_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

resource "aws_iam_role_policy_attachment" "AWSLambdaVPCAccessExecutionRole" {
    role       = aws_iam_role.lambda_role.name
    policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole"
}

resource "aws_iam_role_policy" "lambda_ssm_policy" {
  name   = "${aws_iam_role.lambda_role.name}-ssm-policy"
  role   =  aws_iam_role.lambda_role.name

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:PutObject",
          "s3:GetObject",
          "s3:ListBucket",
          "ssm:GetParametersByPath",
          "ssm:GetParameters",
          "ssm:GetParameter"
        ]
        Resource = concat([
              "arn:aws:s3:::${var.stage}-preview-portal-export-bucket",
              "arn:aws:s3:::${var.stage}-preview-portal-export-bucket/*",
              "arn:aws:ssm:eu-west-1:249265253269:parameter/${var.environment}/databases/common/connectionString",
              "arn:aws:ssm:eu-west-1:249265253269:parameter/live/site/preview/sourceMediaDataBucket/*",
              "arn:aws:ssm:eu-west-1:249265253269:parameter/${var.environment}/site/clientportal/jwtSecretKey",
              "arn:aws:ssm:eu-west-1:249265253269:parameter/preview/iam/eu-west-1/${var.environment}/backend/issuer",
              "arn:aws:ssm:eu-west-1:249265253269:parameter/${var.environment}/administration/dremio/preview/*",
              "arn:aws:ssm:eu-west-1:249265253269:parameter/live/site/preview/internalApiSecret"],
            [
              for audience in var.valid_audiences : "arn:aws:ssm:eu-west-1:249265253269:parameter/${audience}"
            ])
      }
    ]
  })
}

resource "aws_lambda_function" "my_lambda" {
  function_name = "${local.service_prefix_with_stage}-lambda"
  role          = aws_iam_role.lambda_role.arn
  handler       = "WebAPI"
  runtime       = "dotnet8"
  memory_size      = 5120
  timeout          = 30

  filename        = "../PreViewPortal.API/src/WebAPI/publish/lambda-package.zip"
  source_code_hash = filebase64sha256("../PreViewPortal.API/src/WebAPI/publish/lambda-package.zip")

    environment {
    variables = merge(
      {
          ASPNETCORE_ENVIRONMENT                        = var.dotnet_environment
          Startup__ShouldResolveSecrets                 = true
          ConnectionStrings__StudyDatabase              = "$${ssm:/${var.environment}/databases/common/connectionString}"
          ConnectionStrings__Redis                      = "${aws_elasticache_cluster.redis_cluster.cluster_id}.e3otyc.0001.euw1.cache.amazonaws.com"
          SourceMediaDataBucket__KeyPairId              = "$${ssm:/live/site/preview/sourceMediaDataBucket/keyPairId}"
          SourceMediaDataBucket__PrivateKey             = "$${ssm:/live/site/preview/sourceMediaDataBucket/privateKey}"
          RealeyesJwtAuthentication__SigningKey         = "$${ssm:/${var.environment}/site/clientportal/jwtSecretKey}"
          RealeyesJwtAuthentication__ValidIssuer        = "$${ssm:/preview/iam/eu-west-1/${var.environment}/backend/issuer}"
          Dremio__BaseUrl                               = "$${ssm:/${var.environment}/administration/dremio/preview/baseUrl}"
          Dremio__Username                              = "$${ssm:/${var.environment}/administration/dremio/preview/username}"
          Dremio__Password                              = "$${ssm:/${var.environment}/administration/dremio/preview/password}"
          ApiAuthorization__InternalApiSecret           = "$${ssm:/live/site/preview/internalApiSecret}"
      },
      {
          for idx, audience in var.valid_audiences :
          "RealeyesJwtAuthentication__ValidAudiences__${idx}" => "$${ssm:/${audience}}"
      }
    )
  }

  vpc_config {
    subnet_ids          = [data.aws_subnet.subnet_1.id, data.aws_subnet.subnet_2.id]
    security_group_ids  = [data.aws_security_group.sec_group.id]
  }

   depends_on = [aws_elasticache_cluster.redis_cluster]
}

resource "aws_cloudwatch_log_group" "lambda_log_group" {
  name = "/aws/lambda/${aws_lambda_function.my_lambda.function_name}"
  retention_in_days = 30 
}

resource "aws_api_gateway_rest_api" "api" {
  name        =  "${local.service_prefix_with_stage}-gateway"
  description = "API Gateway for .NET Lambda"
  binary_media_types = ["*/*"]
}

resource "aws_api_gateway_resource" "proxy_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "{proxy+}"  
}

resource "aws_api_gateway_method" "proxy_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.proxy_resource.id
  http_method   = "ANY"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "lambda_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.proxy_resource.id
  http_method             = aws_api_gateway_method.proxy_method.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = aws_lambda_function.my_lambda.invoke_arn
}

resource "aws_lambda_permission" "allow_api_gateway" {
  statement_id  = "AllowApiGatewayInvoke"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.my_lambda.function_name
  principal     = "apigateway.amazonaws.com"
}

resource "aws_api_gateway_deployment" "api_deployment" {
  depends_on = [aws_api_gateway_integration.lambda_integration]
  rest_api_id = aws_api_gateway_rest_api.api.id
}

resource "aws_api_gateway_stage" "api_stage" {
  rest_api_id  = aws_api_gateway_rest_api.api.id
  deployment_id = aws_api_gateway_deployment.api_deployment.id
  stage_name   =  var.stage
  description  = "API Gateway Stage"
}


resource "aws_s3_bucket" "ui_bucket" {
  bucket = "${local.service_prefix_with_stage}-ui-bucket"
}

resource "aws_s3_object" "ui_files" {
  for_each = fileset("../PreViewPortal.UI/build", "**")

  bucket = aws_s3_bucket.ui_bucket.bucket
  key    = each.key
  source = "../PreViewPortal.UI/build/${each.key}"

  #to upload only changed files
  etag   = filemd5("../PreViewPortal.UI/build/${each.key}")

  #otherwise terraform uploads files as "application/octet-stream"
  content_type = lookup({
    "html" = "text/html"
    "css"  = "text/css"
    "js"   = "application/javascript"
    "json" = "application/json"
    "svg"  = "image/svg+xml"
    "png"  = "image/png"
    "jpg"  = "image/jpeg"
    "jpeg" = "image/jpeg"
    "ico"  = "image/x-icon"
    "txt"  = "text/plain"
    "md"  =  "text/markdown"
  }, lower(element(reverse(split(".", each.key)), 0)), "application/octet-stream")


  cache_control = lookup(
    {
      "index.html" = "no-cache",
      "config.js"  = "no-cache"
    },
    each.key,
    startswith(each.key, "assets/") ? "public, max-age=31536000, immutable" : null
  )

  #not to apply default resources tags
  provider = aws.aws-without-default-tags

  depends_on = [aws_s3_bucket.ui_bucket, aws_api_gateway_deployment.api_deployment]
}

resource "aws_cloudfront_origin_access_identity" "oai" {
  comment = "access-identity-${aws_s3_bucket.ui_bucket.bucket}.s3.amazonaws.com"
}

resource "aws_s3_bucket_policy" "ui_bucket_policy" {
  bucket = aws_s3_bucket.ui_bucket.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
        {
            Effect = "Allow"
            Principal = {
                  AWS = "${aws_cloudfront_origin_access_identity.oai.iam_arn}"
            }
            Action = [
                "s3:PutObject",
                "s3:GetObject",
                "s3:ListBucket"
            ]
            Resource = [
                "${aws_s3_bucket.ui_bucket.arn}/*",       
                "${aws_s3_bucket.ui_bucket.arn}"              
            ]
        }
    ]
  })

  depends_on = [aws_cloudfront_origin_access_identity.oai, aws_s3_bucket.ui_bucket]
}

resource "aws_cloudfront_distribution" "cdn" {
  enabled             = true
  is_ipv6_enabled     = true
  default_root_object = "index.html"
  comment             = "CDN for PreView Portal (${var.stage})"
  price_class         = var.price_class
  aliases = [var.domain_name]

  logging_config {
    bucket = "cdnaccesslogs.s3.amazonaws.com"
    prefix = "${local.service_prefix_with_stage}-cdn-logs/"
    include_cookies = false
  }

  web_acl_id = var.stage == "beta" ? "arn:aws:wafv2:us-east-1:249265253269:global/webacl/vpn-only-ip-ranges/60442e68-d790-41db-8072-2491e6a665ad" : null

  viewer_certificate {
    acm_certificate_arn            = "arn:aws:acm:us-east-1:249265253269:certificate/ee7b495a-45ae-46e0-85bf-7cc095aafcee"
    ssl_support_method             = "sni-only"
    minimum_protocol_version       = "TLSv1.1_2016"
  }

  origin {
    domain_name = local.api_gateway_url
    origin_id   = "api"
    origin_path = "/${var.stage}"

    custom_origin_config {
      http_port              = 80
      https_port             = 443
      origin_protocol_policy = "https-only"
      origin_ssl_protocols   = ["TLSv1.2"]
    }
  }

  origin {
    domain_name = "${aws_s3_bucket.ui_bucket.bucket}.s3.eu-west-1.amazonaws.com"
    origin_id   = "ui"

    s3_origin_config {
      origin_access_identity = "origin-access-identity/cloudfront/${aws_cloudfront_origin_access_identity.oai.id}"
    }
  }

  default_cache_behavior {
    target_origin_id       = "ui"
    viewer_protocol_policy = "redirect-to-https"
    allowed_methods        = ["GET", "HEAD"]
    cached_methods         = ["GET", "HEAD"]

    cache_policy_id = "658327ea-f89d-4fab-a63d-7e88639e58f6"
  }

  ordered_cache_behavior {
    path_pattern           = "api/*"
    target_origin_id       = "api"
    viewer_protocol_policy = "https-only"
    allowed_methods        = ["GET", "HEAD", "OPTIONS", "PUT", "POST", "PATCH", "DELETE"]
    cached_methods         = ["GET", "HEAD"]

    cache_policy_id          = "4135ea2d-6df8-44a3-9df3-4b5a84be39ad"
    origin_request_policy_id = "b689b0a8-53d0-40ab-baf2-68738e2966ac"
  }

  custom_error_response {
    error_code            = 404
    response_code         = 404
    response_page_path    = "/index.html"
    error_caching_min_ttl = 300
  }

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  depends_on = [aws_cloudfront_origin_access_identity.oai, aws_s3_bucket.ui_bucket, aws_api_gateway_deployment.api_deployment]
}

resource "aws_route53_record" "route53" {
  zone_id = "Z3TDZUYT0CQR37"
  name    = var.domain_name
  type    = "CNAME"
  ttl     = 60

  records = [
    aws_cloudfront_distribution.cdn.domain_name
  ]

  depends_on = [aws_cloudfront_origin_access_identity.oai]
}

resource "aws_elasticache_subnet_group" "cache_subnet_group" {
  name        = "${local.service_prefix_with_stage}-elasticache-subnet-group"
  description = "PreView Portal ElastiCache Subnet Group (${var.stage})"
  subnet_ids  = [data.aws_subnet.subnet_1.id, data.aws_subnet.subnet_2.id]
}

resource "aws_elasticache_cluster" "redis_cluster" {
  cluster_id            = "${local.service_prefix_with_stage}-redis-cache-cluster"
  engine                = "redis"
  node_type             = "cache.t4g.micro"
  num_cache_nodes       = 1
  subnet_group_name     = aws_elasticache_subnet_group.cache_subnet_group.name
  security_group_ids    = [data.aws_security_group.sec_group.id]
  auto_minor_version_upgrade = true

  depends_on = [aws_elasticache_subnet_group.cache_subnet_group]
}

output "api_url" {
  value = local.api_gateway_url
}