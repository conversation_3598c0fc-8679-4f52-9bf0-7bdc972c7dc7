import logging
from pathlib import Path
from typing import Union, Callable

from DIET.dremio_api import <PERSON><PERSON><PERSON><PERSON><PERSON>, DremioApiEnviroment
from DIET.dremio_source import DremioFileSource, DremioSource, DremioUrlSource, DremioFolderSource
from DIET.dremio_target import DremioFileTarget, DremioTarget, DremioUrlTarget
from DIET.dremio_types import FolderExport
from DIET.util import load_config


class Dremio:
    def __init__(self) -> None:
        self.source_data: FolderExport = None

    def read_from_json(self, source_json_file_path: str):
        source = DremioFileSource(source_json_file_path)
        self.source_data = source.read()

    def read_from_folder(self, source_json_folder_path: str, catalog_path: str, file_pattern: str = None):
        source = DremioFolderSource(source_json_folder_path, catalog_path, file_pattern)
        self.source_data = source.read()

    def read_from_dremio(self, source_url: str, source_api: Dr<PERSON>io<PERSON><PERSON>, dataset_filter: Union[str, Callable[[str], bool]] = None,
                         skip_reflections: bool = False):
        source = DremioUrlSource(source_api, source_url, dataset_filter=dataset_filter, skip_reflections=skip_reflections)
        self.source_data = source.read()
        return self

    def write_to_json(self, target_folder_name="ChangeScripts/result.json"):
        target = DremioFileTarget(target_folder_name)
        target.write(self.source_data)
        return self

    def write_to_folder(self, target_folder_name="ChangeScripts", inline_sql_limit: int = 200):
        target = DremioFileTarget(target_folder_name, inline_sql_limit)
        target.write(self.source_data)
        return self

    def write_to_dremio(self, target_api: DremioApi, recreate_datasets: bool = False, update_reflections: bool = False):
        target = DremioUrlTarget(target_api, recreate_datasets=recreate_datasets, update_reflections=update_reflections)
        target.write(self.source_data)
        return self

    @staticmethod
    def set_config(path_or_folder: Path):
        if path_or_folder.is_file():
            load_config(path_or_folder)
        elif Path(path_or_folder/"config.json").exists():
            load_config(Path(path_or_folder/"config.json"))
        else:
            logging.warning(f"No config file found. Searched at: {path_or_folder}")
