import logging
from abc import ABC, abstractmethod
from collections import defaultdict, OrderedDict
from pathlib import Path
from shutil import rmtree
from DIET.dremio_api import <PERSON><PERSON>ioApi, DremioApiEnviroment
from dateutil.parser import parse
from datetime import datetime
from DIET.dremio_types import FolderImport, FolderExport, ReflectionImport
import json
import copy
import os

from DIET import util


class DremioTarget(ABC):

    @abstractmethod
    def write(self, *args, **kwargs):
        pass


class DremioFileTarget(DremioTarget):

    def __init__(self, target_folder_name, inline_sql_limit) -> None:
        super().__init__()
        self.target_folder_name = str(target_folder_name)
        self.inline_sql_limit = inline_sql_limit

    def write(self, target_data: FolderExport, empty_folder: bool = True):
        folder_name = self.target_folder_name
        folder_name = folder_name.replace('{timestamp}', datetime.utcnow().strftime("%Y-%m-%d_%H-%M-%S"))
        print(f'Export to file system started to folder/file: {folder_name}')
        if folder_name.lower().endswith('.json'):
            Path(folder_name).parent.mkdir(parents=True, exist_ok=True)
            self.create_json_file(target_data, folder_name)
        else:
            if empty_folder:
                rmtree(f"folder_name/{target_data['path']}", ignore_errors=True)
            Path(folder_name).mkdir(parents=True, exist_ok=True)
            self.create_folder_or_json_file(target_data, folder_name)

        print(f'Export to file system finished. Path: {folder_name}')
        return f"{folder_name}"

    def create_folder_or_json_file(self, node: FolderExport, folder_name) -> None:
        if node is None:
            return
        for node in node['children']:
            if node and 'folder' in node['catalog']['entityType']:
                self.create_folder_or_json_file(node, folder_name)
            if 'path' in node and node['path'] is not None:
                path = f"{folder_name}/{node['path']}"
                if node and 'dataset' in node['catalog']['entityType']:
                    dirPath = path[0:path.rfind('/')]
                    self.create_directory(dirPath)
                    self.create_dataset_file(node, path)

    def create_dataset_file(self, node, path: str) -> dict:
        catalog = node['catalog']
        essential_data = OrderedDict()
        if 'sql' in catalog:
            sql = catalog['sql']
            if len(sql) > self.inline_sql_limit:
                sql_path = path + '.sql'
                self.create_sql_file(sql, sql_path)
                essential_data['sql'] = Path(sql_path).name
            else:
                essential_data['sql'] = sql
            if 'sqlContext' in catalog:
                sql_context = '.'.join(catalog['sqlContext'])
                essential_data['sqlContext'] = sql_context

        if 'format' in catalog:
            essential_data['format'] = catalog['format']

        essential_data['depends'] = list(set(self.convert_path_list_to_string(p['path']) for p in node['parents'] if p['path'])) if node['parents'] else []
        essential_data['reflections'] = list(DremioFileTarget.trim_reflection_model(r) for r in node['reflections'])

        self.create_json_file(essential_data, path + '.json', sort_keys=False)
        return essential_data

    @staticmethod
    def create_directory(path):
        if not os.path.exists(path):
            os.makedirs(path, exist_ok=True)

    @staticmethod
    def trim_reflection_model(reflection):
        model = {'name': reflection['name'], 'type': reflection['type']}
        if reflection.get('sortFields', None):
            model['sortFields'] = list(f['name'] for f in reflection['sortFields'])
        if reflection.get('displayFields', None):
            model['displayFields'] = list(f['name'] for f in reflection['displayFields'])
        if reflection.get('dimensionFields', None):
            model['dimensionFields'] = list(f['name'] for f in reflection['dimensionFields'])
        if reflection.get('measureFields', None):
            model['measureFields'] = list(f['name'] for f in reflection['measureFields'])
        if reflection.get('distributionFields', None):
            model['distributionFields'] = list(f['name'] for f in reflection['distributionFields'])
        if reflection.get('partitionFields', None):
            model['partitionFields'] = list(f['name'] if 'transform' not in f else f for f in reflection['partitionFields'])
        if reflection['enabled'] is False:
            model['enabled'] = False
        return model

    @staticmethod
    def create_sql_file(sql, path, sort_keys=True) -> None:
        try:
            # use wb bytes mode to avoid auto convertion of newlines in windows(can force double newlines)
            with open(f"{path}", "wb") as f:
                f.write(sql.encode())
        except Exception as e:
            print(f"File error {path}: {e}")

    @staticmethod
    def create_json_file(node, path, sort_keys=True) -> None:
        try:
            # use wb bytes mode to avoid auto convertion of newlines in windows(can force double newlines)
            with open(f"{path}", "w") as f:
                json.dump(node, f, sort_keys=sort_keys,
                          indent=4, separators=(',', ': '))
        except Exception as e:
            print(f"File error {path}: {e}")

    @staticmethod
    def convert_path_list_to_string(path: list[str]) -> str:
        try:
            def quote(key):
                return f'"{key}"' if '.' in key else key
            return '.'.join(map(quote, path))
        except Exception as e:
            a =1


class DremioUrlTarget(DremioTarget):

    def __init__(self, target_api: DremioApi, recreate_datasets: bool = False, update_reflections: bool = False) -> None:
        super().__init__()
        self.target_api = target_api
        self.all_nodes_to_import = {}
        self.target_reflections_dict = {}
        self.found_external_catalogs = {}
        self.recreate_datasets = recreate_datasets
        # if self.update_reflections and target_api.environment == DremioApiEnviroment.STAGE:
        #     logging.warning('')
        self.update_reflections = update_reflections

    def write(self, import_data: FolderExport):
        logging.info(f'Export to dremio {self.target_api.dremio_server} started'
                     f'(recreate_datasets={self.recreate_datasets}, update_reflections={self.update_reflections}). \n')

        import_data = copy.deepcopy(import_data)
        util.set_replacement_values(import_data, self.target_api.environment)

        self.validate_root_exists(import_data)

        if self.update_reflections and not self.recreate_datasets:
            # read a list of all target exiting reflections to be able to update them based on target dataset id
            # no need to read existing reflections for update if recreate of dataset+reflections will be forced
            dataset_prefix = '.'.join(import_data['catalog']['path'])
            self.target_reflections_dict = self.read_all_target_reflections(dataset_prefix)

        # keep track of all nodes we have to import to resolve parents graph order creation.
        self.all_nodes_to_import = self.read_all_nodes(import_data)
        logging.info(f'Found {len(self.all_nodes_to_import)} nodes(folder,datasets) to import')

        self.import_node(import_data)
        logging.info('Export to dremio finished')

    def import_node(self, node):
        catalog = node['catalog']
        path = node['path']
        if 'dataset' in catalog['entityType']:

            import_state = self.all_nodes_to_import[path]
            if import_state['importing'] or import_state['imported']:
                return
            else:
                import_state['importing'] = True

            logging.info(f'dataset {path}')

            reflections = node['reflections']
            target_catalog = self.target_api.get_catalog_by_path(path)
            target_dataset_exists = target_catalog and target_catalog['entityType'] == 'dataset'

            if target_dataset_exists and self.recreate_datasets:
                self.target_api.delete_catalog(target_catalog['id'])
                target_dataset_exists = False

            if target_dataset_exists:
                if not self.is_update_needed(catalog, target_catalog):
                    logging.info(
                        'no need to update dataset: sql and sql context has not changed or dataset is physical dataset(never updated)')
                else:
                    # First ensure parents are updated first before child to avoid schema mismatch:
                    self.import_parents(node)
                    self.target_api.update_catalog(target_catalog['id'], catalog)
                if self.update_reflections and reflections:
                    self.do_update_reflections(target_catalog['id'], reflections)
            else:
                # First ensure parents exists:
                self.import_parents(node)

                new_catalog = self.target_api.create_catalog(catalog)
                if self.update_reflections and reflections:
                    self.create_reflections(new_catalog['id'], reflections)

            import_state['imported'] = True
            import_state['importing'] = False

        elif catalog['entityType'] in ['folder', 'space', 'source']:
            logging.info(f"{catalog['entityType']} {path}")
            children = node['children']
            for child in children:
                self.import_node(child)

    def read_all_nodes(self, folder_node: FolderExport):
        result = {}
        children = folder_node['children']
        for child in children:
            if 'dataset' in child['catalog']['entityType']:
                result[child['path']] = {'imported': False, 'importing': False, 'node': child}
            elif 'folder' in child['catalog']['entityType']:
                for k, v in self.read_all_nodes(child).items():
                    result[k] = v
        return result

    def read_all_target_reflections(self, dataset_prefix: str):
        target_reflections = util.get_reflections_fallback_to_sql(self.target_api, dataset_prefix, True)
        dataset_ids = [r['datasetId'] for r in target_reflections]
        source_reflections_dic = defaultdict(list)
        for datasetId, ref in zip(dataset_ids, target_reflections):
            source_reflections_dic[datasetId].append(ref)
        return source_reflections_dic

    @staticmethod
    def is_update_needed(catalog, target_catalog):
        if 'sql' in catalog:
            if catalog['sql'] != target_catalog['sql']:
                return True
            if catalog.get('sqlContext', []) != target_catalog.get('sqlContext', []):
                return True
        return False

    def do_update_reflections(self, dataset_id, reflections):
        logging.info(f'updating/creating {len(reflections)} reflections')
        existing_reflections = self.target_reflections_dict.get(dataset_id, [])
        for reflection in reflections:
            mapped_reflection = {**reflection, 'datasetId': dataset_id}
            # RAW or AGGREGATE
            reflection_name = mapped_reflection['name']
            # search for existing reflection by name
            first_matched = next((r for r in existing_reflections if r['name'] == reflection_name), None)
            if first_matched:
                self.target_api.update_reflection(first_matched['id'], ReflectionImport({**mapped_reflection, 'tag': first_matched['tag']}))
            else:
                self.target_api.create_reflection(ReflectionImport(mapped_reflection))

    def create_reflections(self, dataset_id, reflections):
        logging.debug(f'creating {len(reflections)} reflections')
        for reflection in reflections:
            mapped_reflection = {**reflection, 'datasetId': dataset_id}
            self.target_api.create_reflection(ReflectionImport(mapped_reflection))

    def import_parents(self, node):
        path = node['path']
        parents = node['parents']
        for parent in parents:
            parent_path = '/'.join(parent['path'])
            if parent_path in self.all_nodes_to_import:
                parent_node = self.all_nodes_to_import[parent_path]['node']
                self.import_node(parent_node)
            elif not self.is_parent_catalog_exists(parent_path):
                raise Exception(
                    f'Dataset {path} depends on dataset {parent_path} that does not exists and is not defined in imported file')

    def is_parent_catalog_exists(self, parent_path):
        if parent_path not in self.found_external_catalogs:
            logging.debug(f'checking parent {parent_path} exists or can be created')
            parent_catalog = self.target_api.get_catalog_by_path(parent_path)

            if not parent_catalog or parent_catalog['entityType'] != 'dataset':
                self.found_external_catalogs[parent_path] = False
            else:
                # cache to avoid same parent lookups further
                self.found_external_catalogs[parent_path] = True
        return self.found_external_catalogs[parent_path]

    def validate_root_exists(self, import_data: FolderExport):
        root = import_data['path'].split('/')[0]
        if self.target_api.get_catalog_by_path(root) is None:
            raise Exception(f"Root catalog does not exist on target: {root}. You have to create it manually first")
