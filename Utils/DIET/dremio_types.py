from os import pathsep
from typing import TypedDict


class Node(dict):
    catalog: object
    path: str

    def __init__(self, catalog):
        self['catalog'] = catalog
        self['path'] = '/'.join(catalog['path']) if 'path' in catalog else catalog['name']

    @staticmethod
    def create_node(catalog):
        if 'entityType' in catalog and 'folder' in catalog['entityType']:
            return FolderExport(catalog)
        if 'entityType' in catalog and 'dataset' in catalog['entityType']:
            return DataSetExport(catalog)
        return Node(catalog)


class FolderExport(Node):

    def __init__(self, catalog):
        super().__init__(catalog)
        self['children'] = []


class DataSetExport(Node):

    def __init__(self, catalog, reflections=None, parents=None):
        super().__init__(catalog)
        self['reflections'] = reflections or []
        self['parents'] = parents or []


class FolderImport(dict):
    def __init__(self, catalog):
        self['entityType'] = 'folder'
        self['path'] = catalog['path']


# class DatasetImport(dict):
#
#     def __init__(self, catalog):
#         self['entityType'] = 'dataset'
#         self['type'] = "VIRTUAL_DATASET"
#         self['tag'] = catalog['tag']
#         self['path'] = catalog['path']
#         self['sql'] = catalog['sql']
#         if 'sqlContext' in catalog:
#             self['sqlContext'] = catalog['sqlContext']


class ReflectionImport(dict):

    def __init__(self, reflection):
        if 'tag' in reflection:
            self['tag'] = reflection['tag']
        self['type'] = reflection['type']
        self['name'] = reflection['name']
        self['datasetId'] = reflection['datasetId']
        self['enabled'] = reflection['enabled']
        self['arrowCachingEnabled'] = reflection['arrowCachingEnabled']
        if 'displayFields' in reflection:
            self['displayFields'] = reflection['displayFields']
        if 'dimensionFields' in reflection:
            self['dimensionFields'] = reflection['dimensionFields']
        if 'measureFields' in reflection:
            self['measureFields'] = reflection['measureFields']
        if 'distributionFields' in reflection:
            self['distributionFields'] = reflection['distributionFields']
        if 'partitionFields' in reflection:
            self['partitionFields'] = reflection['partitionFields']
        if 'sortFields' in reflection:
            self['sortFields'] = reflection['sortFields']
        self['partitionDistributionStrategy'] = reflection[
            'partitionDistributionStrategy']
        self['entityType'] = 'reflection'
