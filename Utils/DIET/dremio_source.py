import logging
import re
from abc import ABC, abstractmethod
from collections import defaultdict
from pathlib import Path
from typing import Union, Callable

from DIET.dremio_api import DremioApi, DremioApiEnviroment
from DIET.dremio_types import DataSetExport, FolderExport, Node
from dateutil.parser import parse
import json

from DIET import util


class DremioSource(ABC):
    @abstractmethod
    def read(self) -> FolderExport:
        pass


class DremioUrlSource(DremioSource):

    def __init__(self, source_api: DremioApi, source_url: str, dataset_filter: Union[str, Callable[[str], bool]] = None,
                 skip_reflections: bool = False) -> None:
        super().__init__()
        self.source_api = source_api
        self.source_url = source_url
        self.dataset_filter = dataset_filter
        self.skip_reflections = skip_reflections
        self.is_data_source_catalog = False
        self.source_export_settings = {}
        if self.source_api.environment == DremioApiEnviroment.STAGE:
            logging.warning('STAGE does not support graph retrieval. Entity create order can not be resolved on deploy. '
                            'Manually set "depends" fields in exported json dataset config files after exporting to folder')

    def read(self) -> FolderExport:
        logging.info(f'Export from dremio {self.source_api.dremio_server} started'
                     f'(skip_reflections={self.skip_reflections})')

        self.initialize_source()

        source_catalog = self.get_source_catalog()

        source_reflections_dic = defaultdict(list)
        if not self.skip_reflections:
            source_reflections = util.get_reflections_fallback_to_sql(self.source_api, self.source_url.replace('/', '.'), False)
            dataset_ids = [r['datasetId'] for r in source_reflections]
            for datasetId, ref in zip(dataset_ids, source_reflections):
                source_reflections_dic[datasetId].append(ref)
        # root will implicitly marked as folder export even if it is space or source, cos anyway write to Dremio will not create it
        root = FolderExport(source_catalog)

        for source_child_catalog in source_catalog.get('children', []):
            child = self.export_node(source_child_catalog, source_reflections_dic)
            if child:
                root['children'].append(child)

        if self.is_data_source_catalog:
            # remove all empty folders (some sources(db, s3) may have hundreds of folders without physical datasets) to make json less verbose
            self.remove_empty_folders(root)

        util.set_replacement_placeholders(root, self.source_api.environment)
        logging.info('\nExport from dremio finished')
        return root

    def export_node(self, source_catalog, source_reflections_dic):
        catalog_source_url = '/'.join(source_catalog['path'])
        dataset_name = source_catalog['path'][-1]
        if (not self._test_dataset_name(catalog_source_url)) or util.excluded(catalog_source_url):
            return None
        elif source_catalog['type'] == 'CONTAINER' and util.stop_traverse(catalog_source_url):
            # here we stop only if entity is not a dataset but a container. stop_traverse is applied only to folders that are not PDS.
            return None

        source_catalog = self.source_api.get_catalog_by_path(catalog_source_url)
        source_catalog_id = source_catalog['id']

        if 'folder' in source_catalog['entityType']:
            folder = FolderExport(source_catalog)
            logging.info(f'folder:  {folder["path"]}')

            for child_catalog in source_catalog['children']:
                child = self.export_node(child_catalog, source_reflections_dic)
                if child:
                    folder['children'].append(child)
            # Remove summary children node, it is not needed any more in the exported output and will add verbosity
            del source_catalog['children']
            return folder

        elif 'dataset' in source_catalog['entityType']:
            self.trim_catalog(source_catalog)
            dataset = DataSetExport(source_catalog)
            logging.info(f'{source_catalog["type"]}:  {dataset["path"]}')
            try:
                source_graph = self.source_api.get_catalog_graph(source_catalog_id)
                dataset['parents'] = source_graph['parents']
            except Exception as e:
                logging.warning(
                    f'getting source graph for {catalog_source_url} returned an error: {e}. Skipping')
                dataset['parents'] = []

            if source_catalog_id in source_reflections_dic:
                dataset['reflections'] = source_reflections_dic[source_catalog_id]

            return dataset

    def get_source_catalog(self):
        source_catalog = self.source_api.get_catalog_by_path(self.source_url)
        if source_catalog is None:
            raise Exception(f'source path does not exist: {self.source_url}')
        return source_catalog

    def remove_empty_folders(self, root: FolderExport):
        logging.debug('Removing all empty folders from source exported tree to remove verbosity')
        self._remove_empty_folders(root)
        pass

    def _remove_empty_folders(self, node):
        if type(node) == DataSetExport:
            return False
        elif type(node) == FolderExport:
            children = node.get('children', [])
            if len(children) == 0:
                return True

            for child in list(children):
                if self._remove_empty_folders(child):
                    children.remove(child)
            if len(children) == 0:
                return True
            return False

    def get_root_type(self):
        root = self.source_url.split('/')[0]
        root_catalog = self.source_api.get_catalog_by_path(root)
        if root_catalog is None or root_catalog['entityType'] not in ['space', 'folder', 'source']:
            raise Exception(f'Root catalog "{root}" does not exist or its type is not supported()')
        return root_catalog['entityType']

    def initialize_source(self):
        self.is_data_source_catalog = self.get_root_type() == 'source'

    def trim_catalog(self, source_catalog):
        # Remove dataset fields node, it is not needed any more in the exported output and will add verbosity
        del source_catalog['fields']
        # remove location and etc PDS format fields
        if 'format' in source_catalog:
            if 'location' in source_catalog:
                del source_catalog['location']
            if 'fullPath' in source_catalog:
                del source_catalog['fullPath']

    def _test_dataset_name(self, name: str):
        if self.dataset_filter:
            if callable(self.dataset_filter):
                return self.dataset_filter(name)
            else:
                return bool(re.match(self.dataset_filter, name, re.IGNORECASE))
        return True


class DremioFileSource(DremioSource):

    def __init__(self, source_file_path, dataset_pattern: str = None) -> None:
        super().__init__()
        self.source_file_path = source_file_path
        self.dataset_pattern = dataset_pattern

    def read(self) -> dict:
        path = Path(self.source_file_path)
        if path.is_file():
            f = open(f"{self.source_file_path}", "r")
            tree = json.loads(f.read())
            if self.dataset_pattern:
                # implement filtering is needed, it's not complex
                raise Exception('Not supported yet: you can not use --include filter with file as --folder parameter ')
            return tree
        else:
            raise Exception(f'\'{self.source_file_path}\' is not a file')


class DremioFolderSource(DremioSource):
    def __init__(self, source_folder_path, catalog_path: str, file_pattern: str = None) -> None:
        super().__init__()
        self.source_folder_path = Path(source_folder_path)
        self.catalog_path = catalog_path
        self.file_pattern = file_pattern
        self.id_counter = 0

    def read(self) -> FolderExport:
        logging.info(f'Reading catalog {self.catalog_path} from folder: {self.source_folder_path.absolute()}')
        self.id_counter = 0
        return self.export_folder(self.catalog_path)

    def export_folder(self, catalog_path):
        folder_path = self.source_folder_path / catalog_path
        catalog = {'path': list(catalog_path.split('/')), 'entityType': 'folder'}
        folder_export = Node.create_node(catalog)
        for path in folder_path.iterdir():
            child_path = catalog_path + '/' + path.name
            if path.is_dir():
                child_node = self.export_folder(child_path)
            elif path.name.lower().endswith('.json') and self._test_file_path(path.name.lower()):
                child_node = self.export_dataset(child_path)
            else:
                continue
            folder_export['children'].append(child_node)
        return folder_export

    def export_dataset(self, catalog_file_path):
        json_file_path = self.source_folder_path / catalog_file_path
        catalog_path = catalog_file_path[:catalog_file_path.rfind(".")]

        self.id_counter += 1
        catalog = {'path': list(catalog_path.split('/')), 'entityType': 'dataset'}

        f = open(f"{json_file_path}", "r")
        essential_data = json.loads(f.read())

        if 'format' in essential_data:
            catalog['type'] = 'PHYSICAL_DATASET'
            catalog['format'] = essential_data['format']

        if 'sql' in essential_data:
            catalog['type'] = 'VIRTUAL_DATASET'
            sql: str = essential_data['sql']
            if sql.lower().endswith('.sql'):
                sql_path = sql
                if not Path(sql_path).is_absolute():
                    sql_path = json_file_path.parent / sql_path
                f = open(f"{sql_path}", "rb")
                sql = f.read().decode()
            catalog['sql'] = sql
            if 'sqlContext' in essential_data:
                sql_context = essential_data['sqlContext']
                catalog['sqlContext'] = list(sql_context.split('.'))
        else:
            # if json has no sql let's check if there's a sibling sql file next to json.
            sql_path = catalog_path + '.sql'
            if not Path(sql_path).is_absolute():
                sql_path = json_file_path.parent / sql_path
                if Path(sql_path).is_file():
                    catalog['type'] = 'VIRTUAL_DATASET'
                    f = open(f"{sql_path}", "rb")
                    sql = f.read().decode()
                    catalog['sql'] = sql

        dataset_export = Node.create_node(catalog)

        for parent_path in essential_data.get('depends', []):
            parent = {'path': self.convert_string_path_to_list(parent_path)}
            dataset_export['parents'].append(parent)

        for reflection in essential_data.get('reflections', []):
            dataset_export['reflections'].append(self.restore_reflection_model(reflection))

        return dataset_export

    def _test_file_path(self, name: str):
        if self.file_pattern:
            return bool(re.match(self.file_pattern, name, re.IGNORECASE))
        return True

    @staticmethod
    def restore_reflection_model(model):
        reflection = {
            'name': model['name'],
            'type': model['type'],
            'enabled': model.get('enabled', True),
            'arrowCachingEnabled': model.get('arrowCachingEnabled', False),
            'partitionDistributionStrategy': 'CONSOLIDATED'
        }
        if model.get('sortFields', None):
            reflection['sortFields'] = list({"name": f} for f in model['sortFields'])
        if model.get('displayFields', None):
            reflection['displayFields'] = list({"name": f} for f in model['displayFields'])
        if model.get('dimensionFields', None):
            reflection['dimensionFields'] = list({"name": f} for f in model['dimensionFields'])
        if model.get('measureFields', None):
            reflection['measureFields'] = list({"name": f} for f in model['measureFields'])
        if model.get('distributionFields', None):
            reflection['distributionFields'] = list({"name": f} for f in model['distributionFields'])
        if model.get('partitionFields', None):
            reflection['partitionFields'] = list({"name": f} if isinstance(f, str) else f for f in model['partitionFields'])
        return reflection

    @staticmethod
    def convert_string_path_to_list(path: str) -> list:
        result = []
        current = []
        in_quotes = False

        i = 0
        while i < len(path):
            if path[i] == '"':
                in_quotes = not in_quotes
            elif path[i] == '.' and not in_quotes:
                result.append(''.join(current))
                current = []
            else:
                current.append(path[i])
            i += 1

        # Append the last part
        if current:
            result.append(''.join(current))

        return result
