import logging
import os

import typer
from DIET import __app_name__, __version__
from typing import Optional, Callable
from pathlib import Path
from DIET.dremio import <PERSON><PERSON><PERSON>
from DIET.dremio_api import DremioApiEnviroment, DremioApi
from DIET.dremio_source import DremioFolderSource, DremioFileSource
from DIET.util import create_dataset_filter_function

app = typer.Typer()


def _version_callback(value: bool) -> None:
    if value:
        typer.echo(f"{__app_name__} v{__version__}")
        raise typer.Exit()


def _debug_log_callback(value: bool) -> None:
    if value:
        typer.echo(f"DEBUG mode log level enabled")
        log_level='DEBUG'
    else:
        log_level='INFO'
    if len(logging.getLogger().handlers) > 0:
        logging.getLogger().setLevel(log_level)
    else:
        logging.basicConfig(level=log_level)


enable_debug = typer.Option(False, "--debug", "-d", callback=_debug_log_callback, is_eager=True)


@app.callback()
def main(
    version: Optional[bool] = typer.Option(False, "--version", "-v", callback=_version_callback, is_eager=True)
) -> None:
    pass


@app.command(name="export", help="Exports from Dremio to folder structure or file(if folder param is file path)")
def export(
    catalog: str = typer.Option(..., "--catalog", "-c", help="Dremio catalog root folder(i.e virtual/data/...)"),
    stage: DremioApiEnviroment = typer.Option(..., "--stage", "-s", help="export from Dremio instance stage", case_sensitive=False),
    folder: Path = typer.Option(..., "--folder", "-f", help="export tree to folder"),
    dataset_pattern: Optional[str] = typer.Option(None, '--include', '-i', help="include only datasets by filter"),
    inline_sql_limit: int = typer.Option(20, "--inline_sql_limit", help="max sql length that is inlined in json dataset config"),
    skip_reflections: Optional[bool] = typer.Option(False, '--skip_reflections', '-sr', help="do not export reflections"),
    sync_folder_only: Optional[bool] = typer.Option(False, '--sync_only', '-so', help="sync only existing objects in folder"),
    debug: Optional[bool] = enable_debug
) -> None:

    dremio = Dremio()

    dremio.set_config(folder.parent if folder.is_file() else folder)

    from DIET.util import CREDENTIALS
    api_config = CREDENTIALS.get(stage.value, None)

    if sync_folder_only:
        if dataset_pattern:
            raise Exception("Cannot use --sync_only with --include option. Use only one of them.")
        dataset_pattern = create_dataset_filter_function(catalog, folder, dataset_pattern)

    dremio.read_from_dremio(catalog, DremioApi(stage, config=api_config), dataset_pattern, skip_reflections)
    if folder.name.lower().endswith('.json'):
        # export to single file(quite verbose). use for migration between environments
        dremio.write_to_json(folder)
    else:
        dremio.write_to_folder(folder, inline_sql_limit)


@app.command(name="import", help="Imports from folder structure or file(if folder param is file path) to Dremio")
def _import(
    catalog: str = typer.Option(..., "--catalog", "-c", help="Dremio catalog root folder(i.e virtual/data/...)"),
    stage: DremioApiEnviroment = typer.Option(..., "--stage", "-s", help="export from Dremio instance stage", case_sensitive=False),
    folder: Path = typer.Option(..., "--folder", "-f", help="import to folder"),
    file_pattern: Optional[str] = typer.Option(None, '--include', '-i', help="include only datasets by filter"),
    recreate_datasets: Optional[bool] = typer.Option(False, '--reset', '-r', help="recreate(drop/create) datasets"),
    update_reflections: Optional[bool] = typer.Option(False, '--update_reflections', '-ur', help="update/create reflections by name match"),
    debug: Optional[bool] = enable_debug
) -> None:
    dremio = Dremio()
    dremio.set_config(folder.parent if folder.is_file() else folder)
    if folder.name.lower().endswith('.json'):
        # export to single file(quite verbose). use for migration between environments
        dremio.read_from_json(folder)
    else:
        dremio.read_from_folder(folder, catalog, file_pattern)

    from DIET.util import CREDENTIALS
    api_config = CREDENTIALS.get(stage.value, None)
    dremio.write_to_dremio(DremioApi(stage, config=api_config), recreate_datasets, update_reflections)



