import os
from math import fabs
from os import read, environ
from pathlib import Path

from DIET.dremio import <PERSON><PERSON><PERSON>
from timeit import default_timer as timer
from datetime import timedelta
import logging

from DIET.dremio_api import Dr<PERSON>io<PERSON><PERSON>, DremioApiEnviroment
LOG_LEVEL = environ.get('LOG_LEVEL', 'INFO')

if __name__ == '__main__':
    '''Make sure working directory is DIET/Scripts'''
    if not os.getcwd().endswith('Scripts'):
        raise Exception('Set working directory to DIET/Scripts')

    logging.basicConfig(level=LOG_LEVEL)
    start = timer()

    dremio = Dremio()
    dremio.set_config(Path(os.getcwd()))
    api = DremioApi(DremioApiEnviroment.LIVE)
    ref = api.get_reflections()

    dremio.read_from_dremio("virtual/data/preview/main",
                              <PERSON><PERSON>ioApi(DremioApiEnviroment.LIVE))
    # DIET/Scripts/ChangeScripts is gitignored so you can play with it here'
    # dremio.write_to_json("ChangeScripts/STAGE/result.json")
    dremio.write_to_json('ChangeScripts/STAGE')

    dremio.read_from_folder('ChangeScripts/STAGE', "virtual/data/DIET_test")
    dremio.write_to_dremio(DremioApi(DremioApiEnviroment.STAGE), True, True)
    end = timer()
    logging.info(f"Execution time: {timedelta(seconds=end - start)}")
