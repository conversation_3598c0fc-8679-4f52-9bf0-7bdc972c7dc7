{"sources": {"live_data_lake_semi_structured": {"include_folders": ["features", "media_segments"], "stop_traverse_pattern": [], "exclude": ["sourcemediagroup=", "sourcemediaid=", "meetingid=", "sessionid=", "algorithmid="]}}, "replacements": {"LIVE": {"@data_lake": "live_data_lake_semi_structured", "@fcp_db": "fcp_live", "@fcp_results": "fcp_pipeline_results"}, "STAGE": {"@data_lake": "stage_data_lake_semi_structured", "@fcp_db": "fcp_stage", "@fcp_results": "fcp_pipeline_results_stage"}}}