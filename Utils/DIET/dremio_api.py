import logging
import urllib
from enum import Enum, unique
import time
import requests
import json
from DIET.dremio_types import ReflectionImport, FolderImport
from datetime import datetime
import boto3

# sleep timeout when fetching job status in a format of 'current:next'
SLEEP_TIMEOUTS = {0.25: 0.5, 0.5: 1, 1: 1}


class DremioApi:
    headers = {'content-type': 'application/json'}
    sleep_seconds = 0.2

    def __init__(self, environment: str = None, config = None) -> None:
        """
        :param environment: optional(LIVE/STAGE) to automatically get credentials and api url from aws when config is None
        :param config: custom config (when environment is None) with 'pat_token' or 'user_name'/'password' and 'dremio_server' properties
        """
        # disable requests debug logs
        # logging.getLogger("urllib3.connectionpool").setLevel(logging.INFO)
        self.pat_token = self.user_name = self.password = self.dremio_server = ""
        self.custom_config = config
        self.environment = environment
        self.set_enviroment()
        self.session = None
        self.login()

    def set_enviroment(self):
        ssm = boto3.client('ssm')
        if self.custom_config:
            if 'pat_token' in self.custom_config:
                self.pat_token = self.custom_config['pat_token']
            else:
                self.user_name = self.custom_config.get('user_name', None)
                self.password = self.custom_config.get('password', None)
            self.dremio_server = self.custom_config.get('dremio_server', None)

        if self.environment is DremioApiEnviroment.LIVE:
            if not self.user_name:
                user_name_response = ssm.get_parameter(
                    Name='/live/administration/dremio/preview/username',
                    WithDecryption=True)
                self.user_name = user_name_response['Parameter']['Value']
            if not self.password:
                password_response = ssm.get_parameter(
                    Name='/live/administration/dremio/preview/password',
                    WithDecryption=True)
                self.password = password_response['Parameter']['Value']
            if not self.dremio_server:
                self.dremio_server = 'https://preview-data.realeyesit.com:9047'
        elif self.environment is DremioApiEnviroment.STAGE:
            if not self.user_name:
                user_name_response = ssm.get_parameter(
                    Name='/stage/administration/dremio/preview/username',
                    WithDecryption=True)
                self.user_name = user_name_response['Parameter']['Value']
            if not self.password:
                password_response = ssm.get_parameter(
                    Name='/stage/administration/dremio/preview/password',
                    WithDecryption=True)
                self.password = password_response['Parameter']['Value']
            if not self.dremio_server:
                self.dremio_server = 'https://old-data.realeyesit.com'

    def login(self):
        if not self.pat_token:
            logging.debug(
                f'Debug login start. Username={self.user_name}, Server={self.dremio_server}'
            )
            # we login using the old api for now
            login_data = {'userName': self.user_name, 'password': self.password}

            # http call
            response = requests.post(f'{self.dremio_server}/apiv2/login',
                                     headers=self.headers,
                                     data=json.dumps(login_data),
                                     verify=False)

            logging.debug(f'Debug response {response}')
            data = json.loads(response.text)

            # retrieve the login token
            token = data['token']
            headers = {
                'content-type': 'application/json',
                'authorization': f'_dremio{token}'
            }
        else:
            headers = {
                'content-type': 'application/json',
                'authorization': f'Bearer {self.pat_token}'
            }

        self.session = requests.Session()
        self.session.headers = headers

    def get(self, url, check_status=True):
        time.sleep(self.sleep_seconds)
        try:
            response = self.session.get(url)
            if response.status_code not in (200, 404) and check_status:
                raise Exception(f"{response.status_code}-{response.text}")
        except Exception as e:
            self.write_error_log(url, f"{str(e)}")
            raise
        return response

    def delete(self, url, check_status=True):
        time.sleep(self.sleep_seconds)
        try:
            response = self.session.delete(url)
            if response.status_code not in (200, 204) and check_status:
                raise Exception(f"{response.status_code}-{response.text}")
        except Exception as e:
            self.write_error_log(url, f"{str(e)}")
            raise
        return response

    def post(self, url, payload, check_status=True):
        time.sleep(self.sleep_seconds)
        try:
            response = self.session.post(url, data=payload)
            if response.status_code != 200 and check_status:
                raise Exception(f"{response.status_code}-{response.text}")
        except Exception as e:
            self.write_error_log(url, f"{str(e)}")
            raise
        return response

    def put(self, url, payload, check_status=True):
        time.sleep(self.sleep_seconds)
        try:
            response = self.session.put(url, data=payload)
            if response.status_code != 200 and check_status:
                raise Exception(f"{response.status_code}-{response.text}")
        except Exception as e:
            self.write_error_log(url, f"{str(e)}")
            raise
        return response

    def is_catalog_exist(self, path):
        response = self.get(
            f'{self.dremio_server}/api/v3/catalog/by-path/{path}')
        return response.status_code == 200

    def get_catalog_graph(self, id):
        """Enterprise Edition only allowed method"""
        response = self.get(f'{self.dremio_server}/api/v3/catalog/{id}/graph', check_status=False)
        if response.status_code in [404, 405]:
            raise Exception('View the Graph Information is Enterprise Edition only allowed method')
        elif response.status_code in [500]:
            error = json.loads(response.text).get('errorMessage') or response.text
            raise Exception(error)
        return json.loads(response.text)

    def get_catalog(self, id):
        response = self.get(f'{self.dremio_server}/api/v3/catalog/{id}')
        if response.status_code == 404:
            return None
        return json.loads(response.text)

    def get_catalog_by_path(self, path):
        path = urllib.parse.quote(path)
        try:
            response = self.get(
                f'{self.dremio_server}/api/v3/catalog/by-path/{path}')
            if response.status_code == 404:
                return None
            return json.loads(response.text)
        except Exception as e:
            if 'Can not get internal item from non-filesystem source' in str(e):
                logging.warning(f'{e}. In case of sql databases sources it indicates dataset doesn''t exist({path})')
                return None
            raise

    def create_folders_recursive(self, path_or_url):
        if type(path_or_url) == str:
            path = path_or_url.split('/')
        else:
            path = path_or_url
        url = '/'.join(path)
        if self.is_catalog_exist(url):
            return
        if len(path) > 1:
            self.create_folders_recursive(path[:-1])
        return self.create_catalog(FolderImport({'path': path}))

    def create_catalog(self, catalog):
        if 'id' in catalog:
            catalog = catalog.copy()
            del catalog['id']
        catalog_type = catalog.get('type', None)
        if catalog_type == 'PHYSICAL_DATASET':
            # NOTE Dremio web server  should be properly configured to allow escaped id in the url!
            id = urllib.parse.quote(f"dremio:/{'/'.join(catalog['path'])}", safe='')
            response = self.post(f'{self.dremio_server}/api/v3/catalog/{id}', json.dumps(catalog))
        else:
            folder_path = catalog['path'][:-1]
            self.create_folders_recursive(folder_path)
            response = self.post(f'{self.dremio_server}/api/v3/catalog', json.dumps(catalog))
        return json.loads(response.text)

    def update_catalog(self, id, catalog):
        # weird but we also have to set id inside the model while it also exists in the PUT url
        catalog = {**catalog, 'id': id}
        jason = json.dumps(catalog)
        response = self.put(f'{self.dremio_server}/api/v3/catalog/{id}', jason)
        return json.loads(response.text)

    def delete_catalog(self, id):
        response = self.delete(f'{self.dremio_server}/api/v3/catalog/{id}')
        return json.loads(response.text) if response.text else None

    def get_reflections(self):
        """Enterprise Edition only allowed method"""
        response = json.loads(
            self.get(f'{self.dremio_server}/api/v3/reflection').text)
        if 'errorMessage' in response and response['errorMessage'] == "HTTP 405 Method Not Allowed":
            raise Exception('List reflections is Enterprise Edition only allowed method')
        return response['data']

    def get_reflection(self, reflection_id):
        response = self.get(f'{self.dremio_server}/api/v3/reflection/{reflection_id}')
        return json.loads(response.text)

    def create_reflection(self, reflection: ReflectionImport):
        if 'id' in reflection:
            reflection.pop('id')
        jason = json.dumps(reflection)
        response = self.post(f'{self.dremio_server}/api/v3/reflection', jason)
        return json.loads(response.text)

    def update_reflection(self, id, reflection: ReflectionImport):
        jason = json.dumps(reflection)
        response = self.put(f'{self.dremio_server}/api/v3/reflection/{id}',
                            jason)
        return json.loads(response.text)

    def query(self, sql, timeout: int = 60, limit: int = 5000):
        job_info = json.loads(self.post(f'{self.dremio_server}/api/v3/sql', json.dumps({"sql": sql})).text)
        job_id = job_info['id']

        state = 'PENDING'
        timeout = time.time() + timeout
        sleep = 0.25
        rows = []
        while state not in ['COMPLETED', 'FAILED', 'CANCELED']:
            job_info = json.loads(self.get(f'{self.dremio_server}/api/v3/job/{job_id}').text)
            state = job_info['jobState']
            if state == 'FAILED':
                raise Exception(f'Dremio query failed with: {job_info["errorMessage"]} (job id {job_id})')
            if state == 'CANCELED':
                raise Exception(f'Dremio query was canceled(job id {job_id})')
            elif state == 'COMPLETED':
                row_count = job_info['rowCount']
                while len(rows) < row_count and len(rows) < limit:
                    url = f'{self.dremio_server}/api/v3/job/{job_id}/results?limit=500&offset={len(rows)}'
                    job_results = json.loads(self.get(url).text)
                    rows.extend(job_results['rows'])
                return rows
            elif timeout - time.time() < 0:
                self.post(f'{self.dremio_server}/api/v3/job/{job_id}/cancel')
                raise Exception(f'Dremio query timeout ({timeout} sec) (jobid {job_id})')
            time.sleep(sleep)
            sleep = SLEEP_TIMEOUTS.get(sleep, 1)
        raise Exception(f'Dremio query job unknown jobState returned {state}')

    def write_error_log(self, url,  message):
        logging.error(f'{url}: api method returned an error:{message}')
        time = datetime.utcnow().strftime("%Y-%m-%d_%H-%M-%S")
        with open(f"api_errors.log", "a") as f:
            f.write(json.dumps(f"#{time} {message}# "))


@unique
class DremioApiEnviroment(Enum):
    LIVE = 'LIVE'
    STAGE = 'STAGE'
    CUSTOM = 'CUSTOM'
