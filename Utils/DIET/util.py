import json
import logging
import re
from pathlib import Path
from typing import Callable

from DIET.dremio_api import <PERSON><PERSON>ioApi, DremioApiEnviroment
from DIET.dremio_types import DataSetExport, FolderExport


def get_reflections_fallback_to_sql(api: Dr<PERSON>ioA<PERSON>, dataset_prefix: str = None, set_tags: bool = False):
    reflections = []
    try:
        reflections = api.get_reflections()
    except Exception as e:
        logging.debug(f'Reflection list api method failed({e}). Falling back to sql method')
        sql = f"select * from sys.reflections where dataset_name like '{dataset_prefix}%'"
        reflection_rows = api.query(sql, timeout=15)
        reflections = list(map(row_to_reflection_entity, reflection_rows))
        if set_tags:
            for reflection in reflections:
                # previous version (tag) is needed for reflection UPDATE api call
                reflection['tag'] = api.get_reflection(reflection['id'])['tag']
    return reflections


def row_to_reflection_entity(row):
    reflection = {
        'entityType': 'reflection', "partitionDistributionStrategy": "CONSOLIDATED",
        'id': row['reflection_id'], 'name': row['reflection_name'], 'type': row['type'],
        'datasetId': row['dataset_id'],
        'arrowCachingEnabled': row['arrow_cache'], 'enabled': row['status'] != 'DISABLED'
    }
    if _to_column_list(row['display_columns']):
        reflection['displayFields'] = _to_column_list(row['display_columns'])
    if _to_column_list(row['dimensions']):
        reflection['dimensionFields'] = _to_column_list(row['dimensions'])
    if _to_column_list(row['measures']):
        reflection['measureFields'] = _to_column_list(row['measures'])
    if _to_column_list(row['distribution_columns']):
        reflection['distributionFields'] = _to_column_list(row['distribution_columns'])
    if _to_column_list(row['partition_columns']):
        reflection['partitionFields'] = _to_column_list(row['partition_columns'])
    if _to_column_list(row['sort_columns']):
        reflection['sortFields'] = _to_column_list(row['sort_columns'])
    return reflection


def _to_column_list(columns_as_str):
    if columns_as_str:
        return list({'name': name.strip()} for name in columns_as_str.split(','))
    return []


REPLACEMENTS = {
    DremioApiEnviroment.STAGE.value: {},
    DremioApiEnviroment.LIVE.value: {}
}

SOURCES = {
}

CREDENTIALS = {
}


def load_config(config_path: str):
    config = json.load(open(config_path))
    global REPLACEMENTS
    REPLACEMENTS = config.get('replacements', REPLACEMENTS)
    # Upper case environment keys to fit DremioApiEnviroment enumeration values
    REPLACEMENTS = {k.upper(): v for k, v in REPLACEMENTS.items()}
    global SOURCES
    SOURCES = get_source_settings(config)
    global CREDENTIALS
    CREDENTIALS = config.get('credentials', CREDENTIALS)


def get_source_settings(config):
    sources = config.get('sources', {})
    source_export_settings = {}
    for k, settings in sources.items():
        source_export_settings[k] = {}
        include_folders = settings.get('include_folders', [])
        source_export_settings[k]['include_folders'] = include_folders if type(include_folders) == list else [include_folders]
        stop_pattern = settings.get('stop_traverse_pattern', [])
        source_export_settings[k]['stop_traverse_pattern'] = stop_pattern if type(stop_pattern) == list else [stop_pattern]
        exclude = settings.get('exclude', [])
        source_export_settings[k]['exclude'] = exclude if type(exclude) == list else [exclude]
    return source_export_settings


def set_replacement_placeholders(vds: DataSetExport, environment: DremioApiEnviroment):
    replacements = REPLACEMENTS.get(environment.value, {})
    reverse_mapping = {}
    for k, v in replacements.items():
        if type(v) is dict:
            # can be boolean or array of [pattern, replacement] re.sub parameters
            on_export = v.get('on_export', True)
            if on_export:
                if type(on_export) is list:
                    pattern, replacement = on_export
                    reverse_mapping[pattern] = replacement
                elif 'value' in v:
                    value = v['value']
                    reverse_mapping[value] = k
                else:
                    raise Exception(
                        'when replacement value is dict, value attribute or on_export array attribute are required for on export replacement')
        else:
            reverse_mapping[v] = k

    logging.info(f'Applying reverse replacements: {reverse_mapping}')
    _apply_replacements(vds, reverse_mapping)


def set_replacement_values(folder: FolderExport, target_environment: DremioApiEnviroment):
    replacements = REPLACEMENTS.get(target_environment.value, {})
    replacements = {k: (v.get('value', k) if type(v) is dict else v) for k, v in replacements.items()}
    logging.info(f'Applying replacements: {replacements}')
    _apply_replacements(folder, replacements)


def _apply_replacements(node, replacements):
    catalog = node['catalog']
    if catalog['entityType'] in ['folder', 'space', 'source']:
        for child in node['children']:
            _apply_replacements(child, replacements)
    elif catalog['entityType'] == 'dataset':
        if 'sql' in catalog:
            sql: str = catalog['sql']
            for k, v in replacements.items():
                if re.search(k, sql, flags=re.IGNORECASE):
                    sql = re.sub(k + r"(?=\W|$)", v, sql, flags=re.IGNORECASE)
            catalog['sql'] = sql

            sqlContext: list = catalog.get('sqlContext', [])
            for i, name in enumerate(sqlContext):
                if name in replacements:
                    sqlContext[i] = replacements[name]
        if 'format' in catalog:
            format = catalog['format']
            if 'location' in format:
                for k, v in replacements.items():
                    if re.search(k, format['location'], flags=re.IGNORECASE):
                        format['location'] = re.sub(k + r"(?=\W|$)", v, format['location'], flags=re.IGNORECASE)

        parents = node['parents']
        for p in parents:
            path: list = p['path']
            for i, name in enumerate(path):
                if name in replacements:
                    path[i] = replacements[name]

    for k, v in replacements.items():
        if re.search(k, node['path'], flags=re.IGNORECASE):
            node['path'] = re.sub(k + r"(?=\W|$)", v, node['path'], flags=re.IGNORECASE)

    if 'path' in catalog:
        path: list = catalog['path']
        for i, name in enumerate(path):
            if name in replacements:
                path[i] = replacements[name]


def excluded(catalog_source_url):
    source_name = catalog_source_url.split('/')[0]
    source_export_settings = SOURCES.get(source_name, None)
    include = True
    if source_export_settings:
        relative_path = catalog_source_url[catalog_source_url.index('/') + 1:]
        for folder in source_export_settings['include_folders']:
            ic = re.IGNORECASE
            # note re.match implicitly searches at the begin of string (^pattern)
            if re.match(f'{folder}(/|$)', relative_path, flags=ic) or re.match(f'{relative_path}(/|$)', folder, flags=ic):
                break
        else:
            include = False

        if include:
            if source_export_settings['exclude']:
                for pattern in source_export_settings['exclude']:
                    if re.search(pattern, relative_path, flags=re.IGNORECASE):
                        return True

    return not include


def stop_traverse(catalog_source_url):
    source_name = catalog_source_url.split('/')[0]
    source_export_settings = SOURCES.get(source_name, None)
    if source_export_settings and source_export_settings['stop_traverse_pattern']:
        relative_path = catalog_source_url[catalog_source_url.index('/') + 1:]
        for pattern in source_export_settings['stop_traverse_pattern']:
            if re.search(pattern, relative_path, flags=re.IGNORECASE):
                return True
    return False


def create_dataset_filter_function(catalog_path, folder: Path, dataset_pattern: str) -> Callable[[str], bool]:
    # avoid recursive module load error
    from DIET.dremio_source import DremioFileSource, DremioFolderSource
    if folder.name.lower().endswith('.json'):
        source_data = DremioFileSource(folder, dataset_pattern).read()
    else:
        source_data = DremioFolderSource(folder, catalog_path, dataset_pattern).read()
    all_datasets = []
    stack = [source_data]

    while stack:
        node = stack.pop()
        all_datasets.append(node["path"])

        if "children" in node:
            for child in reversed(node["children"]):
                stack.append(child)

    def filter_function(dataset_path):
        return any(include_dataset == dataset_path for include_dataset in all_datasets)

    return filter_function
