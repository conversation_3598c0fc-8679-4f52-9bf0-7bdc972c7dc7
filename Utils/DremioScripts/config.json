{"credentials": {"STAGE": {"user_name": "ct_team", "password": "TU4m6LB5Ty9EyrJ", "pat_token": "", "dremio_server": "https://dremio-stage.realeyesit.com"}}, "sources": {"live_data_lake_semi_structured": {"include_folders": ["features", "media_segments"], "stop_traverse_pattern": [], "exclude": ["sourcemediagroup=", "sourcemediaid=", "meetingid=", "sessionid=", "algorithmid="]}}, "replacements": {"LIVE": {"@data_lake": "live_data_lake_semi_structured", "@data_lake_raw": "live_data_lake_raw", "@semi_bucket": "live-realeyes-data-lake-semi-structured", "@aws_glue": "Aws glue", "@fcp_db": "fcp_live", "@fcp_results": "fcp_pipeline_results", "@algo_id": {"value:": "264", "on_export": ["(algorithmid\\s*?=\\s*?)264", "\\1@algo_id"]}, "@min_sessions": {"value": "30", "on_export": ["(views\\s*?>=\\s*?)(30|50)", "\\1@min_sessions"]}}, "STAGE": {"@data_lake": "stage_data_lake_semi_structured", "@data_lake_raw": "stage_data_lake_raw", "@semi_bucket": "stage-realeyes-data-lake-semi-structured", "@aws_glue": "Aws glue", "@fcp_db": "fcp_stage", "@fcp_results": "fcp_pipeline_results", "@algo_id": {"value": "270", "on_export": ["(algorithmid\\s*?=\\s*?)264", "\\1@algo_id"]}, "@min_sessions": {"value": "10", "on_export": ["(views\\s*?>=\\s*?)(10|50)", "\\1@min_sessions"]}}}}