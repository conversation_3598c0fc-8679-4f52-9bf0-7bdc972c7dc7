SELECT 
    mapping.project_id as projectid, 
    mapping.pnumber,
    mapping.subjectgroup_id as subjectgroupid, 
    fcp.taskid, 
    fcp.sourcemediaid, fcp.testid, 
    mapping.brand,
    case when mapping.is_infocus then NULL else mapping.parent_creative end as parentcreative, --TODO change it when fixed on PD
    mapping.sourcemedia,
    case when mapping.is_infocus then 'in-focus' else 'in-context' end as projectpart,
    fcp.algorithmid, fcp.views, confirmit.views as views_confirmit 
FROM virtual.data.InContextPoC.MediaMapping as mapping
LEFT JOIN "@aws_glue"."@fcp_db"."mean_counts_media_duration_classifiers_creative" as fcp
    ON mapping.sourcemedia_id = fcp.sourcemediaid AND mapping.test_id = fcp.testid and mapping.task_id = fcp.taskid
LEFT JOIN "@aws_glue"."@fcp_db"."confirmit_media_survey_creative" as confirmit
    ON mapping.sourcemedia_id = confirmit.sourcemediaid AND mapping.test_id = confirmit.testid and mapping.task_id = confirmit.taskid and fcp.segment_key = confirmit.segment_key and fcp.algorithmid = confirmit.algorithmid
where is_max_second is True and fcp.segment_key = 'all'