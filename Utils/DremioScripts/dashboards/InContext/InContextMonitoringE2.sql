select sm.id as TargetSourceMediaID, sm.name as TargetSourceMediaName,  e2proj.*,case when exists (select null from virtual.data.preview.api."preview_api_counts_session_metrics" apiData
     where apiData.EyeSquareProjectID = e2Proj.EyeSquareProjectID and apiData.EyeSquareSubjectGroupID = e2Proj.EyeSquareSubjectGroupID
     and apiData.TaskID = e2Proj.TaskID and apiData.SubjectID = e2Proj.IdentityProviderKey ) then 1 else 0 end as HasAttentionMetrics
 from StudyDatabaseReadonly.StudyDatabase.dbo.vw_Monitoring_E2_Projects e2proj
 left join StudyDatabaseReadonly.StudyDatabase.dbo.InContextProjectMapping ipm 
    on ipm.EyeSquareProjectID = e2proj.EyeSquareProjectID and ipm.EyeSquareSubjectGroupID = e2proj.EyeSquareSubjectGroupID and ipm.TaskID = e2Proj.<PERSON><PERSON>
left join StudyDatabaseReadonly.StudyDatabase.dbo.SourceMedia sm on sm.id = ipm.SourceMediaID


