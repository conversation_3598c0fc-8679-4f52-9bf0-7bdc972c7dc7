{"sql": "InContextMonitoringE2.sql", "depends": ["virtual.data.preview.api.preview_api_counts_session_metrics", "StudyDatabaseReadonly.StudyDatabase.dbo.vw_Monitoring_E2_Projects", "StudyDatabaseReadonly.StudyDatabase.dbo.InContextProjectMapping", "StudyDatabaseReadonly.StudyDatabase.dbo.SourceMedia"], "reflections": [{"name": "Raw Reflection", "type": "RAW", "sortFields": ["EyeSquareProjectID", "EyeSquareSubjectGroupID", "TaskID"], "displayFields": ["TargetSourceMediaID", "TargetSourceMediaName", "DefaultStudy", "EyeSquareProjectID", "EyeSquareSubjectGroupID", "TaskID", "AdFormat", "IdentityProviderKey", "SessionID", "SessionPartID", "TestID", "CountryCode", "CountryName", "Browser", "Version", "MajorVersion", "CompatibilityVersion", "<PERSON><PERSON>", "<PERSON><PERSON>", "TestCreationDate", "SessionCreationTime", "ParticipantRecordingCollected", "RecordingProcessed", "StimulusDataExisits", "StimulusDataExistsForTarget", "EmotionDataExisitsForTarget", "HasAttentionMetrics"]}]}