WITH latest_fcp AS (
    select * from "@aws_glue"."@fcp_db"."mean_counts_media_duration_classifiers_creative"
    where algorithmid = 'algorithm-v6.0.0-nelSDK' and is_max_second = 1 and segment_key = 'all'
    and sm_group >= 17
),
running_projects as (
    SELECT CONCAT( CAST(mapping.test_id AS CHAR), '/', 
                CAST(mapping.sourcemedia_id AS CHAR), '/', 
                CAST(mapping.task_id AS CHAR) ) AS creative_id
        ,TO_DATE( TO_TIMESTAMP( mapping.creation_time, 'YYYY-MM-DD HH24:MI:SS.FFFFFFF TZO', 1) ) AS CreationDate
        --SCORE METADATA---------------------------------------
        ,fcpIC.views AS ViewsIC
        ,fcpIF.views AS ViewsIF
        ,mapping.sourcemedia AS SourceMedia -- Creative Version
        ,mapping.account As Account
        ,mapping.account_id AS AccountID
        ,mapping.project_id AS ProjectID
        ,mapping.is_enabled_for_clients AS IsEnabledForClients
        ,mapping.subjectgroup_id AS SubjectGroupID
        ,mapping.is_forced_exposure AS IsForcedExposure
        ,mapping.sourcemedia_id AS SourceMediaID
        ,mapping.test_id AS TestID
        ,mapping.task_id AS TaskID
        ,mapping.infocus_test_id AS InFocusTestID
        
    FROM virtual.data.InContextPoC.mediaMapping AS mapping
    LEFT JOIN StudyDatabaseReadonly.StudyDatabase.dbo.PreViewDisplaySettings psd on psd.InContextTestID = mapping.test_id 
    JOIN virtual.data.InContextPoC.mediaMapping AS in_focus_mapping 
        on in_focus_mapping.sourcemedia_id = mapping.sourcemedia_id and in_focus_mapping.test_id = mapping.infocus_test_id 
        and in_focus_mapping.project_id = mapping.project_id and in_focus_mapping.subjectgroup_id = mapping.subjectgroup_id
    JOIN latest_fcp as fcpIC on mapping.sourcemedia_id = cast(fcpIC.sourcemediaid as bigint) and mapping.test_id = fcpIC.testid and mapping.task_id = fcpIC.taskid
    JOIN latest_fcp as fcpIF on mapping.sourcemedia_id = cast(fcpIF.sourcemediaid as bigint) and in_focus_mapping.test_id = fcpIF.testid and in_focus_mapping.task_id = fcpIF.taskid and fcpIC.segment_key = fcpIF.segment_key
    WHERE mapping.is_target = TRUE and in_focus_mapping.is_target = TRUE -- only targets, not clutters
        AND mapping.is_infocus = FALSE -- main object is IC media test
        AND coalesce(psd.HiddenOnPreViewUI,FALSE) = FALSE
        AND mapping.is_enabled_for_clients = 0
)
select * from running_projects
where 1.0 * ABS(ViewsIF - ViewsIC)/GREATEST(ViewsIF, ViewsIC) >= 0.4 and ABS(ViewsIF - ViewsIC) >= 15
ORDER BY CreationDate desc, ProjectID desc