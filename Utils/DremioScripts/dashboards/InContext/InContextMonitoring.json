{"sql": "InContextMonitoring.sql", "sqlContext": "StudyDatabaseReadonly.StudyDatabase.dbo", "depends": ["StudyDatabaseReadonly.StudyDatabase.dbo.Participant", "StudyDatabaseReadonly.StudyDatabase.dbo.InContextProjectMapping", "StudyDatabaseReadonly.StudyDatabase.dbo.Product", "StudyDatabaseReadonly.StudyDatabase.dbo.Test", "StudyDatabaseReadonly.StudyDatabase.dbo.DataCollectionStructure", "StudyDatabaseReadonly.StudyDatabase.dbo.Project", "StudyDatabaseReadonly.StudyDatabase.dbo.Country", "StudyDatabaseReadonly.StudyDatabase.dbo.Account", "StudyDatabaseReadonly.StudyDatabase.dbo.QuestionVariableValue", "StudyDatabaseReadonly.StudyDatabase.dbo.SessionState", "StudyDatabaseReadonly.StudyDatabase.dbo.Precalc_SessionDimensions", "StudyDatabaseReadonly.StudyDatabase.dbo.Text"], "reflections": [{"name": "Raw Reflection", "type": "RAW", "displayFields": ["Account", "Product", "ParticipantID", "EyeSquareProjectID", "EyeSquareSubjectGroupID", "ProjectName", "RealEyesProject", "InContextTestID", "InFocusTestID", "CollectionName", "ProductName", "InContextState", "Country", "CountryCode", "InContextDevice", "InContextOS", "InContextBrowser", "InContextCreationDay", "InContextSessionCreationDay", "InContextCapable", "InContextPrompted", "InContextAgreed", "InContextCollected", "InContextIncluded", "InFocusState", "InFocusDevice", "InFocusOS", "InFocus<PERSON><PERSON>er", "InFocusCreationDay", "InFocusSessionCreationDay", "InFocusCapable", "InFocusPrompted", "InFocusAgreed", "InFocusCollected", "InFocusIncluded"]}]}