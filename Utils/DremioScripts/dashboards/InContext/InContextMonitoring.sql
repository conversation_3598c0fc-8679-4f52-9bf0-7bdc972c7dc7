select a.name as Account, prod.name Product, sIC.ParticipantID, ipm.EyeSquareProjectID,ipm.EyeSquareSubjectGroupID,ICProj.Name as ProjectName, CASE when ipm.EyeSquareProjectID like '%_RE%' then 1 else 0 end RealEyesProject, ipm.InContextTestID,dcsIF.TestID InFocusTestID,
dcsIF.Name CollectionName,COALESCE(prod.name,'Set up by E2') ProductName,sIC_State.Name InContextState,cIC.Name as Country,cIC.TwoLetterCode as CountryCode, sIC.Device InContextDevice, sIC.Os InContextOS, sIC.Browser as InContextBrowser,DATE_TRUNC('DAY',tIC.CreateDate) InContextCreationDay,DATE_TRUNC('DAY',sIC.CreationDate) InContextSessionCreationDay,
CAST(sIC.Capable as INTEGER) InContextCapable,CAST(sIC.Prompted as INTEGER)  InContextPrompted, CAST(sIC.AgreedToRecord as INTEGER)  as InContextAgreed, CAST(sIC.Collected as INTEGER)  as InContextCollected, 
CAST(sIC.IncludedInAnalysis as INTEGER) as InContextIncluded,
sIF_State.Name InFocusState,sIF.Device InFocusDevice, sIF.Os InFocusOS, sIF.Browser as InFocusBrowser,DATE_TRUNC('DAY',dcsIF.CreateDate) InFocusCreationDay, DATE_TRUNC('DAY',sIF.CreationDate) InFocusSessionCreationDay,
CAST(sIF.Capable as INTEGER)  InFocusCapable,CAST(sIF.Prompted as INTEGER)  InFocusPrompted, CAST(sIF.AgreedToRecord as INTEGER)  as InFocusAgreed, CAST(sIF.Collected as INTEGER)  as InFocusCollected, CAST(sIF.IncludedInAnalysis as INTEGER)  as InFocusIncluded
from 
(select distinct EyeSquareProjectID,EyeSquareSubjectGroupID,InContextTestID,DatacollectionStructureID, ProjectID from InContextProjectMapping) ipm
join Precalc_SessionDimensions sIC on sIC.TestID = ipm.InContextTestID and sIC.algorithmID =274
join Test tIC on tIC.ID = sIC.TestID
join Country cIC on cIC.ID = sIC.TopCountryID
join Participant pIC on sIC.ParticipantID=pIC.ID
join SessionState sIC_State on sIC_State.ID = sIC.SessionStateID
join Project AS ICProj on ICProj.ID = ipm.ProjectID
join Product AS prod ON prod.ID =ICproj.ProductID
join Account a on a.ID = ICProj.AccountID
left join datacollectionStructure dcsIF on dcsIF.id =ipm.DatacollectionStructureID
left join Text t_rid on t_rid."Value"=pIC.IdentityProviderKey
left join QuestionVariableValue qvvIF on qvvIF.AnswerTextID = t_rid.ID and qvvIF."Value"='RID'
left join Precalc_SessionDimensions sIF on sIF.DataCollectionStructureID=dcsIF.ID and sIF.SessionID=qvvIF.SessionID and sIF.algorithmID =@algo_id
left join SessionState sIF_State on sIF_State.ID = sIF.SessionStateID

