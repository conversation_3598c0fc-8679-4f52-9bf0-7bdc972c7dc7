# Folders with source files
Three folders @data_lake and virtual and dashboards are kept separate under source control and are deployed/exported separately too.

## Modifying source files

For virtual datasets you can manually modify SQL files and json files if needed to alter or add new keeping same json format.
Another option is to change it on live and sync with source folders(see BELOW)

## Adding new datasets

You can add new dataset manually under source control by doing two steps:
* Create new file (empty json `{}`) under proper folder in source control with the dataset name and `{name}.json` extension
  * you can also create nonempty file with sql and sqlContext nodes:
    ```json
    {
      "sql": "{name}.sql",
      "sqlContext": "virtual"
    }
    ```
* Create new SQL file with the same name as json file but with `{name}.sql` extension and add your SQL query there.
* OR you can call DIET tool to populate sql automatically (and it will update json file too if needed(i.e. add reflections, etc.)):
  * `PreViewPortal\Utils> py -m DIET export -s LIVE -f DremioScripts -c virtual/{path_to_file_or_root_folder} -so`

## Syncing source control with LIVE

When making changes directly on LIVE environment you can sync source control with LIVE environment using DIET tool periodically

# Commands:

Sync source control from LIVE (use -so flag to avoid populating source control with tons of un versioned vds we don't want to keep under source control)

`PreViewPortal\Utils> py -m DIET export -s LIVE -f DremioScripts -c virtual/data/preview -so` 
 
`PreViewPortal\Utils> py -m DIET export -s LIVE -f DremioScripts -c live_data_lake_semi_structured -so` 

Deploy source to STAGE

`PreViewPortal\Utils> py -m DIET import -s STAGE -f DremioScripts -c virtual/data/preview`

`PreViewPortal\Utils> py -m DIET import -s STAGE -f DremioScripts -c @data_lake`

Note that we do not update reflections here(-ur flag in import command). We did it once initially when importing to STAGE. 
At the moment we don't have a mechanism to skip update if reflection definition didn't change, so with that flag we will call update api for every reflection on each import.