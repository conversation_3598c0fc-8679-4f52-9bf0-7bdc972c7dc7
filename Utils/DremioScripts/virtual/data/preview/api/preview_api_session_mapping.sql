select mapping.EyeSquareProjectID,mapping.EyeSquareSubjectGroupID, mapping.TaskID,mapping.SourceMediaID,mapping.ElementID, subjects.IdentityProviderKey as SubjectID, subjects.SessionID,subjects.SessionPartID,subjects.TestID 
from StudyDatabaseReadonly.dbo.vw_EyeSquareIdentifierSetSubjects subjects
join StudyDatabaseReadonly.StudyDatabase.dbo.InContextProjectMapping as mapping on mapping.InContextTestID = subjects.TestID and mapping.TaskID = subjects.EyeSquareTaskID and subjects.EyeSquareProjectID = mapping.EyeSquareProjectID and subjects.EyeSquareSubjectGroupID = mapping.EyeSquareSubjectGroupID and subjects.SourceMediaID=mapping.SourceMediaID
order by subjects.TestID, subjects.IdentityProv<PERSON><PERSON><PERSON>,subjects.sessionid,subjects.sessionpartid