{"sql": "preview_api_stats_counts_media_duration_metrics.sql", "sqlContext": "virtual.data.preview.api", "depends": ["@aws_glue.@fcp_db.stats_media_duration_classifiers_creative", "@aws_glue.@fcp_db.mean_counts_media_duration_classifiers_creative", "StudyDatabaseReadonly.StudyDatabase.dbo.InContextProjectMapping"], "reflections": [{"name": "Raw Reflection", "type": "RAW", "sortFields": ["AccountID", "EyeSquareProjectID"], "displayFields": ["AccountID", "EyeSquareProjectID", "EyeSquareSubjectGroupID", "TaskID", "ElementID", "second", "confusion_viewer_share_avg", "confusion_viewer_share_max", "confusion_viewer_share_std", "contempt_viewer_share_avg", "contempt_viewer_share_max", "contempt_viewer_share_std", "disgust_viewer_share_avg", "disgust_viewer_share_max", "disgust_viewer_share_std", "exposure", "eyeson_viewer_share_avg", "eyeson_viewer_share_max", "eyeson_viewer_share_std", "happiness_viewer_share_avg", "happiness_viewer_share_max", "happiness_viewer_share_std", "inattentive_visible_viewer_share_avg", "inattentive_visible_viewer_share_max", "inattentive_visible_viewer_share_std", "negativity_viewer_share_avg", "negativity_viewer_share_max", "negativity_viewer_share_std", "neutral_attention_viewer_share_avg", "neutral_attention_viewer_share_max", "neutral_attention_viewer_share_std", "neutral_eyeson_viewer_share_avg", "neutral_eyeson_viewer_share_max", "neutral_eyeson_viewer_share_std", "not_visible_viewer_share_avg", "not_visible_viewer_share_max", "not_visible_viewer_share_std", "reactions_viewer_share_avg", "reactions_viewer_share_max", "reactions_viewer_share_std", "surprise_viewer_share_avg", "surprise_viewer_share_max", "surprise_viewer_share_std", "visible_viewer_share_avg", "visible_viewer_share_max", "visible_viewer_share_std", "eyeson_seconds_avg", "eyeson_seconds_all_exposures_avg", "eyeson_seconds_from_duration_avg", "eyeson_viewthrough_avg", "happiness_seconds_all_exposures_avg", "happiness_seconds_avg", "confusion_seconds_all_exposures_avg", "confusion_seconds_avg", "contempt_seconds_all_exposures_avg", "contempt_seconds_avg", "disgust_seconds_all_exposures_avg", "disgust_seconds_avg", "surprise_seconds_all_exposures_avg", "surprise_seconds_avg", "negativity_seconds_all_exposures_avg", "negativity_seconds_avg", "percentage_with_confusion_seconds", "percentage_with_contempt_seconds", "percentage_with_disgust_seconds", "percentage_with_eyeson_seconds", "percentage_with_happiness_seconds", "percentage_with_inattentive_visible_seconds", "percentage_with_negativity_seconds", "percentage_with_reactions_seconds", "percentage_with_surprise_seconds", "reactions_seconds_all_exposures_avg", "reactions_seconds_avg", "visible_seconds_all_exposures_avg", "visible_seconds_avg", "viewthrough_avg", "exposures", "views"]}]}