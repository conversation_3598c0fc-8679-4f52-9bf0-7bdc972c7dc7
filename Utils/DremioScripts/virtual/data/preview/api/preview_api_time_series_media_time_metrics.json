{"sql": "preview_api_time_series_media_time_metrics.sql", "sqlContext": "virtual", "depends": ["StudyDatabaseReadonly.StudyDatabase.dbo.InContextProjectMapping", "@aws_glue.@fcp_db.mean_media_time_classifiers_creative"], "reflections": [{"name": "Raw Reflection", "type": "RAW", "sortFields": ["AccountID", "EyeSquareProjectID"], "displayFields": ["AccountID", "EyeSquareProjectID", "EyeSquareSubjectGroupID", "TaskID", "ElementID", "second", "visible_viewer_share", "attentive_viewer_share", "happiness_viewer_share", "eyeson_viewer_share", "confusion_viewer_share", "contempt_viewer_share", "disgust_viewer_share", "surprise_viewer_share", "negativity_viewer_share", "reactions_viewer_share", "inattentive_visible_viewer_share", "not_visible_viewer_share", "exposure", "views"]}]}