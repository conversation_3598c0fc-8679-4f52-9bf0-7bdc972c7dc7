select
6577 as AccountID,
mapping.EyeSquareProjectID,
mapping.EyeSquareSubjectGroupID, 
mapping.TaskID, 
mapping.ElementID,
stats."second",
stats.confusion_viewer_share_avg,
stats.confusion_viewer_share_max,
stats.confusion_viewer_share_std,
stats.contempt_viewer_share_avg,
stats.contempt_viewer_share_max,
stats.contempt_viewer_share_std,
stats.disgust_viewer_share_avg,
stats.disgust_viewer_share_max,
stats.disgust_viewer_share_std,
stats.exposure,
stats.eyeson_viewer_share_avg,
stats.eyeson_viewer_share_max,
stats.eyeson_viewer_share_std,
stats.happiness_viewer_share_avg,
stats.happiness_viewer_share_max,
stats.happiness_viewer_share_std,
stats.inattentive_visible_viewer_share_avg,
stats.inattentive_visible_viewer_share_max,
stats.inattentive_visible_viewer_share_std,
stats.negativity_viewer_share_avg,
stats.negativity_viewer_share_max,
stats.negativity_viewer_share_std,
stats.neutral_attention_viewer_share_avg,
stats.neutral_attention_viewer_share_max,
stats.neutral_attention_viewer_share_std,
stats.neutral_eyeson_viewer_share_avg,
stats.neutral_eyeson_viewer_share_max,
stats.neutral_eyeson_viewer_share_std,
stats.not_visible_viewer_share_avg,
stats.not_visible_viewer_share_max,
stats.not_visible_viewer_share_std,
stats.reactions_viewer_share_avg,
stats.reactions_viewer_share_max,
stats.reactions_viewer_share_std,
stats.surprise_viewer_share_avg,
stats.surprise_viewer_share_max,
stats.surprise_viewer_share_std,
stats.visible_viewer_share_avg,
stats.visible_viewer_share_max,
stats.visible_viewer_share_std,
counts.eyeson_seconds_avg,
counts.eyeson_seconds_all_exposures_avg,
counts.eyeson_seconds_from_duration_avg,
counts.eyeson_viewthrough_avg,
counts.happiness_seconds_all_exposures_avg,
counts.happiness_seconds_avg,
counts.confusion_seconds_all_exposures_avg,
counts.confusion_seconds_avg,
counts.contempt_seconds_all_exposures_avg,
counts.contempt_seconds_avg,
counts.disgust_seconds_all_exposures_avg,
counts.disgust_seconds_avg,
counts.surprise_seconds_all_exposures_avg,
counts.surprise_seconds_avg,
counts.negativity_seconds_all_exposures_avg,
counts.negativity_seconds_avg,
counts.percentage_with_confusion_seconds,
counts.percentage_with_contempt_seconds,
counts.percentage_with_disgust_seconds,
counts.percentage_with_eyeson_seconds,
counts.percentage_with_happiness_seconds,
counts.percentage_with_inattentive_visible_seconds,
counts.percentage_with_negativity_seconds,
counts.percentage_with_reactions_seconds,
counts.percentage_with_surprise_seconds,
counts.reactions_seconds_all_exposures_avg,
counts.reactions_seconds_avg,
counts.visible_seconds_all_exposures_avg,
counts.visible_seconds_avg,
counts.viewthrough_avg,
counts.exposures,
counts.views
from "@aws_glue"."@fcp_db"."stats_media_duration_classifiers_creative" stats
JOIN "@aws_glue"."@fcp_db"."mean_counts_media_duration_classifiers_creative" counts on stats.testid = counts.testid and stats.taskid = counts.taskid and stats.sourcemediaid = counts.sourcemediaid and counts.is_max_second = TRUE and stats.is_max_second = TRUE
join StudyDatabaseReadonly.StudyDatabase.dbo.InContextProjectMapping as mapping on mapping.InContextTestID = stats.TestID and mapping.TaskID = stats.TaskID and mapping.sourcemediaid=stats.sourcemediaid
order by mapping.EyeSquareProjectID,mapping.EyeSquareSubjectGroupID, mapping.TaskID, mapping.ElementID



