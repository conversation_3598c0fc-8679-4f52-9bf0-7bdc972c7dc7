{"sql": "preview_api_session_mapping.sql", "sqlContext": "@a_forgacs", "depends": ["StudyDatabaseReadonly.StudyDatabase.dbo.vw_EyeSquareIdentifierSetSubjects", "StudyDatabaseReadonly.StudyDatabase.dbo.InContextProjectMapping"], "reflections": [{"name": "Raw Reflection", "type": "RAW", "sortFields": ["SessionPartID", "SessionID", "SourceMediaID"], "displayFields": ["EyeSquareProjectID", "EyeSquareSubjectGroupID", "TaskID", "SourceMediaID", "SessionID", "SessionPartID", "TestID", "ElementID", "SubjectID"]}]}