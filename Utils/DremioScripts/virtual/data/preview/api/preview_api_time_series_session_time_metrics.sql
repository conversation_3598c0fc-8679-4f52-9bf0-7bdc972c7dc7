select 6577 as AccountID,
  mapping.EyeSquareProjectID,mapping.EyeSquareSubjectGroupID,mapping.SubjectID, mapping.TaskID, mapping.ElementID, ts.exposure,ts."second",ts.attentive,ts.eyeson,ts.happiness,ts.confusion,ts.contempt,ts.disgust,ts.surprise,ts.negativity,ts.reactions,ts.visible
from "@aws_glue"."@fcp_db"."time_series_session_time_classifiers_creative" ts
join virtual.data.preview.api."preview_api_session_mapping" mapping on mapping.sessionpartid=ts.sessionpartid and mapping.sourcemediaid=ts.sourcemediaid
where ts.algorithmid = 'algorithm-v6.0.0-nelSDK'
order by mapping.EyeSquareProjectID,mapping.EyeSquareSubjectGroupID,mapping.SubjectID, mapping.TaskID,ts.exposure,ts."second"