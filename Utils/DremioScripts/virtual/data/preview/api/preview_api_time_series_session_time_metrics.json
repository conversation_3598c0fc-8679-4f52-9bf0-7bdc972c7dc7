{"sql": "preview_api_time_series_session_time_metrics.sql", "sqlContext": "@aws_glue", "depends": ["@aws_glue.@fcp_db.time_series_session_time_classifiers_creative", "virtual.data.preview.api.preview_api_session_mapping"], "reflections": [{"name": "Raw Reflection", "type": "RAW", "sortFields": ["EyeSquareProjectID", "EyeSquareSubjectGroupID", "TaskID", "SubjectID"], "displayFields": ["AccountID", "EyeSquareProjectID", "EyeSquareSubjectGroupID", "SubjectID", "TaskID", "ElementID", "exposure", "second", "attentive", "eyeson", "happiness", "confusion", "contempt", "disgust", "surprise", "negativity", "reactions", "visible"]}]}