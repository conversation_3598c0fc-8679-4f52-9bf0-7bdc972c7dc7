select 6577 as AccountID,
  mapping.EyeSquareProjectID,mapping.EyeSquareSubjectGroupID, mapping.TaskID, mapping.ElementID, 
  ts.exposure,
  ts."second",
  ts.views,
  ts.visible_viewer_share,
ts.attentive_viewer_share,
ts.happiness_viewer_share,
ts.eyeson_viewer_share,
ts.confusion_viewer_share,
ts.contempt_viewer_share,
ts.disgust_viewer_share,
ts.surprise_viewer_share,
ts.negativity_viewer_share,
ts.reactions_viewer_share,
ts.inattentive_visible_viewer_share,
ts.not_visible_viewer_share
from "@aws_glue"."@fcp_db"."mean_media_time_classifiers_creative" ts
join StudyDatabaseReadonly.StudyDatabase.dbo.InContextProjectMapping as mapping on mapping.InContextTestID = ts.TestID and mapping.TaskID = ts.TaskID and mapping.sourcemediaid=ts.sourcemediaid
order by mapping.EyeSquareProjectID,mapping.EyeSquareSubjectGroupID, mapping.TaskID, ts.exposure, ts."second"

