{"sql": "preview_api_counts_session_metrics.sql", "sqlContext": "virtual", "depends": ["@aws_glue.@fcp_db.counts_session_duration_classifiers_creative", "virtual.data.preview.api.preview_api_session_mapping"], "reflections": [{"name": "Raw Reflection", "type": "RAW", "sortFields": ["EyeSquareProjectID", "EyeSquareSubjectGroupID", "SubjectID", "TaskID"], "displayFields": ["AccountID", "EyeSquareProjectID", "EyeSquareSubjectGroupID", "SubjectID", "TaskID", "ElementID", "exposures", "attentive_seconds", "eyeson_seconds", "happiness_seconds", "confusion_seconds", "contempt_seconds", "disgust_seconds", "surprise_seconds", "negativity_seconds", "reactions_seconds", "visible_seconds", "eyeson_seconds_all_exposures", "attentive_seconds_all_exposures", "unseen_visible_seconds", "attentive_seconds_from_duration", "eyeson_seconds_from_duration"], "enabled": false}]}