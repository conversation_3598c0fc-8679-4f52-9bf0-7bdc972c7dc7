-- 17-MAR-2022: used Row_number() rather than Rank() to dedup redundant records (same timestamps)
select 
    SourceMediaID
    ,MediaSecond
    ,Tag
    ,Confidence
    --,UpdateDate
from (
    select 
        cast(sm.id as int) SourceMediaID
        ,cast(ceiling(1.0*EndTime/1000) as int) MediaSecond
        ,lower(valuevarchar) as Tag
        ,cast(valuereal as decimal(10,3)) as Confidence
        --,cast(tags.updatedate as date) as UpdateDate
        ,row_number() over (partition by MediaVariableID, tags.SourceMediaID, cast(EndTime as int), lower(valuevarchar) order by tags.id DESC) as LastUpdate
    from 
	StudyDatabaseReadonly.StudyDatabase.dbo.SourceMedia sm
	left join StudyDatabaseReadonly.StudyDatabase.dbo.MediaStorage ms on sm.MediaStorageID = ms.ID
	INNER join StudyDatabaseReadonly.StudyDatabase.dbo.TestElementSequence tes ON sm.ID = tes.SourceMediaID		
    join StudyDatabaseReadonly.StudyDatabase.dbo.MediaSceneVariableValue tags
    on tags.SourceMediaID = sm.id
    and tags.EndTime <= coalesce(sm.Duration,ms.Duration,tes.Cutoff)
    where MediaVariableID = 43
) X
where LastUpdate = 1