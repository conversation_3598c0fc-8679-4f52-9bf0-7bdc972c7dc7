-- 13-JUL-2022:
-- Changed CTE to be robust to non-unique durations in sourcemediaIDs  
-- 17-MAR-2022:
-- exclude seconds with 'no person' tags 
-- remove 'people' after finding problematic false positives (dogs->people) and boosted confidence minimum from 0.75 to 0.95 to increase specificity
with media_durations as (
    -- select distinct cast(SourceMediaID as int) SourceMediaID, cast(sourcemediadurationsec as int) as Duration
    -- from StudyDatabaseReadonly.StudyDatabase.dbo.vw_PreView_MediaSegments
    -- where sourcemediadurationsec > 0
    select cast(SourceMediaID as int) SourceMediaID, cast(max(sourcemediadurationsec) as int) as Duration
    from StudyDatabaseReadonly.StudyDatabase.dbo.vw_PreView_MediaSegments
    where sourcemediadurationsec > 0
    group by sourcemediaid
)
select 
    media_durations.SourceMediaID, 
    media_durations.Duration,
    coalesce(Seconds_with_people, 0) Seconds_with_people,
    cast(coalesce(1.0*Seconds_with_people/media_durations.Duration,0) as decimal(10,3)) HumanPresence,
    Humans_earliest_position,
    Humans_avg_position,
    Humans_latest_position,
    -- note: if no presence, then by default relative position metrics are set to 1 (this way 0 always means early and 1 late)
    cast(coalesce(1.0*Humans_earliest_position/media_durations.Duration, 1) as decimal(10,3)) Humans_Relative_EarliestPosition,
    cast(coalesce(1.0*Humans_avg_position/media_durations.Duration, 1) as decimal(10,3)) Humans_RelativePosition,
    cast(coalesce(1.0*Humans_latest_position/media_durations.Duration, 1) as decimal(10,3)) Humans_Relative_LatestPosition
from media_durations
left join
(
    select
        SourceMediaID 
        ,count(distinct(MediaSecond)) Seconds_with_people
        ,max(case when MediaSecond <= 5 then 1 else 0 end) Humans_in_first_5sec
        ,cast(min(MediaSecond) as int) Humans_earliest_position 
        ,cast(avg(MediaSecond) as decimal(10,3)) Humans_avg_position
        ,cast(max(MediaSecond) as int) Humans_latest_position   
    from
    (select 
        SourceMediaID,
        MediaSecond,
        max(case when tag = 'no person' then 1 else 0 end) has_no_person 
        from virtual.data.preview.content.preview_media_time_clarifai_tags
        where tag in (
            'no person',
            'person', 'human', 'guy',
            'woman', 'man', 'women', 'men', 'female', 'male',
            'baby', 'child', 'girl', 'boy', 'teenager',
            'couple', 'family', 'parent'
        ) and confidence >= 0.95
    group by sourcemediaid, mediasecond ) as seconds_ppl
    where has_no_person = 0
    group by SourceMediaID
) media_ppl 
on media_durations.sourcemediaid = media_ppl.sourcemediaid
