-- author: <EMAIL>
-- updates: 
-- 2022-07-13: changed CTE to be robust to non-unique Duration per sourcemediaID
-- 2022-03-03: bump minimum confidence to 0.8, add Typography and Label as accepted tags
-- 2022-03-02: removed Order By
-- 2022-11-26: added Text_Relative_EarliestPosition and Text_Relative_LatestPosition
-- 2022-11-26: avoid coalesce for earliest/latest position (if no text, no value defined), changed naming convention
with media_durations as (
    -- select distinct cast(SourceMediaID as int) SourceMediaID, cast(sourcemediadurationsec as int) as Duration
    -- from StudyDatabaseReadonly.StudyDatabase.dbo.vw_PreView_MediaSegments
    -- where sourcemediadurationsec > 0
    select cast(SourceMediaID as int) SourceMediaID, cast(max(sourcemediadurationsec) as int) as Duration
    from StudyDatabaseReadonly.StudyDatabase.dbo.vw_PreView_MediaSegments
    where sourcemediadurationsec > 0
    group by sourcemediaid
)
select 
    media_durations.SourceMediaID, 
    media_durations.Duration,
    coalesce(Seconds_with_text, 0) Seconds_with_text,
    coalesce(Text_in_first_5sec, 0) Text_in_first_5sec,
    Text_earliest_position, -- 26Nov2021: no coalesce, null if no text
    coalesce(Text_avg_position, 0) Text_avg_position,
    Text_latest_position, -- 26Nov2021: no coalesce, null if no text
    cast(coalesce(1.0*Seconds_with_text/media_durations.Duration, 0) as decimal(10,3)) Text_OverallPresence,
    cast(coalesce(1.0*Text_avg_position/media_durations.Duration, 1) as decimal(10,3)) Text_RelativePosition, -- 26Nov2021: set to 1 if null, as implies text is a late as possible to come (lim->100%)
    cast(coalesce(1.0*Text_earliest_position/media_durations.Duration, 1) as decimal(10,3)) Text_Relative_EarliestPosition,
    cast(coalesce(1.0*Text_latest_position/media_durations.Duration, 1) as decimal(10,3)) Text_Relative_LatestPosition
from media_durations
left join 
(select 
    SourceMediaID
    ,count(distinct(case when tag in ('text', 'label', 'typography') then MediaSecond end)) Seconds_with_text 
    ,max(case when MediaSecond <= 5 then 1 else 0 end) Text_in_first_5sec
    ,cast(min(MediaSecond) as int) Text_earliest_position 
    ,cast(avg(MediaSecond) as decimal(10,3)) Text_avg_position
    ,cast(max(MediaSecond) as int) Text_latest_position  
from virtual.data.preview.content.preview_media_time_clarifai_tags
where tag in ('text', 'label', 'typography') and confidence >= 0.8
group by sourcemediaid ) as seconds_ppl
on media_durations.sourcemediaid = seconds_ppl.sourcemediaid