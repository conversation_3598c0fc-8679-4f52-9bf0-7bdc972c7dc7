{"sql": "preview_media_time_clarifai_tags.sql", "sqlContext": "virtual.data.preview.content", "depends": ["StudyDatabaseReadonly.StudyDatabase.dbo.TestElementSequence", "StudyDatabaseReadonly.StudyDatabase.dbo.MediaStorage", "StudyDatabaseReadonly.StudyDatabase.dbo.MediaSceneVariableValue", "StudyDatabaseReadonly.StudyDatabase.dbo.SourceMedia"], "reflections": [{"name": "Raw Reflection", "type": "RAW", "sortFields": ["SourceMediaID"], "displayFields": ["SourceMediaID", "MediaSecond", "Tag", "Confidence"]}]}