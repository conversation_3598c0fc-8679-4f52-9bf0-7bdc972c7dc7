-- 13-JUL-2022: changed CTE to be robust to non-unique Duration by SourceMediaID
-- 17-MAR-2022: boosted min confidence requirement to 0.95
with media_durations as (
    -- select distinct cast(SourceMediaID as int) SourceMediaID, cast(sourcemediadurationsec as int) as Duration
    -- from StudyDatabaseReadonly.StudyDatabase.dbo.vw_PreView_MediaSegments
    -- where sourcemediadurationsec > 0
    select cast(SourceMediaID as int) SourceMediaID, cast(max(sourcemediadurationsec) as int) as Duration
    from StudyDatabaseReadonly.StudyDatabase.dbo.vw_PreView_MediaSegments
    where sourcemediadurationsec > 0
    group by sourcemediaid
)
select 
    media_durations.SourceMediaID, 
    media_durations.Duration,
    coalesce(Seconds_with_women, 0) Seconds_with_women,
    coalesce(Seconds_with_men, 0) Seconds_with_men,
    cast(coalesce(1.0*Seconds_with_women/media_durations.Duration,0) as decimal(10,3)) FemalePresence,
    cast(coalesce(1.0*Seconds_with_men/media_durations.Duration,0) as decimal(10,3)) MalePresence,
    cast(coalesce(1.0*Seconds_with_women/(Seconds_with_women+Seconds_with_men),0) as decimal(10,3)) Female_representation_ratio
from media_durations
left join 
(select 
    SourceMediaID
    ,count(distinct(case when tag in ('female', 'woman', 'women') then MediaSecond end)) Seconds_with_women 
    ,count(distinct(case when tag in ('male', 'man', 'men') then MediaSecond end)) Seconds_with_men 
from virtual.data.preview.content.preview_media_time_clarifai_tags
where tag in (
    'female', 'male',
    'woman', 'man', 
    'women', 'men' 
) and confidence >= 0.95
group by sourcemediaid ) as seconds_ppl
on media_durations.sourcemediaid = seconds_ppl.sourcemediaid


