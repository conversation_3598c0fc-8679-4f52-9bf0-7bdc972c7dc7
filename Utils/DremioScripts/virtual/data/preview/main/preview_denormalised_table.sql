select
case when sr.QualityScore is null then 1 else 0 end as Missing_QualityScore
,case when sr.Encode is null then 1 else 0 end as Missing_Encode
,case when sr.Negativity_peak is null then 1 else 0 end as Missing_Negativity_peak
,case when sr.Surprise_avg is null then 1 else 0 end as Missing_Emotion_averages
,case when md.Country is null then 1 else 0 end as Missing_Country
,case when md.BrandID is null then 1 else 0 end as Missing_Brand
,case when md.Duration = 0 then 1 else 0 end as Wrong_Duration
,case when md.Duration is null then 1 else 0 end as Missing_Duration
,case when sr.Benchmark_sample_size < 20 then 1 else 0 end as Low_Benchmark_sample_size 
,
md.SourceMediaID,
md.TestID,
sr.<PERSON>,
md.Parent<PERSON>randID,
md.Brand<PERSON>,
md.ParentBrandLogoFileName,
md.BrandLogoFileName,
md.TopCategoryID,
md.MidCategoryID,
md.SubCategoryID,
md.CategoryID,
md.CountryID,
md.External_key,
md.Date_of_test,
md.<PERSON>,
md.SourceMedia,
md.SourceMediaThumbnailFileName,
md.Test,
md.<PERSON>rent<PERSON>rand,
md.Brand,
md.Category,
'' as CategoryTree,
md.TopCategory,
md.MidCategory,
md.SubCategory,
md.Continent,
md.Country,
md.Country_code,
md.Duration,
md.Cap,
sr.Views,
sr.Benchmark_sample_size,
sr.QualityScore,
sr.CreativeEfficiency,
sr.Capture_decile,
sr.Retain_decile,
sr.Encode_decile,
sr.QualityScore_traffic_light,
sr.QualityScore_index,
sr.Capture,
sr.Retain,
sr.Encode,
sr.Capture_percentile,
sr.Retain_percentile,
sr.Encode_percentile,
sr.Capture_benchmark_median,
sr.Retain_benchmark_median,
sr.Encode_benchmark_median,
sr.Capture_benchmark_top20,
sr.Retain_benchmark_top20,
sr.Encode_benchmark_top20,
sr.Capture_index,
sr.Retain_index,
sr.Encode_index,
sr.Distraction_avg,
sr.Neutral_attention_avg,
sr.Engaged_attention_avg,
sr.Distraction_decile,
sr.Neutral_attention_decile,
sr.Engaged_attention_decile,
sr.Distraction_percentile,
sr.Neutral_attention_percentile,
sr.Engaged_attention_percentile,
sr.Distraction_benchmark_median,
sr.Neutral_attention_benchmark_median,
sr.Engaged_attention_benchmark_median,
sr.Distraction_benchmark_top20,
sr.Neutral_attention_benchmark_top20,
sr.Engaged_attention_benchmark_top20,
sr.Distraction_index,
sr.Neutral_attention_index,
sr.Engaged_attention_index,
sr.Happiness_avg,
sr.Surprise_avg,
sr.Negativity_avg,
sr.Confusion_avg,
sr.Contempt_avg,
sr.Disgust_avg,
sr.Happiness_avg_decile,
sr.Surprise_avg_decile,
sr.Negativity_avg_decile,
sr.Confusion_avg_decile,
sr.Contempt_avg_decile,
sr.Disgust_avg_decile,
sr.Happiness_avg_percentile,
sr.Surprise_avg_percentile,
sr.Negativity_avg_percentile,
sr.Confusion_avg_percentile,
sr.Contempt_avg_percentile,
sr.Disgust_avg_percentile,
sr.Happiness_avg_benchmark_median,
sr.Surprise_avg_benchmark_median,
sr.Negativity_avg_benchmark_median,
sr.Confusion_avg_benchmark_median,
sr.Contempt_avg_benchmark_median,
sr.Disgust_avg_benchmark_median,
sr.Happiness_avg_benchmark_top20,
sr.Surprise_avg_benchmark_top20,
sr.Negativity_avg_benchmark_top20,
sr.Confusion_avg_benchmark_top20,
sr.Contempt_avg_benchmark_top20,
sr.Disgust_avg_benchmark_top20,
sr.Happiness_avg_index,
sr.Surprise_avg_index,
sr.Negativity_avg_index,
sr.Confusion_avg_index,
sr.Contempt_avg_index,
sr.Disgust_avg_index,
sr.Happiness_peak,
sr.Surprise_peak,
sr.Negativity_peak,
sr.Confusion_peak,
sr.Contempt_peak,
sr.Disgust_peak,
sr.Happiness_peak_decile,
sr.Surprise_peak_decile,
sr.Negativity_peak_decile,
sr.Confusion_peak_decile,
sr.Contempt_peak_decile,
sr.Disgust_peak_decile,
sr.Happiness_peak_percentile,
sr.Surprise_peak_percentile,
sr.Negativity_peak_percentile,
sr.Confusion_peak_percentile,
sr.Contempt_peak_percentile,
sr.Disgust_peak_percentile,
sr.Happiness_peak_benchmark_median,
sr.Surprise_peak_benchmark_median,
sr.Negativity_peak_benchmark_median,
sr.Confusion_peak_benchmark_median,
sr.Contempt_peak_benchmark_median,
sr.Disgust_peak_benchmark_median,
sr.Happiness_peak_benchmark_top20,
sr.Surprise_peak_benchmark_top20,
sr.Negativity_peak_benchmark_top20,
sr.Confusion_peak_benchmark_top20,
sr.Contempt_peak_benchmark_top20,
sr.Disgust_peak_benchmark_top20,
sr.Happiness_peak_index,
sr.Surprise_peak_index,
sr.Negativity_peak_index,
sr.Confusion_peak_index,
sr.Contempt_peak_index,
sr.Disgust_peak_index
from virtual.data.preview.main.preview_metadata md
join virtual.data.preview.main.preview_scores sr
on md.SourceMediaID = sr.SourceMediaID
and md.TestID = sr.TestID