{"sql": "preview_metadata.sql", "sqlContext": "@a_forgacs", "depends": ["@data_lake_raw.eye_square.categorisations.\"geographic_regions.parquet\"", "StudyDatabaseReadonly.StudyDatabase.dbo.vw_PreView_MediaSegments", "virtual.data.preview.main.preview_brand_parentbrand", "StudyDatabaseReadonly.StudyDatabase.dbo.Brand", "StudyDatabaseReadonly.StudyDatabase.dbo.Country", "StudyDatabaseReadonly.StudyDatabase.dbo.MediaStorage", "StudyDatabaseReadonly.StudyDatabase.dbo.TestDeviceType", "StudyDatabaseReadonly.StudyDatabase.dbo.Continent", "StudyDatabaseReadonly.StudyDatabase.dbo.BrandLogo"], "reflections": [{"name": "Raw Reflection", "type": "RAW", "sortFields": ["SourceMediaID"], "displayFields": ["SourceMediaID", "TestID", "ParentBrandID", "BrandID", "ParentBrandLogoFileName", "BrandLogoFileName", "TopCategoryID", "MidCategoryID", "SubCategoryID", "CategoryID", "CountryID", "External_key", "Date_of_test", "SourceMedia", "SourceMediaThumbnailFileName", "SourceMediaConvertedFileName", "Test", "Category", "TopCategory", "MidCategory", "SubCategory", "<PERSON><PERSON><PERSON><PERSON>", "Brand", "Continent", "Country", "Country_code", "Duration", "Cap", "<PERSON><PERSON><PERSON><PERSON>", "geographic_region", "format", "TestDevice", "CollectionID"]}]}