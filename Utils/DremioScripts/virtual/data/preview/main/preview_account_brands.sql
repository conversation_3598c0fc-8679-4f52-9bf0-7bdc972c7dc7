-----------------
-- UPDATES
-----------------
-- 16 Feb 2022: define MidTopCategory there to join to brand-level scores directly
-- 07 Feb 2022: refactor to conform to new tables and goals
select AccountID, 
    pb.ParentBrandID,
    pb.BrandID, 
    coalesce(c.CategoryID, c.IndustryID) as MidTopCategoryID, 
    c.CategoryID as MidCategoryID, 
    c.IndustryID as TopCategoryID,
    coalesce(c.CategoryName, c.IndustryName) as MidTopCategory
from StudyDatabaseReadonly.StudyDatabase.dbo.Account_Brand ab
left join StudyDatabaseReadonly.StudyDatabase.dbo.Brand_ParentBrand pb
on ab.BrandID = pb.ParentBrandID
join StudyDatabaseReadonly.StudyDatabase.dbo.Brand b
on pb.BrandID = b.ID
join StudyDatabaseReadonly.StudyDatabase.dbo."vw_CategoryHierarchy" c
on b.CategoryID = c.ID
order by AccountID, ParentBrandID, BrandID
