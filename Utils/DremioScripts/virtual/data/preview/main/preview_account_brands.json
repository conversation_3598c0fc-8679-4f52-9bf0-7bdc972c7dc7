{"sql": "preview_account_brands.sql", "sqlContext": "virtual.data.preview.main", "depends": ["StudyDatabaseReadonly.StudyDatabase.dbo.Account_Brand", "StudyDatabaseReadonly.StudyDatabase.dbo.Brand_ParentBrand", "StudyDatabaseReadonly.StudyDatabase.dbo.Brand", "StudyDatabaseReadonly.StudyDatabase.dbo.vw_CategoryHierarchy"], "reflections": [{"name": "Raw Reflection", "type": "RAW", "sortFields": ["AccountID"], "displayFields": ["AccountID", "ParentBrandID", "BrandID", "MidTopCategoryID", "MidCategoryID", "TopCategoryID", "MidTopCategory"]}]}