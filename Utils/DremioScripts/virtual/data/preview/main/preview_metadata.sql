----------------
-- UPDATES
----------------
-- 01 Aug 2022: joined parentbrand via new VDS that provides ParentBrandRank and filter by ParentBrandRank=1 to ensure arbitrarily that 1 BrandID only returns 1 ParentBrandID 
-- 13 Jul 2022: added HackyHash, a trick to count unique test results based on unique SMID, TestID and CountryID in children tables
-- 22 Mar 2022: removed order by
-- 21 Mar 2022: Removed format_length (not used and costly case when)
-- 21 Mar 2022: Added ParentBrandLogoFileName
-- 16 Feb 2022: Added ParentBrandID (nullable) and ParentBrand (nullable)
select *, cast(sourcemediaid * 1000000000.0 +  TestID * 1000.0 + coalesce(countryid, 0) as bigint) HackyHash 
from 
( select
    distinct 
    cast(ms.sourcemediaid as int) SourceMediaID 
    ,cast(ms.testid as int) TestID
    ,cast(ms.CollectionId as int) CollectionID
    ,case td.DeviceType
        when 1 then 'desktop'
        when 2 then 'tablet'
        when 3 then 'mobile'
        when 10 then 'mixed'
        else 'unknown' end
    as TestDevice
    ,cast(pb.parentbrandid as int) ParentBrandID
    ,cast(ms.brandid as int) BrandID
    ,mstorpbl.ConvertedFileName as ParentBrandLogoFileName
    ,mstorbl.ConvertedFileName as BrandLogoFileName
    ,cast(TopCategoryID as int) as TopCategoryID
    ,cast(MidCategoryID as int) as MidCategoryID
    ,cast(SubCategoryID as int) as SubCategoryID
    ,cast(ms.categoryid as int) CategoryID -- Category = deepest level defined in the category tree (can be top, mid, sub)
    ,cast(ct.CountryID as int) CountryID
    ,ms.externalkey as External_key
    ,cast(ms.testcreatedate as date) Date_of_test
    -- ,cast(latestsession as date) Latest_session -- can introduce duplicates
    -- ,cast(sessionscount as int) Session_count -- can introduce duplicates
    ,ms.sourcemedianame as SourceMedia
    ,mstor.ThumbnailFileName SourceMediaThumbnailFileName
    ,mstor.ConvertedFileName SourceMediaConvertedFileName
    ,cast(ms.testname as varchar) as Test    
    ,ms.categoryname as Category    
    ,ms.TopCategoryName as TopCategory
    ,ms.MidCategoryName as MidCategory
    ,coalesce(ms.SubCategoryName, 'Not assigned') as SubCategory
    ,b.Name as ParentBrand
    ,ms.brandname as Brand
    ,ct.Continent
    ,ct.Country
    ,regions.region as geographic_region
    ,ms.collectioncountry as Country_code
    ,ms.sourcemediadurationsec as Duration
    ,case ms.sourceMediaTypeID
      when 3 then 'Video'
      when 8 then 'Image'
      else 'Unknown'
    end as format
    ,case when cast(ms.sourcemediadurationsec as int) > 15 then 15 else cast(ms.sourcemediadurationsec as int) end as Cap -- To join correct audience retention value for Retain
--    ,case when ms.sourcemediadurationsec between 1 and 10 then 'Short format' when ms.sourcemediadurationsec > 30 then 'Long format' when ms.sourcemediadurationsec between 10 and 30 then 'Intermediate' end as Format_length

from StudyDatabaseReadonly.StudyDatabase.dbo.vw_PreView_MediaSegments ms
left join StudyDatabaseReadonly.StudyDatabase.dbo.TestDeviceType td ON ms.sourcemediaid = td.sourcemediaid and ms.testid = td.testid
left join StudyDatabaseReadonly.StudyDatabase.dbo.MediaStorage mstor on mstor.ID=ms.MediaStorageID
left join(select BrandID, MAX(MediaStorageID) MediaStorageID from StudyDatabaseReadonly.StudyDatabase.dbo.BrandLogo GROUP BY BrandID) bl on bl.BrandID= ms.BrandID
left join StudyDatabaseReadonly.StudyDatabase.dbo.MediaStorage mstorbl on mstorbl.ID=bl.MediaStorageID
left join (
    select 
        CTY.ID as CountryID, -- adding back since not used in media segments
        CTY.TwoLetterCode,
        CTY.CountryNumber,
        CTY.Name as Country,
        CNT.Name as Continent
    from StudyDatabaseReadonly.StudyDatabase.dbo.Country CTY
    join StudyDatabaseReadonly.StudyDatabase.dbo.Continent CNT
    on CTY.ContinentID = CNT.ID
) as ct
on ms.collectioncountry = ct.TwoLetterCode
left join (
    select * from "@data_lake_raw"."eye_square".categorisations."geographic_regions.parquet"
    where region_source = 'UN geoscheme'
) regions
on ct.CountryNumber = regions.CountryNumber
left join virtual.data.preview.main."preview_brand_parentbrand" pb
on ms.BrandID = pb.BrandID
left join StudyDatabaseReadonly.StudyDatabase.dbo.Brand b
on pb.ParentBrandID = b.ID
left join StudyDatabaseReadonly.StudyDatabase.dbo.BrandLogo pbl on pbl.BrandID = pb.ParentBrandID
left join StudyDatabaseReadonly.StudyDatabase.dbo.MediaStorage mstorpbl on mstorpbl.ID=pbl.MediaStorageID
where (pb.ParentBrandRank = 1 or pb.parentbrandRank is null)
) x