
select s.id as StudyID,case when public_studies.studyid  is null then 'Private' else 'Public' end as DataUse,
case when count(distinct ste.sourcemediaid) = count(distinct pws.mediaid) then 'Published' else 'InProgress' end as PublishState
 from StudyDatabaseReadonly.StudyDatabase.dbo.Study s
join StudyDatabaseReadonly.StudyDatabase.dbo.StudyTargetElement ste on ste.studyid = s.id 
left join StudyDatabaseReadonly.StudyDatabase.dbo.PreViewScore pws on pws.Studyid = s.id
left join (select distinct studyid from StudyDatabaseReadonly.StudyDatabase.dbo.Study_account sa where sa.accountid = 1771) public_studies on public_studies.studyid = s.id
group by s.id,public_studies.studyid order by s.id desc