{"sql": "negativity_avg.sql", "sqlContext": "virtual", "depends": ["@aws_glue.@fcp_db.traffic_lights_cumulative_mean_negative_media_duration_derived_classifiers_creative", "virtual.data.preview.main.preview_metadata"], "reflections": [{"name": "Raw Reflection", "type": "RAW", "sortFields": ["sourcemediaid"], "displayFields": ["sourcemediaid", "testid", "segment_key", "duration", "Negativity_avg", "Negativity_avg_decile", "Negativity_avg_percentile", "Negativity_avg_benchmark_median", "Negativity_avg_benchmark_top20", "Negativity_avg_index"]}]}