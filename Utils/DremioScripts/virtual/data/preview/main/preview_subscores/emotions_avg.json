{"sql": "emotions_avg.sql", "sqlContext": "@<EMAIL>", "depends": ["@aws_glue.@fcp_db.traffic_lights_mean_media_duration_emotions_classifiers_creative", "virtual.data.preview.main.preview_metadata"], "reflections": [{"name": "Raw Reflection", "type": "RAW", "sortFields": ["sourcemediaid"], "displayFields": ["sourcemediaid", "testid", "segment_key", "duration", "Happiness_avg", "Surprise_avg", "Confusion_avg", "Contempt_avg", "Disgust_avg", "Happiness_avg_decile", "Surprise_avg_decile", "Confusion_avg_decile", "Contempt_avg_decile", "Disgust_avg_decile", "Happiness_avg_percentile", "Surprise_avg_percentile", "Confusion_avg_percentile", "Contempt_avg_percentile", "Disgust_avg_percentile", "Happiness_avg_benchmark_median", "Surprise_avg_benchmark_median", "Confusion_avg_benchmark_median", "Contempt_avg_benchmark_median", "Disgust_avg_benchmark_median", "Happiness_avg_benchmark_top20", "Surprise_avg_benchmark_top20", "Confusion_avg_benchmark_top20", "Contempt_avg_benchmark_top20", "Disgust_avg_benchmark_top20", "Happiness_avg_index", "Surprise_avg_index", "Confusion_avg_index", "Contempt_avg_index", "Disgust_avg_index", "sm_group"], "partitionFields": [{"name": "sm_group", "transform": {"type": "IDENTITY"}}]}]}