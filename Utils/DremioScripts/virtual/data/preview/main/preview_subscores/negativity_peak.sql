select cast(us.sourcemediaid as int) sourcemediaid
    ,cast(us.testid as int) testid
    ,segment_key
    ,cast(relativestarttime as int) as duration
    ,cast(max_negative as decimal(10,3)) as Negativity_peak
    ,cast(max_negative_decile as int) as Negativity_peak_decile
    ,cast(max_negative_percentile as int) as Negativity_peak_percentile
    ,cast(max_negative_median as decimal(10,3)) as Negativity_peak_benchmark_median
    ,cast(max_negative_top_20 as decimal(10,3)) as Negativity_peak_benchmark_top20
    ,cast(max_negative_index as int) as Negativity_peak_index
from virtual.data.preview.main.preview_metadata us
join "@aws_glue"."@fcp_db".traffic_lights_cumulative_max_negative_media_duration_derived_classifiers_creative as negativity_peak
    on negativity_peak.sourcemediaid = us.sourcemediaid
    and negativity_peak.testid = us.testid
    and negativity_peak.relativestarttime = us.duration
where algorithmid='nel-v4.4.1' and (insertionmode = 'VTP' or insertionmode is NULL)