{"sql": "benchmarked_confirmit.sql", "sqlContext": "virtual.data.preview.main.preview_subscores", "depends": ["@aws_glue.@fcp_db.benchmarked_confirmit_media_survey_creative"], "reflections": [{"name": "Raw Reflection", "type": "RAW", "displayFields": ["algorithmid", "sm_group", "sourcemediaid", "testid", "taskid", "region", "environment_category", "ad_format", "device", "is_forced_exposure", "is_skippable", "format", "real_sample_size", "norm_create_date", "fallback", "norm_segment_key", "custom_norm_id", "views", "segment_key", "br_recognised", "br_recognised_decile", "br_recognised_median", "ad_liking", "ad_liking_decile", "ad_liking_median", "ar_recognised", "ar_recognised_decile", "ar_recognised_median", "brand_trust", "brand_trust_decile", "brand_trust_median", "percentage_with_persuasion", "percentage_with_persuasion_decile", "percentage_with_persuasion_median"], "partitionFields": [{"name": "sm_group", "transform": {"type": "TRUNCATE", "truncateTransform": {"truncateLength": 1}}}]}]}