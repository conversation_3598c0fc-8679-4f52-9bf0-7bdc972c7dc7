/* 
This vds is used for perfromance improvements of our app vds, cos this vds has its own reflection that is incremental
see https://docs.dremio.com/current/acceleration/manual-reflections/refreshing-reflections/#incremental-refreshes-when-changes-to-an-anchor-table-include-non-append-operations
*/
select 
-- keep base table partition column for raw reflection partitioning
algorithmid, sm_group,
-- other columns main columns
sourcemediaid, testid, taskid, "second",
-- norm related metadata columns
region, environment_category, ad_format, device, is_forced_exposure, is_skippable, format, norm_duration, real_sample_size, norm_create_date,
fallback, norm_segment_key, custom_norm_id,
-- scores
views, segment_key, 
-- keep this single attentive column, cos we accidentally used it in QS formula instead of eye_on and have to keep it consistent atm.
attentive_viewthrough_avg_decile,
viewthrough_avg, viewthrough_avg_decile, viewthrough_avg_median,
eyeson_seconds_avg, eyeson_seconds_avg_decile, eyeson_seconds_avg_median,
eyeson_viewthrough_avg, eyeson_viewthrough_avg_decile, eyeson_viewthrough_avg_median,
visible_seconds_avg, visible_seconds_avg_decile, visible_seconds_avg_median

from "@aws_glue"."@fcp_db"."benchmarked_mean_counts_media_duration_classifiers_creative" fcp
where algorithmid = 'algorithm-v6.0.0-nelSDK' and is_max_second = 1 
-- include only scores for min existing norms and custom norms
and (is_min_fallback = 1 or custom_norm_id is not null)