/* 
This vds is used for perfromance improvements of our app vds, cos this vds has its own reflection that is incremental
see https://docs.dremio.com/current/acceleration/manual-reflections/refreshing-reflections/#incremental-refreshes-when-changes-to-an-anchor-table-include-non-append-operations
*/
select 
-- keep base table partition column for raw reflection partitioning
algorithmid, sm_group,
-- other columns main columns
sourcemediaid, testid, taskid, "second",
-- norm related metadata columns
region, environment_category, ad_format, device, is_forced_exposure, is_skippable, format, norm_duration, real_sample_size, norm_create_date,
fallback, norm_segment_key, custom_norm_id,
-- scores
views, segment_key, 
-- keep this single attentive column, cos we accidentally used it in QS formula instead of eye_on and have to keep it consistent atm.
visible_viewer_share, visible_viewer_share_mean,
eyeson_viewer_share, eyeson_viewer_share_mean,
neutral_eyeson_viewer_share, neutral_eyeson_viewer_share_mean,
inattentive_visible_viewer_share, inattentive_visible_viewer_share_mean,
reactions_viewer_share, reactions_viewer_share_mean,
negativity_viewer_share, negativity_viewer_share_mean,
happiness_viewer_share, happiness_viewer_share_mean,
confusion_viewer_share, confusion_viewer_share_mean,
contempt_viewer_share, contempt_viewer_share_mean,
disgust_viewer_share, disgust_viewer_share_mean,
surprise_viewer_share, surprise_viewer_share_mean

from "@aws_glue"."@fcp_db"."benchmarked_mean_media_time_classifiers_creative" fcp
where algorithmid = 'algorithm-v6.0.0-nelSDK'
-- include only scores for min existing norms and custom norms
and (is_min_fallback = 1 or custom_norm_id is not null)