select us.sourcemediaid
    ,cast(us.testid as int) testid
    ,segment_key
    ,cast(relativestarttime as int) as duration
    ,cast(n_sessions as int) as views
    ,cast(norm_sample_size as int) as benchmark_sample_size
    ,cast(qualityscore as int) as qualityscore
    ,cast(viewing_time as decimal(10,3)) as capture
    ,cast(viewing_time_decile as int) capture_decile
    ,cast(viewing_time_percentile as decimal(10,3)) as capture_percentile
    ,cast(viewing_time_median as decimal(10,3)) as capture_benchmark_median
    ,cast(viewing_time_top_20 as decimal(10,3)) as capture_benchmark_top20
    ,cast(viewing_time_index as int) as capture_index
    ,cast(interestscore as decimal(10,3)) as encode
    ,cast(interestscore_decile as int) encode_decile
    ,cast(interestscore_percentile as decimal(10,3)) as encode_percentile
    ,cast(interestscore_median as decimal(10,3)) as encode_benchmark_median
    ,cast(interestscore_top_20 as decimal(10,3)) as encode_benchmark_top20
    ,cast(interestscore_index as int) as encode_index
    --
    ,cast(engaged_decile as int) as Engaged_attention_decile
    ,cast(engaged_percentile as decimal(10,3)) as Engaged_attention_percentile
    ,cast(engaged_index as int) as Engaged_attention_index
    ,cast(engaged as decimal(10,3)) as Engaged_attention_avg
    ,cast(engaged_median as decimal(10,3)) as Engaged_attention_benchmark_median
    ,cast(engaged_top_20 as decimal(10,3)) as Engaged_attention_benchmark_top20
    --
    ,cast(neutral_decile as int) as Neutral_attention_decile
    ,cast(neutral_percentile as decimal(10,3)) as Neutral_attention_percentile
    ,cast(neutral_index as int) as Neutral_attention_index
    ,cast(neutral as decimal(10,3)) as Neutral_attention_avg
    ,cast(neutral_median as decimal(10,3)) as Neutral_attention_benchmark_median
    ,cast(neutral_top_20 as decimal(10,3)) as Neutral_attention_benchmark_top20
    --
    ,cast(distracted_decile as int) as Distraction_decile
    ,cast(distracted_percentile as decimal(10,3)) as Distraction_percentile
    ,cast(distracted_index as int) as Distraction_index
    ,cast(distracted as decimal(10,3)) as Distraction_avg
    ,cast(distracted_median as decimal(10,3)) as Distraction_benchmark_median
    ,cast(distracted_top_20 as decimal(10,3)) as Distraction_benchmark_top20
    from virtual.data.preview.main.preview_metadata us
inner join "@aws_glue"."@fcp_db".quality_score_media_duration_benchmarking_combined_creative qs
on qs.sourcemediaid = us.sourcemediaid
and qs.relativestarttime = us.duration
and qs.testid = us.testid
where insertionmode = 'VTP' and unskippable_seconds = 0
and algorithmid='nel-v4.4.1'