{"sql": "benchmarked_mean_time.sql", "sqlContext": "virtual.data.preview.main.preview_subscores", "depends": ["@aws_glue.@fcp_db.benchmarked_mean_media_time_classifiers_creative"], "reflections": [{"name": "Raw Reflection", "type": "RAW", "displayFields": ["algorithmid", "sm_group", "sourcemediaid", "testid", "taskid", "second", "region", "environment_category", "ad_format", "device", "is_forced_exposure", "is_skippable", "format", "norm_duration", "real_sample_size", "norm_create_date", "fallback", "norm_segment_key", "custom_norm_id", "views", "segment_key", "visible_viewer_share", "visible_viewer_share_mean", "eyeson_viewer_share", "eyeson_viewer_share_mean", "neutral_eyeson_viewer_share", "neutral_eyeson_viewer_share_mean", "inattentive_visible_viewer_share", "inattentive_visible_viewer_share_mean", "reactions_viewer_share", "reactions_viewer_share_mean", "negativity_viewer_share", "negativity_viewer_share_mean", "happiness_viewer_share", "happiness_viewer_share_mean", "confusion_viewer_share", "confusion_viewer_share_mean", "contempt_viewer_share", "contempt_viewer_share_mean", "disgust_viewer_share", "disgust_viewer_share_mean", "surprise_viewer_share", "surprise_viewer_share_mean"], "partitionFields": [{"name": "sm_group", "transform": {"type": "TRUNCATE", "truncateTransform": {"truncateLength": 1}}}]}]}