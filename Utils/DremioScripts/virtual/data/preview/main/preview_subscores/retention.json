{"sql": "retention.sql", "sqlContext": "@<EMAIL>", "depends": ["@aws_glue.@fcp_db.quality_score_media_duration_benchmarking_combined_creative", "virtual.data.preview.main.preview_metadata"], "reflections": [{"name": "Raw Reflection", "type": "RAW", "sortFields": ["sourcemediaid"], "displayFields": ["sourcemediaid", "testid", "segment_key", "cap", "retain", "retain_decile", "retain_percentile", "retain_benchmark_median", "retain_benchmark_top20", "retain_index"]}]}