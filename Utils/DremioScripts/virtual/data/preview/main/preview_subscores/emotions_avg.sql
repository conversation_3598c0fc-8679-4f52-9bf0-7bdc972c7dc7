
select sm_group
    ,cast(us.sourcemediaid as int) sourcemediaid
    ,cast(us.testid as int) testid
    ,segment_key
    ,cast(us.duration as int) as duration
    ,cast(mean_happy_intensity as decimal(10,3)) as Happiness_avg
    ,cast(mean_surprise_intensity as decimal(10,3)) as Surprise_avg
    ,cast(mean_confused_intensity as decimal(10,3)) as Confusion_avg
    ,cast(mean_contempt_intensity as decimal(10,3)) as Contempt_avg
    ,cast(mean_disgust_intensity as decimal(10,3)) as Disgust_avg
    --​
    ,cast(mean_happy_intensity_decile as int) Happiness_avg_decile
    ,cast(mean_surprise_intensity_decile as int) as Surprise_avg_decile
    ,cast(mean_confused_intensity_decile as int) as Confusion_avg_decile
    ,cast(mean_contempt_intensity_decile as int) as Contempt_avg_decile
    ,cast(mean_disgust_intensity_decile as int) as Disgust_avg_decile
    --
    ,cast(mean_happy_intensity_percentile as int) Happiness_avg_percentile
    ,cast(mean_surprise_intensity_percentile as int) as Surprise_avg_percentile
    ,cast(mean_confused_intensity_percentile as int) as Confusion_avg_percentile
    ,cast(mean_contempt_intensity_percentile as int) as Contempt_avg_percentile
    ,cast(mean_disgust_intensity_percentile as int) as Disgust_avg_percentile
    --
    ,cast(mean_happy_intensity_median as decimal(10,3)) as Happiness_avg_benchmark_median
    ,cast(mean_surprise_intensity_median as decimal(10,3)) as Surprise_avg_benchmark_median
    ,cast(mean_confused_intensity_median as decimal(10,3)) as Confusion_avg_benchmark_median
    ,cast(mean_contempt_intensity_median as decimal(10,3)) as Contempt_avg_benchmark_median
    ,cast(mean_disgust_intensity_median as decimal(10,3)) as Disgust_avg_benchmark_median
    --
    ,cast(mean_happy_intensity_top_20 as decimal(10,3)) as Happiness_avg_benchmark_top20
    ,cast(mean_surprise_intensity_top_20 as decimal(10,3)) as Surprise_avg_benchmark_top20
    ,cast(mean_confused_intensity_top_20 as decimal(10,3)) as Confusion_avg_benchmark_top20
    ,cast(mean_contempt_intensity_top_20 as decimal(10,3)) as Contempt_avg_benchmark_top20
    ,cast(mean_disgust_intensity_top_20 as decimal(10,3)) as Disgust_avg_benchmark_top20
    --
    ,cast(mean_happy_intensity_index as int) as Happiness_avg_index
    ,cast(mean_surprise_intensity_index as int) as Surprise_avg_index
    ,cast(mean_confused_intensity_index as int) as Confusion_avg_index
    ,cast(mean_contempt_intensity_index as int) as Contempt_avg_index
    ,cast(mean_disgust_intensity_index as int) as Disgust_avg_index
from virtual.data.preview.main.preview_metadata us
join "@aws_glue"."@fcp_db".traffic_lights_mean_media_duration_emotions_classifiers_creative emotions_avg
on emotions_avg.sourcemediaid = us.sourcemediaid
    and emotions_avg.testid = us.testid
    and emotions_avg.relativestarttime = us.duration
where algorithmid='nel-v4.4.1' and (insertionmode = 'VTP' or insertionmode is NULL)
--and segment_key = 'all'
