/* 
This vds is used for perfromance improvements of our app vds, cos this vds has its own reflection that is incremental
see https://docs.dremio.com/current/acceleration/manual-reflections/refreshing-reflections/#incremental-refreshes-when-changes-to-an-anchor-table-include-non-append-operations
*/
select 
-- keep base table partition column for raw reflection partitioning
algorithmid, sm_group,
-- other columns main columns
sourcemediaid, testid, taskid, "second",
-- norm related metadata columns
region, environment_category, ad_format, device, is_forced_exposure, is_skippable, format, norm_duration, real_sample_size, norm_create_date,
fallback, norm_segment_key, custom_norm_id,
-- scores
views, segment_key, 
eyeson_viewer_share_avg, eyeson_viewer_share_avg_decile, eyeson_viewer_share_avg_median,
eyeson_viewer_share_max, eyeson_viewer_share_max_decile, eyeson_viewer_share_max_median,
reactions_viewer_share_avg, reactions_viewer_share_avg_decile, reactions_viewer_share_avg_median,
happiness_viewer_share_avg, happiness_viewer_share_avg_decile, happiness_viewer_share_avg_median,
happiness_viewer_share_max, happiness_viewer_share_max_decile, happiness_viewer_share_max_median,
surprise_viewer_share_avg, surprise_viewer_share_avg_decile, surprise_viewer_share_avg_median,
surprise_viewer_share_max, surprise_viewer_share_max_decile, surprise_viewer_share_max_median,
confusion_viewer_share_max, confusion_viewer_share_max_decile, confusion_viewer_share_max_median,
confusion_viewer_share_avg, confusion_viewer_share_avg_decile, confusion_viewer_share_avg_median,
contempt_viewer_share_max, contempt_viewer_share_max_decile, contempt_viewer_share_max_median,
contempt_viewer_share_avg, contempt_viewer_share_avg_decile, contempt_viewer_share_avg_median,
disgust_viewer_share_max, disgust_viewer_share_max_decile, disgust_viewer_share_max_median,
disgust_viewer_share_avg, disgust_viewer_share_avg_decile, disgust_viewer_share_avg_median,
negativity_viewer_share_avg, negativity_viewer_share_avg_decile, negativity_viewer_share_avg_median,
neutral_eyeson_viewer_share_avg, neutral_eyeson_viewer_share_avg_decile, neutral_eyeson_viewer_share_avg_median,
inattentive_visible_viewer_share_avg, inattentive_visible_viewer_share_avg_decile, inattentive_visible_viewer_share_avg_median


from "@aws_glue"."@fcp_db"."benchmarked_stats_media_duration_classifiers_creative" fcp
where algorithmid = 'algorithm-v6.0.0-nelSDK' and is_max_second = 1 
-- include only scores for min existing norms and custom norms
and (is_min_fallback = 1 or custom_norm_id is not null)