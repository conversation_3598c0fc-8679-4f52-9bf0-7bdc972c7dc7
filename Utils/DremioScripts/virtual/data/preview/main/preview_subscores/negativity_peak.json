{"sql": "negativity_peak.sql", "sqlContext": "virtual", "depends": ["@aws_glue.@fcp_db.traffic_lights_cumulative_max_negative_media_duration_derived_classifiers_creative", "virtual.data.preview.main.preview_metadata"], "reflections": [{"name": "Raw Reflection", "type": "RAW", "sortFields": ["sourcemediaid"], "displayFields": ["sourcemediaid", "testid", "segment_key", "duration", "Negativity_peak", "Negativity_peak_decile", "Negativity_peak_percentile", "Negativity_peak_benchmark_median", "Negativity_peak_benchmark_top20", "Negativity_peak_index"]}]}