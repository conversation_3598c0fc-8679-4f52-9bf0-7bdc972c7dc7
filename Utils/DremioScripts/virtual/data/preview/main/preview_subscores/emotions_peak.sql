select algorithmid, sm_group
        ,cast(us.sourcemediaid as int) sourcemediaid
        ,cast(us.testid as int) testid
        ,segment_key
        ,us.duration duration
        ,cast(max_happy_intensity as decimal(10,3)) as Happiness_peak
        ,cast(max_surprise_intensity as decimal(10,3)) as Surprise_peak
        ,cast(max_confused_intensity as decimal(10,3)) as Confusion_peak
        ,cast(max_contempt_intensity as decimal(10,3)) as Contempt_peak
        ,cast(max_disgust_intensity as decimal(10,3)) as Disgust_peak
        --
        ,cast(max_happy_intensity_decile as int) Happiness_peak_decile
        ,cast(max_surprise_intensity_decile as int) as Surprise_peak_decile
        ,cast(max_confused_intensity_decile as int) as Confusion_peak_decile
        ,cast(max_contempt_intensity_decile as int) as Contempt_peak_decile
        ,cast(max_disgust_intensity_decile as int) as Disgust_peak_decile
        --
        ,cast(max_happy_intensity_percentile as int) Happiness_peak_percentile
        ,cast(max_surprise_intensity_percentile as int) as Surprise_peak_percentile
        ,cast(max_confused_intensity_percentile as int) as Confusion_peak_percentile
        ,cast(max_contempt_intensity_percentile as int) as Contempt_peak_percentile
        ,cast(max_disgust_intensity_percentile as int) as Disgust_peak_percentile
        --
        ,cast(max_happy_intensity_median as decimal(10,3)) as Happiness_peak_benchmark_median
        ,cast(max_surprise_intensity_median as decimal(10,3)) as Surprise_peak_benchmark_median
        ,cast(max_confused_intensity_median as decimal(10,3)) as Confusion_peak_benchmark_median
        ,cast(max_contempt_intensity_median as decimal(10,3)) as Contempt_peak_benchmark_median
        ,cast(max_disgust_intensity_median as decimal(10,3)) as Disgust_peak_benchmark_median
        --
        ,cast(max_happy_intensity_top_20 as decimal(10,3)) as Happiness_peak_benchmark_top20
        ,cast(max_surprise_intensity_top_20 as decimal(10,3)) as Surprise_peak_benchmark_top20
        ,cast(max_confused_intensity_top_20 as decimal(10,3)) as Confusion_peak_benchmark_top20
        ,cast(max_contempt_intensity_top_20 as decimal(10,3)) as Contempt_peak_benchmark_top20
        ,cast(max_disgust_intensity_top_20 as decimal(10,3)) as Disgust_peak_benchmark_top20
        --
        ,cast(max_happy_intensity_index as int) as Happiness_peak_index
        ,cast(max_surprise_intensity_index as int) as Surprise_peak_index
        ,cast(max_confused_intensity_index as int) as Confusion_peak_index
        ,cast(max_contempt_intensity_index as int) as Contempt_peak_index
        ,cast(max_disgust_intensity_index as int) as Disgust_peak_index
    from virtual.data.preview.main.preview_metadata us
    join "@aws_glue"."@fcp_db".traffic_lights_max_media_duration_emotions_classifiers_creative emotions_peak
    on emotions_peak.sourcemediaid = us.sourcemediaid
        and emotions_peak.testid = us.testid
        and emotions_peak.relativestarttime = us.duration
    where algorithmid='nel-v4.4.1' and (insertionmode = 'VTP' or insertionmode is NULL)