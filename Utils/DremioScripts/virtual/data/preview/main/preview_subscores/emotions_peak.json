{"sql": "emotions_peak.sql", "sqlContext": "virtual.data.preview.main.preview_subscores", "depends": ["@aws_glue.@fcp_db.traffic_lights_max_media_duration_emotions_classifiers_creative", "virtual.data.preview.main.preview_metadata"], "reflections": [{"name": "Raw Reflection", "type": "RAW", "sortFields": ["sourcemediaid"], "displayFields": ["algorithmid", "sm_group", "sourcemediaid", "testid", "segment_key", "duration", "Happiness_peak", "Surprise_peak", "Confusion_peak", "Contempt_peak", "Disgust_peak", "Happiness_peak_decile", "Surprise_peak_decile", "Confusion_peak_decile", "Contempt_peak_decile", "Disgust_peak_decile", "Happiness_peak_percentile", "Surprise_peak_percentile", "Confusion_peak_percentile", "Contempt_peak_percentile", "Disgust_peak_percentile", "Happiness_peak_benchmark_median", "Surprise_peak_benchmark_median", "Confusion_peak_benchmark_median", "Contempt_peak_benchmark_median", "Disgust_peak_benchmark_median", "Happiness_peak_benchmark_top20", "Surprise_peak_benchmark_top20", "Confusion_peak_benchmark_top20", "Contempt_peak_benchmark_top20", "Disgust_peak_benchmark_top20", "Happiness_peak_index", "Surprise_peak_index", "Confusion_peak_index", "Contempt_peak_index", "Disgust_peak_index"], "partitionFields": ["algorithmid", "sm_group"]}]}