/* 
This vds is used for perfromance improvements of our app vds, cos this vds has its own reflection that is incremental
see https://docs.dremio.com/current/acceleration/manual-reflections/refreshing-reflections/#incremental-refreshes-when-changes-to-an-anchor-table-include-non-append-operations
*/
select 
-- keep base table partition column for raw reflection partitioning
algorithmid, sm_group,
-- other columns main columns
sourcemediaid, testid, taskid,
-- norm related metadata columns
region, environment_category, ad_format, device, is_forced_exposure, is_skippable, format, real_sample_size, norm_create_date,
fallback, norm_segment_key, custom_norm_id,
-- scores
views, segment_key, 
br_recognised, br_recognised_decile, br_recognised_median,
ad_liking, ad_liking_decile, ad_liking_median,
ar_recognised, ar_recognised_decile, ar_recognised_median,
brand_trust, brand_trust_decile, brand_trust_median,
percentage_with_persuasion, percentage_with_persuasion_decile, percentage_with_persuasion_median

from "@aws_glue"."@fcp_db"."benchmarked_confirmit_media_survey_creative" fcp
where algorithmid = 'algorithm-v6.0.0-nelSDK'
-- include only scores for min existing norms and custom norms
and (is_min_fallback = 1 or custom_norm_id is not null)