select cast(us.sourcemediaid as int) sourcemediaid
    ,cast(us.testid as int) testid
    ,segment_key
    ,cast(relativestarttime as int) as duration
    ,cast(mean_negative as decimal(10,3)) as Negativity_avg
    ,cast(mean_negative_decile as int) as Negativity_avg_decile
    ,cast(mean_negative_percentile as int) as Negativity_avg_percentile
    ,cast(mean_negative_median as decimal(10,3)) as Negativity_avg_benchmark_median
    ,cast(mean_negative_top_20 as decimal(10,3)) as Negativity_avg_benchmark_top20
    ,cast(mean_negative_index as int) as Negativity_avg_index
from virtual.data.preview.main.preview_metadata us
join "@aws_glue"."@fcp_db".traffic_lights_cumulative_mean_negative_media_duration_derived_classifiers_creative negativity_avg
    on negativity_avg.sourcemediaid = us.sourcemediaid
    and negativity_avg.testid = us.testid
    and negativity_avg.relativestarttime = us.duration
where algorithmid='nel-v4.4.1' and (insertionmode = 'VTP' or insertionmode is NULL)