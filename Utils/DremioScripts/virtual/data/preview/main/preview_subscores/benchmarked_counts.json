{"sql": "benchmarked_counts.sql", "sqlContext": "virtual.data.preview.main.preview_subscores", "depends": ["@aws_glue.@fcp_db.benchmarked_mean_counts_media_duration_classifiers_creative"], "reflections": [{"name": "Raw Reflection", "type": "RAW", "displayFields": ["algorithmid", "sm_group", "sourcemediaid", "testid", "taskid", "second", "region", "environment_category", "ad_format", "device", "is_forced_exposure", "is_skippable", "format", "norm_duration", "real_sample_size", "norm_create_date", "fallback", "norm_segment_key", "views", "segment_key", "eyeson_seconds_avg", "eyeson_seconds_avg_decile", "eyeson_seconds_avg_median", "eyeson_viewthrough_avg", "eyeson_viewthrough_avg_decile", "eyeson_viewthrough_avg_median", "attentive_viewthrough_avg_decile", "visible_seconds_avg", "visible_seconds_avg_decile", "visible_seconds_avg_median", "custom_norm_id", "viewthrough_avg", "viewthrough_avg_decile", "viewthrough_avg_median"], "partitionFields": [{"name": "sm_group", "transform": {"type": "TRUNCATE", "truncateTransform": {"truncateLength": 1}}}]}]}