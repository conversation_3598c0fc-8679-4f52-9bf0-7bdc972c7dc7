select us.sourcemediaid
    ,cast(us.testid as int) testid
    ,segment_key
    ,cast(relativestarttime as int) as cap
    ,cast(vtr as decimal(10,3)) as retain
    ,cast(vtr_decile as int) retain_decile
    ,cast(vtr_percentile as decimal(10,3)) as retain_percentile
    ,cast(vtr_median as decimal(10,3)) as retain_benchmark_median
    ,cast(vtr_top_20 as decimal(10,3)) as retain_benchmark_top20
    ,cast(vtr_index as int) as retain_index
from virtual.data.preview.main.preview_metadata us
join "@aws_glue"."@fcp_db".quality_score_media_duration_benchmarking_combined_creative rt
on rt.sourcemediaid = us.sourcemediaid
and rt.relativestarttime = us.cap
and rt.testid = us.testid
where insertionmode = 'VTP' and unskippable_seconds = 0
and algorithmid='nel-v4.4.1'