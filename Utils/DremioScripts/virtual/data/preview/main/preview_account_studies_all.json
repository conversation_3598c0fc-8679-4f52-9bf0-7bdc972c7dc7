{"sql": "preview_account_studies_all.sql", "sqlContext": "virtual.data.preview.main", "depends": ["StudyDatabaseReadonly.StudyDatabase.dbo.Account", "StudyDatabaseReadonly.StudyDatabase.dbo.TestElementSequence", "StudyDatabaseReadonly.StudyDatabase.dbo.PrecalculatedStudyMetric", "StudyDatabaseReadonly.StudyDatabase.dbo.Project", "StudyDatabaseReadonly.StudyDatabase.dbo.Study", "virtual.data.preview.main.preview_study_additional_states", "StudyDatabaseReadonly.StudyDatabase.dbo.Study_Test", "StudyDatabaseReadonly.StudyDatabase.dbo.StudyType", "StudyDatabaseReadonly.StudyDatabase.dbo.DataCollectionStructure", "StudyDatabaseReadonly.StudyDatabase.dbo.StudyTargetElement", "StudyDatabaseReadonly.StudyDatabase.dbo.StudyState", "StudyDatabaseReadonly.StudyDatabase.dbo.Study_Account"], "reflections": [{"name": "Raw Reflection", "type": "RAW", "sortFields": ["AccountID", "SourceMediaID"], "displayFields": ["ProductId", "AccountID", "SourceMediaID", "TestID", "StudytypeID", "StudyID", "Account", "StudyType", "Study", "StudyState", "StudyDate", "recent_study", "DataUse", "PublishState", "StudyViewings"]}]}