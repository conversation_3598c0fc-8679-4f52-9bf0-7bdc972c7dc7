select
    distinct 
    us.SourceMediaID
    ,us.TestID
    ,COALESCE(qs.Segment_Key, 'all') SegmentKey
    ,cast(COALESCE(vw.n_sessions, Views) as int) Views
    ,Benchmark_sample_size
    -- Main KPIs
    ,QualityScore
    ,cast((0.2*(case when Capture_percentile < 1 then 1 else Capture_percentile end) + 0.5*(case when Retain_percentile < 1 then 1 else Retain_percentile end) + 0.3*(case when Encode_percentile < 1 then 1 else Encode_percentile end))/100 as decimal(10,3)) as CreativeEfficiency
    -- 1-to-10 scores
    ,Capture_decile
    ,Retain_decile
    ,Encode_decile
    -- traffic lights
    ,case when QualityScore is Null then Null when QualityScore > 6 then 'High performer' when QualityScore < 4 then 'Low performer' else 'Mid performer' end as QualityScore_traffic_light
    ,case when QualityScore is Null then Null when QualityScore > 6 then 1 when QualityScore < 4 then -1 else 0 end as QualityScore_index
    -- traffic light scores
    ,Capture
    ,Retain
    ,Encode
    -- traffic lights percentiles
    ,Capture_percentile
    ,Retain_percentile
    ,Encode_percentile
    -- traffic lights norm medians
    ,Capture_benchmark_median
    ,Retain_benchmark_median
    ,Encode_benchmark_median
    -- traffic lights norm top20%
    ,Capture_benchmark_top20
    ,Retain_benchmark_top20
    ,Encode_benchmark_top20
    -- traffic lights indexes
    ,Capture_index
    ,Retain_index
    ,Encode_index
    ---- ATTENTION BREAKDOWN ----
    ,qs.Distraction_avg
    ,qs.Neutral_attention_avg
    ,qs.Engaged_attention_avg
    -- deciles
    ,qs.Distraction_decile
    ,qs.Neutral_attention_decile
    ,qs.Engaged_attention_decile
    -- percentiles
    ,qs.Distraction_percentile
    ,qs.Neutral_attention_percentile
    ,qs.Engaged_attention_percentile
    -- median
    ,qs.Distraction_benchmark_median
    ,qs.Neutral_attention_benchmark_median
    ,qs.Engaged_attention_benchmark_median
    -- top20
    ,qs.Distraction_benchmark_top20
    ,qs.Neutral_attention_benchmark_top20
    ,qs.Engaged_attention_benchmark_top20
    -- indexes
    ,qs.Distraction_index
    ,qs.Neutral_attention_index
    ,qs.Engaged_attention_index
    -- EMOTION AVERAGES --
    ,emotions_avg.Happiness_avg
    ,emotions_avg.Surprise_avg
    ,negativity_avg.Negativity_avg
    ,emotions_avg.Confusion_avg
    ,emotions_avg.Contempt_avg
    ,emotions_avg.Disgust_avg
    -- deciles
    ,emotions_avg.Happiness_avg_decile
    ,emotions_avg.Surprise_avg_decile
    ,negativity_avg.Negativity_avg_decile
    ,emotions_avg.Confusion_avg_decile
    ,emotions_avg.Contempt_avg_decile
    ,emotions_avg.Disgust_avg_decile
    -- percentiles
    ,emotions_avg.Happiness_avg_percentile
    ,emotions_avg.Surprise_avg_percentile
    ,negativity_avg.Negativity_avg_percentile
    ,emotions_avg.Confusion_avg_percentile
    ,emotions_avg.Contempt_avg_percentile
    ,emotions_avg.Disgust_avg_percentile
    -- norms median
    ,emotions_avg.Happiness_avg_benchmark_median
    ,emotions_avg.Surprise_avg_benchmark_median
    ,negativity_avg.Negativity_avg_benchmark_median
    ,emotions_avg.Confusion_avg_benchmark_median
    ,emotions_avg.Contempt_avg_benchmark_median
    ,emotions_avg.Disgust_avg_benchmark_median
    -- norms top 20
    ,emotions_avg.Happiness_avg_benchmark_top20
    ,emotions_avg.Surprise_avg_benchmark_top20
    ,negativity_avg.Negativity_avg_benchmark_top20
    ,emotions_avg.Confusion_avg_benchmark_top20
    ,emotions_avg.Contempt_avg_benchmark_top20
    ,emotions_avg.Disgust_avg_benchmark_top20
    -- indexes
    ,emotions_avg.Happiness_avg_index
    ,emotions_avg.Surprise_avg_index
    ,negativity_avg.Negativity_avg_index
    ,emotions_avg.Confusion_avg_index
    ,emotions_avg.Contempt_avg_index
    ,emotions_avg.Disgust_avg_index
    -- EMOTION PEAKS --
    ,emotions_peak.Happiness_peak
    ,emotions_peak.Surprise_peak
    ,negativity_peak.Negativity_peak
    ,emotions_peak.Confusion_peak
    ,emotions_peak.Contempt_peak
    ,emotions_peak.Disgust_peak
    -- deciles
    ,emotions_peak.Happiness_peak_decile
    ,emotions_peak.Surprise_peak_decile
    ,negativity_peak.Negativity_peak_decile
    ,emotions_peak.Confusion_peak_decile
    ,emotions_peak.Contempt_peak_decile
    ,emotions_peak.Disgust_peak_decile
    -- percentiles
    ,emotions_peak.Happiness_peak_percentile
    ,emotions_peak.Surprise_peak_percentile
    ,negativity_peak.Negativity_peak_percentile
    ,emotions_peak.Confusion_peak_percentile
    ,emotions_peak.Contempt_peak_percentile
    ,emotions_peak.Disgust_peak_percentile
    -- norms median
    ,emotions_peak.Happiness_peak_benchmark_median
    ,emotions_peak.Surprise_peak_benchmark_median
    ,negativity_peak.Negativity_peak_benchmark_median
    ,emotions_peak.Confusion_peak_benchmark_median
    ,emotions_peak.Contempt_peak_benchmark_median
    ,emotions_peak.Disgust_peak_benchmark_median
    -- norms top 20
    ,emotions_peak.Happiness_peak_benchmark_top20
    ,emotions_peak.Surprise_peak_benchmark_top20
    ,negativity_peak.Negativity_peak_benchmark_top20
    ,emotions_peak.Confusion_peak_benchmark_top20
    ,emotions_peak.Contempt_peak_benchmark_top20
    ,emotions_peak.Disgust_peak_benchmark_top20
    -- indexes
    ,emotions_peak.Happiness_peak_index
    ,emotions_peak.Surprise_peak_index
    ,negativity_peak.Negativity_peak_index
    ,emotions_peak.Confusion_peak_index
    ,emotions_peak.Contempt_peak_index
    ,emotions_peak.Disgust_peak_index
from virtual.data.preview.main.preview_metadata us
left join virtual.data.preview.main.preview_subscores.quality_score qs
    on qs.sourcemediaid = us.sourcemediaid
    and qs.duration = us.duration
    and qs.testid = us.testid
left join virtual.data.preview.main.preview_subscores.retention rt
    on rt.sourcemediaid = us.sourcemediaid
    and rt.cap = us.cap
    and rt.testid = us.testid
    and rt.segment_key = qs.segment_key
left join virtual.data.preview.main.preview_subscores.emotions_avg
    on emotions_avg.sourcemediaid = us.sourcemediaid
    and emotions_avg.testid = us.testid
    and emotions_avg.duration = us.duration
    and emotions_avg.segment_key = qs.segment_key
left join virtual.data.preview.main.preview_subscores.negativity_avg
    on negativity_avg.sourcemediaid = us.sourcemediaid
    and negativity_avg.testid = us.testid
    and negativity_avg.duration = us.duration
    and negativity_avg.segment_key = qs.segment_key
left join virtual.data.preview.main.preview_subscores.emotions_peak
    on emotions_peak.sourcemediaid = us.sourcemediaid
    and emotions_peak.testid = us.testid
    and emotions_peak.duration = us.duration
    and emotions_peak.segment_key = qs.segment_key
left join virtual.data.preview.main.preview_subscores.negativity_peak
    on negativity_peak.sourcemediaid = us.sourcemediaid
    and negativity_peak.testid = us.testid
    and negativity_peak.duration = us.duration
    and negativity_peak.segment_key = qs.segment_key
left join "@aws_glue".@fcp_db.viewings_media_delivery_combined_creative vw
    on vw.sourcemediaid = us.sourcemediaid
    and vw.testid = us.testid
    and vw.segment_key = qs.segment_key
    and vw.algorithmid='nel-v4.4.1'     