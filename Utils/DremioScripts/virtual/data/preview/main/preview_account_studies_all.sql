select
distinct
    p.ProductId, 
    sa.AccountID
    ,ste.SourceMediaID
    ,st.TestID
    ,s.StudytypeID
    ,s.id as StudyID
    ,a.name as Account
    ,stype.name as StudyType
    ,s.name as Study
    ,sstate.name as StudyState
    ,cast(s.CreateDate as date) StudyDate
    ,ROW_NUMBER() OVER (PARTITION BY sa.AccountID, ste.SourceMediaID, st.testid ORDER BY s.id DESC) as recent_study
    ,sas.DataUse
    ,sas.PublishState
    ,psm.Viewings StudyViewings
from StudyDatabaseReadonly.StudyDatabase.dbo.study s
join StudyDatabaseReadonly.StudyDatabase.dbo.studyType stype on stype.id = s.StudyTypeID
join StudyDatabaseReadonly.StudyDatabase.dbo.studyState sstate on sstate.id = s.StudyStateID
join StudyDatabaseReadonly.StudyDatabase.dbo.Study_Account sa on sa.StudyID = s.ID
join StudyDatabaseReadonly.StudyDatabase.dbo.Account a on a.ID = sa.AccountID
join StudyDatabaseReadonly.StudyDatabase.dbo.StudyTargetElement ste on ste.StudyID = s.ID
join StudyDatabaseReadonly.StudyDatabase.dbo.Study_Test st on st.StudyID = s.ID
join StudyDatabaseReadonly.StudyDatabase.dbo.TestElementSequence tes on tes.testid = st.testid and tes.sourcemediaid = ste.sourcemediaid
join virtual.data.preview.main."preview_study_additional_states" sas on sas.studyid = s.id
join StudyDatabaseReadonly.StudyDatabase.dbo.DataCollectionStructure dcs on dcs.testid = st.testid
left join StudyDatabaseReadonly.StudyDatabase.dbo.Project p on p.alias = dcs.ExternalKey
left join StudyDatabaseReadonly.StudyDatabase.dbo.PrecalculatedStudyMetric psm -- TODO: Reassess why we need this join
on ste.SourceMediaID = psm.SourceMediaID
and s.ID = psm.StudyID and psm.MetricMeasureID = 35 and psm.SegmentId=4050 and psm.Algorithmid = @algo_id
---2024-02-27 We filter out incontext projects' infocus part by this
where coalesce(p.ProductID, 0) != 44
--where (psm.Algorithmid = @algo_id or psm.AlgorithmId is Null) -- FIX from Guilhem 2023/10/06