{"sql": "preview_account_studies_latest.sql", "sqlContext": "@a_forgacs", "depends": ["virtual.data.preview.main.preview_account_studies_all"], "reflections": [{"name": "Raw Reflection", "type": "RAW", "sortFields": ["AccountID", "SourceMediaID"], "displayFields": ["AccountID", "SourceMediaID", "TestID", "StudyTypeID", "StudyID", "Account", "StudyType", "Study", "StudyState", "StudyDate", "DataUse", "PublishState", "StudyViewings"]}]}