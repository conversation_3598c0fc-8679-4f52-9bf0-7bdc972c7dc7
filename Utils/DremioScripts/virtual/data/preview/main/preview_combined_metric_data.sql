select 
results.Missing_QualityScore,
results.Missing_Encode,
results.Missing_Negativity_peak,
results.Missing_Emotion_averages,
results.Missing_Country,
results.Missing_Brand,
results.Wrong_Duration,
results.Missing_Duration,
results.Low_Benchmark_sample_size,
results.SourceMediaID,
results.TestID,
results.<PERSON><PERSON><PERSON><PERSON>,
results.ParentBrandID,
results.BrandID,
results.ParentBrandLogoFileName,
results.BrandLogoFileName,
results.TopCategoryID,
results.MidCategoryID,
results.SubCategoryID,
results.CategoryID,
results.CountryID,
results.External_key,
results.Date_of_test,
results.SourceMedia,
results.SourceMediaThumbnailFileName,
results.Test,
results.ParentBrand,
results.Brand,
results.Category,
results.CategoryTree,
results.TopCategory,
results.MidCategory,
results.SubCategory,
results.Continent,
results.Country,
results.Country_code,
results.Duration,
results.Cap,
--results.Format_length,
results.Views,
results.Benchmark_sample_size,
results.QualityScore,
results.CreativeEfficiency,
cast(10.0/results.CreativeEfficiency as decimal(10,2)) as qCPM,
results.Capture_decile,
results.Retain_decile,
results.Encode_decile,
results.QualityScore_traffic_light,
results.QualityScore_index,
results.Capture,
results.Retain,
results.Encode,
results.Capture_percentile,
results.Retain_percentile,
results.Encode_percentile,
results.Capture_benchmark_median,
results.Retain_benchmark_median,
results.Encode_benchmark_median,
results.Capture_benchmark_top20,
results.Retain_benchmark_top20,
results.Encode_benchmark_top20,
results.Capture_index,
results.Retain_index,
results.Encode_index,
results.Distraction_avg,
results.Neutral_attention_avg,
results.Engaged_attention_avg,
results.Distraction_decile,
results.Neutral_attention_decile,
results.Engaged_attention_decile,
results.Distraction_percentile,
results.Neutral_attention_percentile,
results.Engaged_attention_percentile,
results.Distraction_benchmark_median,
results.Neutral_attention_benchmark_median,
results.Engaged_attention_benchmark_median,
results.Distraction_benchmark_top20,
results.Neutral_attention_benchmark_top20,
results.Engaged_attention_benchmark_top20,
results.Distraction_index,
results.Neutral_attention_index,
results.Engaged_attention_index,
results.Happiness_avg,
results.Surprise_avg,
results.Negativity_avg,
results.Confusion_avg,
results.Contempt_avg,
results.Disgust_avg,
results.Happiness_avg_decile,
results.Surprise_avg_decile,
results.Negativity_avg_decile,
results.Confusion_avg_decile,
results.Contempt_avg_decile,
results.Disgust_avg_decile,
results.Happiness_avg_percentile,
results.Surprise_avg_percentile,
results.Negativity_avg_percentile,
results.Confusion_avg_percentile,
results.Contempt_avg_percentile,
results.Disgust_avg_percentile,
results.Happiness_avg_benchmark_median,
results.Surprise_avg_benchmark_median,
results.Negativity_avg_benchmark_median,
results.Confusion_avg_benchmark_median,
results.Contempt_avg_benchmark_median,
results.Disgust_avg_benchmark_median,
results.Happiness_avg_benchmark_top20,
results.Surprise_avg_benchmark_top20,
results.Negativity_avg_benchmark_top20,
results.Confusion_avg_benchmark_top20,
results.Contempt_avg_benchmark_top20,
results.Disgust_avg_benchmark_top20,
results.Happiness_avg_index,
results.Surprise_avg_index,
results.Negativity_avg_index,
results.Confusion_avg_index,
results.Contempt_avg_index,
results.Disgust_avg_index,
results.Happiness_peak,
results.Surprise_peak,
results.Negativity_peak,
results.Confusion_peak,
results.Contempt_peak,
results.Disgust_peak,
results.Happiness_peak_decile,
results.Surprise_peak_decile,
results.Negativity_peak_decile,
results.Confusion_peak_decile,
results.Contempt_peak_decile,
results.Disgust_peak_decile,
results.Happiness_peak_percentile,
results.Surprise_peak_percentile,
results.Negativity_peak_percentile,
results.Confusion_peak_percentile,
results.Contempt_peak_percentile,
results.Disgust_peak_percentile,
results.Happiness_peak_benchmark_median,
results.Surprise_peak_benchmark_median,
results.Negativity_peak_benchmark_median,
results.Confusion_peak_benchmark_median,
results.Contempt_peak_benchmark_median,
results.Disgust_peak_benchmark_median,
results.Happiness_peak_benchmark_top20,
results.Surprise_peak_benchmark_top20,
results.Negativity_peak_benchmark_top20,
results.Confusion_peak_benchmark_top20,
results.Contempt_peak_benchmark_top20,
results.Disgust_peak_benchmark_top20,
results.Happiness_peak_index,
results.Surprise_peak_index,
results.Negativity_peak_index,
results.Confusion_peak_index,
results.Contempt_peak_index,
results.Disgust_peak_index,
human.HumanPresence,
gender.FemalePresence,
gender.MalePresence,
gender.Female_representation_ratio,
tos.Text_OverallPresence as OverallTextPresence, -- TODO: name change app side before removing alias
tos.Text_in_first_5sec,
tos.Text_RelativePosition as RelativeText_avg_position, -- TODO: name change app side before removing alias
results.HackyHash
from virtual.data.preview.main.preview_denormalised_table results
left join virtual.data.preview.content."preview_gender_representation" gender
on results.SourceMediaID = gender.SourceMediaID
left join virtual.data.preview.content."preview_human_presence" human
on results.SourceMediaID = human.SourceMediaID
left join virtual.data.preview.content."preview_text_on_screen" tos
on results.SourceMediaID = tos.SourceMediaID
