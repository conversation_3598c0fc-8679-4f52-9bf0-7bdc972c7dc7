WITH default_custom_norms_base as (
    select distinct smcn.source_media_id,smcn.test_id,smcn.custom_norm_id,smcn.is_default,smcn.fallback,cn.filters from 
    "fcp_postgre".public."source_media_custom_norm" smcn 
    join "fcp_postgre".public.custom_norm cn on cn.id = smcn.custom_norm_id
    join "fcp_postgre".public.feature f on f.id = smcn.feature_id
    where f.name in (
        'mean-media_time-classifiers-creative'
    )
),
fcp AS (
    select cn_fcp.is_default as custom_norm_is_default,cn_fcp.fallback custom_norm_fallback, cn_fcp.filters custom_norm_filters, fcp.* 
    from virtual.data.preview.main."preview_subscores"."benchmarked_mean_time" fcp
    join default_custom_norms_base cn_fcp on cn_fcp.source_media_id=fcp.sourcemediaid and cn_fcp.test_id=fcp.testid and fcp.custom_norm_id=cn_fcp.custom_norm_id
    where cn_fcp.is_default=1
    UNION
    select null as custom_norm_is_default,null as custom_norm_fallback, null as custom_norm_filters, fcp.* 
    from virtual.data.preview.main."preview_subscores"."benchmarked_mean_time" fcp
    where fcp.custom_norm_id is null
    and not exists (select null from default_custom_norms_base cn_fcp where cn_fcp.source_media_id=fcp.sourcemediaid and cn_fcp.test_id=fcp.testid and cn_fcp.is_default=1)
),
ic_mapping_table as (
    select DISTINCT icMapping.sourcemedia_id, icMapping.test_id, ifMapping.test_id infocus_test_id, icMapping.task_id, ifMapping.task_id infocus_task_id
    FROM virtual.data.InContextPoC.mediaMapping AS icMapping 
    JOIN virtual.data.InContextPoC.mediaMapping AS ifMapping 
        ON ifMapping.sourcemedia_id = icMapping.sourcemedia_id AND ifMapping.test_id = icMapping.infocus_test_id AND ifMapping.subjectgroup_id = icMapping.subjectgroup_id
    WHERE 1=1
        AND icMapping.is_target = TRUE AND icMapping.is_infocus = FALSE AND icMapping.test_id IS NOT NULL AND icMapping.task_id IS NOT NULL    
        AND ifMapping.is_target = TRUE AND ifMapping.is_infocus = TRUE AND ifMapping.test_id IS NOT NULL  
)
SELECT distinct
    CONCAT( CAST(icMapping.test_id AS CHAR), '/', 
            CAST(icMapping.sourcemedia_id AS CHAR), '/', 
            CAST(icMapping.task_id AS CHAR) ) AS creative_id
    -- ,icMapping.account As Account
    -- ,icMapping.account_id AS AccountID
    -- ,icMapping.project_id AS ProjectID
    -- ,icMapping.subjectgroup_id AS SubjectGroupID
    -- ,icMapping.is_forced_exposure AS IsForcedExposure
    ,icMapping.sourcemedia_id AS SourceMediaID
    ,icMapping.test_id AS TestID
    ,icMapping.task_id AS TaskID
    -- ,icMapping.infocus_test_id AS InFocusTestID
    -- ,icMapping.infocus_task_id AS InFocusTaskID
    ,tsIC.segment_key AS SegmentKey
    ,tsIC."second" "Second"
    ,tsIF."second" "IFSecond"  
    ,round(tsIC.visible_viewer_share, 4) AS PlaybackIC
    ,round(tsIC.visible_viewer_share_mean, 4) AS PlaybackICNorm
    ,round(tsIC.eyeson_viewer_share, 4) AS Attention --AttentionIC
    ,round(tsIC.eyeson_viewer_share_mean, 4) AS AttentionNorm  --AttentionICNorm  
    ,round(tsIC.neutral_eyeson_viewer_share, 4) NeutralAttention
    ,round(tsIC.neutral_eyeson_viewer_share_mean, 4) NeutralAttentionNorm          
    ,round(tsIC.inattentive_visible_viewer_share, 4) AS DistractionIC --uses eyeson_viewer_share underneath
    ,round(tsIC.inattentive_visible_viewer_share_mean, 4) AS DistractionICNorm   
    ,round(tsIC.reactions_viewer_share, 4) AllReactionsIC
    ,round(tsIC.reactions_viewer_share_mean, 4) AllReactionsICNorm    
    ,round(tsIC.negativity_viewer_share, 4) NegativityIC
    ,round(tsIC.negativity_viewer_share_mean, 4) NegativityICNorm     
    ,round(tsIC.happiness_viewer_share, 4) HappinessIC
    ,round(tsIC.happiness_viewer_share_mean, 4) HappinessICNorm     
    ,round(tsIC.confusion_viewer_share, 4) ConfusionIC
    ,round(tsIC.confusion_viewer_share_mean, 4) ConfusionICNorm    
    ,round(tsIC.contempt_viewer_share, 4) ContemptIC
    ,round(tsIC.contempt_viewer_share_mean, 4) ContemptICNorm     
    ,round(tsIC.disgust_viewer_share, 4) DisgustIC
    ,round(tsIC.disgust_viewer_share_mean, 4) DisgustICNorm      
    ,round(tsIC.surprise_viewer_share, 4) SurpriseIC
    ,round(tsIC.surprise_viewer_share_mean, 4) SurpriseICNorm  
    ,round(tsIF.visible_viewer_share, 4) AS PlaybackIF
    ,round(tsIF.visible_viewer_share_mean, 4) AS PlaybackIFNorm  
    ,round(tsIF.eyeson_viewer_share, 4) AS AttentionIF
    ,round(tsIF.eyeson_viewer_share_mean, 4) AS AttentionIFNorm   
    ,round(tsIF.neutral_eyeson_viewer_share, 4) NeutralAttentionIF
    ,round(tsIF.neutral_eyeson_viewer_share_mean, 4) NeutralAttentionIFNorm
    ,round(tsIF.inattentive_visible_viewer_share, 4) AS DistractionIF --uses eyeson_viewer_share underneath
    ,round(tsIF.inattentive_visible_viewer_share_mean, 4) AS DistractionIFNorm   
    ,round(tsIF.reactions_viewer_share, 4) AllReactions --AllReactionsIF
    ,round(tsIF.reactions_viewer_share_mean, 4) AllReactionsNorm --AllReactionsIFNorm
    ,round(tsIF.negativity_viewer_share, 4) Negativity --NegativityIF
    ,round(tsIF.negativity_viewer_share_mean, 4) NegativityNorm --NegativityIFNorm
    ,round(tsIF.happiness_viewer_share, 4) Happiness --HappinessIF
    ,round(tsIF.happiness_viewer_share_mean, 4) HappinessNorm --HappinessIFNorm
    ,round(tsIF.confusion_viewer_share, 4) Confusion --ConfusionIF
    ,round(tsIF.confusion_viewer_share_mean, 4) ConfusionNorm --ConfusionIFNorm
    ,round(tsIF.contempt_viewer_share, 4) Contempt --ContemptIF
    ,round(tsIF.contempt_viewer_share_mean, 4) ContemptNorm --ContemptIFNorm 
    ,round(tsIF.disgust_viewer_share, 4) Disgust --DisgustIF
    ,round(tsIF.disgust_viewer_share_mean, 4) DisgustNorm --DisgustIFNorm   
    ,round(tsIF.surprise_viewer_share, 4) Surprise --SurpriseIF
    ,round(tsIF.surprise_viewer_share_mean, 4) SurpriseNorm --SurpriseIFNorm      
FROM ic_mapping_table icMapping
JOIN fcp AS tsIC 
    ON icMapping.test_id = tsIC.TestID AND icMapping.sourcemedia_id = tsIC.sourcemediaid AND icMapping.task_id = tsIC.TaskID
JOIN fcp AS tsIF 
    ON icMapping.infocus_test_id = tsIF.TestID AND icMapping.sourcemedia_id = tsIF.sourcemediaid AND icMapping.infocus_task_id = tsIF.TaskID 
    AND tsIF."second" = tsIC."second" AND tsIF.segment_key = tsIC.segment_key
--ORDER BY icMapping.project_id, icMapping.subjectgroup_id, icMapping.task_id, tsIC.segment_key, tsIC."second"