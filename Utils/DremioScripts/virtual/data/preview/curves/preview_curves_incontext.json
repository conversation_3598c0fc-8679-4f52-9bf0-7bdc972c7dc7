{"sql": "preview_curves_incontext.sql", "depends": ["virtual.data.InContextPoC.mediaMapping", "virtual.data.preview.main.preview_subscores.benchmarked_mean_time", "fcp_postgre.public.custom_norm", "fcp_postgre.public.source_media_custom_norm", "fcp_postgre.public.feature"], "reflections": [{"name": "Raw Reflection", "type": "RAW", "sortFields": ["SourceMediaID", "TestID"], "displayFields": ["creative_id", "SourceMediaID", "TestID", "TaskID", "SegmentKey", "Second", "IFSecond", "PlaybackIC", "PlaybackICNorm", "Attention", "AttentionNorm", "DistractionIC", "DistractionICNorm", "AllReactionsIC", "AllReactionsICNorm", "NegativityIC", "NegativityICNorm", "HappinessIC", "HappinessICNorm", "ConfusionIC", "ConfusionICNorm", "ContemptIC", "ContemptICNorm", "DisgustIC", "DisgustICNorm", "SurpriseIC", "SurpriseICNorm", "PlaybackIF", "PlaybackIFNorm", "AttentionIF", "AttentionIFNorm", "DistractionIF", "DistractionIFNorm", "AllReactions", "AllReactionsNorm", "Negativity", "NegativityNorm", "Happiness", "HappinessNorm", "Confusion", "ConfusionNorm", "Contempt", "ContemptNorm", "<PERSON><PERSON><PERSON><PERSON>", "DisgustNorm", "Surprise", "SurpriseNorm", "NeutralAttention", "NeutralAttentionNorm", "NeutralAttentionIF", "NeutralAttentionIFNorm"]}]}