WITH default_custom_norms_base as (
    select distinct smcn.source_media_id,smcn.test_id,smcn.custom_norm_id,smcn.is_default,smcn.fallback,cn.filters from 
    "fcp_postgre".public."source_media_custom_norm" smcn 
    join "fcp_postgre".public.custom_norm cn on cn.id = smcn.custom_norm_id
    join "fcp_postgre".public.feature f on f.id = smcn.feature_id
    where f.name in (
        'mean-media_time-classifiers-creative'
    )
),
fcp AS (
    select cn_fcp.is_default as custom_norm_is_default,cn_fcp.fallback custom_norm_fallback, cn_fcp.filters custom_norm_filters, fcp.* 
    from virtual.data.preview.main."preview_subscores"."benchmarked_mean_time" fcp
    join default_custom_norms_base cn_fcp on cn_fcp.source_media_id=fcp.sourcemediaid and cn_fcp.test_id=fcp.testid and fcp.custom_norm_id=cn_fcp.custom_norm_id
    where taskid is NULL and cn_fcp.is_default=1
    UNION ALL
    select null as custom_norm_is_default,null as custom_norm_fallback, null as custom_norm_filters, fcp.* 
    from virtual.data.preview.main."preview_subscores"."benchmarked_mean_time" fcp
    where taskid is NULL and fcp.custom_norm_id is null
    and not exists (select null from default_custom_norms_base cn_fcp where cn_fcp.source_media_id=fcp.sourcemediaid and cn_fcp.test_id=fcp.testid and cn_fcp.is_default=1)
)
SELECT 
    CONCAT( CAST(fcp.testid AS CHAR), '/', 
            CAST(fcp.sourcemediaid AS CHAR)) AS creative_id
    --,adsetaccount_order.AdSetExternalKey OrderExternalKey
    --,adsetaccount_order.Name As "Order" 
    ,fcp.custom_norm_id as custom_norm_id
    --,adsetaccount_order.accountid AS AccountID
    -- ,mapping.project_id AS ProjectID
    -- ,mapping.is_enabled_for_clients AS IsEnabledForClients
    -- ,mapping.subjectgroup_id AS SubjectGroupID
    ,true AS IsForcedExposure
    --,adsetaccount_order.SourceMediaExternalKey
    ,fcp.sourcemediaid AS SourceMediaID
    ,fcp.testid AS TestID
    ,fcp.taskid AS TaskID
    -- ,mapping.infocus_test_id AS InFocusTestID
    ,fcp.segment_key AS SegmentKey
    ,fcp."second" "Second"
    ,round(fcp.visible_viewer_share, 4) AS Playback
    ,round(fcp.visible_viewer_share_mean, 4) AS PlaybackNorm
    ,round(fcp.eyeson_viewer_share, 4) AS Attention
    ,round(fcp.eyeson_viewer_share_mean, 4) AS AttentionNorm
    ,round(fcp.neutral_eyeson_viewer_share, 4) NeutralAttention
    ,round(fcp.neutral_eyeson_viewer_share_mean, 4) NeutralAttentionNorm          
    ,round(fcp.inattentive_visible_viewer_share, 4) AS Distraction --uses eyeson_viewer_share underneath
    ,round(fcp.inattentive_visible_viewer_share_mean, 4) AS DistractionNorm   
    ,round(fcp.reactions_viewer_share, 4) AllReactions
    ,round(fcp.reactions_viewer_share_mean, 4) AllReactionsNorm      
    ,round(fcp.negativity_viewer_share, 4) Negativity    
    ,round(fcp.negativity_viewer_share_mean, 4) NegativityNorm  
    ,round(fcp.happiness_viewer_share, 4) Happiness 
    ,round(fcp.happiness_viewer_share_mean, 4) HappinessNorm 
    ,round(fcp.confusion_viewer_share, 4) Confusion
    ,round(fcp.confusion_viewer_share_mean, 4) ConfusionNorm
    ,round(fcp.contempt_viewer_share, 4) Contempt 
    ,round(fcp.contempt_viewer_share_mean, 4) ContemptNorm 
    ,round(fcp.disgust_viewer_share, 4) Disgust 
    ,round(fcp.disgust_viewer_share_mean, 4) DisgustNorm
    ,round(fcp.surprise_viewer_share, 4) Surprise
    ,round(fcp.surprise_viewer_share_mean, 4) SurpriseNorm
FROM fcp 
WHERE --fcp.segment_key = 'all' -- TODO remove once dashboard is ready to handle segments
    --AND coalesce(psd.HiddenOnPreViewUI,FALSE) = FALSE
    -- maybe add anti join to mapping table to explicitely exclude all ICIF adds
    NOT EXISTS(select m.sourcemedia_id, m.test_id from virtual.data.InContextPoC.mediaMapping m where m.sourcemedia_id = fcp.sourcemediaid and m.test_id = fcp.testid) 
    AND taskid is NULL   
--ORDER BY SourceMediaID, TestID, SegmentKey, "second"