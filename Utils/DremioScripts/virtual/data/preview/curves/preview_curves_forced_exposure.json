{"sql": "preview_curves_forced_exposure.sql", "depends": ["virtual.data.preview.main.preview_subscores.benchmarked_mean_time", "virtual.data.InContextPoC.mediaMapping", "fcp_postgre.public.custom_norm", "fcp_postgre.public.source_media_custom_norm", "fcp_postgre.public.feature"], "reflections": [{"name": "Raw Reflection", "type": "RAW", "sortFields": ["SourceMediaID", "TestID"], "displayFields": ["creative_id", "custom_norm_id", "IsForcedExposure", "SourceMediaID", "TestID", "TaskID", "SegmentKey", "Second", "Playback", "PlaybackNorm", "Attention", "AttentionNorm", "NeutralAttention", "NeutralAttentionNorm", "Distraction", "DistractionNorm", "AllReactions", "AllReactionsNorm", "Negativity", "NegativityNorm", "Happiness", "HappinessNorm", "Confusion", "ConfusionNorm", "Contempt", "ContemptNorm", "<PERSON><PERSON><PERSON><PERSON>", "DisgustNorm", "Surprise", "SurpriseNorm"]}]}