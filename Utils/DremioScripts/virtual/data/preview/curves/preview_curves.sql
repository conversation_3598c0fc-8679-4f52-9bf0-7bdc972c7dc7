-- <PERSON><PERSON><PERSON><PERSON> dataset
-- Primary Key: EmotionClassifier, SourceMediaID, TestID, Second
-- Updates
-- 2022-08-15: added Distracted, Neutral_attentive, Reactive_attentive that were forgotten in previous update
-- 2022-08-08: added Negativity
-- 2022-08-08: switched from "featureid=preview-media_time-benchmarking-combined-creative" to "featureid=preview-media_time-benchmarking-combined-creative" to bring audience retention data (and distracted/neutral/reacting) but lost Views_predicted_audience_retention and Predicted_audience_retention_modelBaseline as a result
-- 2022-01-14: temporary use of viewing_time_key_moments_percentile_curve-media_time-benchmarking-combined-creative to append audience retention
-- WARNING: two blockers here: 1) this feature only works for identified key moments (not all seconds) 2) this feature is currently not activated on all ads
-- 2022-01-12: introduction of emotions and attention curves
--             currently missing: negativity, attention states (neutral, distracted, reactive), audience retention

SELECT
    distinct
    l.AccountID
    ,coalesce(av.EmotionClassifier, em.EmotionClassifier) as EmotionClassifier
    ,coalesce(av.sourcemediaid, em.SourceMediaID) as SourceM<PERSON><PERSON>
    ,coalesce(av.testid, em.testid) as TestID
    ,coalesce(av.<PERSON><PERSON><PERSON><PERSON>, em.<PERSON>gment<PERSON>ey) as Segment<PERSON>ey
    ,coalesce(av."Second", em."Second") as "Second"
    ,av.Attention
    ,em.Happiness
    ,em.Surprise
    ,em.Confusion
    ,em.Contempt
    ,em.Disgust
    ,em.Fear_DEPRECATED
    ,em.Empathy_DEPRECATED
    ,em.Neutral_DEPRECATED
    ,ng.Negativity
    ,ar.Distracted
    ,ar.Neutral_attentive
    ,ar.Reactive_attentive
    ,ar.Predicted_audience_retention
    ,ar.Predicted_audience_retention_percentile
    ,ar.Predicted_audience_retention_benchmarkMedian
    --,ar.Predicted_audience_retention_modelBaseline
    ,em.Views_emotions
    ,ng.Views_negativity
    ,av.Views_attention
    --,ar.Views_predicted_audience_retention
FROM ( 
    SELECT
        algorithmid as EmotionClassifier,
        sourcemediaid as SourceMediaID,
        testid as TestID,
        cast(segment_key as varchar) as SegmentKey,
        relativestarttime "Second",
        round(happy_intensity, 3) as Happiness,
        round(surprise_intensity, 3) as Surprise,
        round(confused_intensity, 3) as Confusion,
        round(contempt_intensity, 3) as Contempt,
        round(disgust_intensity, 3) as Disgust,
        round(fear_intensity, 3) as Fear_DEPRECATED,
        round(sad_intensity, 3) as Empathy_DEPRECATED,
        round(neutral_intensity, 3) as Neutral_DEPRECATED,
        n_sessions as Views_emotions
    FROM "@aws_glue"."@fcp_db"."mean_media_time_emotions_classifiers_creative"
    where algorithmid='nel-v4.4.1'
 ) em
FULL JOIN (
    SELECT
        algorithmid as EmotionClassifier,
        sourcemediaid as SourceMediaID,
        testid as TestID,
        cast(segment_key as varchar) as SegmentKey,
        relativestarttime "Second",
        round(attention_detection_probability, 3) as Attention,
        n_sessions as Views_attention
    FROM "@aws_glue"."@fcp_db"."mean_media_time_attention_classifiers_creative"
    where algorithmid='nel-v4.4.1'
) av
ON av.EmotionClassifier = em.EmotionClassifier
AND av.SourceMediaID = em.SourceMediaID
AND av.TestID = em.TestID 
AND av."Second" = em."Second"
AND av.SegmentKey = em.SegmentKey
LEFT JOIN (
    SELECT
       algorithmid as EmotionClassifier,
        sourcemediaid as SourceMediaID,
        testid as TestID,
        cast(segment_key as varchar) as SegmentKey,
        relativestarttime "Second", 
        round(negative, 3) as Negativity,
        n_sessions as Views_negativity
    from "@aws_glue"."@fcp_db"."mean_negative_media_time_derived_classifiers_creative"
    where algorithmid='nel-v4.4.1'
) ng
ON ng.EmotionClassifier = em.EmotionClassifier
AND ng.SourceMediaID = em.SourceMediaID
AND ng.TestID = em.TestID 
AND ng."Second" = em."Second"
AND ng.SegmentKey = em.SegmentKey  
LEFT JOIN (
    SELECT
        algorithmid as EmotionClassifier,
        sourcemediaid as SourceMediaID,
        testid as TestID,
        cast(segment_key as varchar) as SegmentKey,
        "second" "Second",
        round(vtr, 3) as Predicted_audience_retention,
        round(vtr_percentile, 3) as Predicted_audience_retention_percentile,
        round(vtr_median, 3) as Predicted_audience_retention_benchmarkMedian,
        --cast(benchmark_vtr as decimal(10,3)) as Predicted_audience_retention_modelBaseline,
        --cast(n_sessions as int) as Views_predicted_audience_retention,
        round(distracted, 3) as Distracted,
        round(neutral, 3) as Neutral_attentive,
        round(engaged, 3) as Reactive_attentive
    FROM "@aws_glue"."@fcp_db"."preview_media_time_benchmarking_combined_creative" vtr --"@data_lake".features."featureid=viewing_time_key_moments_percentile_curve-media_time-benchmarking-combined-creative" vtr
    WHERE insertionmode = 'VTP'
    AND unskippable_seconds = 0
    AND algorithmid='nel-v4.4.1'
) ar
ON av.SourceMediaID = ar.SourceMediaID
AND av.TestID = ar.TestID
AND av."Second" = ar."Second"
AND av."EmotionClassifier" = ar."EmotionClassifier"
AND av.SegmentKey = ar.SegmentKey
join virtual.data.preview.main.preview_account_studies_latest l on l.sourcemediaid = coalesce(av.sourcemediaid, em.SourceMediaID) and l.testid = coalesce(av.testid, em.testid)
order by SourceMediaID, TestID, SegmentKey, coalesce(av."Second", em."Second")