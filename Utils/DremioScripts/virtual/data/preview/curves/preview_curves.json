{"sql": "preview_curves.sql", "sqlContext": "virtual", "depends": ["@aws_glue.@fcp_db.mean_media_time_emotions_classifiers_creative", "@aws_glue.@fcp_db.mean_negative_media_time_derived_classifiers_creative", "@aws_glue.@fcp_db.mean_media_time_attention_classifiers_creative", "@aws_glue.@fcp_db.preview_media_time_benchmarking_combined_creative", "virtual.data.preview.main.preview_account_studies_latest"], "reflections": [{"name": "Raw Reflection", "type": "RAW", "sortFields": ["AccountID", "SourceMediaID", "TestID", "SegmentKey", "Second"], "displayFields": ["EmotionClassifier", "SourceMediaID", "TestID", "SegmentKey", "Second", "Attention", "Happiness", "Surprise", "Confusion", "Contempt", "<PERSON><PERSON><PERSON><PERSON>", "Fear_DEPRECATED", "Empathy_DEPRECATED", "Neutral_DEPRECATED", "Negativity", "Distracted", "Neutral_attentive", "Reactive_attentive", "Predicted_audience_retention", "Predicted_audience_retention_percentile", "Predicted_audience_retention_benchmarkMedian", "Views_emotions", "Views_negativity", "Views_attention", "AccountID"]}]}