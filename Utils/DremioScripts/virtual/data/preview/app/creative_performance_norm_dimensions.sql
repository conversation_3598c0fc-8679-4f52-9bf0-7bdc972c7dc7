with 
region_distinct as (
    select distinct algorithmid, region, sourcemediaid, testid, taskid from "@aws_glue"."@fcp_db"."norm_segments_base"
),
region as (
    select algorithmid, 'region' as name, region as "value", count(*) ad_count from region_distinct group by algorithmid, region
),
country_distinct as (
    select distinct algorithmid, country, sourcemediaid, testid, taskid from "@aws_glue"."@fcp_db"."norm_segments_base"
),
country as (
    select algorithmid, 'country' as name, country as "value", count(*) ad_count from country_distinct group by algorithmid, country
),
industry_distinct as (
    select distinct algorithmid, top_category, sourcemediaid, testid, taskid from "@aws_glue"."@fcp_db"."norm_segments_base"
),
industry as (
    select algorithmid, 'industry' as name, top_category as "value", count(*) ad_count from industry_distinct group by algorithmid, top_category
),
category_distinct as (
    select distinct algorithmid, mid_category, sourcemediaid, testid, taskid from "@aws_glue"."@fcp_db"."norm_segments_base"
),
category as (
    select algorithmid, 'category' as name, mid_category as "value", count(*) ad_count from category_distinct group by algorithmid, mid_category
),
brand_distinct as (
    select distinct algorithmid, brand, sourcemediaid, testid, taskid from "@aws_glue"."@fcp_db"."norm_segments_base"
),
brand as (
    select algorithmid, 'brand' as name, brand as "value", count(*) ad_count from "@aws_glue"."@fcp_db"."norm_segments_base" group by algorithmid, brand
),
environment_distinct as (
    select distinct algorithmid, environment_category, sourcemediaid, testid, taskid from "@aws_glue"."@fcp_db"."norm_segments_base"
),
environment as (
    select algorithmid, 'environment' as name, environment_category as "value", count(*) ad_count from environment_distinct group by algorithmid, environment_category
),
ad_format_distinct as (
    select distinct algorithmid, ad_format, sourcemediaid, testid, taskid from "@aws_glue"."@fcp_db"."norm_segments_base"
),
ad_format as (
    select algorithmid, 'ad_format' as name, ad_format as "value", count(*) ad_count from ad_format_distinct group by algorithmid, ad_format
),
device_distinct as (
    select distinct algorithmid, device, sourcemediaid, testid, taskid from "@aws_glue"."@fcp_db"."norm_segments_base"
),
device as (
    select algorithmid, 'device' as name, device as "value", count(*) ad_count from device_distinct group by algorithmid, device
),
is_forced_exposure_distinct as (
     select distinct algorithmid, is_forced_exposure, sourcemediaid, testid, taskid from "@aws_glue"."@fcp_db"."norm_segments_base"
),
is_forced_exposure as (
    select algorithmid, 'is_forced_exposure' as name, is_forced_exposure as "value", count(*) ad_count from is_forced_exposure_distinct group by algorithmid, is_forced_exposure
),
is_skippable_distinct as (
     select distinct algorithmid, is_skippable, sourcemediaid, testid, taskid from "@aws_glue"."@fcp_db"."norm_segments_base"
),
is_skippable as (
    select algorithmid, 'is_skippable' as name, is_skippable as "value", count(*) ad_count from is_skippable_distinct group by algorithmid, is_skippable
),
format_distinct as (
     select distinct algorithmid, format, sourcemediaid, testid, taskid from "@aws_glue"."@fcp_db"."norm_segments_base"
),
format as (
    select algorithmid, 'format' as name, format as "value", count(*) ad_count from format_distinct group by algorithmid, format
),
duration_distinct as (
     select distinct algorithmid, duration_with_data, sourcemediaid, testid, taskid from "@aws_glue"."@fcp_db"."norm_segments_base"
),
duration as (
    select algorithmid, 'duration' as name, duration_with_data as "value", count(*) ad_count from duration_distinct group by algorithmid, duration_with_data
),
duration_expanded as (
    select algorithmid, 'duration' as name, n.i as "value", sum(ad_count) ad_count from duration d JOIN virtual.numbers n ON d."value" >= n.i where n.i between 1 and 60 group by algorithmid, n.i
)
select * from region
union all
select * from country
union all
select * from industry
union all
select * from category
union all
select * from brand
union all
select * from environment
union all
select * from ad_format
union all
select * from device
union all
select * from is_forced_exposure
union all
select * from is_skippable
union all
select * from format
union all
select * from duration_expanded
--
-- select * from "@aws_glue"."@fcp_db"."norm_segments_base" limit 1000


-- alter table "@aws_glue"."@fcp_db"."norm_segments_base" refresh metadata