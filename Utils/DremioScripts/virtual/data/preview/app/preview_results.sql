----------------------------------------------
-- UPDATES
----------------------------------------------
-- 03 Aug 2022
-- modify 'MidCategory'
-- 25 Jul 2022
-- modify 'SubCategory'

SELECT 
    l.AccountID, 
    l.StudyID, 
    l.StudyTypeID, 
    l.Account, 
    l.StudyType, 
    l.Study, 
    l.StudyDate, 
    case when apb.BrandID is not null then True else False end Account_owned_brand,
    --results
    Missing_QualityScore, 
    Missing_Encode, 
    Missing_Negativity_peak, 
    Missing_Emotion_averages, 
    Missing_Country, 
    Missing_Brand, 
    Wrong_Duration, 
    Missing_Duration, 
    Low_Benchmark_sample_size,
    case when 1.0*(StudyViewings - Views)/StudyViewings > 0.05 then 1 else 0 end as Missing_Views,
    results.SourceMediaID, 
    results.TestID,
    SegmentKey,
    results.ParentBrandID, 
    results.BrandID, 
    ParentBrandLogoFileName,
    BrandLogoFileName, 
    results.TopCategoryID, 
    results.MidCategoryID, 
    SubCategoryID, 
    CategoryID, 
    CountryID, 
    External_key, 
    Date_of_test, 
    SourceMedia, 
    SourceMediaThumbnailFileName, 
    Test,
    ParentBrand, 
    Brand, 
    Category, 
    CategoryTree, 
    TopCategory, 
    case when MidCategory = 'Not assigned' or MidCategory is null  then TopCategory
        else MidCategory end as MidCategory, 
    case 
        when SubCategory = 'Not assigned' or SubCategory  is null  then
            case when MidCategory = 'Not assigned' or MidCategory is null  then TopCategory
            else MidCategory end
        else SubCategory end as SubCategory,
    Continent, 
    Country, 
    Country_code, 
    Duration, 
    Cap, 
--    Format_length,
    StudyViewings,
    Views,
    Benchmark_sample_size, 
    QualityScore, 
    CreativeEfficiency, 
    qCPM, 
    Capture_decile, 
    Retain_decile, 
    Encode_decile, 
    QualityScore_traffic_light, 
    QualityScore_index, 
    Capture, 
    Retain, 
    Encode, 
    Capture_percentile, 
    Retain_percentile, 
    Encode_percentile, 
    Capture_benchmark_median, 
    Retain_benchmark_median, 
    Encode_benchmark_median, 
    Capture_benchmark_top20, 
    Retain_benchmark_top20, 
    Encode_benchmark_top20, 
    Capture_index, 
    Retain_index, 
    Encode_index, 
    Distraction_avg, 
    Neutral_attention_avg, 
    Engaged_attention_avg,
    Distraction_decile, 
    Neutral_attention_decile, 
    Engaged_attention_decile,
    Distraction_percentile, 
    Neutral_attention_percentile, 
    Engaged_attention_percentile, 
    Distraction_benchmark_median, 
    Neutral_attention_benchmark_median,
    Engaged_attention_benchmark_median, 
    Distraction_benchmark_top20, 
    Neutral_attention_benchmark_top20, 
    Engaged_attention_benchmark_top20,
    Distraction_index, 
    Neutral_attention_index, 
    Engaged_attention_index,  
    Happiness_avg, 
    Surprise_avg, 
    Negativity_avg, 
    Confusion_avg, 
    Contempt_avg, 
    Disgust_avg, 
    Happiness_avg_decile, 
    Surprise_avg_decile, 
    Negativity_avg_decile, 
    Confusion_avg_decile, 
    Contempt_avg_decile, 
    Disgust_avg_decile, 
    Happiness_avg_percentile, 
    Surprise_avg_percentile, 
    Negativity_avg_percentile, 
    Confusion_avg_percentile, 
    Contempt_avg_percentile, 
    Disgust_avg_percentile, 
    Happiness_avg_benchmark_median, 
    Surprise_avg_benchmark_median, 
    Negativity_avg_benchmark_median, 
    Confusion_avg_benchmark_median, 
    Contempt_avg_benchmark_median, 
    Disgust_avg_benchmark_median, 
    Happiness_avg_benchmark_top20, 
    Surprise_avg_benchmark_top20, 
    Negativity_avg_benchmark_top20, 
    Confusion_avg_benchmark_top20, 
    Contempt_avg_benchmark_top20, 
    Disgust_avg_benchmark_top20, 
    Happiness_avg_index, 
    Surprise_avg_index, 
    Negativity_avg_index, 
    Confusion_avg_index, 
    Contempt_avg_index, 
    Disgust_avg_index, 
    Happiness_peak, 
    Surprise_peak, 
    Negativity_peak, 
    Confusion_peak, 
    Contempt_peak, 
    Disgust_peak, 
    Happiness_peak_decile, 
    Surprise_peak_decile, 
    Negativity_peak_decile, 
    Confusion_peak_decile, 
    Contempt_peak_decile, 
    Disgust_peak_decile, 
    Happiness_peak_percentile, 
    Surprise_peak_percentile, 
    Negativity_peak_percentile, 
    Confusion_peak_percentile, 
    Contempt_peak_percentile, 
    Disgust_peak_percentile, 
    Happiness_peak_benchmark_median, 
    Surprise_peak_benchmark_median, 
    Negativity_peak_benchmark_median, 
    Confusion_peak_benchmark_median, 
    Contempt_peak_benchmark_median, 
    Disgust_peak_benchmark_median, 
    Happiness_peak_benchmark_top20, 
    Surprise_peak_benchmark_top20, 
    Negativity_peak_benchmark_top20, 
    Confusion_peak_benchmark_top20, 
    Contempt_peak_benchmark_top20, 
    Disgust_peak_benchmark_top20, 
    Happiness_peak_index, 
    Surprise_peak_index, 
    Negativity_peak_index, 
    Confusion_peak_index, 
    Contempt_peak_index, 
    Disgust_peak_index, 
    HumanPresence, 
    FemalePresence, 
    MalePresence, 
    Female_representation_ratio,
    OverallTextPresence, 
    Text_in_first_5sec, 
    RelativeText_avg_position,
    HackyHash  
FROM virtual.data.preview.main.preview_account_studies_latest l
JOIN virtual.data.preview.main.preview_combined_metric_data results
  on l.SourceMediaID = results.SourceMediaID
  and l.TestID = results.TestID
LEFT JOIN virtual.data.preview.main."preview_account_brands" apb
on l.AccountID = apb.AccountID
and results.BrandID = apb.BrandID
WHERE missing_brand = 0 
  and missing_country = 0
  and missing_qualityscore = 0
  --and missing_negativity_peak = 0
  and missing_emotion_averages = 0
  and missing_encode = 0