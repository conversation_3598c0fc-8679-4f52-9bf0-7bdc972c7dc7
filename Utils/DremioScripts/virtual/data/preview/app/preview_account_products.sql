select A<PERSON>unt<PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>,HasRE<PERSON>n<PERSON>ontext,HasRENonInContext,IsPreviewEnabled, HasAccessToNewForcedExposure,
case when 
    exists (select null from virtual.data.preview.app."creative_performance_portfolio_forced_exposure_grid" cn where cn.accountid = ap.accountid 
    and (cn.AttentionNormCustomNormFilters is not null or cn.ReactionsNormCustomNormFilters is not null)) 
    OR exists  (select null from virtual.data.preview.app."creative_performance_portfolio_grid" cn where cn.accountid = ap.accountid and 
    (cn.AttentionNormCustomNormFiltersIC is not null or cn.ReactionsNormCustomNormFiltersIC is not null or cn.SurveyNormCustomNormFiltersIC is not null
        or cn.AttentionNormCustomNormFiltersIF is not null or cn.ReactionsNormCustomNormFiltersIF is not null or cn.SurveyNormCustomNormFiltersIF is not null
     )) 
then 1 else 0 end as HasCustomNorm,
case when 
    exists (select null from virtual.data.preview.app."creative_performance_portfolio_forced_exposure_grid" cn where cn.accountid = ap.accountid 
    and (cn.AttentionNormCustomNormFilters is not null or cn.ReactionsNormCustomNormFilters is not null)) 
then 1 else 0 end as HasCustomNormFE,
case when 
exists  (select null from virtual.data.preview.app."creative_performance_portfolio_grid" cn where cn.accountid = ap.accountid and 
    (cn.AttentionNormCustomNormFiltersIC is not null or cn.ReactionsNormCustomNormFiltersIC is not null or cn.SurveyNormCustomNormFiltersIC is not null
        or cn.AttentionNormCustomNormFiltersIF is not null or cn.ReactionsNormCustomNormFiltersIF is not null or cn.SurveyNormCustomNormFiltersIF is not null
     )) 
then 1 else 0 end as HasCustomNormIC
 from studydatabasereadonly.StudyDatabase.dbo.vw_PreView_AccountProducts ap