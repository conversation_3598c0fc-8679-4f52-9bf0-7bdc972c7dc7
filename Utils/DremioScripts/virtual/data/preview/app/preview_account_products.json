{"sql": "preview_account_products.sql", "sqlContext": "@a_forgacs", "depends": ["virtual.data.preview.app.creative_performance_portfolio_grid", "virtual.data.preview.app.creative_performance_portfolio_forced_exposure_grid", "StudyDatabaseReadonly.StudyDatabase.dbo.vw_PreView_AccountProducts"], "reflections": [{"name": "Raw Reflection", "type": "RAW", "sortFields": ["Account<PERSON><PERSON>", "AccountID"], "displayFields": ["AccountID", "Account<PERSON><PERSON>", "HasREInContext", "HasRENonInContext", "IsPreviewEnabled", "HasAccessToNewForcedExposure", "HasCustomNorm", "HasCustomNormFE", "HasCustomNormIC"]}]}