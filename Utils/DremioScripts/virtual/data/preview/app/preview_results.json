{"sql": "preview_results.sql", "sqlContext": "virtual.data.preview.app", "depends": ["virtual.data.preview.main.preview_combined_metric_data", "virtual.data.preview.main.preview_account_brands", "virtual.data.preview.main.preview_account_studies_latest"], "reflections": [{"name": "Raw Reflection", "type": "RAW", "sortFields": ["Account"], "displayFields": ["AccountID", "StudyID", "StudyTypeID", "Account", "StudyType", "Study", "StudyDate", "Account_owned_brand", "Missing_QualityScore", "Missing_Encode", "Missing_Negativity_peak", "Missing_Emotion_averages", "Missing_Country", "Missing_Brand", "Wrong_Duration", "Missing_Duration", "Low_Benchmark_sample_size", "Missing_Views", "SourceMediaID", "TestID", "SegmentKey", "ParentBrandID", "BrandID", "ParentBrandLogoFileName", "BrandLogoFileName", "TopCategoryID", "MidCategoryID", "SubCategoryID", "CategoryID", "CountryID", "External_key", "Date_of_test", "SourceMedia", "SourceMediaThumbnailFileName", "Test", "<PERSON><PERSON><PERSON><PERSON>", "Brand", "Category", "CategoryTree", "TopCategory", "MidCategory", "SubCategory", "Continent", "Country", "Country_code", "Duration", "Cap", "StudyViewings", "Views", "Benchmark_sample_size", "QualityScore", "CreativeEfficiency", "qCPM", "Capture_decile", "Retain_decile", "Encode_decile", "QualityScore_traffic_light", "QualityScore_index", "Capture", "<PERSON><PERSON>", "Encode", "Capture_percentile", "Retain_percentile", "Encode_percentile", "Capture_benchmark_median", "Retain_benchmark_median", "Encode_benchmark_median", "Capture_benchmark_top20", "Retain_benchmark_top20", "Encode_benchmark_top20", "Capture_index", "Retain_index", "Encode_index", "Distraction_avg", "Neutral_attention_avg", "Engaged_attention_avg", "Distraction_decile", "Neutral_attention_decile", "Engaged_attention_decile", "Distraction_percentile", "Neutral_attention_percentile", "Engaged_attention_percentile", "Distraction_benchmark_median", "Neutral_attention_benchmark_median", "Engaged_attention_benchmark_median", "Distraction_benchmark_top20", "Neutral_attention_benchmark_top20", "Engaged_attention_benchmark_top20", "Distraction_index", "Neutral_attention_index", "Engaged_attention_index", "Happiness_avg", "Surprise_avg", "Negativity_avg", "Confusion_avg", "Contempt_avg", "Disgust_avg", "Happiness_avg_decile", "Surprise_avg_decile", "Negativity_avg_decile", "Confusion_avg_decile", "Contempt_avg_decile", "Disgust_avg_decile", "Happiness_avg_percentile", "Surprise_avg_percentile", "Negativity_avg_percentile", "Confusion_avg_percentile", "Contempt_avg_percentile", "Disgust_avg_percentile", "Happiness_avg_benchmark_median", "Surprise_avg_benchmark_median", "Negativity_avg_benchmark_median", "Confusion_avg_benchmark_median", "Contempt_avg_benchmark_median", "Disgust_avg_benchmark_median", "Happiness_avg_benchmark_top20", "Surprise_avg_benchmark_top20", "Negativity_avg_benchmark_top20", "Confusion_avg_benchmark_top20", "Contempt_avg_benchmark_top20", "Disgust_avg_benchmark_top20", "Happiness_avg_index", "Surprise_avg_index", "Negativity_avg_index", "Confusion_avg_index", "Contempt_avg_index", "Disgust_avg_index", "Happiness_peak", "Surprise_peak", "Negativity_peak", "Confusion_peak", "Contempt_peak", "Disgust_peak", "Happiness_peak_decile", "Surprise_peak_decile", "Negativity_peak_decile", "Confusion_peak_decile", "Contempt_peak_decile", "Disgust_peak_decile", "Happiness_peak_percentile", "Surprise_peak_percentile", "Negativity_peak_percentile", "Confusion_peak_percentile", "Contempt_peak_percentile", "Disgust_peak_percentile", "Happiness_peak_benchmark_median", "Surprise_peak_benchmark_median", "Negativity_peak_benchmark_median", "Confusion_peak_benchmark_median", "Contempt_peak_benchmark_median", "Disgust_peak_benchmark_median", "Happiness_peak_benchmark_top20", "Surprise_peak_benchmark_top20", "Negativity_peak_benchmark_top20", "Confusion_peak_benchmark_top20", "Contempt_peak_benchmark_top20", "Disgust_peak_benchmark_top20", "Happiness_peak_index", "Surprise_peak_index", "Negativity_peak_index", "Confusion_peak_index", "Contempt_peak_index", "Disgust_peak_index", "HumanPresence", "FemalePresence", "MalePresence", "Female_representation_ratio", "OverallTextPresence", "Text_in_first_5sec", "RelativeText_avg_position", "<PERSON><PERSON><PERSON><PERSON>"]}]}