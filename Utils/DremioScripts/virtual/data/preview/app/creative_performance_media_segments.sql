with media_segment_keys as (
    select sourcemediaid, testid, taskid, segment_key Segment<PERSON><PERSON>, views AS Views 
    from virtual.data.preview.main."preview_subscores"."benchmarked_counts" where custom_norm_id is null
)
SELECT ms.sourcemediaid as SourceMedia<PERSON>, ms.testid as <PERSON><PERSON>, ms.taskid as <PERSON><PERSON>, ms.<PERSON>, ms.Views,
COALESCE(sg.Label, sg.Name) Question, sg.Key <PERSON>, sg."Order" QuestionOrder, COALESCE(s.Label, s.Name) Answer, s.Key <PERSON>, sg."Order" AnswerOrder
FROM media_segment_keys ms
left join "fcp_postgre".public."segment_group" sg on SPLIT_PART(ms.SegmentKey, '_', 1) = sg.<PERSON>
left join "fcp_postgre".public."segment" s on sg.ID = s.Segment_group_ID AND SPLIT_PART(ms.SegmentKey, '_', 2) = s.Key
order by sourcemediaid, testid, sg."Order", s."Order"
