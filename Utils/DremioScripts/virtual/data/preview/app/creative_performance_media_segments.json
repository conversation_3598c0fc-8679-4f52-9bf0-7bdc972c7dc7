{"sql": "creative_performance_media_segments.sql", "sqlContext": "virtual.data.preview.app", "depends": ["fcp_postgre.public.segment_group", "fcp_postgre.public.segment", "virtual.data.preview.main.preview_subscores.benchmarked_counts"], "reflections": [{"name": "Raw Reflection", "type": "RAW", "sortFields": ["SourceMediaID", "TestID", "TaskID"], "displayFields": ["SourceMediaID", "TestID", "TaskID", "SegmentKey", "Views", "Question", "<PERSON><PERSON><PERSON>", "QuestionOrder", "Answer", "<PERSON><PERSON><PERSON>", "AnswerOrder"]}]}