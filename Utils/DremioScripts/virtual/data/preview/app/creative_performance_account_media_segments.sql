with in_context_account_media as (
    select DISTINCT account_id AccountID, sourcemedia_id SourceMediaID, test_id TestID, 'IC' ProductType 
    from virtual.data.InContextPoC.mediaMapping AS mapping where mapping.is_target = TRUE and mapping.is_infocus = FALSE
),
forced_exposure_account_media as (
    select AccountID, SourceMediaID, TestID, 'IF' ProductType 
    from virtual.data.preview.main.preview_account_studies_latest latest
    WHERE NOT EXISTS(
        select * from virtual.data.InContextPoC.mediaMapping m where m.sourcemedia_id = latest.sourcemediaid and m.test_id = latest.testid
    )
),
all_product_type_media as (
    select AccountID, SourceMediaID, TestID, ProductType 
    from in_context_account_media
    UNION ALL
    select AccountID, SourceMediaID, TestID, ProductType 
    from forced_exposure_account_media
)
select DISTINCT ProductType, AccountID, ms.Segment<PERSON><PERSON>,
ms.<PERSON>, ms.<PERSON><PERSON><PERSON>, ms.QuestionOrder, 
ms.Answer, ms.<PERSON>, ms.AnswerOrder   from all_product_type_media a
JOIN virtual.data.preview.app."creative_performance_media_segments" ms ON a.SourceMediaID = ms.SourceMediaID AND a.TestID = ms.TestID
Order by ProductType, AccountID, Question<PERSON>rder, Question<PERSON>ey, Answer<PERSON>rder, Answer<PERSON>ey