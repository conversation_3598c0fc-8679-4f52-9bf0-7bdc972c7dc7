{"sql": "creative_performance_portfolio_forced_exposure_grid.sql", "depends": ["fcp_postgre.public.source_media_custom_norm", "virtual.data.InContextPoC.eyesquare_mapping", "StudyDatabaseReadonly.StudyDatabase.dbo.vw_AdSet_TestElement_Account", "virtual.data.preview.main.preview_metadata", "virtual.data.InContextPoC.mediaMapping", "StudyDatabaseReadonly.StudyDatabase.dbo.Project", "StudyDatabaseReadonly.StudyDatabase.dbo.AdSetType", "virtual.data.preview.main.preview_subscores.benchmarked_counts", "virtual.data.preview.main.preview_subscores.benchmarked_stats", "StudyDatabaseReadonly.StudyDatabase.dbo.AdSetAttribute", "fcp_postgre.public.custom_norm", "fcp_postgre.public.feature", "StudyDatabaseReadonly.StudyDatabase.dbo.DataCollectionStructure"], "reflections": [{"name": "Raw Reflection", "type": "RAW", "sortFields": ["AccountID", "SourceMediaID"], "displayFields": ["creative_id", "OrderAdSetID", "OrderExternalKey", "Order", "OrderType", "CustomAdSets", "SourceMediaExternalKey", "Account", "AccountID", "AttentionNormCustomNormFilters", "AttentionNormRegion", "AttentionNormEnvironmentCategory", "AttentionNormAdFormat", "AttentionNormDevice", "AttentionNormIsForcedExposure", "AttentionNormIsSkippable", "AttentionNormFormat", "AttentionNormDuration", "AttentionNormSampleSize", "AttenionNormRefreshDate", "ReactionsNormCustomNormFilters", "ReactionsNormRegion", "ReactionsNormEnvironmentCategory", "ReactionsNormAdFormat", "ReactionsNormDevice", "ReactionsNormIsForcedExposure", "ReactionsNormIsSkippable", "ReactionsNormFormat", "ReactionsNormDuration", "ReactionsNormSampleSize", "ReactionsNormRefreshDate", "IsForcedExposure", "SourceMediaID", "TestID", "TaskID", "SegmentKey", "Views", "AdformatTextIF", "Nor<PERSON><PERSON>allback", "NormSegmentKey", "norm_sample_size", "QualityScore", "QualityScore_index", "AttentiveSeconds", "AttentiveSecondsRank", "AttentiveSecondsMedian", "AttentiveSecondsDiff", "AttentiveSecondsVTR", "AttentiveSecondsVTRRank", "AttentiveSecondsVTRMedian", "AttentiveSecondsVTRDiff", "VTR", "VTRRank", "VTRMedian", "VTRDiff", "Playback<PERSON><PERSON><PERSON><PERSON>", "PlaybackSecondsRank", "PlaybackSecondsMedian", "PlaybackSecondsDiff", "AttentionAvg", "AttentionAvgRank", "AttentionAvgMedian", "AttentionAvgDiff", "Reactions", "ReactionsRank", "ReactionsMedian", "ReactionsDiff", "HappyPeak", "HappyPeakRank", "HappyPeakMedian", "HappyPeakDiff", "SurprisePeak", "SurprisePeakRank", "SurprisePeakMedian", "SurprisePeakDiff", "ConfusionPeak", "ConfusionPeakRank", "ConfusionPeakMedian", "ConfusionPeakDiff", "ContemptPeak", "ContemptPeakRank", "ContemptPeakMedian", "ContemptPeakDiff", "DisgustPeak", "DisgustPeakRank", "DisgustPeakMedian", "DisgustPeakDiff", "NegativityAvg", "NegativityAvgRank", "NegativityAvgMedian", "NegativityAvgDiff", "NeutralAttentionAvg", "NeutralAttentionAvgRank", "NeutralAttentionAvgMedian", "NeutralAttentionAvgDiff", "DistractionAvg", "DistractionAvgRank", "DistractionAvgMedian", "DistractionAvgDiff", "AttentionPeak", "AttentionPeakRank", "AttentionPeakMedian", "HappyAvg", "HappyAvgRank", "HappyAvgMedian", "SurpriseAvg", "SurpriseAvgRank", "SurpriseAvgMedian", "ConfusionAvg", "ConfusionAvgRank", "ConfusionAvgMedian", "ContemptAvg", "ContemptAvgRank", "ContemptAvgMedian", "DisgustAvg", "DisgustAvgRank", "DisgustAvgMedian", "Country", "Country_code", "GeographicRegion", "SourceMediaThumbnailFileName", "SourceMediaThumbstripFileName", "SourceMediaConvertedFileName", "ParentCreative", "SourceMedia", "SourceMediaType", "Duration", "CreationDate", "Brand", "BrandID", "BrandLogoFileName", "TopCategory", "MidCategory", "SubCategory", "Platform", "<PERSON><PERSON>", "Adformat", "AdformatText", "IsEnabledForClients", "PDProjectID", "SurveyKeyAlias"]}]}