WITH mediasegments AS (
    select DISTINCT pvm.sourcemediaid, SourceMedia, pvm.TestID, Date_of_test, TopCategory, MidCategory, SubCategory,
    SourceMediaThumbnailFileName, SourceMediaConvertedFileName, Duration,
    BrandLogoFileName, Country, Country_code, geographic_region,
    Brand BrandName, BrandID, Format, TestDevice
    from virtual.data.preview.main."preview_metadata" pvm
),
custom_adsets as (
    select adsetaccount.AccountID,adsetaccount.SourceMediaID,adsetaccount.TestID,LISTAGG(adsetaccount.Name,',') CustomAdSets from StudyDatabaseReadonly.StudyDatabase.dbo."vw_AdSet_TestElement_Account" adsetaccount
    where adsetaccount.AdSetTypeID=3
    group by adsetaccount.AccountID,adsetaccount.SourceMediaID,adsetaccount.TestID
),
default_custom_norms_base as (
    select distinct 
    case when f.name = 'mean_counts-media_duration-classifiers-creative' then 'fcp'
    when f.name ='stats-media_duration-classifiers-creative' then 'fcp_stats'
    when f.name='confirmit-media-survey-creative' then 'confirmit'
    when f.name='mean-media_time-classifiers-creative' then 'curves'
    else NULL
    end as feature, smcn.source_media_id,smcn.test_id,smcn.custom_norm_id,smcn.is_default,smcn.fallback,cn.filters from 
    "fcp_postgre".public."source_media_custom_norm" smcn 
    join "fcp_postgre".public.custom_norm cn on cn.id = smcn.custom_norm_id
    join "fcp_postgre".public.feature f on f.id = smcn.feature_id
    where f.name in (
    'mean_counts-media_duration-classifiers-creative',
    'stats-media_duration-classifiers-creative',
    'confirmit-media-survey-creative',
    'mean-media_time-classifiers-creative'
    )
)
,fcp AS (
    select cn_fcp.is_default as custom_norm_is_default,cn_fcp.fallback custom_norm_fallback, cn_fcp.filters custom_norm_filters, fcp.* 
    from virtual.data.preview.main."preview_subscores"."benchmarked_counts" fcp
    join default_custom_norms_base cn_fcp on cn_fcp.source_media_id=fcp.sourcemediaid and cn_fcp.test_id=fcp.testid and cn_fcp.feature='fcp' and fcp.custom_norm_id=cn_fcp.custom_norm_id
    where taskid is NULL and cn_fcp.is_default=1
    UNION ALL 
    select null as custom_norm_is_default,null as custom_norm_fallback, null as custom_norm_filters, fcp.* 
    from virtual.data.preview.main."preview_subscores"."benchmarked_counts" fcp
    where taskid is NULL and fcp.custom_norm_id is null
    and not exists (select null from default_custom_norms_base cn_fcp where cn_fcp.source_media_id=fcp.sourcemediaid and cn_fcp.test_id=fcp.testid and cn_fcp.feature='fcp' and cn_fcp.is_default=1)
),
fcp_stats AS (
    select cn_fcp_stats.is_default as custom_norm_is_default,cn_fcp_stats.fallback custom_norm_fallback, cn_fcp_stats.filters custom_norm_filters,fcp_stats.* 
    from virtual.data.preview.main."preview_subscores"."benchmarked_stats" fcp_stats
    join default_custom_norms_base cn_fcp_stats on cn_fcp_stats.source_media_id=fcp_stats.sourcemediaid and cn_fcp_stats.test_id=fcp_stats.testid and cn_fcp_stats.feature='fcp_stats' and fcp_stats.custom_norm_id=cn_fcp_stats.custom_norm_id
    where taskid is NULL and cn_fcp_stats.is_default=1
    UNION ALL
    select null as custom_norm_is_default,null as custom_norm_fallback, null as custom_norm_filters,fcp_stats.* 
    from virtual.data.preview.main."preview_subscores"."benchmarked_stats" fcp_stats    
    where taskid is NULL and fcp_stats.custom_norm_id is null
    and not exists (select null from default_custom_norms_base cn_fcp_stats where cn_fcp_stats.source_media_id=fcp_stats.sourcemediaid and cn_fcp_stats.test_id=fcp_stats.testid and cn_fcp_stats.feature='fcp_stats' and cn_fcp_stats.is_default=1)
)
SELECT DISTINCT
    CONCAT( CAST(adsetaccount_order.testid AS CHAR), '/', 
            CAST(adsetaccount_order.sourcemediaid AS CHAR)) AS creative_id
    ,adsetaccount_order.AdSetID as OrderAdSetID            
    ,adsetaccount_order.AdSetExternalKey as OrderExternalKey
    ,adsetaccount_order.Name As "Order"
    ,adsettype.Name as OrderType
    ,custom_adsets.CustomAdSets
    ,adsetaccount_order.SourceMediaExternalKey    
    ,adsetaccount_order.account As Account
    ,adsetaccount_order.accountid AS AccountID
    ,fcp.custom_norm_filters as AttentionNormCustomNormFilters
    ,fcp.region AttentionNormRegion
    ,fcp.environment_category AttentionNormEnvironmentCategory
    ,fcp.ad_format AttentionNormAdFormat
    ,fcp.device AttentionNormDevice
    ,fcp.is_forced_exposure AttentionNormIsForcedExposure
    ,fcp.is_skippable as AttentionNormIsSkippable
    ,fcp.format as AttentionNormFormat
    ,fcp.norm_duration as AttentionNormDuration
    ,fcp.real_sample_size as AttentionNormSampleSize
    ,fcp.norm_create_date as AttenionNormRefreshDate --TODO: change when data available in features
    ,fcp_stats.custom_norm_filters as ReactionsNormCustomNormFilters
    ,fcp_stats.region ReactionsNormRegion
    ,fcp_stats.environment_category ReactionsNormEnvironmentCategory
    ,fcp_stats.ad_format ReactionsNormAdFormat
    ,fcp_stats.device ReactionsNormDevice
    ,fcp_stats.is_forced_exposure ReactionsNormIsForcedExposure
    ,fcp_stats.is_skippable as ReactionsNormIsSkippable
    ,fcp_stats.format as ReactionsNormFormat
    ,fcp_stats.norm_duration as ReactionsNormDuration    
    ,fcp_stats.real_sample_size as ReactionsNormSampleSize
    ,fcp_stats.norm_create_date as ReactionsNormRefreshDate --TODO: change when data available in features
    ,true AS IsForcedExposure
    ,adsetaccount_order.sourcemediaid AS SourceMediaID
    ,adsetaccount_order.testid AS TestID
    ,NULL AS TaskID
    ,fcp.segment_key AS SegmentKey
    ,fcp.views AS Views
    ,'Realeyes Video Player' AS AdformatTextIF
    --MAIN SCORES---------------------------------------
    ,fcp.fallback as NormFallback
    ,fcp.norm_segment_key as NormSegmentKey
    ,fcp.real_sample_size as norm_sample_size
    ,round((fcp.eyeson_seconds_avg_decile + fcp.attentive_viewthrough_avg_decile + fcp_stats.reactions_viewer_share_avg_decile + fcp_stats.happiness_viewer_share_max_decile)/4.0*10, 1) AS QualityScore
    ,case when QualityScore is null or QualityScore < 40 then -1 when QualityScore > 70 then 1 else 0 end AS QualityScore_index 
    ,round(fcp.eyeson_seconds_avg, 2) AS AttentiveSeconds
    ,fcp.eyeson_seconds_avg_decile as AttentiveSecondsRank
    ,round(fcp.eyeson_seconds_avg_median, 2) as AttentiveSecondsMedian
    ,case when AttentiveSecondsMedian = 0 then null else round(AttentiveSeconds / AttentiveSecondsMedian - 1, 3) end AS AttentiveSecondsDiff
    ,round(fcp.eyeson_viewthrough_avg, 4) AS AttentiveSecondsVTR
    ,fcp.eyeson_viewthrough_avg_decile as AttentiveSecondsVTRRank
    ,round(fcp.eyeson_viewthrough_avg_median, 4) as AttentiveSecondsVTRMedian
    ,case when AttentiveSecondsVTRMedian = 0 then null else round(AttentiveSecondsVTR / AttentiveSecondsVTRMedian - 1, 3) end AS AttentiveSecondsVTRDiff
    ,round(fcp.viewthrough_avg, 4) AS VTR
    ,fcp.viewthrough_avg_decile as VTRRank
    ,round(fcp.viewthrough_avg_median, 4) as VTRMedian
    ,case when VTRMedian = 0 then null else round(VTR / VTRMedian - 1, 3) end AS VTRDiff
    ,round(fcp.visible_seconds_avg, 2) AS PlaybackSeconds 
    ,fcp.visible_seconds_avg_decile as PlaybackSecondsRank
    ,round(fcp.visible_seconds_avg_median, 2) as PlaybackSecondsMedian
    ,case when PlaybackSecondsMedian = 0 then null else round(PlaybackSeconds / PlaybackSecondsMedian - 1, 3) end AS PlaybackSecondsDiff
    ,round(fcp_stats.eyeson_viewer_share_avg , 4) AS AttentionAvg
    ,fcp_stats.eyeson_viewer_share_avg_decile as AttentionAvgRank
    ,round(fcp_stats.eyeson_viewer_share_avg_median, 4) as AttentionAvgMedian
    ,case when AttentionAvgMedian = 0 then null else round(AttentionAvg / AttentionAvgMedian - 1, 3) end AS AttentionAvgDiff
    ,round(fcp_stats.reactions_viewer_share_avg, 4) AS Reactions
    ,fcp_stats.reactions_viewer_share_avg_decile as ReactionsRank
    ,round(fcp_stats.reactions_viewer_share_avg_median, 4) AS ReactionsMedian
    ,case when ReactionsMedian = 0 then null else round(Reactions / ReactionsMedian - 1, 3) end AS ReactionsDiff
    ,round(fcp_stats.happiness_viewer_share_max, 4) AS HappyPeak
    ,fcp_stats.happiness_viewer_share_max_decile as HappyPeakRank
    ,round(fcp_stats.happiness_viewer_share_max_median, 4) AS HappyPeakMedian
    ,case when HappyPeakMedian = 0 then null else round(HappyPeak / HappyPeakMedian - 1, 3) end AS HappyPeakDiff
    ,round(fcp_stats.surprise_viewer_share_max, 4) AS SurprisePeak
    ,fcp_stats.surprise_viewer_share_max_decile as SurprisePeakRank
    ,round(fcp_stats.surprise_viewer_share_max_median, 4) AS SurprisePeakMedian
    ,case when SurprisePeakMedian = 0 then null else round(SurprisePeak / SurprisePeakMedian - 1, 3) end AS SurprisePeakDiff
    ,round(fcp_stats.confusion_viewer_share_max, 4) AS ConfusionPeak
    ,fcp_stats.confusion_viewer_share_max_decile as ConfusionPeakRank
    ,round(fcp_stats.confusion_viewer_share_max_median, 4) AS ConfusionPeakMedian
    ,case when ConfusionPeakMedian = 0 then null else round(ConfusionPeak / ConfusionPeakMedian - 1, 3) end AS ConfusionPeakDiff
    ,round(fcp_stats.contempt_viewer_share_max, 4) AS ContemptPeak
    ,fcp_stats.contempt_viewer_share_max_decile as ContemptPeakRank
    ,round(fcp_stats.contempt_viewer_share_max_median, 4) AS ContemptPeakMedian
    ,case when ContemptPeakMedian = 0 then null else round(ContemptPeak / ContemptPeakMedian - 1, 3) end AS ContemptPeakDiff
    ,round(fcp_stats.disgust_viewer_share_max, 4) AS DisgustPeak
    ,fcp_stats.disgust_viewer_share_max_decile as DisgustPeakRank
    ,round(fcp_stats.disgust_viewer_share_max_median, 4) AS DisgustPeakMedian
    ,case when DisgustPeakMedian = 0 then null else round(DisgustPeak / DisgustPeakMedian - 1, 3) end AS DisgustPeakDiff
    ,round(fcp_stats.negativity_viewer_share_avg, 4) AS NegativityAvg
    ,fcp_stats.negativity_viewer_share_avg_decile as NegativityAvgRank
    ,round(fcp_stats.negativity_viewer_share_avg_median, 4) AS NegativityAvgMedian
    ,case when NegativityAvgMedian = 0 then null else round(NegativityAvg / NegativityAvgMedian - 1, 3) end AS NegativityAvgDiff
    --NEL4 COMPATIBILITY SCORES---------------------------------------   
    ,round(fcp_stats.neutral_eyeson_viewer_share_avg, 4) as NeutralAttentionAvg
    ,fcp_stats.neutral_eyeson_viewer_share_avg_decile as NeutralAttentionAvgRank
    ,round(fcp_stats.neutral_eyeson_viewer_share_avg_median, 4) as NeutralAttentionAvgMedian
    ,case when NeutralAttentionAvgMedian = 0 then null else round(NeutralAttentionAvg / NeutralAttentionAvgMedian - 1, 3) end AS NeutralAttentionAvgDiff
    ,round(fcp_stats.inattentive_visible_viewer_share_avg, 4) as DistractionAvg
    ,fcp_stats.inattentive_visible_viewer_share_avg_decile as DistractionAvgRank
    ,round(fcp_stats.inattentive_visible_viewer_share_avg_median, 4) as DistractionAvgMedian
    ,case when DistractionAvgMedian = 0 then null else round(DistractionAvg / DistractionAvgMedian - 1, 3) end AS DistractionAvgDiff
    --AI COMPATIBILITY SCORES--------------------------------------- 
    ,round(fcp_stats.eyeson_viewer_share_max , 4) AS AttentionPeak
    ,fcp_stats.eyeson_viewer_share_max_decile as AttentionPeakRank
    ,round(fcp_stats.eyeson_viewer_share_max_median, 4) as AttentionPeakMedian
    ,round(fcp_stats.happiness_viewer_share_avg, 4) AS HappyAvg
    ,fcp_stats.happiness_viewer_share_avg_decile as HappyAvgRank
    ,round(fcp_stats.happiness_viewer_share_avg_median, 4) AS HappyAvgMedian
    ,round(fcp_stats.surprise_viewer_share_avg, 4) AS SurpriseAvg
    ,fcp_stats.surprise_viewer_share_avg_decile as SurpriseAvgRank
    ,round(fcp_stats.surprise_viewer_share_avg_median, 4) AS SurpriseAvgMedian
    ,round(fcp_stats.confusion_viewer_share_avg, 4) AS ConfusionAvg
    ,fcp_stats.confusion_viewer_share_avg_decile as ConfusionAvgRank
    ,round(fcp_stats.confusion_viewer_share_avg_median, 4) AS ConfusionAvgMedian
    ,round(fcp_stats.contempt_viewer_share_avg, 4) AS ContemptAvg
    ,fcp_stats.contempt_viewer_share_avg_decile as ContemptAvgRank
    ,round(fcp_stats.contempt_viewer_share_avg_median, 4) AS ContemptAvgMedian
    ,round(fcp_stats.disgust_viewer_share_avg, 4) AS DisgustAvg
    ,fcp_stats.disgust_viewer_share_avg_decile as DisgustAvgRank
    ,round(fcp_stats.disgust_viewer_share_avg_median, 4) AS DisgustAvgMedian
    --------------------------------------------------------
    ,mediasegments.country AS Country
    ,mediasegments.country_code AS Country_code -- Country !! 3 letters code
    ,mediasegments.geographic_region AS GeographicRegion
    ,mediasegments.SourceMediaThumbnailFileName -- Media thumbnail file
    ,case when mediasegments.SourceMediaConvertedFileName is null then null else concat('thumbstrip/', substring(mediasegments.SourceMediaConvertedFileName, 11, 36), '/batch=0.png') end AS SourceMediaThumbstripFileName
    ,mediasegments.SourceMediaConvertedFileName
    ,mediasegments.sourcemedia AS ParentCreative-- Creative
    ,CONCAT(mediasegments.sourcemedia,case when adsetaccount_order.accountid = 1889 then REGEXP_EXTRACT(adsetaccount_order.Name, 'Kantar pilot(.*?) - Import', 1) else '' end)  AS SourceMedia  -- Creative Version
    ,mediasegments.Format AS SourceMediaType
    ,mediasegments.duration AS Duration
    ,mediasegments.Date_of_test AS CreationDate -- Date Tested
    ,mediasegments.BrandName as Brand
    ,mediasegments.BrandID
    ,mediasegments.BrandLogoFileName -- Brand logo file
    ,mediasegments.TopCategory -- Industry (top level) 
    ,mediasegments.MidCategory -- Industry (mid level)
    ,mediasegments.SubCategory -- Industry (sub level)
    ,e2_formats.Platform AS Platform
    ,mediasegments.TestDevice as Device
    ,e2_formats.ad_format AS Adformat
    ,CONCAT(e2_formats.name , ' (', e2_formats.environment_category, ')')   AS AdformatText,
    CASE WHEN proj.IsEnabledForClients is null THEN true ELSE proj.IsEnabledForClients END AS IsEnabledForClients,
    proj.ID as PDProjectID,
    case when coalesce(proj.SurveyExternalKey,'')!='' then proj.SurveyExternalKey else proj.Alias end as SurveyKeyAlias
FROM StudyDatabaseReadonly.StudyDatabase.dbo."vw_AdSet_TestElement_Account" adsetaccount_order
JOIN StudyDatabaseReadonly.StudyDatabase.dbo.AdSetType adsettype on adsettype.ID=adsetaccount_order.AdSetTypeID 
left join StudyDatabaseReadonly.StudyDatabase.dbo.AdSetAttribute aaVisible on aaVisible.AdSetID= adsetaccount_order.AdSetID and aaVisible.Key='IsVisible'
left join custom_adsets custom_adsets on custom_adsets.AccountID=adsetaccount_order.AccountID and custom_adsets.TestID=adsetaccount_order.TestID and custom_adsets.SourceMediaID=adsetaccount_order.SourceMediaID
JOIN mediasegments ON adsetaccount_order.sourcemediaid = mediasegments.sourcemediaid and adsetaccount_order.testid = mediasegments.testid
left join virtual.data.InContextPoC.eyesquare_mapping e2_formats on e2_formats.ad_format = 'realeyesInFocus' 
JOIN fcp on adsetaccount_order.sourcemediaid = fcp.sourcemediaid and adsetaccount_order.testid = fcp.testid
left join StudyDatabaseReadonly.StudyDatabase.dbo.DataCollectionStructure as dcs on adsetaccount_order.testid = dcs.testid
left join StudyDatabaseReadonly.StudyDatabase.dbo.Project proj on proj.alias = dcs.externalkey
JOIN fcp_stats on adsetaccount_order.sourcemediaid = fcp_stats.sourcemediaid and adsetaccount_order.testid = fcp_stats.testid
    and fcp.segment_key = fcp_stats.segment_key
WHERE NOT EXISTS(select m.sourcemedia_id, m.test_id from virtual.data.InContextPoC.mediaMapping m where m.sourcemedia_id = adsetaccount_order.sourcemediaid and m.test_id = adsetaccount_order.testid)
and COALESCE(aaVisible."Value",'true')='true'
and adsetaccount_order.AdSetTypeID in (1,2)
-- maybe add anti join to mapping table to explicitely exclude all ICIF adds