{"sql": "creative_performance_portfolio_grid.sql", "depends": ["fcp_postgre.public.source_media_custom_norm", "StudyDatabaseReadonly.StudyDatabase.dbo.vw_AdSet_TestElement_Account", "virtual.data.InContextPoC.eyesquare_mapping", "StudyDatabaseReadonly.StudyDatabase.dbo.vw_PreView_MediaSegments", "StudyDatabaseReadonly.StudyDatabase.dbo.AdSetType", "StudyDatabaseReadonly.StudyDatabase.dbo.MediaStorage", "StudyDatabaseReadonly.StudyDatabase.dbo.SourceMedia", "virtual.data.preview.main.preview_subscores.benchmarked_counts", "StudyDatabaseReadonly.StudyDatabase.dbo.AdSetAttribute", "virtual.data.preview.main.preview_subscores.benchmarked_stats", "fcp_postgre.public.feature", "virtual.data.InContextPoC.mediaMappingWithAdSet", "fcp_postgre.public.custom_norm", "StudyDatabaseReadonly.StudyDatabase.dbo.BrandLogo", "StudyDatabaseReadonly.StudyDatabase.dbo.PreViewDisplaySettings", "virtual.data.preview.main.preview_subscores.benchmarked_confirmit"], "reflections": [{"name": "Raw Reflection", "type": "RAW", "sortFields": ["AccountID", "SourceMediaID"], "displayFields": ["creative_id", "PDProjectID", "SurveyKeyAlias", "OrderAdSetID", "OrderExternalKey", "Order", "OrderType", "CustomAdSets", "SourceMediaExternalKey", "Account", "AccountID", "ProjectID", "IsEnabledForClients", "SubjectGroupID", "IsForcedExposure", "SourceMediaID", "TestID", "TaskID", "InFocusTestID", "AttentionNormCustomNormFiltersIC", "AttentionNormRegionIC", "AttentionNormEnvironmentCategoryIC", "AttentionNormAdFormatIC", "AttentionNormDeviceIC", "AttentionNormIsForcedExposureIC", "AttentionNormIsSkippableIC", "AttentionNormFormatIC", "AttentionNormDurationIC", "AttentionNormSampleSizeIC", "AttentionNormRefreshDateIC", "AttentionNormAdFormatNameIC", "ReactionsNormCustomNormFiltersIC", "ReactionsNormRegionIC", "ReactionsNormEnvironmentCategoryIC", "ReactionsNormAdFormatIC", "ReactionsNormDeviceIC", "ReactionsNormIsForcedExposureIC", "ReactionsNormIsSkippableIC", "ReactionsNormFormatIC", "ReactionsNormDurationIC", "ReactionsNormSampleSizeIC", "ReactionsNormRefreshDateIC", "ReactionsNormAdFormatNameIC", "SurveyNormCustomNormFiltersIC", "SurveyNormRegionIC", "SurveyNormEnvironmentCategoryIC", "SurveyNormAdFormatIC", "SurveyNormDeviceIC", "SurveyNormIsForcedExposureIC", "SurveyNormIsSkippableIC", "SurveyNormFormatIC", "SurveyNormSampleSizeIC", "SurveyNormRefreshDateIC", "SurveyNormAdFormatNameIC", "AttentionNormCustomNormFiltersIF", "AttentionNormRegionIF", "AttentionNormEnvironmentCategoryIF", "AttentionNormAdFormatIF", "AttentionNormDeviceIF", "AttentionNormIsForcedExposureIF", "AttentionNormIsSkippableIF", "AttentionNormFormatIF", "AttentionNormDurationIF", "AttentionNormSampleSizeIF", "AttentionNormRefreshDateIF", "AttentionNormAdFormatNameIF", "ReactionsNormCustomNormFiltersIF", "ReactionsNormRegionIF", "ReactionsNormEnvironmentCategoryIF", "ReactionsNormAdFormatIF", "ReactionsNormDeviceIF", "ReactionsNormIsForcedExposureIF", "ReactionsNormIsSkippableIF", "ReactionsNormFormatIF", "ReactionsNormDurationIF", "ReactionsNormSampleSizeIF", "ReactionsNormRefreshDateIF", "ReactionsNormAdFormatNameIF", "SurveyNormCustomNormFiltersIF", "SurveyNormRegionIF", "SurveyNormEnvironmentCategoryIF", "SurveyNormAdFormatIF", "SurveyNormDeviceIF", "SurveyNormIsForcedExposureIF", "SurveyNormIsSkippableIF", "SurveyNormFormatIF", "SurveyNormSampleSizeIF", "SurveyNormRefreshDateIF", "SurveyNormAdFormatNameIF", "SegmentKey", "Views", "NormFallbackIC", "NormFallbackIF", "NormFallbackSurveyIC", "NormFallbackSurveyIF", "NormSegmentKeyIC", "NormSegmentKeyIF", "NormSegmentKeySurveyIC", "NormSegmentKeySurveyIF", "norm_sample_size", "survey_norm_sample_size", "QualityScore", "QualityScore_index", "BrandRecognition", "BrandRecognitionRank", "BrandRecognitionMedian", "BrandRecognitionDiff", "AdLikeability", "AdLikeabilityRank", "AdLikeabilityMedian", "AdLikeabilityDiff", "AdRecognition", "AdRecognitionRank", "AdRecognitionMedian", "AdRecognitionDiff", "BrandTrust", "BrandTrustRank", "BrandTrustMedian", "BrandTrustDiff", "Persuasion", "PersuasionRank", "PersuasionMedian", "PersuasionDiff", "AttentiveSeconds", "AttentiveSecondsRank", "AttentiveSecondsMedian", "AttentiveSecondsDiff", "AttentiveSecondsVTR", "AttentiveSecondsVTRRank", "AttentiveSecondsVTRMedian", "AttentiveSecondsVTRDiff", "VTR", "VTRRank", "VTRMedian", "VTRDiff", "Playback<PERSON><PERSON><PERSON><PERSON>", "PlaybackSecondsRank", "PlaybackSecondsMedian", "PlaybackSecondsDiff", "AttentionAvgIC", "AttentionAvgICRank", "AttentionAvgICMedian", "AttentionAvgICDiff", "NeutralAttentionAvgIC", "NeutralAttentionAvgICRank", "NeutralAttentionAvgICMedian", "NeutralAttentionAvgICDiff", "DistractionAvgIC", "DistractionAvgICRank", "DistractionAvgICMedian", "DistractionAvgICDiff", "ReactionsIC", "ReactionsICRank", "ReactionsICMedian", "ReactionsICDiff", "HappyPeakIC", "HappyPeakICRank", "HappyPeakICMedian", "HappyPeakICDiff", "SurprisePeakIC", "SurprisePeakICRank", "SurprisePeakICMedian", "SurprisePeakICDiff", "ConfusionPeakIC", "ConfusionPeakICRank", "ConfusionPeakICMedian", "ConfusionPeakICDiff", "ContemptPeakIC", "ContemptPeakICRank", "ContemptPeakICMedian", "ContemptPeakICDiff", "DisgustPeakIC", "DisgustPeakICRank", "DisgustPeakICMedian", "DisgustPeakICDiff", "NegativityAvgIC", "NegativityAvgICRank", "NegativityAvgICMedian", "NegativityAvgICDiff", "AttentiveSecondsIF", "AttentiveSecondsIFRank", "AttentiveSecondsIFMedian", "AttentiveSecondsIFDiff", "AttentiveSecondsVTRIF", "AttentiveSecondsVTRIFRank", "AttentiveSecondsVTRIFMedian", "AttentiveSecondsVTRIFDiff", "VTRIF", "VTRIFRank", "VTRIFMedian", "VTRIFDiff", "PlaybackSecondsIF", "PlaybackSecondsIFRank", "PlaybackSecondsIFMedian", "PlaybackSecondsIFDiff", "AttentionAvgIF", "AttentionAvgIFRank", "AttentionAvgIFMedian", "AttentionAvgIFDiff", "NeutralAttentionAvgIF", "NeutralAttentionAvgIFRank", "NeutralAttentionAvgIFMedian", "NeutralAttentionAvgIFDiff", "DistractionAvgIF", "DistractionAvgIFRank", "DistractionAvgIFMedian", "DistractionAvgIFDiff", "Reactions", "ReactionsRank", "ReactionsMedian", "ReactionsDiff", "HappyPeak", "HappyPeakRank", "HappyPeakMedian", "HappyPeakDiff", "SurprisePeak", "SurprisePeakRank", "SurprisePeakMedian", "SurprisePeakDiff", "ConfusionPeak", "ConfusionPeakRank", "ConfusionPeakMedian", "ConfusionPeakDiff", "ContemptPeak", "ContemptPeakRank", "ContemptPeakMedian", "ContemptPeakDiff", "DisgustPeak", "DisgustPeakRank", "DisgustPeakMedian", "DisgustPeakDiff", "NegativityAvgIF", "NegativityAvgIFRank", "NegativityAvgIFMedian", "NegativityAvgIFDiff", "Country", "Country_code", "GeographicRegion", "SourceMediaThumbnailFileName", "SourceMediaThumbstripFileName", "SourceMediaConvertedFileName", "ParentCreative", "SourceMedia", "SourceMediaType", "Duration", "CreationDate", "Brand", "BrandID", "BrandLogoFileName", "TopCategory", "MidCategory", "SubCategory", "Platform", "<PERSON><PERSON>", "Adformat", "AdformatText", "AdformatName", "EnvironmentCategory", "AdformatTextIF"]}]}