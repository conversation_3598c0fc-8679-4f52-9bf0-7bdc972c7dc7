{"sql": "creative_performance_account_media_segments.sql", "depends": ["virtual.data.InContextPoC.mediaMapping", "virtual.data.preview.app.creative_performance_media_segments", "virtual.data.preview.main.preview_account_studies_latest"], "reflections": [{"name": "Raw Reflection", "type": "RAW", "sortFields": ["AccountID"], "displayFields": ["ProductType", "AccountID", "SegmentKey", "Question", "<PERSON><PERSON><PERSON>", "QuestionOrder", "Answer", "<PERSON><PERSON><PERSON>", "AnswerOrder"]}]}