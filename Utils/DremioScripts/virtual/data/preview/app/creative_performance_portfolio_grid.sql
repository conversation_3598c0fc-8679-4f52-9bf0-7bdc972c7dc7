WITH preview_mediasegments AS (
    select distinct sourcemediaid, topcategoryname, midcategoryname, subcategoryname 
    from StudyDatabaseReadonly.StudyDatabase.dbo.vw_PreView_MediaSegments
),
mediasegments AS (
    select DISTINCT pvm.sourcemediaid, topcategoryname, midcategoryname, subcategoryname,
    thumbnail_mediastorage.ThumbnailFileName, thumbnail_mediastorage.ConvertedFileName ThumbnailConvertedFileName,
    brandlogo_mediastorage.ConvertedFileName BrandLogoConvertedFileName
    from preview_mediasegments pvm
    JOIN StudyDatabaseReadonly.StudyDatabase.dbo.Sourcemedia AS sourcemedia ON pvm.sourcemediaid = sourcemedia.ID
    JOIN StudyDatabaseReadonly.StudyDatabase.dbo.MediaStorage thumbnail_mediastorage ON thumbnail_mediastorage.ID=sourcemedia.MediaStorageID
    JOIN studyDatabaseReadonly.StudyDatabase.dbo.BrandLogo AS brandlogo ON sourcemedia.BrandID = brandlogo.BrandId
    JOIN StudyDatabaseReadonly.StudyDatabase.dbo.MediaStorage brandlogo_mediastorage ON brandlogo_mediastorage.ID=brandlogo.MediaStorageID
    WHERE brandlogo.countryId IS NULL -- To remove duplicates, functionality very rarely used
),
custom_adsets as (
    select adsetaccount.AccountID,adsetaccount.SourceMediaID,adsetaccount.TestID,LISTAGG(adsetaccount.Name,',') CustomAdSets from StudyDatabaseReadonly.StudyDatabase.dbo."vw_AdSet_TestElement_Account" adsetaccount
    where adsetaccount.AdSetTypeID=3
    group by adsetaccount.AccountID,adsetaccount.SourceMediaID,adsetaccount.TestID
),
default_custom_norms_base as (
    select distinct 
    case when f.name = 'mean_counts-media_duration-classifiers-creative' then 'fcp'
    when f.name ='stats-media_duration-classifiers-creative' then 'fcp_stats'
    when f.name='confirmit-media-survey-creative' then 'confirmit'
    when f.name='mean-media_time-classifiers-creative' then 'curves'
    else NULL
    end as feature, smcn.source_media_id,smcn.test_id,smcn.custom_norm_id,smcn.is_default,smcn.fallback,cn.filters from 
    "fcp_postgre".public."source_media_custom_norm" smcn 
    join "fcp_postgre".public.custom_norm cn on cn.id = smcn.custom_norm_id
    join "fcp_postgre".public.feature f on f.id = smcn.feature_id
    where f.name in (
    'mean_counts-media_duration-classifiers-creative',
    'stats-media_duration-classifiers-creative',
    'confirmit-media-survey-creative',
    'mean-media_time-classifiers-creative'
    )
),
fcp AS (
    select cn_fcp.is_default as custom_norm_is_default,cn_fcp.fallback custom_norm_fallback, cn_fcp.filters custom_norm_filters, fcp.* 
    from virtual.data.preview.main."preview_subscores"."benchmarked_counts" fcp
    join default_custom_norms_base cn_fcp on cn_fcp.source_media_id=fcp.sourcemediaid and cn_fcp.test_id=fcp.testid and cn_fcp.feature='fcp' and fcp.custom_norm_id=cn_fcp.custom_norm_id
    where cn_fcp.is_default=1
    UNION ALL
    select null as custom_norm_is_default,null as custom_norm_fallback, null as custom_norm_filters, fcp.* 
    from virtual.data.preview.main."preview_subscores"."benchmarked_counts" fcp
    where fcp.custom_norm_id is null
    and not exists (select null from default_custom_norms_base cn_fcp where cn_fcp.source_media_id=fcp.sourcemediaid and cn_fcp.test_id=fcp.testid and cn_fcp.feature='fcp' and cn_fcp.is_default=1)
),
fcp_stats AS (
    select cn_fcp_stats.is_default as custom_norm_is_default,cn_fcp_stats.fallback custom_norm_fallback, cn_fcp_stats.filters custom_norm_filters,fcp_stats.* 
    from virtual.data.preview.main."preview_subscores"."benchmarked_stats" fcp_stats
    join default_custom_norms_base cn_fcp_stats on cn_fcp_stats.source_media_id=fcp_stats.sourcemediaid and cn_fcp_stats.test_id=fcp_stats.testid and cn_fcp_stats.feature='fcp_stats' and fcp_stats.custom_norm_id=cn_fcp_stats.custom_norm_id
    where cn_fcp_stats.is_default=1
    UNION ALL
    select null as custom_norm_is_default,null as custom_norm_fallback, null as custom_norm_filters,fcp_stats.* 
    from virtual.data.preview.main."preview_subscores"."benchmarked_stats" fcp_stats    
    where fcp_stats.custom_norm_id is null
    and not exists (select null from default_custom_norms_base cn_fcp_stats where cn_fcp_stats.source_media_id=fcp_stats.sourcemediaid and cn_fcp_stats.test_id=fcp_stats.testid and cn_fcp_stats.feature='fcp_stats' and cn_fcp_stats.is_default=1)
),
fcp_confirmit AS (
    select cn_fcp_confirmit.is_default as custom_norm_is_default,cn_fcp_confirmit.fallback custom_norm_fallback, cn_fcp_confirmit.filters custom_norm_filters,fcp_confirmit.* 
    from virtual.data.preview.main."preview_subscores"."benchmarked_confirmit" fcp_confirmit
    join default_custom_norms_base cn_fcp_confirmit on cn_fcp_confirmit.source_media_id=fcp_confirmit.sourcemediaid and cn_fcp_confirmit.test_id=fcp_confirmit.testid and cn_fcp_confirmit.feature='confirmit' and fcp_confirmit.custom_norm_id=cn_fcp_confirmit.custom_norm_id
    where cn_fcp_confirmit.is_default=1
    UNION ALL
    select null as custom_norm_is_default,null as custom_norm_fallback, null as custom_norm_filters,fcp_confirmit.* 
    from virtual.data.preview.main."preview_subscores"."benchmarked_confirmit" fcp_confirmit    
    where fcp_confirmit.custom_norm_id is null
    and not exists (select null from default_custom_norms_base cn_fcp_confirmit where cn_fcp_confirmit.source_media_id=fcp_confirmit.sourcemediaid and cn_fcp_confirmit.test_id=fcp_confirmit.testid and cn_fcp_confirmit.feature='confirmit' and cn_fcp_confirmit.is_default=1)
)
SELECT DISTINCT
    CONCAT( CAST(mapping.test_id AS CHAR), '/', 
            CAST(mapping.sourcemedia_id AS CHAR), '/', 
            CAST(mapping.task_id AS CHAR) ) AS creative_id
    ,mapping.PDProjectID
    ,mapping.SurveyKeyAlias       
    ,mapping.AdSetID as OrderAdSetID
    ,mapping.AdSetExternalKey as OrderExternalKey
    ,mapping.AdSetName As "Order"
    ,adsettype.Name as OrderType
    ,custom_adsets.CustomAdSets
    ,mapping.SourceMediaExternalKey        
    ,mapping.account As Account
    ,mapping.account_id AS AccountID
    ,mapping.project_id AS ProjectID
    ,mapping.is_enabled_for_clients AS IsEnabledForClients
    ,mapping.subjectgroup_id AS SubjectGroupID
    ,mapping.is_forced_exposure AS IsForcedExposure
    ,mapping.sourcemedia_id AS SourceMediaID
    ,mapping.test_id AS TestID
    ,mapping.task_id AS TaskID
    ,mapping.infocus_test_id AS InFocusTestID

    ,fcpIC.custom_norm_filters as AttentionNormCustomNormFiltersIC
    ,fcpIC.region AttentionNormRegionIC
    ,fcpIC.environment_category AttentionNormEnvironmentCategoryIC
    ,fcpIC.ad_format AttentionNormAdFormatIC
    ,fcpIC.device AttentionNormDeviceIC
    ,fcpIC.is_forced_exposure AttentionNormIsForcedExposureIC
    ,fcpIC.is_skippable as AttentionNormIsSkippableIC
    ,fcpIC.format as AttentionNormFormatIC
    ,fcpIC.norm_duration as AttentionNormDurationIC
    ,fcpIC.real_sample_size as AttentionNormSampleSizeIC
    ,fcpIC.norm_create_date as AttentionNormRefreshDateIC
    ,CASE WHEN fcpIC.ad_format='all' then fcpIC.ad_format ELSE
        CASE WHEN fcpICformat.ad_format = 'youtubePreroll' THEN CONCAT('Youtube', case when fcpIC.is_skippable then ' skippable ' else ' non-skippable ' end, 'preroll ad') 
        WHEN fcpICformat.name IS NOT NULL THEN fcpICformat.name        
        ELSE 'Unknown' 
        END
    END AS AttentionNormAdFormatNameIC    

    ,fcp_statsIC.custom_norm_filters as ReactionsNormCustomNormFiltersIC
    ,fcp_statsIC.region ReactionsNormRegionIC
    ,fcp_statsIC.environment_category ReactionsNormEnvironmentCategoryIC
    ,fcp_statsIC.ad_format ReactionsNormAdFormatIC
    ,fcp_statsIC.device ReactionsNormDeviceIC
    ,fcp_statsIC.is_forced_exposure ReactionsNormIsForcedExposureIC
    ,fcp_statsIC.is_skippable as ReactionsNormIsSkippableIC
    ,fcp_statsIC.format as ReactionsNormFormatIC
    ,fcp_statsIC.norm_duration as ReactionsNormDurationIC
    ,fcp_statsIC.real_sample_size as ReactionsNormSampleSizeIC
    ,fcp_statsIC.norm_create_date as ReactionsNormRefreshDateIC
    ,CASE WHEN fcp_statsIC.ad_format='all' then fcp_statsIC.ad_format ELSE
        CASE WHEN fcp_statsICformat.ad_format = 'youtubePreroll' THEN CONCAT('Youtube', case when fcp_statsIC.is_skippable then ' skippable ' else ' non-skippable ' end, 'preroll ad') 
        WHEN fcp_statsICformat.name IS NOT NULL THEN fcp_statsICformat.name        
        ELSE 'Unknown' 
        END
    END AS ReactionsNormAdFormatNameIC  

    ,fcp_confirmitIC.custom_norm_filters as SurveyNormCustomNormFiltersIC
    ,fcp_confirmitIC.region SurveyNormRegionIC
    ,fcp_confirmitIC.environment_category SurveyNormEnvironmentCategoryIC
    ,fcp_confirmitIC.ad_format SurveyNormAdFormatIC
    ,fcp_confirmitIC.device SurveyNormDeviceIC
    ,fcp_confirmitIC.is_forced_exposure SurveyNormIsForcedExposureIC
    ,fcp_confirmitIC.is_skippable as SurveyNormIsSkippableIC
    ,fcp_confirmitIC.format as SurveyNormFormatIC    
    ,fcp_confirmitIC.real_sample_size as SurveyNormSampleSizeIC
    ,fcp_confirmitIC.norm_create_date as SurveyNormRefreshDateIC
    ,CASE WHEN fcp_confirmitIC.ad_format='all' then fcp_confirmitIC.ad_format ELSE
        CASE WHEN fcp_confirmitICformat.ad_format = 'youtubePreroll' THEN CONCAT('Youtube', case when fcp_confirmitIC.is_skippable then ' skippable ' else ' non-skippable ' end, 'preroll ad') 
        WHEN fcp_confirmitICformat.name IS NOT NULL THEN fcp_confirmitICformat.name        
        ELSE 'Unknown' 
        END
    END AS SurveyNormAdFormatNameIC  

    ,fcpIF.custom_norm_filters as AttentionNormCustomNormFiltersIF
    ,fcpIF.region AttentionNormRegionIF
    ,fcpIF.environment_category AttentionNormEnvironmentCategoryIF
    ,fcpIF.ad_format AttentionNormAdFormatIF
    ,fcpIF.device AttentionNormDeviceIF
    ,fcpIF.is_forced_exposure AttentionNormIsForcedExposureIF
    ,fcpIF.is_skippable as AttentionNormIsSkippableIF
    ,fcpIF.format as AttentionNormFormatIF
    ,fcpIF.norm_duration as AttentionNormDurationIF
    ,fcpIF.real_sample_size as AttentionNormSampleSizeIF
    ,fcpIF.norm_create_date as AttentionNormRefreshDateIF
    ,'Realeyes Video Player' as AttentionNormAdFormatNameIF    

    ,fcp_statsIF.custom_norm_filters as ReactionsNormCustomNormFiltersIF
    ,fcp_statsIF.region ReactionsNormRegionIF
    ,fcp_statsIF.environment_category ReactionsNormEnvironmentCategoryIF
    ,fcp_statsIF.ad_format ReactionsNormAdFormatIF
    ,fcp_statsIF.device ReactionsNormDeviceIF
    ,fcp_statsIF.is_forced_exposure ReactionsNormIsForcedExposureIF
    ,fcp_statsIF.is_skippable as ReactionsNormIsSkippableIF
    ,fcp_statsIF.format as ReactionsNormFormatIF
    ,fcp_statsIF.norm_duration as ReactionsNormDurationIF
    ,fcp_statsIF.real_sample_size as ReactionsNormSampleSizeIF
    ,fcp_statsIF.norm_create_date as ReactionsNormRefreshDateIF
    ,'Realeyes Video Player' as ReactionsNormAdFormatNameIF    

    ,fcp_confirmitIF.custom_norm_filters as SurveyNormCustomNormFiltersIF
    ,fcp_confirmitIF.region SurveyNormRegionIF
    ,fcp_confirmitIF.environment_category SurveyNormEnvironmentCategoryIF
    ,fcp_confirmitIF.ad_format SurveyNormAdFormatIF
    ,fcp_confirmitIF.device SurveyNormDeviceIF
    ,fcp_confirmitIF.is_forced_exposure SurveyNormIsForcedExposureIF
    ,fcp_confirmitIF.is_skippable as SurveyNormIsSkippableIF
    ,fcp_confirmitIF.format as SurveyNormFormatIF    
    ,fcp_confirmitIF.real_sample_size as SurveyNormSampleSizeIF
    ,fcp_confirmitIF.norm_create_date as SurveyNormRefreshDateIF
    ,'Realeyes Video Player' as SurveyNormAdFormatNameIF    

    --SCORE METADATA---------------------------------------
    ,fcpIC.segment_key AS SegmentKey
    ,fcpIC.views AS Views
    --NORM FALLBACK FLAGS
    ,fcpIC.fallback as NormFallbackIC -- Fallback level for IC attention & emotion scores 
    ,fcpIF.fallback as NormFallbackIF -- Fallback level for IF attention & emotion scores 
    ,fcp_confirmitIC.fallback as NormFallbackSurveyIC -- Fallback level for IC survey scores (Brand Recognition, Ad Recognition)
    ,fcp_confirmitIF.fallback as NormFallbackSurveyIF -- Fallback level for IF survey scores (Ad Likeability, Brand Trust, Persuasion)  --- NOTE: Placeholder to define column name! Change to actual source later
    ,fcpIC.norm_segment_key as NormSegmentKeyIC -- Fallback for IC attention & emotion scores - If equal to "SegmentKey", segmented norms are used. If equal to "all", fallback to full audience
    ,fcpIF.norm_segment_key as NormSegmentKeyIF -- Fallback for IF attention & emotion scores
    ,fcp_confirmitIC.norm_segment_key as NormSegmentKeySurveyIC -- Fallback for IC survey scores (Brand Recognition, Ad Recognition)
    ,fcp_confirmitIF.norm_segment_key as NormSegmentKeySurveyIF -- Fallback for IF survey scores (Ad Likeability, Brand Trust, Persuasion)  --- NOTE: Placeholder to define column name! Change to actual source later
    -- TODO: add sample sizes of IC and IF norms???
    ,fcpIC.real_sample_size as norm_sample_size
    ,fcp_confirmitIC.real_sample_size as survey_norm_sample_size

    ,round((fcpIC.eyeson_seconds_avg_decile + fcpIC.attentive_viewthrough_avg_decile + fcp_statsIF.reactions_viewer_share_avg_decile + fcp_statsIF.happiness_viewer_share_max_decile
        + fcp_confirmitIC.br_recognised_decile + fcp_confirmitIF.ad_liking_decile)/6.0*10, 1) AS QualityScore
    ,case when QualityScore is null or QualityScore < 40 then -1 when QualityScore > 70 then 1 else 0 end AS QualityScore_index 
    --SURVEY SCORES---------------------------------------
    ,round(fcp_confirmitIC.br_recognised, 4) AS BrandRecognition
    ,fcp_confirmitIC.br_recognised_decile as BrandRecognitionRank
    ,round(fcp_confirmitIC.br_recognised_median, 4) AS BrandRecognitionMedian
    ,case when BrandRecognitionMedian = 0 then null else round(BrandRecognition / BrandRecognitionMedian - 1, 3) end AS BrandRecognitionDiff
    ,round(fcp_confirmitIF.ad_liking, 2) AS AdLikeability
    ,fcp_confirmitIF.ad_liking_decile as AdLikeabilityRank
    ,round(fcp_confirmitIF.ad_liking_median, 2) AS AdLikeabilityMedian
    ,case when AdLikeabilityMedian = 0 then null else round(AdLikeability / AdLikeabilityMedian - 1, 3) end AS AdLikeabilityDiff
    ,round(fcp_confirmitIC.ar_recognised, 4) AS AdRecognition
    ,fcp_confirmitIC.ar_recognised_decile as AdRecognitionRank
    ,round(fcp_confirmitIC.ar_recognised_median, 4) AS AdRecognitionMedian
    ,case when AdRecognitionMedian = 0 then null else round(AdRecognition / AdRecognitionMedian - 1, 3) end AS AdRecognitionDiff
    ,round(fcp_confirmitIF.brand_trust, 2) AS BrandTrust
    ,fcp_confirmitIF.brand_trust_decile as BrandTrustRank
    ,round(fcp_confirmitIF.brand_trust_median, 2) AS BrandTrustMedian
    ,case when BrandTrustMedian = 0 then null else round(BrandTrust / BrandTrustMedian - 1, 3) end AS BrandTrustDiff
    ,round(fcp_confirmitIF.percentage_with_persuasion, 4) AS Persuasion
    ,fcp_confirmitIF.percentage_with_persuasion_decile as PersuasionRank
    ,round(fcp_confirmitIF.percentage_with_persuasion_median, 4) AS PersuasionMedian
    ,case when PersuasionMedian = 0 then null else round(Persuasion / PersuasionMedian - 1, 3) end AS PersuasionDiff
    --IN-CONTEXT SCORES---------------------------------------
    ,round(fcpIC.eyeson_seconds_avg, 2) AS AttentiveSeconds -- AttentiveSecondsIC
    ,fcpIC.eyeson_seconds_avg_decile as AttentiveSecondsRank -- AttentiveSecondsICRank
    ,round(fcpIC.eyeson_seconds_avg_median, 2) as AttentiveSecondsMedian -- AttentiveSecondsICMedian / Norm
    ,case when AttentiveSecondsMedian = 0 then null else round(AttentiveSeconds / AttentiveSecondsMedian - 1, 3) end AS AttentiveSecondsDiff
    ,round(fcpIC.eyeson_viewthrough_avg, 4) AS AttentiveSecondsVTR  -- AttentiveVTRIC
    ,fcpIC.eyeson_viewthrough_avg_decile as AttentiveSecondsVTRRank -- AttentiveVTRICRank
    ,round(fcpIC.eyeson_viewthrough_avg_median, 4) as AttentiveSecondsVTRMedian  -- AttentiveVTRICMedian / Norm
    ,case when AttentiveSecondsVTRMedian = 0 then null else round(AttentiveSecondsVTR / AttentiveSecondsVTRMedian - 1, 3) end AS AttentiveSecondsVTRDiff
    ,round(fcpIC.viewthrough_avg, 4) AS VTR  -- VTRIC
    ,fcpIC.viewthrough_avg_decile as VTRRank -- VTRICRank
    ,round(fcpIC.viewthrough_avg_median, 4) as VTRMedian -- VTRICMedian / Norm
    ,case when VTRMedian = 0 then null else round(VTR / VTRMedian - 1, 3) end AS VTRDiff
    ,round(fcpIC.visible_seconds_avg, 2) AS PlaybackSeconds -- PlaybackSecondsIC
    ,fcpIC.visible_seconds_avg_decile as PlaybackSecondsRank -- PlaybackSecondsICRank
    ,round(fcpIC.visible_seconds_avg_median, 2) as PlaybackSecondsMedian -- PlaybackSecondsICMedian / Norm
    ,case when PlaybackSecondsMedian = 0 then null else round(PlaybackSeconds / PlaybackSecondsMedian - 1, 3) end AS PlaybackSecondsDiff
    ,round(fcp_statsIC.eyeson_viewer_share_avg, 4) AS AttentionAvgIC
    ,fcp_statsIC.eyeson_viewer_share_avg_decile as AttentionAvgICRank
    ,round(fcp_statsIC.eyeson_viewer_share_avg_median, 4) AS AttentionAvgICMedian
    ,case when AttentionAvgICMedian = 0 then null else round(AttentionAvgIC / AttentionAvgICMedian - 1, 3) end AS AttentionAvgICDiff
    ,round(fcp_statsIC.neutral_eyeson_viewer_share_avg, 4) as NeutralAttentionAvgIC
    ,fcp_statsIC.neutral_eyeson_viewer_share_avg_decile as NeutralAttentionAvgICRank
    ,round(fcp_statsIC.neutral_eyeson_viewer_share_avg_median, 4) as NeutralAttentionAvgICMedian    
    ,case when NeutralAttentionAvgICMedian = 0 then null else round(NeutralAttentionAvgIC / NeutralAttentionAvgICMedian - 1, 3) end AS NeutralAttentionAvgICDiff
    ,round(fcp_statsIC.inattentive_visible_viewer_share_avg, 4) AS DistractionAvgIC
    ,fcp_statsIC.inattentive_visible_viewer_share_avg_decile as DistractionAvgICRank
    ,round(fcp_statsIC.inattentive_visible_viewer_share_avg_median, 4) AS DistractionAvgICMedian
    ,case when DistractionAvgICMedian = 0 then null else round(DistractionAvgIC / DistractionAvgICMedian - 1, 3) end AS DistractionAvgICDiff
    ,round(fcp_statsIC.reactions_viewer_share_avg, 4) AS ReactionsIC
    ,fcp_statsIC.reactions_viewer_share_avg_decile as ReactionsICRank
    ,round(fcp_statsIC.reactions_viewer_share_avg_median, 4) AS ReactionsICMedian
    ,case when ReactionsICMedian = 0 then null else round(ReactionsIC / ReactionsICMedian - 1, 3) end AS ReactionsICDiff
    ,round(fcp_statsIC.happiness_viewer_share_max, 4) AS HappyPeakIC
    ,fcp_statsIC.happiness_viewer_share_max_decile as HappyPeakICRank
    ,round(fcp_statsIC.happiness_viewer_share_max_median, 4) AS HappyPeakICMedian
    ,case when HappyPeakICMedian = 0 then null else round(HappyPeakIC / HappyPeakICMedian - 1, 3) end AS HappyPeakICDiff
    ,round(fcp_statsIC.surprise_viewer_share_max, 4) AS SurprisePeakIC
    ,fcp_statsIC.surprise_viewer_share_max_decile as SurprisePeakICRank
    ,round(fcp_statsIC.surprise_viewer_share_max_median, 4) AS SurprisePeakICMedian
    ,case when SurprisePeakICMedian = 0 then null else round(SurprisePeakIC / SurprisePeakICMedian - 1, 3) end AS SurprisePeakICDiff
    ,round(fcp_statsIC.confusion_viewer_share_max, 4) AS ConfusionPeakIC
    ,fcp_statsIC.confusion_viewer_share_max_decile as ConfusionPeakICRank
    ,round(fcp_statsIC.confusion_viewer_share_max_median, 4) AS ConfusionPeakICMedian
    ,case when ConfusionPeakICMedian = 0 then null else round(ConfusionPeakIC / ConfusionPeakICMedian - 1, 3) end AS ConfusionPeakICDiff
    ,round(fcp_statsIC.contempt_viewer_share_max, 4) AS ContemptPeakIC
    ,fcp_statsIC.contempt_viewer_share_max_decile as ContemptPeakICRank
    ,round(fcp_statsIC.contempt_viewer_share_max_median, 4) AS ContemptPeakICMedian
    ,case when ContemptPeakICMedian = 0 then null else round(ContemptPeakIC / ContemptPeakICMedian - 1, 3) end AS ContemptPeakICDiff
    ,round(fcp_statsIC.disgust_viewer_share_max, 4) AS DisgustPeakIC
    ,fcp_statsIC.disgust_viewer_share_max_decile as DisgustPeakICRank
    ,round(fcp_statsIC.disgust_viewer_share_max_median, 4) AS DisgustPeakICMedian
        ,case when DisgustPeakICMedian = 0 then null else round(DisgustPeakIC / DisgustPeakICMedian - 1, 3) end AS DisgustPeakICDiff
    ,round(fcp_statsIC.negativity_viewer_share_avg, 4) AS NegativityAvgIC
    ,fcp_statsIC.negativity_viewer_share_avg_decile as NegativityAvgICRank
    ,round(fcp_statsIC.negativity_viewer_share_avg_median, 4) AS NegativityAvgICMedian
    ,case when NegativityAvgICMedian = 0 then null else round(NegativityAvgIC / NegativityAvgICMedian - 1, 3) end AS NegativityAvgICDiff
    --IN-FOCUS SCORES---------------------------------------
    ,round(fcpIF.eyeson_seconds_avg, 2) AS AttentiveSecondsIF
    ,fcpIF.eyeson_seconds_avg_decile as AttentiveSecondsIFRank
    ,round(fcpIF.eyeson_seconds_avg_median, 2) as AttentiveSecondsIFMedian
    ,case when AttentiveSecondsIFMedian = 0 then null else round(AttentiveSecondsIF / AttentiveSecondsIFMedian - 1, 3) end AS AttentiveSecondsIFDiff
    ,round(fcpIF.eyeson_viewthrough_avg, 4) AS AttentiveSecondsVTRIF
    ,fcpIF.eyeson_viewthrough_avg_decile as AttentiveSecondsVTRIFRank
    ,round(fcpIF.eyeson_viewthrough_avg_median, 4) as AttentiveSecondsVTRIFMedian
    ,case when AttentiveSecondsVTRIFMedian = 0 then null else round(AttentiveSecondsVTRIF / AttentiveSecondsVTRIFMedian - 1, 3) end AS AttentiveSecondsVTRIFDiff
    ,round(fcpIF.viewthrough_avg, 4) AS VTRIF
    ,fcpIF.viewthrough_avg_decile as VTRIFRank
    ,round(fcpIF.viewthrough_avg_median, 4) as VTRIFMedian
    ,case when VTRIFMedian = 0 then null else round(VTRIF / VTRIFMedian - 1, 3) end AS VTRIFDiff
    ,round(fcpIF.visible_seconds_avg, 2) AS PlaybackSecondsIF
    ,fcpIF.visible_seconds_avg_decile as PlaybackSecondsIFRank
    ,round(fcpIF.visible_seconds_avg_median, 2) as PlaybackSecondsIFMedian
    ,case when PlaybackSecondsIFMedian = 0 then null else round(PlaybackSecondsIF / PlaybackSecondsIFMedian - 1, 3) end AS PlaybackSecondsIFDiff
    ,round(fcp_statsIF.eyeson_viewer_share_avg, 4) AS AttentionAvgIF
    ,fcp_statsIF.eyeson_viewer_share_avg_decile as AttentionAvgIFRank
    ,round(fcp_statsIF.eyeson_viewer_share_avg_median, 4) AS AttentionAvgIFMedian
    ,case when AttentionAvgIFMedian = 0 then null else round(AttentionAvgIF / AttentionAvgIFMedian - 1, 3) end AS AttentionAvgIFDiff
    ,round(fcp_statsIF.neutral_eyeson_viewer_share_avg, 4) as NeutralAttentionAvgIF
    ,fcp_statsIF.neutral_eyeson_viewer_share_avg_decile as NeutralAttentionAvgIFRank
    ,round(fcp_statsIF.neutral_eyeson_viewer_share_avg_median, 4) as NeutralAttentionAvgIFMedian    
    ,case when NeutralAttentionAvgIFMedian = 0 then null else round(NeutralAttentionAvgIF / NeutralAttentionAvgIFMedian - 1, 3) end AS NeutralAttentionAvgIFDiff    
    ,round(fcp_statsIF.inattentive_visible_viewer_share_avg, 4) AS DistractionAvgIF
    ,fcp_statsIF.inattentive_visible_viewer_share_avg_decile as DistractionAvgIFRank
    ,round(fcp_statsIF.inattentive_visible_viewer_share_avg_median, 4) AS DistractionAvgIFMedian
    ,case when DistractionAvgIFMedian = 0 then null else round(DistractionAvgIF / DistractionAvgIFMedian - 1, 3) end AS DistractionAvgIFDiff
    ,round(fcp_statsIF.reactions_viewer_share_avg, 4) AS Reactions  -- ReactionsIF
    ,fcp_statsIF.reactions_viewer_share_avg_decile as ReactionsRank -- ReactionsIFRank
    ,round(fcp_statsIF.reactions_viewer_share_avg_median, 4) AS ReactionsMedian -- ReactionsIFMedian / Norm
    ,case when ReactionsMedian = 0 then null else round(Reactions / ReactionsMedian - 1, 3) end AS ReactionsDiff
    ,round(fcp_statsIF.happiness_viewer_share_max, 4) AS HappyPeak -- HappyPeakIF
    ,fcp_statsIF.happiness_viewer_share_max_decile as HappyPeakRank -- HappyPeakIFRank
    ,round(fcp_statsIF.happiness_viewer_share_max_median, 4) AS HappyPeakMedian -- HappyPeakIFMedian / Norm
    ,case when HappyPeakMedian = 0 then null else round(HappyPeak / HappyPeakMedian - 1, 3) end AS HappyPeakDiff
    ,round(fcp_statsIF.surprise_viewer_share_max, 4) AS SurprisePeak -- SurprisePeakIF
    ,fcp_statsIF.surprise_viewer_share_max_decile as SurprisePeakRank -- SurprisePeakIFRank
    ,round(fcp_statsIF.surprise_viewer_share_max_median, 4) AS SurprisePeakMedian -- SurprisePeakIFMedian / Norm
    ,case when SurprisePeakMedian = 0 then null else round(SurprisePeak / SurprisePeakMedian - 1, 3) end AS SurprisePeakDiff
    ,round(fcp_statsIF.confusion_viewer_share_max, 4) AS ConfusionPeak -- ConfusionPeakIF
    ,fcp_statsIF.confusion_viewer_share_max_decile as ConfusionPeakRank -- ConfusionPeakIFRank
    ,round(fcp_statsIF.confusion_viewer_share_max_median, 4) AS ConfusionPeakMedian -- ConfusionPeakIFMedian / Norm
    ,case when ConfusionPeakMedian = 0 then null else round(ConfusionPeak / ConfusionPeakMedian - 1, 3) end AS ConfusionPeakDiff
    ,round(fcp_statsIF.contempt_viewer_share_max, 4) AS ContemptPeak -- ContemptPeakIF
    ,fcp_statsIF.contempt_viewer_share_max_decile as ContemptPeakRank -- ContemptPeakIFRank
    ,round(fcp_statsIF.contempt_viewer_share_max_median, 4) AS ContemptPeakMedian -- ContemptPeakIFMedian / Norm
    ,case when ContemptPeakMedian = 0 then null else round(ContemptPeak / ContemptPeakMedian - 1, 3) end AS ContemptPeakDiff
    ,round(fcp_statsIF.disgust_viewer_share_max, 4) AS DisgustPeak -- DisgustPeakIF
    ,fcp_statsIF.disgust_viewer_share_max_decile as DisgustPeakRank -- DisgustPeakIFRank
    ,round(fcp_statsIF.disgust_viewer_share_max_median, 4) AS DisgustPeakMedian -- DisgustPeakIFMedian / Norm
    ,case when DisgustPeakMedian = 0 then null else round(DisgustPeak / DisgustPeakMedian - 1, 3) end AS DisgustPeakDiff
    ,round(fcp_statsIF.negativity_viewer_share_avg, 4) AS NegativityAvgIF
    ,fcp_statsIF.negativity_viewer_share_avg_decile as NegativityAvgIFRank
    ,round(fcp_statsIF.negativity_viewer_share_avg_median, 4) AS NegativityAvgIFMedian
    ,case when NegativityAvgIFMedian = 0 then null else round(NegativityAvgIF / NegativityAvgIFMedian - 1, 3) end AS NegativityAvgIFDiff
    --------------------------------------------------------
    ,mapping.country AS Country
    ,mapping.country_code AS Country_code -- Country !! 3 letters code
    ,mapping.geographic_region AS GeographicRegion
    ,mediasegments.ThumbnailFileName AS SourceMediaThumbnailFileName -- Media thumbnail file
    ,case when mediasegments.ThumbnailConvertedFileName is null then null else concat('thumbstrip/', substring(mediasegments.ThumbnailConvertedFileName, 11, 36), '/batch=0.png') end AS SourceMediaThumbstripFileName
    ,mediasegments.ThumbnailConvertedFileName as SourceMediaConvertedFileName
    ,mapping.parent_creative AS ParentCreative-- Creative
    ,mapping.sourcemedia AS SourceMedia -- Creative Version
    ,mapping.format AS SourceMediaType
    ,mapping.duration AS Duration
    ,TO_DATE( TO_TIMESTAMP( mapping.creation_time, 'YYYY-MM-DD HH24:MI:SS.FFFFFFF TZO', 1) ) AS CreationDate -- Date Tested
    ,mapping.brand AS Brand
    ,mapping.brand_id AS BrandID
    ,mediasegments.BrandLogoConvertedFileName AS BrandLogoFileName -- Brand logo file
    ,mediasegments.topcategoryname AS TopCategory -- Industry (top level) 
    ,mediasegments.midcategoryname AS MidCategory -- Industry (mid level)
    ,mediasegments.subcategoryname AS SubCategory -- Industry (sub level)
    ,mapping.platform AS Platform
    ,initcap(mapping.device) as Device
    ,mapping.ad_format AS Adformat
    ,CASE WHEN mapping.ad_format = 'youtubePreroll' THEN CONCAT('Youtube', case when mapping.is_skippable then ' skippable ' else ' non-skippable ' end, 'preroll ad', ' (', mapping.environment_category, ')')
        WHEN mapping.ad_format_name IS NOT NULL AND mapping.environment_category IS NOT NULL AND mapping.environment_category != '' THEN CONCAT( mapping.ad_format_name , ' (', mapping.environment_category, ')')  
        WHEN mapping.ad_format_name IS NOT NULL THEN mapping.ad_format_name
        ELSE 'Unknown' 
        END AS AdformatText -- Ad Format
    ,CASE WHEN mapping.ad_format = 'youtubePreroll' THEN CONCAT('Youtube', case when mapping.is_skippable then ' skippable ' else ' non-skippable ' end, 'preroll ad') 
        WHEN mapping.ad_format_name IS NOT NULL THEN mapping.ad_format_name
        ELSE 'Unknown' 
        END AS AdformatName
    ,mapping.environment_category AS EnvironmentCategory
    ,'Realeyes Video Player' AS AdformatTextIF
FROM virtual.data.InContextPoC.mediaMappingWithAdSet AS mapping
JOIN StudyDatabaseReadonly.StudyDatabase.dbo.AdSetType adsettype on adsettype.ID=mapping.AdSetTypeID 
left join StudyDatabaseReadonly.StudyDatabase.dbo.AdSetAttribute aaVisible on aaVisible.AdSetID= mapping.AdSetID and aaVisible.Key='IsVisible'
left join custom_adsets custom_adsets on custom_adsets.AccountID=mapping.account_id and custom_adsets.TestID=mapping.test_id and custom_adsets.SourceMediaID=mapping.sourcemedia_id
LEFT JOIN StudyDatabaseReadonly.StudyDatabase.dbo.PreViewDisplaySettings psd on psd.InContextTestID = mapping.test_id 
JOIN virtual.data.InContextPoC.mediaMappingWithAdSet AS in_focus_mapping 
    on in_focus_mapping.sourcemedia_id = mapping.sourcemedia_id and in_focus_mapping.test_id = mapping.infocus_test_id 
    and in_focus_mapping.project_id = mapping.project_id and in_focus_mapping.subjectgroup_id = mapping.subjectgroup_id
JOIN mediasegments ON mapping.sourcemedia_id = mediasegments.sourcemediaid
JOIN fcp as fcpIC on mapping.sourcemedia_id = cast(fcpIC.sourcemediaid as bigint) and mapping.test_id = fcpIC.testid and mapping.task_id = fcpIC.taskid
JOIN fcp_stats as fcp_statsIC on mapping.sourcemedia_id = cast(fcp_statsIC.sourcemediaid as bigint) and mapping.test_id = fcp_statsIC.testid and mapping.task_id = fcp_statsIC.taskid and fcpIC.segment_key = fcp_statsIC.segment_key
JOIN fcp as fcpIF on mapping.sourcemedia_id = cast(fcpIF.sourcemediaid as bigint) and in_focus_mapping.test_id = fcpIF.testid and in_focus_mapping.task_id = fcpIF.taskid and fcpIC.segment_key = fcpIF.segment_key
JOIN fcp_stats as fcp_statsIF on mapping.sourcemedia_id = cast(fcp_statsIF.sourcemediaid as bigint) and in_focus_mapping.test_id = fcp_statsIF.testid and in_focus_mapping.task_id = fcp_statsIF.taskid and fcpIC.segment_key = fcp_statsIF.segment_key
LEFT JOIN fcp_confirmit fcp_confirmitIC on mapping.sourcemedia_id = cast(fcp_confirmitIC.sourcemediaid as bigint) and mapping.test_id = fcp_confirmitIC.testid and mapping.task_id = fcp_confirmitIC.taskid and fcp_confirmitIC.segment_key = fcpIC.segment_key
LEFT JOIN fcp_confirmit fcp_confirmitIF on mapping.sourcemedia_id = cast(fcp_confirmitIF.sourcemediaid as bigint) and in_focus_mapping.test_id = fcp_confirmitIF.testid and in_focus_mapping.task_id = fcp_confirmitIF.taskid and fcp_confirmitIF.segment_key = fcpIF.segment_key
left join virtual.data.InContextPoC.eyesquare_mapping fcpICformat on fcpICformat.ad_format=coalesce(REGEXP_EXTRACT(fcpIC.custom_norm_filters, '"ad_format":\s*\[\s*"([^"]+)"',1),fcpIC.ad_format)
left join virtual.data.InContextPoC.eyesquare_mapping fcp_statsICformat on fcp_statsICformat.ad_format=coalesce(REGEXP_EXTRACT(fcp_statsIC.custom_norm_filters, '"ad_format":\s*\[\s*"([^"]+)"',1),fcp_statsIC.ad_format)
left join virtual.data.InContextPoC.eyesquare_mapping fcp_confirmitICformat on fcp_confirmitICformat.ad_format=coalesce(REGEXP_EXTRACT(fcp_confirmitIC.custom_norm_filters, '"ad_format":\s*\[\s*"([^"]+)"',1),fcp_confirmitIC.ad_format)
WHERE mapping.is_target = TRUE and in_focus_mapping.is_target = TRUE -- only targets, not clutters
    AND mapping.is_infocus = FALSE -- main object is IC media test
    AND coalesce(psd.HiddenOnPreViewUI,FALSE) = FALSE    
    and COALESCE(aaVisible."Value",'true')='true'