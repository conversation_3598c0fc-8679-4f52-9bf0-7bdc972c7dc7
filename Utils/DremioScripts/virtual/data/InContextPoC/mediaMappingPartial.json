{"sql": "mediaMappingPartial.sql", "sqlContext": "@<EMAIL>", "depends": ["StudyDatabaseReadonly.StudyDatabase.dbo.InContextProjectMapping", "StudyDatabaseReadonly.StudyDatabase.dbo.Brand", "virtual.data.InContextPoC.eyesquare_mapping", "StudyDatabaseReadonly.StudyDatabase.dbo.SourceMedia", "StudyDatabaseReadonly.StudyDatabase.dbo.Country", "StudyDatabaseReadonly.StudyDatabase.dbo.InContextMediaGroup", "StudyDatabaseReadonly.StudyDatabase.dbo.Account", "StudyDatabaseReadonly.StudyDatabase.dbo.CountryLanguagePair", "StudyDatabaseReadonly.StudyDatabase.dbo.Project", "StudyDatabaseReadonly.StudyDatabase.dbo.DataCollectionStructure"], "reflections": [{"name": "Raw Reflection", "type": "RAW", "sortFields": ["account_id"], "displayFields": ["account", "account_id", "project_id", "pnumber", "subjectgroup_id", "task_id", "element_id", "platform", "environment_category", "ad_format", "ad_format_name", "sourcemedia_id", "test_id", "infocus_datacollectionstructure_id", "duration", "brand", "brand_id", "country", "two_letter_code", "languagelocale_id", "parent_creative_id", "parent_creative", "sourcemedia", "device", "realeyes_project_id", "is_target", "is_infocus", "is_forced_exposure", "is_skippable", "display_order", "project_alias", "creation_time", "format"]}]}