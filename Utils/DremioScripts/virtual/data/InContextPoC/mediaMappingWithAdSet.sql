select 
p.ID as PDProjectID,
case when coalesce(p.<PERSON>Extern<PERSON><PERSON><PERSON>,'')!='' then p.<PERSON>al<PERSON>ey else p.<PERSON>as end as Survey<PERSON>eyAlias,
atea.AdSetID,
atea.Name as AdSetName,
atea.AdSetExternal<PERSON>ey,
atea.SourceMediaExternalKey,
atea.AdSetTypeID,
account.Name as account
,atea.AccountID account_id
,ipm.EyeSquareProjectID as project_id
,p.SurveyExternalKey as pnumber
,ipm.EyeSquareSubjectGroupID as subjectgroup_id
,p.IsEnabledForClients as is_enabled_for_clients
,ipm.TaskID as task_id
,ipm.ElementID as element_id
,e2_formats.platform as platform
,e2_formats.environment_category as environment_category
,ipm.Adformat as ad_format
,e2_formats.name as ad_format_name
,ipm.SourceMediaId as sourcemedia_id
,(case when img.IsInFocus is NULL or img.IsInFocus then dcs.testId else ipm.InContextTestID end) as test_id -- NEW dcs.testId for IF, ipm.InContextTestID for IC 
,ipm.DataCollectionStructureID as infocus_datacollectionstructure_id
,dcs.testId as infocus_test_id
,round(1.0*sm.Duration/1000) as duration
,b.Name as brand
,b.id as brand_id
,c.Name as country
,c.TwoLetterCode as country_code
,c.CountryNumber as country_number
,c.ID as country_id
,regions.region as geographic_region
,clp.LocaleID as languagelocale_id
,ipm.InContextMediaGroupId as parent_creative_id
,img.Name as parent_creative
,sm.Name as sourcemedia
,ipm.Device as device
,ipm.ProjectID as realeyes_project_id
,img.IsTarget as is_target
,img.IsInFocus as is_infocus
,ipm.IsForcedExposure as is_forced_exposure
,COALESCE(ipm.IsSkippable, false) as is_skippable
,ipm.DisplayOrder as display_order
,p.Alias as project_alias
,p.CreationTime as creation_time
,case sm.sourceMediaTypeID
      when 3 then 'Video'
      when 8 then 'Image'
      else 'Unknown'
 end as format
from StudyDatabaseReadonly.StudyDatabase.dbo.InContextProjectMapping ipm
join StudyDatabaseReadonly.StudyDatabase.dbo.InContextMediaGroup img on ipm.InContextMediaGroupID = img.ID
join StudyDatabaseReadonly.StudyDatabase.dbo.Project p on p.id	 = ipm.ProjectID
join StudyDatabaseReadonly.StudyDatabase.dbo.CountryLanguagePair  clp on p.CountryLanguagePairID = clp.ID
join StudyDatabaseReadonly.StudyDatabase.dbo.Country c on c.ID = clp.CountryID
join (
    select * from "@data_lake_raw"."eye_square".categorisations."geographic_regions.parquet"
    where region_source = 'UN geoscheme'
) regions
on c.countryNumber = regions.CountryNumber
join StudyDatabaseReadonly.StudyDatabase.dbo.SourceMedia sm on sm.ID = ipm.SourceMediaID
join StudyDatabaseReadonly.StudyDatabase.dbo.Brand b on b.ID = sm.BrandID
left join virtual.data.InContextPoC.eyesquare_mapping e2_formats on e2_formats.ad_format = ipm.Adformat  
left join StudyDatabaseReadonly.StudyDatabase.dbo.DataCollectionStructure as dcs on ipm.DataCollectionStructureID = dcs.ID
join StudyDatabaseReadonly.StudyDatabase.dbo.vw_AdSet_TestElement_Account atea on atea.SourceMediaID=ipm.SourceMediaID and atea.TestID=(case when img.IsInFocus is NULL or img.IsInFocus then dcs.testId else ipm.InContextTestID end)
join StudyDatabaseReadonly.StudyDatabase.dbo.Account as account on account.ID = atea.AccountID
order by 1 desc