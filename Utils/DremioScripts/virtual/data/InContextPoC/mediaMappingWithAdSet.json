{"sql": "mediaMappingWithAdSet.sql", "depends": ["StudyDatabaseReadonly.StudyDatabase.dbo.InContextProjectMapping", "StudyDatabaseReadonly.StudyDatabase.dbo.Brand", "virtual.data.InContextPoC.eyesquare_mapping", "StudyDatabaseReadonly.StudyDatabase.dbo.vw_AdSet_TestElement_Account", "StudyDatabaseReadonly.StudyDatabase.dbo.SourceMedia", "StudyDatabaseReadonly.StudyDatabase.dbo.Country", "StudyDatabaseReadonly.StudyDatabase.dbo.InContextMediaGroup", "StudyDatabaseReadonly.StudyDatabase.dbo.Account", "StudyDatabaseReadonly.StudyDatabase.dbo.CountryLanguagePair", "StudyDatabaseReadonly.StudyDatabase.dbo.Project", "StudyDatabaseReadonly.StudyDatabase.dbo.DataCollectionStructure", "@data_lake_raw.eye_square.categorisations.\"geographic_regions.parquet\""], "reflections": [{"name": "Raw Reflection", "type": "RAW", "sortFields": ["test_id", "sourcemedia_id"], "displayFields": ["AdSetID", "AdSetExternalKey", "SourceMediaExternalKey", "account", "account_id", "project_id", "pnumber", "subjectgroup_id", "is_enabled_for_clients", "task_id", "element_id", "platform", "environment_category", "ad_format", "ad_format_name", "sourcemedia_id", "test_id", "infocus_datacollectionstructure_id", "infocus_test_id", "duration", "brand", "brand_id", "country", "country_code", "country_number", "country_id", "geographic_region", "languagelocale_id", "parent_creative_id", "parent_creative", "sourcemedia", "device", "realeyes_project_id", "is_target", "is_infocus", "is_forced_exposure", "is_skippable", "display_order", "project_alias", "creation_time", "format", "AdSetTypeID", "AdSetName", "PDProjectID", "SurveyKeyAlias"]}]}