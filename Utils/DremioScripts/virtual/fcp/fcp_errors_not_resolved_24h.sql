with p1 as (
    SELECT *, COALESCE(session, 0) session_not_null FROM @fcp_results where start_time >= CURRENT_TIMESTAMP() - interval '5' day
)
SELECT * FROM p1 where start_time >= CURRENT_TIMESTAMP() - interval '5' day
and error not in ('Session is not yet synced', 'Missing required classifier_aligned columns', 'Missing CategoryID')
and not exists(
    SELECT * FROM @fcp_results p2 where p2.start_time >= CURRENT_TIMESTAMP() - interval '5' day
    and p2.sourcemedia = p1.sourcemedia and COALESCE(p2.session, 0)=session_not_null and p2.pipeline_name = p1.pipeline_name 
    and p2.start_time > p1.start_time)
and start_time BETWEEN CURRENT_TIMESTAMP() - interval '24' hour and CURRENT_TIMESTAMP() - interval '20' minute
order by start_time desc