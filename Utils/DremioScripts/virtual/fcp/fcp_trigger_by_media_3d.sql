with a as (
    SELECT * FROM @fcp_results where start_time >= CURRENT_TIMESTAMP() - interval '3' day
    and pipeline_name  in ('attention_drop_filtered_emotions-session_time-view_through_rate-combined-creative', 'states-session_time-interest-combined-creative', 'mean-session_time-emotions-classifiers-creative','negative-session_time-derived-classifiers-creative')
    and "result" = 'SUCCESSFUL' and ("rows" > 0)
),
b as (
    SELECT sourcemedia, id, start_time, end_time, flatten(regexp_split(triggered_by, '\Q,\E', 'ALL', 300)) triggered_by, pipeline_name, "result" FROM @fcp_results where session is null
    AND start_time >= CURRENT_TIMESTAMP() - interval '3' day
),
ab as (
    select a.id, a.start_time, a.pipeline_name, a.session, a.sourcemedia, b.id next_id, b.pipeline_name next_name, b.end_time next_end, b."result" from a left join b on b.triggered_by = a.id
),
abc as (
    select ab.id, ab.start_time, ab.pipeline_name, ab.session, ab.sourcemedia, b.id next_id, b.pipeline_name next_name, b.end_time next_end, b."result" from ab inner join b on b.triggered_by = ab.next_id
),
abcd as (
    select abc.id, abc.start_time, abc.pipeline_name, abc.session, abc.sourcemedia, b.id next_id, b.pipeline_name next_name, b.end_time next_end, b."result" from abc inner join b on b.triggered_by = abc.next_id
),
deps as (
    SELECT * from 
    (values ('attention_drop_filtered_emotions-session_time-view_through_rate-combined-creative','quality_score-media_duration-benchmarking-combined-creative'),
            ('states-session_time-interest-combined-creative','quality_score-media_duration-benchmarking-combined-creative')
            ,('mean-session_time-emotions-classifiers-creative','traffic_lights_mean-media_duration-emotions-classifiers-creative')
            ,('mean-session_time-emotions-classifiers-creative','traffic_lights_max-media_duration-emotions-classifiers-creative')
            ,('negative-session_time-derived-classifiers-creative','traffic_lights_cumulative_mean_negative-media_duration-derived-classifiers-creative')
            ,('negative-session_time-derived-classifiers-creative','traffic_lights_cumulative_max_negative-media_duration-derived-classifiers-creative')) s(src,target)
),
"all" as (
    select *, 
    CASE WHEN ROW_NUMBER() OVER(partition by id, session order by next_end DESC)=1 THEN 1 ELSE 0 END last_step
/*    CASE WHEN next_name IN ('quality_score-media_duration-benchmarking-combined-creative', 'traffic_lights_mean-media_duration-emotions-classifiers-creative', 'traffic_lights_max-media_duration-emotions-classifiers-creative','traffic_lights_cumulative_mean_negative-media_duration-derived-classifiers-creative') THEN 1 ELSE 0 END final*/
    from
    (
        select * from ab 
        union all 
        select * from abc
        union all
        select * from abcd
    )
)
select id, start_time, pipeline_name, session, sourcemedia, d.target,
MAX(case when next_name=target  then next_id end) final_id,
MAX(case when next_name=target  then next_name end) final_pipeline, MAX(case when next_name=target then next_end end)-start_time time_to_final,
MAX(case when last_step=1 then next_id end) last_triggred_id,
MAX(case when last_step=1 then next_name end) last_triggred_pipeline, MAX(case when last_step=1 then next_end end)-start_time time_to_last --next_id, next_name, next_start, final, "result"
from "all" join deps d ON "all".pipeline_name = d.src
--where sourcemedia=99671
group by id, start_time, pipeline_name, session, sourcemedia, d.target