# Dremio Preview Portal Utils

## DIET (Dremio import export tool)

Tool written in python with command line interface that Exports Dataset tree from Drem<PERSON> instance(only preview Stage/Live instanced are supported).
It uses REST api to traverse SPACE/FOLDER/SOURCE and search for datasets(PDS and VDS). The structure can be outputed as:
* single json file
* local directory with the tree structure and json(+sql file) file per dataset.

When exporting you have to specify catalog path under which everything will be exported. So it can be:
* space or space folder: i.e. `virtual/data/preview` or just `virtual`
* source or source folder: i.e `live_data_lake_semi_structured/feature` or just `live_data_lake_semi_structured`

Exporting single dataset is not supported. You can export folder and use config([here](#sources-export-settings)) to include only single dataset.
Be aware that writing exported dataset to local folder will empty that folder first, so better use temp folder for that(+ put config.json to that folder).

Tool can export optionally reflections

When calling import command you can also pass folder or file as source. When importing tool does:
* checks that root container(space or data source) exists. Tool can not create sources or spaces.
* creates/updates folders and datasets
* optionally creates/updates reflections


Usage:
```
Usage: python -m DIET [OPTIONS] COMMAND [ARGS]...

Options:
  -d, --debug
  -v, --version
  --help                          Show this message and exit.

Commands:
  export  Exports from Dremio to folder structure or file(if folder param...
  import  Imports from folder structure or file(if folder param is file...
```

To Export
```
Usage:  python -m DIET export [OPTIONS]

  Exports from Dremio to folder structure or file(if folder param is file
  path)

Options:
  -c, --catalog TEXT          Dremio catalog root folder(i.e virtual/data/...)
                              [required]
  -s, --stage [LIVE|STAGE|CUSTOM]    export from Dremio instance stage  [required]
  -f, --folder PATH           export tree to folder  [required]
  -so, --sync_only            export only already existing objects in folder(sync by target)
  --inline_sql_limit INTEGER  max sql length that is inlined in json dataset
                              config  [default: 20]
  -i, --include               optional pattern(regex), includes only dataset that match pattern
  -sr, --skip_reflections     do not export reflections
  -v, --version
  --help                      Show this message and exit.
```

To Import

```
Usage: python DIET -m  import [OPTIONS]

  Imports from folder structure or file(if folder param is file path) to
  Dremio

Options:
  -c, --catalog TEXT         Dremio catalog root folder(i.e virtual/data/...)
                             [required]
  -s, --stage [LIVE|STAGE|CUSTOM]   export from Dremio instance stage  [required]
  -f, --folder PATH          export to folder  [required]
  -i, --include              optional pattern(regex), includes only files that match pattern
  -r, --reset                recreate(drop/create) datasets
  -ur, --update_reflections  update/create reflections by name match
  -v, --version
  --help                     Show this message and exit.
```

To call the tool from command line:

` C:\<repo_dir>\PreViewPortal\Utils> py -m DIET ...`

**Note**: Current directory will be `Utils` and it will affect
* `--folder` path if relative value passed

## Debug/run from IDE
We have main entry point file `Scripts/dremio_main.py` that you can edit and run. Note that working folder will be `Scripts` and separate `config.json` file will be used.
You can export catalogs to `Scripts/ChangeScripts` folder in `dremio_main.py` code as it is git ignored.

Debugging of the command line entry point can be done in Pycharm by creating Debug/Run configurations (Run/Edit configurations):
* Add new configuration: Python
* Set fields: Module name: `DIET`, Parameters: `import -s STAGE -f DremioScripts -c @data_lake`(example), Working directory: `<path>\PreViewPortal\Utils`

## Source control

Tool export folder output can be put under source control. We use `Utils/DremioScripts` folder for dataset definitions that we can manually change and deploy.

## Sync only when exporting
When exporting datasets to local folder you can use `--sync_only` / `-so` flag that will only export datasets that already exist in the folder.
We suggest to use this flag always and add new datasets manually(empty {} json placeholder file) to the folder and then export them with `--sync_only`.

## Example commands used to export live and sync with stage (without --sync_only) to make full copy
* Export all Live env virtual datasets for Preview(at **virtual/data/preview** dremio space) to **...Utils/DremioScripts/virtual/data/preview** folder:
`PreViewPortal\Utils> py -m DIET export -s LIVE -f DremioScripts -c virtual/data/preview`
* Sync(export only existing in folder) all Live env virtual datasets for Preview(at **virtual/data/preview** dremio space) to **...Utils/DremioScripts/virtual/data/preview** folder:
`PreViewPortal\Utils> py -m DIET export -s LIVE -f DremioScripts -c virtual/data/preview`
* Export all Live env s3 data lake physical datasets to **@data_lake**(see replacements feature) folder: 
`PreViewPortal\Utils> py -m DIET export -s LIVE -f DremioScripts -c live_data_lake_semi_structured`
* Import all virtual datasets from **DremioScripts/virtual/data/preview** folder to STAGE env: 
`PreViewPortal\Utils> py -m DIET import -s STAGE -f DremioScripts -c virtual/data/preview`
* Same as above but only import single dataset *preview_results*
`PreViewPortal\Utils> py -m DIET import -s STAGE -f DremioScripts -c virtual/data/preview/app -i preview_results`
* Import all s3 data lake physical datasets(**@data_lake** folder) to STAGE env: 
`PreViewPortal\Utils> py -m DIET import -s STAGE -f DremioScripts -c @data_lake`

Note: @data_lake is a replacement that will automatically be substituted to correct bucket name based on config.json replacements section

## Extra features

### --reset flag
Drops and recreates all datasets when importing

### Config.json file

In the folder where you export data and later import from(if export/export to/from file.json that it's parent folder) you can create `config.json` file.
```json
{
  "credentials": {},
  "sources": {
    "source_or_space_name": {
      ...
    }, 
    ...
  },
  "replacements": {
    "LIVE": {
      ...
    },
    "STAGE": {
      ...
    }
  }
}
```
### Credentials and CUSTOM stage

By default tool is supposed to work with predefined Live and Stage Preview Dremio clusters. Based on `stage` parameter url and credentials are
retrieved from AWS parameter store.

If you want to use custom credentials or custom environment(credentials+url) use `credentials` section in config. Use stage name as key based on stage:

```json
{
  "credentials": {
    "LIVE": {
      "user_name": "",
      "password": "",
      "pat_token": "",
      "dremio_server": ""
    }
  }
}
```
Where all parameters are optional. Note that you can use username/password or personal access token generated in dremio UI.
You can use `"CUSTOM"` stage key to define completely new environment to avoid overwriting LIVE/STAGE.

### Dynamic replacements

In the config.json file you can edit "replacements" sections with a dictionary of key:value for each STAGE.
This feature allow you to keep single ALIAS name in the exported to local folder data for the resources that have different names based on environment.
For example:
* data lake bucket source name - can be **live_data_lake_semi_structured** or **stage_data_lake_semi_structured**
* Aws glue database for fcp features - **fcp_live** or **fcp_stage**
* Single dataset for fcp pipelines results - **fcp_pipeline_results** or **fcp_pipeline_results_stage**
* constant value fo the algorithm id for nel 4.1.1 - **264** on live or **270** for stage
* Example fo the correct replacements:
```json
"replacements": {
    "LIVE": {
      "@data_lake": "live_data_lake_semi_structured",
      "@fcp_db": "fcp_live",
      "@fcp_results": "fcp_pipeline_results",
      "@algo_id": {"value:": "264", "on_export": ["(algorithmid\\s*?=\\s*?)264", "\\1@algo_id"] }     
    },
    "STAGE": {
      "@data_lake": "stage_data_lake_semi_structured",
      "@fcp_db": "fcp_stage",
      "@fcp_results": "fcp_pipeline_results_stage",      
      "@algo_id": {"value": "270", "on_export": false  } 
    }
  }
```
Here @data_lake @fcp_db @fcp_results key are aliases that you can select yourself for the same resources.
I suggest adding '@' before the name, though it is not required.
Note: inside `import` and `export` command arguments you should use alias ONLY in `import` command and ONLY for `--folder (-f)` path argument.

Value of replacement can be object with attributes: `value: string`, `on_export: boolean|string_array`. This is reuired if you want to:
* disable applying replacement when you call export command(sync from dremio to source files).
* apply custom replacement regular expression on export when default behavior can break source files with false positives:
*@algo_id* replacement can accidently replace 264 or 270 that is just a platfrom independent constant or part of the word(`+264 20 xxxx` -> `+@algo_id 20 xxxx`).
To avoid that we specify custom replace logic using `re.sub(on_export[0], on_export[1], sql_or_etc)` where you can use backreferences(`\{group_number}`)


### Sources export settings

For sources like s3 bucket you can configure crawling inlclude/exclude rules to optimize export times and do not traverse whole bucket to find PDS that can take infinite time.
Here is an example of semi structured data lake source on LIVE env config:

```json
"sources": {
    "live_data_lake_semi_structured": {
      "include_folders": ["features", "media_segments"],
      "stop_traverse_pattern": [],
      "exclude": ["sourcemediagroup=", "sourcemediaid=", "meetingid=", "sessionid=", "algorithmid="]
    }
  }
```
Here we tell for export tool to:
1) search only **features** and **media_segments** root folders cos we know they contain PDS we are interested in.
2) skip searching subfolders that contain ["sourcemediagroup=", "sourcemediaid=", "meetingid=", "sessionid=", "algorithmid="] patterns inside their names,
cos we know that PDS are only created on top feature folder(featureid=xxxx) not on its children
3) **stop_traverse_pattern** has same behavior as **exclude** patterns BUT is applied for folders only(if folder matching pattern is already a PDS it will be exported)

### Reflections

Tool support exporting and importing reflections next to datasets. 
By default, export include reflection definitions, but can be skipped by `--skip_reflections` flag.
Import vise versa by default does not import reflections, you can enable this by `--update_reflections` flag

Reflections are only UPDATED OR CREATED on import. Existing reflections that are not in the source of import will not be deleted.
Reflection update is done by matching reflection Name as id. By default, in Dremio Web UI name is autogenerated, same name will be exported in json as id.
In json we store reflection definition is less verbose format that Dremio Rest api and there is no way to set proper arbitrary types(COUNT, MAX, etc) for measures, so default set is used.


## Know Issues

### Create of PDS fails
Dremio rest api post catalog for PDS creation can fail with Apache Not found error or `Entity id does not match the path specified in the dataset`
when promoting folders to dataset on s3 source. The reason for that is Apache configuration missing.
Proper settings shpuld be set in `etc/apache2/sites-enabled/dremio.conf`
* Find proper `<VirtualHost *:port> `section for Dremio web
* Add `AllowEncodedSlashes NoDecode`
* If RewriteRule rules are set add NE flag to each rule(no encode) to prevent double encoding: `RewriteRule /(.*) http://localhost:9047/$1 [P,L,NE]`
* If ProxyPass is used add nocanon flag to it: `ProxyPass / http://localhost:9047/ nocanon`