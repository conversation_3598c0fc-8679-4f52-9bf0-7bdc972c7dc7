using BusinessLayer.Model;
using BusinessLayer.Model.Filter;
using BusinessLayer.Model.Request;

namespace BusinessLayer.Examples;

/// <summary>
/// Example showing how to use curve filtering in the InContextExportDataCollector
/// </summary>
public static class CurveFilteringExample
{
    /// <summary>
    /// Example of creating a GetExportDataRequest with curve filtering for Happiness only
    /// </summary>
    /// <returns>GetExportDataRequest configured to filter for Happiness curves only</returns>
    public static GetExportDataRequest CreateHappinessOnlyRequest()
    {
        return new GetExportDataRequest
        {
            AccountId = 123,
            IsAdmin = true,
            SegmentKeys = new[] { "segment1", "segment2" },
            Media = new List<MediaModelRequest>
            {
                new MediaModelRequest 
                { 
                    SourceMediaID = 1, 
                    TestID = 1, 
                    TaskID = "task1" 
                }
            },
            // This is the key part - specify which curve types to keep
            CurveFilter = new List<CurveFilterItem>
            {
                new CurveFilterItem { Type = CurveType.Happiness, ExposureGroup = "Group1" }
            },
            VisibleColumnsOrder = new List<string> { "brand", "views", "happiness" }
        };
    }

    /// <summary>
    /// Example of creating a request with multiple curve types
    /// </summary>
    /// <returns>GetExportDataRequest configured to filter for multiple curve types</returns>
    public static GetExportDataRequest CreateMultipleCurveTypesRequest()
    {
        return new GetExportDataRequest
        {
            AccountId = 123,
            IsAdmin = true,
            SegmentKeys = new[] { "segment1" },
            Media = new List<MediaModelRequest>
            {
                new MediaModelRequest 
                { 
                    SourceMediaID = 1, 
                    TestID = 1, 
                    TaskID = "task1" 
                }
            },
            // Filter for multiple curve types
            CurveFilter = new List<CurveFilterItem>
            {
                new CurveFilterItem { Type = CurveType.Happiness, ExposureGroup = "Group1" },
                new CurveFilterItem { Type = CurveType.Attention, ExposureGroup = "Group1" },
                new CurveFilterItem { Type = CurveType.Negativity, ExposureGroup = "Group2" }
            },
            VisibleColumnsOrder = new List<string> { "brand", "views", "happiness", "attention", "negativity" }
        };
    }

    /// <summary>
    /// Example of creating a request with no curve filtering (keeps all curve data)
    /// </summary>
    /// <returns>GetExportDataRequest with no curve filtering</returns>
    public static GetExportDataRequest CreateNoFilteringRequest()
    {
        return new GetExportDataRequest
        {
            AccountId = 123,
            IsAdmin = true,
            SegmentKeys = new[] { "segment1" },
            Media = new List<MediaModelRequest>
            {
                new MediaModelRequest 
                { 
                    SourceMediaID = 1, 
                    TestID = 1, 
                    TaskID = "task1" 
                }
            },
            // Empty curve filter means no filtering - all curve data is kept
            CurveFilter = new List<CurveFilterItem>(),
            VisibleColumnsOrder = new List<string> { "brand", "views" }
        };
    }

    /// <summary>
    /// Example showing all available curve types that can be filtered
    /// </summary>
    /// <returns>List of all possible curve filter items</returns>
    public static List<CurveFilterItem> GetAllPossibleCurveFilters()
    {
        return new List<CurveFilterItem>
        {
            new CurveFilterItem { Type = CurveType.Attention },
            new CurveFilterItem { Type = CurveType.AllReactions },
            new CurveFilterItem { Type = CurveType.Distraction },
            new CurveFilterItem { Type = CurveType.Happiness },
            new CurveFilterItem { Type = CurveType.Negativity },
            new CurveFilterItem { Type = CurveType.Contempt },
            new CurveFilterItem { Type = CurveType.Surprise },
            new CurveFilterItem { Type = CurveType.Confusion },
            new CurveFilterItem { Type = CurveType.Disgust },
            new CurveFilterItem { Type = CurveType.Playback },
            new CurveFilterItem { Type = CurveType.NeutralAttention }
        };
    }
}
