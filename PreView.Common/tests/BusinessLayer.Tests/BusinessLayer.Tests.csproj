<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="FluentAssertions" Version="6.11.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="8.0.13" />
        <PackageReference Include="Moq" Version="4.18.2" />
        <PackageReference Include="NUnit" Version="3.12.0" />
        <PackageReference Include="NUnit3TestAdapter" Version="3.16.1" />
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="16.5.0" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\src\BusinessLayer\BusinessLayer.csproj" />
    </ItemGroup>
</Project>
