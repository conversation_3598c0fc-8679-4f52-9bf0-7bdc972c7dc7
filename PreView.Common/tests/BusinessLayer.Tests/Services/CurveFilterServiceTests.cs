using BusinessLayer.Model;
using BusinessLayer.Model.Filter;
using BusinessLayer.Model.Response;
using BusinessLayer.Services;
using Xunit;

namespace BusinessLayer.Tests.Services;

public class CurveFilterServiceTests
{
    [Fact]
    public void FilterCurves_WithHappinessFilter_ShouldKeepOnlyHappinessData()
    {
        // Arrange
        var curves = new List<InContextCurve>
        {
            new InContextCurve
            {
                SourceMediaID = 1,
                TestID = 1,
                TaskID = "task1",
                Second = 1,
                SegmentKey = "segment1",
                Happiness = 0.8f,
                HappinessNorm = 0.7f,
                HappinessIC = 0.6f,
                HappinessICNorm = 0.5f,
                Attention = 0.9f,
                AttentionNorm = 0.8f,
                Negativity = 0.3f,
                NegativityNorm = 0.2f
            }
        };

        var filters = new List<CurveFilterItem>
        {
            new CurveFilterItem { Type = CurveType.Happiness }
        };

        // Act
        var result = CurveFilterService.FilterCurves(curves, filters);

        // Assert
        Assert.Single(result);
        var filteredCurve = result.First();
        
        // Should keep identifying properties
        Assert.Equal(1, filteredCurve.SourceMediaID);
        Assert.Equal(1, filteredCurve.TestID);
        Assert.Equal("task1", filteredCurve.TaskID);
        Assert.Equal(1, filteredCurve.Second);
        Assert.Equal("segment1", filteredCurve.SegmentKey);
        
        // Should keep happiness data
        Assert.Equal(0.8f, filteredCurve.Happiness);
        Assert.Equal(0.7f, filteredCurve.HappinessNorm);
        Assert.Equal(0.6f, filteredCurve.HappinessIC);
        Assert.Equal(0.5f, filteredCurve.HappinessICNorm);
        
        // Should remove other curve data
        Assert.Null(filteredCurve.Attention);
        Assert.Null(filteredCurve.AttentionNorm);
        Assert.Null(filteredCurve.Negativity);
        Assert.Null(filteredCurve.NegativityNorm);
    }

    [Fact]
    public void FilterCurves_WithMultipleFilters_ShouldKeepMultipleCurveTypes()
    {
        // Arrange
        var curves = new List<InContextCurve>
        {
            new InContextCurve
            {
                SourceMediaID = 1,
                TestID = 1,
                TaskID = "task1",
                Second = 1,
                SegmentKey = "segment1",
                Happiness = 0.8f,
                HappinessNorm = 0.7f,
                Attention = 0.9f,
                AttentionNorm = 0.8f,
                Negativity = 0.3f,
                NegativityNorm = 0.2f,
                Contempt = 0.1f,
                ContemptNorm = 0.05f
            }
        };

        var filters = new List<CurveFilterItem>
        {
            new CurveFilterItem { Type = CurveType.Happiness },
            new CurveFilterItem { Type = CurveType.Attention }
        };

        // Act
        var result = CurveFilterService.FilterCurves(curves, filters);

        // Assert
        Assert.Single(result);
        var filteredCurve = result.First();
        
        // Should keep happiness and attention data
        Assert.Equal(0.8f, filteredCurve.Happiness);
        Assert.Equal(0.7f, filteredCurve.HappinessNorm);
        Assert.Equal(0.9f, filteredCurve.Attention);
        Assert.Equal(0.8f, filteredCurve.AttentionNorm);
        
        // Should remove other curve data
        Assert.Null(filteredCurve.Negativity);
        Assert.Null(filteredCurve.NegativityNorm);
        Assert.Null(filteredCurve.Contempt);
        Assert.Null(filteredCurve.ContemptNorm);
    }

    [Fact]
    public void FilterCurves_WithEmptyFilters_ShouldReturnOriginalCurves()
    {
        // Arrange
        var curves = new List<InContextCurve>
        {
            new InContextCurve
            {
                SourceMediaID = 1,
                TestID = 1,
                TaskID = "task1",
                Second = 1,
                SegmentKey = "segment1",
                Happiness = 0.8f,
                Attention = 0.9f
            }
        };

        var filters = new List<CurveFilterItem>();

        // Act
        var result = CurveFilterService.FilterCurves(curves, filters);

        // Assert
        Assert.Single(result);
        var resultCurve = result.First();
        Assert.Equal(0.8f, resultCurve.Happiness);
        Assert.Equal(0.9f, resultCurve.Attention);
    }

    [Fact]
    public void FilterCurves_WithNullCurves_ShouldReturnEmptyList()
    {
        // Arrange
        List<InContextCurve> curves = null;
        var filters = new List<CurveFilterItem>
        {
            new CurveFilterItem { Type = CurveType.Happiness }
        };

        // Act
        var result = CurveFilterService.FilterCurves(curves, filters);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public void FilterCurves_WithNullFilters_ShouldReturnOriginalCurves()
    {
        // Arrange
        var curves = new List<InContextCurve>
        {
            new InContextCurve
            {
                SourceMediaID = 1,
                Happiness = 0.8f,
                Attention = 0.9f
            }
        };

        List<CurveFilterItem> filters = null;

        // Act
        var result = CurveFilterService.FilterCurves(curves, filters);

        // Assert
        Assert.Single(result);
        var resultCurve = result.First();
        Assert.Equal(0.8f, resultCurve.Happiness);
        Assert.Equal(0.9f, resultCurve.Attention);
    }
}
