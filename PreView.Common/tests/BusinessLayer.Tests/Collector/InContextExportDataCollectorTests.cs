using BusinessLayer.Collector;
using BusinessLayer.Model;
using BusinessLayer.Model.Filter;
using BusinessLayer.Model.Request;
using BusinessLayer.Model.Response;
using BusinessLayer.Repository;
using Moq;
using Xunit;

namespace BusinessLayer.Tests.Collector;

public class InContextExportDataCollectorTests
{
    private readonly Mock<IInContextCollector> _mockInContextCollector;
    private readonly Mock<ISegmentRepository> _mockSegmentRepository;
    private readonly InContextExportDataCollector _collector;

    public InContextExportDataCollectorTests()
    {
        _mockInContextCollector = new Mock<IInContextCollector>();
        _mockSegmentRepository = new Mock<ISegmentRepository>();
        _collector = new InContextExportDataCollector(_mockInContextCollector.Object, _mockSegmentRepository.Object);
    }

    [Fact]
    public async Task GetExportData_WithCurveFilter_ShouldFilterCurvesCorrectly()
    {
        // Arrange
        var request = new GetExportDataRequest
        {
            AccountId = 1,
            IsAdmin = true,
            SegmentKeys = new[] { "segment1" },
            Media = new List<MediaModelRequest>
            {
                new MediaModelRequest { SourceMediaID = 1, TestID = 1, TaskID = "task1" }
            },
            CurveFilter = new List<CurveFilterItem>
            {
                new CurveFilterItem { Type = CurveType.Happiness }
            },
            VisibleColumnsOrder = new List<string> { "views", "brand" }
        };

        var mockCreatives = new List<InContextCreative>
        {
            new InContextCreative
            {
                SourceMediaID = 1,
                TestID = 1,
                TaskID = "task1",
                SegmentKey = "segment1",
                Brand = "TestBrand",
                Views = 100,
                Audience = "Test Audience"
            }
        };

        var mockCurves = new List<InContextCurve>
        {
            new InContextCurve
            {
                SourceMediaID = 1,
                TestID = 1,
                TaskID = "task1",
                SegmentKey = "segment1",
                Second = 1,
                Happiness = 0.8f,
                HappinessNorm = 0.7f,
                HappinessIC = 0.6f,
                HappinessICNorm = 0.5f,
                Attention = 0.9f,
                AttentionNorm = 0.8f,
                Negativity = 0.3f,
                NegativityNorm = 0.2f
            }
        };

        var mockSegments = new List<SegmentKeyLabel>
        {
            new SegmentKeyLabel
            {
                SegmentKey = "segment1",
                Question = "Test Question",
                Answer = "Test Answer"
            }
        };

        _mockInContextCollector
            .Setup(x => x.GetCreativesAsync(It.IsAny<GetCreativesRequest>()))
            .ReturnsAsync(mockCreatives);

        _mockInContextCollector
            .Setup(x => x.GetCurvesAsync(It.IsAny<GetCurvesRequest>()))
            .ReturnsAsync(mockCurves);

        _mockSegmentRepository
            .Setup(x => x.GetSegmentKeys(It.IsAny<GetSegmentKeysRequest>()))
            .ReturnsAsync(mockSegments);

        // Act
        var result = await _collector.GetExportData(request);

        // Assert
        Assert.NotNull(result);
        Assert.NotNull(result.CurvesPayload);
        
        var curvesWithCreatives = result.CurvesPayload.Cast<InContextCreativeWithCurve>().ToList();
        Assert.Single(curvesWithCreatives);
        
        var curveWithCreative = curvesWithCreatives.First();
        
        // Should have happiness data (filtered curve type)
        Assert.Equal(0.6m, curveWithCreative.HappinessIC);
        Assert.Equal(0.5m, curveWithCreative.HappinessICNorm);
        Assert.Equal(0.8m, curveWithCreative.Happiness);
        Assert.Equal(0.7m, curveWithCreative.HappinessNorm);
        
        // Should NOT have attention data (filtered out)
        Assert.Null(curveWithCreative.Attention);
        Assert.Null(curveWithCreative.AttentionNorm);
        
        // Should NOT have negativity data (filtered out)
        Assert.Null(curveWithCreative.Negativity);
        Assert.Null(curveWithCreative.NegativityNorm);
    }

    [Fact]
    public async Task GetExportData_WithoutCurveFilter_ShouldKeepAllCurveData()
    {
        // Arrange
        var request = new GetExportDataRequest
        {
            AccountId = 1,
            IsAdmin = true,
            SegmentKeys = new[] { "segment1" },
            Media = new List<MediaModelRequest>
            {
                new MediaModelRequest { SourceMediaID = 1, TestID = 1, TaskID = "task1" }
            },
            CurveFilter = new List<CurveFilterItem>(), // Empty filter
            VisibleColumnsOrder = new List<string> { "views", "brand" }
        };

        var mockCreatives = new List<InContextCreative>
        {
            new InContextCreative
            {
                SourceMediaID = 1,
                TestID = 1,
                TaskID = "task1",
                SegmentKey = "segment1",
                Brand = "TestBrand",
                Views = 100,
                Audience = "Test Audience"
            }
        };

        var mockCurves = new List<InContextCurve>
        {
            new InContextCurve
            {
                SourceMediaID = 1,
                TestID = 1,
                TaskID = "task1",
                SegmentKey = "segment1",
                Second = 1,
                Happiness = 0.8f,
                Attention = 0.9f,
                Negativity = 0.3f
            }
        };

        var mockSegments = new List<SegmentKeyLabel>
        {
            new SegmentKeyLabel
            {
                SegmentKey = "segment1",
                Question = "Test Question",
                Answer = "Test Answer"
            }
        };

        _mockInContextCollector
            .Setup(x => x.GetCreativesAsync(It.IsAny<GetCreativesRequest>()))
            .ReturnsAsync(mockCreatives);

        _mockInContextCollector
            .Setup(x => x.GetCurvesAsync(It.IsAny<GetCurvesRequest>()))
            .ReturnsAsync(mockCurves);

        _mockSegmentRepository
            .Setup(x => x.GetSegmentKeys(It.IsAny<GetSegmentKeysRequest>()))
            .ReturnsAsync(mockSegments);

        // Act
        var result = await _collector.GetExportData(request);

        // Assert
        Assert.NotNull(result);
        Assert.NotNull(result.CurvesPayload);
        
        var curvesWithCreatives = result.CurvesPayload.Cast<InContextCreativeWithCurve>().ToList();
        Assert.Single(curvesWithCreatives);
        
        var curveWithCreative = curvesWithCreatives.First();
        
        // Should have all curve data (no filtering applied)
        Assert.Equal(0.8m, curveWithCreative.Happiness);
        Assert.Equal(0.9m, curveWithCreative.Attention);
        Assert.Equal(0.3m, curveWithCreative.Negativity);
    }

    [Fact]
    public void CanHandleProductType_WithInContextType_ShouldReturnTrue()
    {
        // Act
        var result = _collector.CanHandleProductType(ProductType.InContext);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public void CanHandleProductType_WithOtherType_ShouldReturnFalse()
    {
        // Act
        var result = _collector.CanHandleProductType(ProductType.NewForcedExposure);

        // Assert
        Assert.False(result);
    }
}
