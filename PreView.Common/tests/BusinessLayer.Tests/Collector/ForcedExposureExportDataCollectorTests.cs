using BusinessLayer.Collector;
using BusinessLayer.Model.Filter;
using BusinessLayer.Model.Request;
using BusinessLayer.Model.Response;
using BusinessLayer.Repository;
using FluentAssertions;
using Moq;
using NUnit.Framework;

namespace BusinessLayer.Tests.Collector;

[TestFixture]
public class ForcedExposureExportDataCollectorTests
{
    [SetUp]
    public void SetUp()
    {
        _forcedExposureCollectorMock = new Mock<IForcedExposureCollector>();
        _segmentRepositoryMock = new Mock<ISegmentRepository>();
        _collector =
            new ForcedExposureExportDataCollector(_forcedExposureCollectorMock.Object, _segmentRepositoryMock.Object);
    }

    private Mock<IForcedExposureCollector> _forcedExposureCollectorMock;
    private Mock<ISegmentRepository> _segmentRepositoryMock;
    private ForcedExposureExportDataCollector _collector;

    [Test]
    public async Task GetExportData_ShouldReturnExportData_WhenValidRequest()
    {
        // Arrange
        var request = new GetExportDataRequest
        {
            AccountId = 123,
            IsAdmin = true,
            SegmentKeys = new List<string> { "segment1", "segment2" },
            MinViewThresholdForScores = 50,
            VisibleColumnsOrder = new List<string> { "views" }, 
            Sorting = new List<GridSortingItem> { new() { Field = "segmentKey", Direction = "asc" } },
            Media = new List<MediaModelRequest> { new() { SourceMediaID = 1, TestID = 2 } }
        };

        var forcedExposureCollectorMock = new Mock<IForcedExposureCollector>();
        var segmentRepositoryMock = new Mock<ISegmentRepository>();

        forcedExposureCollectorMock
            .Setup(x => x.GetCreativesAsync(It.IsAny<GetCreativesRequest>()))
            .ReturnsAsync(new List<ForcedExposureCreative>
            {
                new()
                {
                    SourceMediaID = 1,
                    TestID = 2,
                    SegmentKey = "segment1",
                    Views = 100,
                    Audience = "Audience1",
                },
                new()
                {
                    SourceMediaID = 1,
                    TestID = 2,
                    SegmentKey = "segment2",
                    Views = 150,
                    Audience = "Audience2",
                }
            });

        segmentRepositoryMock
            .Setup(x => x.GetSegmentKeys(It.IsAny<GetSegmentKeysRequest>()))
            .ReturnsAsync(new List<SegmentKeyLabel>
            {
                new() { SegmentKey = "segment1", Answer = "Answer1", Question = "Question1" },
                new() { SegmentKey = "segment2", Answer = "Answer2", Question = "Question2" }
            });

        forcedExposureCollectorMock
            .Setup(x => x.GetCurvesAsync(It.IsAny<GetCurvesRequest>()))
            .ReturnsAsync(new List<ForcedExposureCurve>
            {
                new()
                {
                    SourceMediaID = 1,
                    TestID = 2,
                    SegmentKey = "segment1",
                    Second = 1,
                    Playback = 0.9f,
                    Attention = 0.8f,
                },
                new()
                {
                    SourceMediaID = 1,
                    TestID = 2,
                    SegmentKey = "segment2",
                    Second = 2,
                    Playback = 0.85f,
                    Attention = 0.75f,
                }
            });

        var collector = new ForcedExposureExportDataCollector(
            _forcedExposureCollectorMock.Object,
            segmentRepositoryMock.Object
        );

        // Act
        var result = await collector.GetExportData(request);

        // Assert
        result.VisibleColumnsOrder.Should().Contain("audience");
        result.VisibleColumnsOrder.Should().Contain("views");
        result.CreativesPayload.Should().NotBeEmpty();
        result.CurvesPayload.Should().NotBeEmpty();
        result.MediaCount.Should().Be(1); 
    }


    [Test]
    public async Task GetExportData_ShouldFilterCreativesBasedOnThreshold()
    {
        // Arrange
        var request = new GetExportDataRequest
        {
            AccountId = 1,
            IsAdmin = true,
            SegmentKeys = new List<string> { "all" },
            MinViewThresholdForScores = 100,
            VisibleColumnsOrder = new List<string> { "views" },
            Sorting = new List<GridSortingItem> { new() { Field = "segmentKey", Direction = "asc" } },
        };

        var mockCreatives = new List<ForcedExposureCreative>
        {
            new()
            {
                SourceMediaID = 1,
                TestID = 1,
                SegmentKey = "all",
                Views = 50, 
                IsEnabledForClients = true,
                Audience = "all",
                Country = "US",
                Brand = "Brand1",
                Platform = "Web"
            },
            new()
            {
                SourceMediaID = 2,
                TestID = 2,
                SegmentKey = "all",
                Views = 150, 
                IsEnabledForClients = true,
                Audience = "all",
                Country = "US",
                Brand = "Brand2",
                Platform = "Mobile"
            },
            new()
            {
                SourceMediaID = 3,
                TestID = 3,
                SegmentKey = "all",
                Views = 200,
                IsEnabledForClients = true,
                Audience = "all",
                Country = "UK",
                Brand = "Brand3",
                Platform = "TV"
            }
        };

        var mockMediaSegments = new List<SegmentKeyLabel>
        {
            new() { SegmentKey = "all" }
        };

        _forcedExposureCollectorMock
            .Setup(r => r.GetCreativesAsync(It.IsAny<GetCreativesRequest>()))
            .ReturnsAsync(mockCreatives); 

        _segmentRepositoryMock
            .Setup(r => r.GetSegmentKeys(It.IsAny<GetSegmentKeysRequest>()))
            .ReturnsAsync(mockMediaSegments); 

        // Act
        var result = await _collector.GetExportData(request);
        result.CreativesPayload.Should().HaveCount(3);
        result.VisibleColumnsOrder.Should().Contain("views");
    }


    [Test]
    public async Task GetExportData_ShouldReturnEmptyIfNoCreativesMatch()
    {
        // Arrange
        var request = new GetExportDataRequest
        {
            AccountId = 1,
            IsAdmin = true,
            SegmentKeys = new List<string> { "segmentKey1" },
            MinViewThresholdForScores = 1000,
            VisibleColumnsOrder = new List<string> { "views" },
            Sorting = new List<GridSortingItem> { new() { Field = "segmentKey", Direction = "asc" } },
            Media = new List<MediaModelRequest> { new() { SourceMediaID = 1, TestID = 2 } }
        };

        var mockCreatives = new List<ForcedExposureCreative>
        {
            new()
            {
                SourceMediaID = 1,
                TestID = 1,
                SegmentKey = "segmentKey1",
                Views = 50,
                IsEnabledForClients = true
            }
        };

        _forcedExposureCollectorMock
            .Setup(r => r.GetCreativesAsync(It.IsAny<GetCreativesRequest>()))
            .ReturnsAsync(mockCreatives);

        var segmentRepositoryMock = new Mock<ISegmentRepository>();
        segmentRepositoryMock
            .Setup(r => r.GetSegmentKeys(It.IsAny<GetSegmentKeysRequest>()))
            .ReturnsAsync(new List<SegmentKeyLabel>());

        var collector = new ForcedExposureExportDataCollector(
            _forcedExposureCollectorMock.Object,
            segmentRepositoryMock.Object
        );

        // Act
        var result = await collector.GetExportData(request);

        // Assert
        result.CreativesPayload.Should().BeEmpty();
        result.VisibleColumnsOrder.Should().Contain("audience");
    }
}