using Amazon;
using Amazon.Extensions.NETCore.Setup;
using Amazon.S3;
using BusinessLayer.Repository;
using BusinessLayer.Service;
using DataLayer.Client;
using DataLayer.Clients;
using DataLayer.Repository;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Realeyes.PreView.Infrastructure.Services.Dremio.Configuration;
using Realeyes.PreView.Infrastructure.Services.Dremio.Extension;

namespace DataLayer.Configuration;

public static class DataConfigurationExtension
{
    public static IServiceCollection AddDataConfiguration(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddScoped<IDremioDataProvider, DremioDataProvider>();
        services.AddScoped<IQueryExecutor, DremioQueryExecutor>();
        services.AddScoped<IForcedExposureRepository, ForcedExposureRepository>();
        services.AddScoped<ISegmentRepository, SegmentRepository>();
        services.AddScoped<IMediaSegmentRepository, MediaSegmentRepository>();
        services.AddScoped<IInContextRepository, InContextRepository>();
        services.AddScoped<IExportClient, ExportClient>();
        services.AddScoped<IExportRepository, ExportRepository>();
        services.AddScoped<IForcedExposureMetricsRepository, ForcedExposureMetricsRepository>();
        services.AddScoped<IExportService, ExportService>();
        services.AddScoped<IAdSetRepository, AdSetRepository>();
        services.AddScoped<IAccountProductRepository, AccountProductRepository>();


        services.AddAWSService<IAmazonS3>(
            new AWSOptions { Region = RegionEndpoint.GetBySystemName("eu-west-1") });

        services.Configure<ExportSettings>(configuration.GetSection(ExportSettings.SectionName));
        services.AddDremioSettingsFrom(configuration);
        services.AddDremioService();        

        return services;
    }    
}