using System;
using System.Collections.Generic;

namespace DataLayer.Dremio.QueryParameters.Report;

public class AdSetSearchQueryParameters
{
    public string SegmentKey { get; set; }
    public string AdSetKey { get; set; }
    public string AdSetName { get; set; }   
    public string SourceMediaName { get; set; }
    public string SourceMediaKey { get; set; }
    public int? Duration { get; set; }
    public DateTimeOffset? CreationDate { get; set; }
    public IEnumerable<int> AccountIds { get;  set; }
}
