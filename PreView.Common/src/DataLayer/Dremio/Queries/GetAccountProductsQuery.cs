using System.Linq;
using DataLayer.Dremio.QueryParameters;
using Realeyes.PreView.Infrastructure.Services.Dremio.Extension;

namespace DataLayer.Dremio.Queries;

public class GetAccountProductsQuery : IDremioQuery
{
    private readonly GetAccountProductsQueryParameters _parameters;

    public GetAccountProductsQuery(GetAccountProductsQueryParameters parameters)
    {
        _parameters = parameters;
    }

    public string Build()
    {
        var query = $@"select * from virtual.data.preview.app.preview_account_products";
      
        var accountIds = _parameters.AccountIds?.ToList();

        if (accountIds.Count > 0)
        {
            query += $"\r\nwhere AccountID in ({string.Join(", ", accountIds)})";


            if (!_parameters.IsAdmin)
            {
                query += $"\r\nand IsPreviewEnabled = 1";
            }

        }

        query += $"\r\norder by AccountName";

        return query;
    }
}