using System;
using System.Text;

namespace DataLayer.Dremio.Queries;

public class DremioSelectQueryBuilder : IDremioQueryBuilder
{
    private string fields;
    private string dataset;
    private string conditions;
    private string orderBy;
    private string groupBy;
    private int limit;
    private int offset;

    public IDremioQueryBuilder Fields(string value)
    {
        this.fields = value;
        return this;
    }

    public IDremioQueryBuilder Dataset(string value)
    {
        this.dataset = value;
        return this;
    }

    public IDremioQueryBuilder Where(string value)
    {
        this.conditions = value;
        return this;
    }

    public IDremioQueryBuilder OrderBy(string value)
    {
        this.orderBy = value;
        return this;
    }

    public IDremioQueryBuilder GroupBy(string value)
    {
        this.groupBy = value;
        return this;
    }

    public IDremioQueryBuilder Limit(int value)
    {
        this.limit = value;
        return this;
    }

    public IDremioQueryBuilder Offset(int value)
    {
        this.offset = value;
        return this;
    }

    public string Build()
    {
        if (string.IsNullOrEmpty(this.dataset)) { throw new InvalidOperationException("Dataset cannot be null or empty in a query."); }
        var query = new StringBuilder();

        AddSelectStatemetTo(query);
        AddStatementTo(query, "WHERE", this.conditions);
        AddStatementTo(query, "GROUP BY", this.groupBy);
        AddStatementTo(query, "ORDER BY", this.orderBy);
        AddPaginationTo(query);

        return query.ToString();
    }

    private void AddSelectStatemetTo(StringBuilder query)
    {
        var fields = string.IsNullOrWhiteSpace(this.fields) ? "*" : this.fields;
        query.AppendLine($"SELECT {fields} FROM {this.dataset}");
    }

    private void AddPaginationTo(StringBuilder query)
    {
        if (this.limit > 0)
        {
            query.AppendLine($"LIMIT {this.limit}");
        }

        if (this.offset >= 0)
        {
            query.AppendLine($"OFFSET {this.offset}");
        }
    }

    private void AddStatementTo(StringBuilder query, string statement, string value)
    {
        if (!string.IsNullOrEmpty(value))
        {
            query.AppendLine($" {statement} {value}");
        }
    }
}