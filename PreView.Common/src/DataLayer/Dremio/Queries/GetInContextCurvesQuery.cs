using System.Linq;
using DataLayer.Dremio.QueryParameters;
using Realeyes.PreView.Infrastructure.Services.Dremio.Extension;

namespace DataLayer.Dremio.Queries;

public class GetInContextCurvesQuery : IDremioQuery
{
    private readonly GetInContextCurvesQueryParameters _parameters;

    public GetInContextCurvesQuery(GetInContextCurvesQueryParameters parameters)
    {
        _parameters = parameters;
    }

    public string Build()
    {
        var query =
            $@"select * from virtual.data.preview.curves.preview_curves_incontext where";

        var hasSegmentKey = _parameters.SegmentKeys?.Count() > 0;

        if (hasSegmentKey)
        {
            var segmentKeys = _parameters.SegmentKeys.Select(s => $"'{s}'");
            query += $"\r\nSegmentKey in ({string.Join(", ", segmentKeys)})";
        }

        if (_parameters.Media?.Count() > 0)
        {
            var mediaIDs = _parameters.Media.Select(m => m.SourceMediaID);
            var testIDs = _parameters.Media.Select(m => m.TestID);
            var taskIDs = _parameters.Media.Select(m => $"'{m.TaskID}'");

            query +=
                $"\r\n{(hasSegmentKey ? "and " : "")}SourceMediaID in ({string.Join(", ", mediaIDs)}) and TestID in ({string.Join(", ", testIDs)}) and TaskID in ({string.Join(", ", taskIDs)})";
        }

        query += "\r\norder by SourceMediaID, TestID, TaskID, SegmentKey, \"Second\"";


        return query;
    }
}