using System.Linq;
using Realeyes.PreView.Infrastructure.Services.Dremio.Extension;

namespace DataLayer.Dremio.Queries;

public class GetForcedExposureCurvesQuery : IDremioQuery
{
    private readonly GetForcedExposureCurvesQueryParameters _parameters;

    public GetForcedExposureCurvesQuery(GetForcedExposureCurvesQueryParameters parameters)
    {
        _parameters = parameters;
    }

    public string Build()
    {
        var query =
            $@"select * from virtual.data.preview.curves.preview_curves_forced_exposure where";

        var hasSegmentKey = _parameters.SegmentKeys?.Count() > 0;

        if (hasSegmentKey)
        {
            var segmentKeys = _parameters.SegmentKeys.Select(s => $"'{s}'");
            query += $"\r\nSegmentKey in ({string.Join(", ", segmentKeys)})";
        }

        if (_parameters.Media?.Count() > 0)
        {
            var mediaIDs = _parameters.Media.Select(m => m.SourceMediaID);
            var testIDs = _parameters.Media.Select(m => m.TestID);

            query +=
                $"\r\n{(hasSegmentKey ? "and " : "")}SourceMediaID in ({string.Join(", ", mediaIDs)}) and TestID in ({string.Join(", ", testIDs)})";
        }

        query += "\r\norder by SourceMediaID, TestID, SegmentKey, \"Second\"";

        return query;
    }
}