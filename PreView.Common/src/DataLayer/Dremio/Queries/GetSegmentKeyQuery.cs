using System.Linq;
using BusinessLayer.Model;
using DataLayer.Dremio.QueryParameters;
using Realeyes.PreView.Infrastructure.Services.Dremio.Extension;

namespace DataLayer.Dremio.Queries;

public class GetSegmentKeyQuery : IDremioQuery
{
    private readonly GetSegmentKeysQueryParameters _parameters;

    public GetSegmentKeyQuery(GetSegmentKeysQueryParameters parameters)
    {
        _parameters = parameters;
    }

    public string Build()
    {
        var query =
            $@"select * from virtual.data.preview.app.creative_performance_account_media_segments where AccountID={_parameters.AccountId}";

        if (_parameters.ProductType != null)
            query += $"\r\nand ProductType='{(_parameters.ProductType == ProductType.InContext ? "IC" : "IF")}'";

        if (_parameters.SegmentKeys?.Count() > 0)
        {
            var segmentKeys = _parameters.SegmentKeys.Select(s => $"'{s}'").ToList();
            query += $"\r\nand SegmentKey in ({string.Join(", ", segmentKeys)})";
        }

        return query;
    }
}