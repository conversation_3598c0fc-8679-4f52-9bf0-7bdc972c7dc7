using DataLayer.Dremio.QueryParameters.Report;
using Realeyes.PreView.Infrastructure.Services.Dremio.Extension;
using System;
using System.Globalization;
using System.Text;

namespace DataLayer.Dremio.Queries.Report;

public class AdSetSearchQuery : IDremioQuery
{
    private const string SegmentKeyColumn = "SegmentKey";
    private const string AdSetColumn = "AdSet";
    private const string DurationColumn = "Duration";
    private const string SourceMediaColumn = "SourceMedia";
    private const string AdSetKeyColumn = "AdSetExternalKey";
    private const string AndOperator = "and";
    private const string OrOperator = "or";
    private const string SourceMediaKeyColumn = "SourceMediaExternalKey";
    private const string CreationDateColumn = "CreationDate";
    private const string AccountIdColumn = "AccountID";

    private readonly AdSetSearchQueryParameters _parameters;
    

    public AdSetSearchQuery(AdSetSearchQueryParameters parameters)
    {
        _parameters = parameters;
    }

    public string Build()
    {
        if(_parameters == null) { return null; }

        var queryBuilder = new DremioSelectQueryBuilder()
            .Fields($"{AdSetKeyColumn},{AccountIdColumn}, count(*) as NumberOfSourceMedias")
            .Dataset("virtual.data.preview.app.creative_performance_portfolio_forced_exposure_grid_with_adset")
            .Where(ConstructWhereStatement())
            .GroupBy($"{AdSetKeyColumn}, {AccountIdColumn}");

        return queryBuilder.Build();
    }

    private string ConstructWhereStatement()
    {
        var nestedStatementBuilder = new StringBuilder();
        AddLikeStatement(nestedStatementBuilder, AdSetColumn, _parameters.AdSetName, AndOperator);
        AddEqualsStatement(nestedStatementBuilder, DurationColumn, _parameters?.Duration, AndOperator);
        AddLikeStatement(nestedStatementBuilder, SourceMediaColumn, _parameters.SourceMediaName, AndOperator);
        AddLikeStatement(nestedStatementBuilder, AdSetKeyColumn, _parameters.AdSetKey, AndOperator);
        AddLikeStatement(nestedStatementBuilder, SourceMediaKeyColumn, _parameters.SourceMediaKey, AndOperator);
        AddDateCompasimStatement(nestedStatementBuilder, CreationDateColumn, _parameters.CreationDate, AndOperator);

        var mainStatementBuilder = new StringBuilder();
        AddEqualsStatement(mainStatementBuilder, SegmentKeyColumn, _parameters.SegmentKey);
        mainStatementBuilder.Append($" {AndOperator} {AccountIdColumn} in ({string.Join(", ", _parameters.AccountIds)})");
        if (nestedStatementBuilder.Length > 0)
        {
            mainStatementBuilder.Append($" {AndOperator} ({nestedStatementBuilder.ToString()})");
        }

        return mainStatementBuilder.ToString();
    }

    private void AddEqualsStatement(StringBuilder stringBuilder, string columnName, string stringValue, string logicalOperator = null)
    {
        if (string.IsNullOrEmpty(stringValue)) { return; }

        if (!string.IsNullOrEmpty(logicalOperator) && stringBuilder.Length != 0)
        {
            stringBuilder.Append($" {logicalOperator} ");
        }

        stringBuilder.Append($"{columnName} = '{stringValue}'");
    }

    private void AddDateCompasimStatement(StringBuilder stringBuilder, string columnName, DateTimeOffset? date, string logicalOperator = null)
    {
        if (!date.HasValue) { return; }

        if (!string.IsNullOrEmpty(logicalOperator) && stringBuilder.Length != 0)
        {
            stringBuilder.Append($" {logicalOperator} ");
        }

        stringBuilder.Append($"{columnName} = '{date.Value.ToString("o", CultureInfo.InvariantCulture)}'");
    }

    private void AddEqualsStatement(StringBuilder stringBuilder, string columnName, int? value, string logicalOperator = null)
    {
        if (!value.HasValue) { return; }

        if (!string.IsNullOrEmpty(logicalOperator) && stringBuilder.Length != 0)
        {
            stringBuilder.Append($" {logicalOperator} ");
        }

        stringBuilder.Append($"{columnName} = {value}");
    }

    private void AddLikeStatement(StringBuilder stringBuilder, string columnName, string stringValue, string logicalOperator = null)
    {
        if (string.IsNullOrEmpty(stringValue)) { return; }
        
        if (!string.IsNullOrEmpty(logicalOperator) && stringBuilder.Length != 0) 
        {
            stringBuilder.Append($" {logicalOperator} ");
        }

        stringBuilder.Append($"{columnName} like '%{stringValue}%'");
    }
}
