using DataLayer.Dremio.QueryParameters.Report;
using Realeyes.PreView.Infrastructure.Services.Dremio.Extension;

namespace DataLayer.Dremio.Queries.Report;

public class GetForcedExposureCurveQuery : IDremioQuery
{
    private readonly GetForcedExposureCurveQueryParameters _parameters;

    public GetForcedExposureCurveQuery(GetForcedExposureCurveQueryParameters parameters)
    {
        _parameters = parameters;
    }

    public string Build()
    {
        var query =
            $"select * from virtual.data.preview.curves.preview_curves_forced_exposure_with_adset where SegmentKey = '{_parameters.SegmentKey}' and AdSetExternalKey = '{_parameters.AdSetExternalKey}'";

        var dremioQueryBuilder = new DremioSelectQueryBuilder()
            .Fields("*")
            .Dataset("virtual.data.preview.curves.preview_curves_forced_exposure_with_adset")
            .Where($"SegmentKey = '{_parameters.SegmentKey}' and AdSetExternalKey = '{_parameters.AdSetExternalKey}'");

        var dynamicQuery = dremioQueryBuilder.Build();
        return dynamicQuery;
    }
}