using DataLayer.Dremio.QueryParameters.Report;
using Realeyes.PreView.Infrastructure.Services.Dremio.Extension;
using System.Linq;

namespace DataLayer.Dremio.Queries.Report;

public class AdSetSearchDetailsQuery : IDremioQuery
{
    private const string SegmentKeyColumn = "SegmentKey";
    private const string AdSetColumn = "AdSet";
    private const string DurationColumn = "Duration";
    private const string SourceMediaColumn = "SourceMedia";
    private const string AdSetKeyColumn = "AdSetExternalKey";
    private const string SourceMediaKeyColumn = "SourceMediaExternalKey";
    private const string CreationDateColumn = "CreationDate";
    private const string AccountIdColumn = "AccountID";
    private readonly AdSetSearchDetailsQueryParameters _parameters;

    public AdSetSearchDetailsQuery(AdSetSearchDetailsQueryParameters parameters)
    {
        _parameters = parameters;
    }

    public string Build()
    {
        if (!_parameters?.AdSetExternelKeys.Any() ?? true) { return null; }

        var externalKeyList = string.Join(", ", _parameters.AdSetExternelKeys.Select(key => $"'{key}'"));

        var queryBuilder = new DremioSelectQueryBuilder()
            .Fields($"{AdSetKeyColumn}, {AdSetColumn}, {SourceMediaKeyColumn}, {SourceMediaColumn}, {DurationColumn}, {CreationDateColumn}, {AccountIdColumn}")
            .Dataset("virtual.data.preview.app.creative_performance_portfolio_forced_exposure_grid_with_adset")
            .Where($"{SegmentKeyColumn} = 'all' and {AdSetKeyColumn} in ({externalKeyList})");

        return queryBuilder.Build();
    }
}
