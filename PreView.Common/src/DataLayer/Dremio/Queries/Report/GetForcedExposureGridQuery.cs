using DataLayer.Dremio.QueryParameters.Report;
using Realeyes.PreView.Infrastructure.Services.Dremio.Extension;

namespace DataLayer.Dremio.Queries.Report;

public class GetForcedExposureGridQuery : IDremioQuery
{
    private readonly GetForcedExposureGridQueryParameters _parameters;

    public GetForcedExposureGridQuery(GetForcedExposureGridQueryParameters parameters)
    {
        _parameters = parameters;
    }

    public string Build()
    {
        var query =
            $"select * from virtual.data.preview.app.creative_performance_portfolio_forced_exposure_grid_with_adset where SegmentKey = '{_parameters.SegmentKey}' and AdSetExternalKey = '{_parameters.AdSetExternalKey}'";

        var dremioQueryBuilder = new DremioSelectQueryBuilder()
            .Fields("*")
            .Dataset("virtual.data.preview.app.creative_performance_portfolio_forced_exposure_grid_with_adset")
            .Where($"SegmentKey = '{_parameters.SegmentKey}' and AdSetExternalKey = '{_parameters.AdSetExternalKey}'");

        var dynamicQuery = dremioQueryBuilder.Build();
        return dynamicQuery;
    }
}