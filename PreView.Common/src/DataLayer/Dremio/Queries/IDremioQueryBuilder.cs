namespace DataLayer.Dremio.Queries
{
    public interface IDremioQueryBuilder
    {
        string Build();
        IDremioQueryBuilder Dataset(string value);
        IDremioQueryBuilder Fields(string value);
        IDremioQueryBuilder GroupBy(string value);
        IDremioQueryBuilder Limit(int value);
        IDremioQueryBuilder Offset(int value);
        IDremioQueryBuilder OrderBy(string value);
        IDremioQueryBuilder Where(string value);
    }
}