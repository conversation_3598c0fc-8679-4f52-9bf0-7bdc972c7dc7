using System.Linq;
using DataLayer.Dremio.QueryParameters;
using Realeyes.PreView.Infrastructure.Services.Dremio.Extension;

namespace DataLayer.Dremio.Queries;

public class GetForcedExposureCreativesQuery : IDremioQuery
{
    private readonly GetForcedExposureCreativesQueryParameters _parameters;

    public GetForcedExposureCreativesQuery(GetForcedExposureCreativesQueryParameters parameters)
    {
        _parameters = parameters;
    }

    public string Build()
    {
        var query =
            $@"select * from virtual.data.preview.app.creative_performance_portfolio_forced_exposure_grid where AccountID = {_parameters.AccountId}";

        if (_parameters.SegmentKeys?.Count() > 0)
        {
            var segmentKeys = _parameters.SegmentKeys.Select(s => $"'{s}'");
            query += $"\r\nand SegmentKey in ({string.Join(", ", segmentKeys)})";
        }

        if (_parameters.Media?.Count() > 0)
        {
            var mediaIDs = _parameters.Media.Select(m => m.SourceMediaID);
            var testIDs = _parameters.Media.Select(m => m.TestID);
            var orderAdSetIDs = _parameters.Media.Select(m => $"'{m.OrderAdSetID}'");

            query +=
                $"\r\nand SourceMediaID in ({string.Join(", ", mediaIDs)}) and TestID in ({string.Join(", ", testIDs)}) and OrderAdSetID in ({string.Join(", ", orderAdSetIDs)})";
        }


        return query;
    }
}