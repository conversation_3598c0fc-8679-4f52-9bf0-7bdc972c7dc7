using System.Linq;
using DataLayer.Dremio.QueryParameters;
using Realeyes.PreView.Infrastructure.Services.Dremio.Extension;

namespace DataLayer.Dremio.Queries;

public class GetMediaSegmentKeyQuery : IDremioQuery
{
    private readonly GetMediaSegmentKeysQueryParameters _parameters;

    public GetMediaSegmentKeyQuery(GetMediaSegmentKeysQueryParameters parameters)
    {
        _parameters = parameters;
    }

    public string Build()
    {
        var query =
            $@"select * from virtual.data.preview.app.creative_performance_media_segments where";


        var mediaIDs = _parameters.Media?.Select(m => m.SourceMediaID);
        var testIDs = _parameters.Media?.Select(m => m.TestID);
        var taskIDs = _parameters.Media?.Where(m => m.TaskID is not null).Select(m => $"'{m.TaskID}'");

        query += $"\r\nSourceMediaID in ({string.Join(", ", mediaIDs)}) and TestID in ({string.Join(", ", testIDs)})";

        if (taskIDs.Any())
        {
            query += $" and TaskID in ({string.Join(", ", taskIDs)})";
        }
        

        return query;
    }
}