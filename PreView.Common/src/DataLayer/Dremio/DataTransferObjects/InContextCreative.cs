using System;

namespace DataLayer.Dremio.DataTransferObjects;

public class InContextCreative
{
    public string Account { get; set; }
    public int? AccountID { get; set; }
    public string ProjectID { get; set; }
    public string SubjectGroupID { get; set; }
    public bool? IsForcedExposure { get; set; }
    public int? SourceMediaID { get; set; }
    public int? TestID { get; set; }
    public int? PDProjectID { get; set; }
    public string SurveyKeyAlias { get; set; }
    public int? InFocusTestID { get; set; }
    public string TaskID { get; set; }
    public string Order { get; set; }
    public string OrderExternalKey { get; set; }
    public int? OrderAdSetID { get; set; }
    public string CustomAdSets { get; set; }
    public string Country { get; set; }
    public string Country_code { get; set; }
    public string SegmentKey { get; set; }
    public string GeographicRegion { get; set; }
    public string SourceMediaThumbnailFileName { get; set; }
    public string SourceMediaThumbstripFileName { get; set; }
    public string SourceMediaConvertedFileName { get; set; }
    public string SourceMedia { get; set; }
    public int? Duration { get; set; }
    public int? NormFallbackIC { get; set; }
    public int? NormFallbackIF { get; set; }
    public int? NormFallbackSurveyIC { get; set; }
    public int? NormFallbackSurveyIF { get; set; }
    public string NormSegmentKeyIC { get; set; }
    public string NormSegmentKeyIF { get; set; }
    public string AdformatName { get; set; }
    public string EnvironmentCategory { get; set; }
    public string Brand { get; set; }
    public int? BrandID { get; set; }
    public string BrandLogoFileName { get; set; }
    public string TopCategory { get; set; }
    public string MidCategory { get; set; }
    public string SubCategory { get; set; }
    public string Platform { get; set; }
    public string Device { get; set; }
    public string Adformat { get; set; }
    public string AdformatText { get; set; }
    public string AdformatTextIF { get; set; }
    public DateTimeOffset? CreationDate { get; set; }
    public bool? IsEnabledForClients { get; set; }
    public int? Views { get; set; }
    public float? QualityScore { get; set; }
    public int? QualityScore_index { get; set; }
    public float? AttentiveSeconds { get; set; }
    public float? AttentiveSecondsMedian { get; set; }
    public float? AttentiveSecondsDiff { get; set; }
    public int? AttentiveSecondsRank { get; set; }
    public float? AttentiveSecondsIF { get; set; }
    public float? AttentiveSecondsIFMedian { get; set; }
    public float? AttentiveSecondsIFDiff { get; set; }
    public int? AttentiveSecondsIFRank { get; set; }
    public float? AttentiveSecondsVTR { get; set; }
    public float? AttentiveSecondsVTRMedian { get; set; }
    public float? AttentiveSecondsVTRDiff { get; set; }
    public int? AttentiveSecondsVTRRank { get; set; }
    public float? AttentiveSecondsVTRIF { get; set; }
    public float? AttentiveSecondsVTRIFMedian { get; set; }
    public float? AttentiveSecondsVTRIFDiff { get; set; }
    public int? AttentiveSecondsVTRIFRank { get; set; }
    public float? Reactions { get; set; }
    public float? ReactionsMedian { get; set; }
    public float? ReactionsDiff { get; set; }
    public int? ReactionsRank { get; set; }
    public float? ReactionsIC { get; set; }
    public float? ReactionsICMedian { get; set; }
    public float? ReactionsICDiff { get; set; }
    public int? ReactionsICRank { get; set; }
    public float? NegativityAvgIC { get; set; }
    public float? NegativityAvgICMedian { get; set; }
    public float? NegativityAvgICDiff { get; set; }
    public int? NegativityAvgICRank { get; set; }
    public float? NegativityAvgIF { get; set; }
    public float? NegativityAvgIFMedian { get; set; }
    public float? NegativityAvgIFDiff { get; set; }
    public int? NegativityAvgIFRank { get; set; }
    public float? AttentionAvgIC { get; set; }
    public float? AttentionAvgICMedian { get; set; }
    public float? AttentionAvgICDiff { get; set; }
    public int? AttentionAvgICRank { get; set; }
    public float? AttentionAvgIF { get; set; }
    public float? AttentionAvgIFMedian { get; set; }
    public float? AttentionAvgIFDiff { get; set; }
    public int? AttentionAvgIFRank { get; set; }
    public float? BrandRecognition { get; set; }
    public float? BrandRecognitionMedian { get; set; }
    public float? BrandRecognitionDiff { get; set; }
    public int? BrandRecognitionRank { get; set; }
    public float? AdLikeability { get; set; }
    public float? AdLikeabilityMedian { get; set; }
    public float? AdLikeabilityDiff { get; set; }
    public int? AdLikeabilityRank { get; set; }
    public float? HappyPeak { get; set; }
    public float? HappyPeakMedian { get; set; }
    public float? HappyPeakDiff { get; set; }
    public int? HappyPeakRank { get; set; }
    public float? HappyPeakIC { get; set; }
    public float? HappyPeakICMedian { get; set; }
    public float? HappyPeakICDiff { get; set; }
    public int? HappyPeakICRank { get; set; }
    public float? VTR { get; set; }
    public int? VTRRank { get; set; }
    public float? VTRMedian { get; set; }
    public float? VTRDiff { get; set; }
    public float? VTRIF { get; set; }
    public int? VTRIFRank { get; set; }
    public float? VTRIFMedian { get; set; }
    public float? VTRIFDiff { get; set; }
    public float? PlaybackSeconds { get; set; }
    public int? PlaybackSecondsRank { get; set; }
    public float? PlaybackSecondsMedian { get; set; }
    public float? PlaybackSecondsDiff { get; set; }
    public float? PlaybackSecondsIF { get; set; }
    public int? PlaybackSecondsIFRank { get; set; }
    public float? PlaybackSecondsIFMedian { get; set; }
    public float? PlaybackSecondsIFDiff { get; set; }
    public float? AdRecognition { get; set; }
    public int? AdRecognitionRank { get; set; }
    public float? AdRecognitionMedian { get; set; }
    public float? AdRecognitionDiff { get; set; }
    public float? SurprisePeak { get; set; }
    public int? SurprisePeakRank { get; set; }
    public float? SurprisePeakMedian { get; set; }
    public float? SurprisePeakDiff { get; set; }
    public float? SurprisePeakIC { get; set; }
    public int? SurprisePeakICRank { get; set; }
    public float? SurprisePeakICMedian { get; set; }
    public float? SurprisePeakICDiff { get; set; }
    public float? ConfusionPeak { get; set; }
    public int? ConfusionPeakRank { get; set; }
    public float? ConfusionPeakMedian { get; set; }
    public float? ConfusionPeakDiff { get; set; }
    public float? ConfusionPeakIC { get; set; }
    public int? ConfusionPeakICRank { get; set; }
    public float? ConfusionPeakICMedian { get; set; }
    public float? ConfusionPeakICDiff { get; set; }
    public float? ContemptPeak { get; set; }
    public int? ContemptPeakRank { get; set; }
    public float? ContemptPeakMedian { get; set; }
    public float? ContemptPeakDiff { get; set; }
    public float? ContemptPeakIC { get; set; }
    public int? ContemptPeakICRank { get; set; }
    public float? ContemptPeakICMedian { get; set; }
    public float? ContemptPeakICDiff { get; set; }
    public float? DisgustPeak { get; set; }
    public int? DisgustPeakRank { get; set; }
    public float? DisgustPeakMedian { get; set; }
    public float? DisgustPeakDiff { get; set; }
    public float? DisgustPeakIC { get; set; }
    public int? DisgustPeakICRank { get; set; }
    public float? DisgustPeakICMedian { get; set; }
    public float? DisgustPeakICDiff { get; set; }
    public float? BrandTrust { get; set; }
    public int? BrandTrustRank { get; set; }
    public float? BrandTrustMedian { get; set; }
    public float? BrandTrustDiff { get; set; }
    public float? Persuasion { get; set; }
    public int? PersuasionRank { get; set; }
    public float? PersuasionMedian { get; set; }
    public float? PersuasionDiff { get; set; }
    public float? DistractionAvgIC { get; set; }
    public int? DistractionAvgICRank { get; set; }
    public float? DistractionAvgICMedian { get; set; }
    public float? DistractionAvgICDiff { get; set; }
    public float? DistractionAvgIF { get; set; }
    public int? DistractionAvgIFRank { get; set; }
    public float? DistractionAvgIFMedian { get; set; }
    public float? DistractionAvgIFDiff { get; set; }

    public string ReactionsNormDeviceIF { get; set; }
    public string ReactionsNormDeviceIC { get; set; }
    public string SurveyNormDeviceIF { get; set; }
    public string SurveyNormDeviceIC { get; set; }
    public string AttentionNormDeviceIF { get; set; }
    public string AttentionNormDeviceIC { get; set; }

    public string ReactionsNormFormatIF { get; set; }
    public string ReactionsNormFormatIC { get; set; }
    public string SurveyNormFormatIF { get; set; }
    public string SurveyNormFormatIC { get; set; }
    public string AttentionNormFormatIF { get; set; }
    public string AttentionNormFormatIC { get; set; }

    public int? ReactionsNormDurationIF { get; set; }
    public int? ReactionsNormDurationIC { get; set; }
    public int? SurveyNormDurationIF { get; set; }
    public int? SurveyNormDurationIC { get; set; }
    public int? AttentionNormDurationIF { get; set; }
    public int? AttentionNormDurationIC { get; set; }

    public string ReactionsNormAdFormatIF { get; set; }
    public string ReactionsNormAdFormatIC { get; set; }
    public string SurveyNormAdFormatIF { get; set; }
    public string SurveyNormAdFormatIC { get; set; }
    public string AttentionNormAdFormatIF { get; set; }
    public string AttentionNormAdFormatIC { get; set; }

    public string ReactionsNormAdFormatNameIF { get; set; }
    public string ReactionsNormAdFormatNameIC { get; set; }
    public string SurveyNormAdFormatNameIF { get; set; }
    public string SurveyNormAdFormatNameIC { get; set; }
    public string AttentionNormAdFormatNameIF { get; set; }
    public string AttentionNormAdFormatNameIC { get; set; }

    public string ReactionsNormEnvironmentCategoryIF { get; set; }
    public string ReactionsNormEnvironmentCategoryIC { get; set; }
    public string SurveyNormEnvironmentCategoryIF { get; set; }
    public string SurveyNormEnvironmentCategoryIC { get; set; }
    public string AttentionNormEnvironmentCategoryIF { get; set; }
    public string AttentionNormEnvironmentCategoryIC { get; set; }

    public string ReactionsNormRegionIF { get; set; }
    public string ReactionsNormRegionIC { get; set; }
    public string SurveyNormRegionIF { get; set; }
    public string SurveyNormRegionIC { get; set; }
    public string AttentionNormRegionIF { get; set; }
    public string AttentionNormRegionIC { get; set; }

    public int? ReactionsNormSampleSizeIF { get; set; }
    public int? ReactionsNormSampleSizeIC { get; set; }
    public int? SurveyNormSampleSizeIF { get; set; }
    public int? SurveyNormSampleSizeIC { get; set; }
    public int? AttentionNormSampleSizeIF { get; set; }
    public int? AttentionNormSampleSizeIC { get; set; }

    public string ReactionsNormRefreshDateIF { get; set; }
    public string ReactionsNormRefreshDateIC { get; set; }
    public string SurveyNormRefreshDateIF { get; set; }
    public string SurveyNormRefreshDateIC { get; set; }
    public string AttentionNormRefreshDateIF { get; set; }
    public string AttentionNormRefreshDateIC { get; set; }

    public string ReactionsNormCustomNormFilters { get; set; }
    public string AttentionNormCustomNormFilters { get; set; }
    public string SurveyNormCustomNormFilters { get; set; }
    public string ReactionsNormCustomNormFiltersIC { get; set; }
    public string AttentionNormCustomNormFiltersIC { get; set; }
    public string SurveyNormCustomNormFiltersIC { get; set; }
    public string ReactionsNormCustomNormFiltersIF { get; set; }
    public string AttentionNormCustomNormFiltersIF { get; set; }
    public string SurveyNormCustomNormFiltersIF { get; set; }
}