namespace DataLayer.Dremio.DataTransferObjects;

public class ForcedExposureCurve
{
    public int SourceMediaID { get; set; }
    public int TestID { get; set; }
    public int Second { get; set; }
    public string SegmentKey { get; set; }
    public float? Attention { get; set; }
    public float? AttentionNorm { get; set; }
    public float? AllReactions { get; set; }
    public float? AllReactionsNorm { get; set; }
    public float? Negativity { get; set; }
    public float? NegativityNorm { get; set; }
    public float? Happiness { get; set; }
    public float? HappinessNorm { get; set; }
    public float? Contempt { get; set; }
    public float? ContemptNorm { get; set; }
    public float? Surprise { get; set; }
    public float? SurpriseNorm { get; set; }
    public float? Confusion { get; set; }
    public float? ConfusionNorm { get; set; }
    public float? Disgust { get; set; }
    public float? DisgustNorm { get; set; }
    public float? Playback { get; set; }
    public float? PlaybackNorm { get; set; }
    public float? Distraction { get; set; }
    public float? DistractionNorm { get; set; }
    public float? NeutralAttention { get; set; }
    public float? NeutralAttentionNorm { get; set; }
}