namespace DataLayer.Dremio.DataTransferObjects;

public class InContextCurve
{
    public int SourceMediaID { get; set; }
    public int TestID { get; set; }
    public string TaskID { get; set; }
    public int Second { get; set; }
    public string SegmentKey { get; set; }
    public float? Attention { get; set; }
    public float? AttentionNorm { get; set; }
    public float? AttentionIF { get; set; }
    public float? AttentionIFNorm { get; set; }
    public float? AllReactions { get; set; }
    public float? AllReactionsNorm { get; set; }
    public float? AllReactionsIC { get; set; }
    public float? AllReactionsICNorm { get; set; }
    public float? NegativityIC { get; set; }
    public float? NegativityICNorm { get; set; }
    public float? Negativity { get; set; }
    public float? NegativityNorm { get; set; }
    public float? Happiness { get; set; }
    public float? HappinessNorm { get; set; }
    public float? HappinessIC { get; set; }
    public float? HappinessICNorm { get; set; }
    public float? Contempt { get; set; }
    public float? ContemptNorm { get; set; }
    public float? ContemptIC { get; set; }
    public float? ContemptICNorm { get; set; }
    public float? Surprise { get; set; }
    public float? SurpriseNorm { get; set; }
    public float? SurpriseIC { get; set; }
    public float? SurpriseICNorm { get; set; }
    public float? Confusion { get; set; }
    public float? ConfusionNorm { get; set; }
    public float? ConfusionIC { get; set; }
    public float? ConfusionICNorm { get; set; }
    public float? Disgust { get; set; }
    public float? DisgustNorm { get; set; }
    public float? DisgustIC { get; set; }
    public float? DisgustICNorm { get; set; }
    public float? PlaybackIC { get; set; }
    public float? PlaybackICNorm { get; set; }
    public float? PlaybackIF { get; set; }
    public float? PlaybackIFNorm { get; set; }
    public float? DistractionIC { get; set; }
    public float? DistractionICNorm { get; set; }
    public float? DistractionIF { get; set; }
    public float? DistractionIFNorm { get; set; }
    public float? NeutralAttention { get; set; }
    public float? NeutralAttentionNorm { get; set; }
    public float? NeutralAttentionIF { get; set; }
    public float? NeutralAttentionIFNorm { get; set; }
}