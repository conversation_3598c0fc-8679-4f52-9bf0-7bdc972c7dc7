using System;

namespace DataLayer.Dremio.DataTransferObjects;

public class ForcedExposureCreative
{
    public string Account { get; set; }
    public int? AccountID { get; set; }
    public string ProjectID { get; set; }
    public string SurveyKeyAlias { get; set; }
    public string SubjectGroupID { get; set; }
    public string SegmentKey { get; set; }
    public int? NormFallback { get; set; }
    public string NormSegmentKey { get; set; }
    public bool? IsForcedExposure { get; set; }
    public int? SourceMediaID { get; set; }
    public int? TestID { get; set; }
    public string TaskID { get; set; }
    public int? PDProjectID { get; set; }
    public string Country { get; set; }
    public string Order { get; set; }
    public string OrderExternalKey { get; set; }
    public int? OrderAdSetID { get; set; }
    public string CustomAdSets { get; set; }
    public string Country_code { get; set; }
    public string AdformatTextIF { get; set; }
    public float? QualityScore { get; set; }
    public int? QualityScore_index { get; set; }
    public string GeographicRegion { get; set; }
    public string SourceMediaThumbnailFileName { get; set; }
    public string SourceMediaThumbstripFileName { get; set; }
    public string SourceMediaConvertedFileName { get; set; }
    public string SourceMedia { get; set; }
    public int? Duration { get; set; }
    public string Brand { get; set; }
    public int? BrandID { get; set; }
    public string BrandLogoFileName { get; set; }
    public string TopCategory { get; set; }
    public string MidCategory { get; set; }
    public string SubCategory { get; set; }
    public string Platform { get; set; }
    public string Device { get; set; }
    public DateTimeOffset? CreationDate { get; set; }
    public bool? IsEnabledForClients { get; set; }
    public int? Views { get; set; }
    public float? AttentionAvg { get; set; }
    public float? AttentionAvgMedian { get; set; }
    public int? AttentionAvgRank { get; set; }
    public float? AttentionAvgDiff { get; set; }
    public float? AttentiveSeconds { get; set; }
    public float? AttentiveSecondsMedian { get; set; }
    public int? AttentiveSecondsRank { get; set; }
    public float? AttentiveSecondsDiff { get; set; }
    public float? AttentiveSecondsVTR { get; set; }
    public float? AttentiveSecondsVTRMedian { get; set; }
    public int? AttentiveSecondsVTRRank { get; set; }
    public float? AttentiveSecondsVTRDiff { get; set; }
    public float? Reactions { get; set; }
    public float? ReactionsMedian { get; set; }
    public int? ReactionsRank { get; set; }
    public float? ReactionsDiff { get; set; }
    public float? NegativityAvg { get; set; }
    public float? NegativityAvgMedian { get; set; }
    public float? NegativityAvgDiff { get; set; }
    public int? NegativityAvgRank { get; set; }
    public float? HappyPeak { get; set; }
    public float? HappyPeakMedian { get; set; }
    public int? HappyPeakRank { get; set; }
    public float? HappyPeakDiff { get; set; }
    public float? VTR { get; set; }
    public float? VTRMedian { get; set; }
    public int? VTRRank { get; set; }
    public float? VTRDiff { get; set; }
    public float? PlaybackSeconds { get; set; }
    public float? PlaybackSecondsMedian { get; set; }
    public int? PlaybackSecondsRank { get; set; }
    public float? PlaybackSecondsDiff { get; set; }
    public float? SurprisePeak { get; set; }
    public float? SurprisePeakMedian { get; set; }
    public int? SurprisePeakRank { get; set; }
    public float? SurprisePeakDiff { get; set; }
    public float? ConfusionPeak { get; set; }
    public float? ConfusionPeakMedian { get; set; }
    public int? ConfusionPeakRank { get; set; }
    public float? ConfusionPeakDiff { get; set; }
    public float? ContemptPeak { get; set; }
    public float? ContemptPeakMedian { get; set; }
    public int? ContemptPeakRank { get; set; }
    public float? ContemptPeakDiff { get; set; }
    public float? DisgustPeak { get; set; }
    public float? DisgustPeakMedian { get; set; }
    public int? DisgustPeakRank { get; set; }
    public float? DisgustPeakDiff { get; set; }
    public float? DistractionAvg { get; set; }
    public float? DistractionAvgMedian { get; set; }
    public int? DistractionAvgRank { get; set; }
    public float? DistractionAvgDiff { get; set; }

    public string AttentionNormFormat { get; set; }
    public string ReactionsNormFormat { get; set; }
    public string SurveyNormFormat { get; set; }

    public string ReactionsNormDevice { get; set; }
    public string SurveyNormDevice { get; set; }
    public string AttentionNormDevice { get; set; }

    public int? ReactionsNormDuration { get; set; }
    public int? SurveyNormDuration { get; set; }
    public int? AttentionNormDuration { get; set; }

    public string ReactionsNormAdFormat { get; set; }
    public string SurveyNormAdFormat { get; set; }
    public string AttentionNormAdFormat { get; set; }

    public string ReactionsNormAdFormatName { get; set; }
    public string SurveyNormAdFormatName { get; set; }
    public string AttentionNormAdFormatName { get; set; }

    public string ReactionsNormEnvironmentCategory { get; set; }
    public string SurveyNormEnvironmentCategory { get; set; }
    public string AttentionNormEnvironmentCategory { get; set; }

    public string ReactionsNormRegion { get; set; }
    public string SurveyNormRegion { get; set; }
    public string AttentionNormRegion { get; set; }

    public int? ReactionsNormSampleSize { get; set; }
    public int? SurveyNormSampleSize { get; set; }
    public int? AttentionNormSampleSize { get; set; }

    public string ReactionsNormRefreshDate { get; set; }
    public string SurveyNormRefreshDate { get; set; }
    public string AttentionNormRefreshDate { get; set; }

    public string ReactionsNormCustomNormFilters { get; set; }
    public string AttentionNormCustomNormFilters { get; set; }
    public string SurveyNormCustomNormFilters { get; set; }
}