using System.Text.Json.Serialization;

namespace DataLayer.Dremio.DataTransferObjects.Report
{
    public class ForcedExposureCurves
    {
        [JsonPropertyName("creative_id")] public string CreativeID { get; set; }
        public int AccountID { get; set; }
        public string SourceMediaExternalKey { get; set; }
        public string AdSetExternalKey { get; set; }
        public bool IsForcedExposure { get; set; }
        public int SourceMediaID { get; set; }
        public int TestID { get; set; }
        public object TaskID { get; set; }
        public string SegmentKey { get; set; }
        public int Second { get; set; }
        public float Playback { get; set; }
        public object PlaybackNorm { get; set; }
        public float Attention { get; set; }
        public object AttentionNorm { get; set; }
        public float Distraction { get; set; }
        public object DistractionNorm { get; set; }
        public float AllReactions { get; set; }
        public object AllReactionsNorm { get; set; }
        public float Negativity { get; set; }
        public object NegativityNorm { get; set; }
        public float Happiness { get; set; }
        public object HappinessNorm { get; set; }
        public float Confusion { get; set; }
        public object ConfusionNorm { get; set; }
        public float Contempt { get; set; }
        public object ContemptNorm { get; set; }
        public float Disgust { get; set; }
        public object DisgustNorm { get; set; }
        public float Surprise { get; set; }
        public object SurpriseNorm { get; set; }        
    }
}