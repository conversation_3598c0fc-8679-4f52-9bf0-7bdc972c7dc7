using System.Text.Json.Serialization;

namespace DataLayer.Dremio.DataTransferObjects.Report
{
    public class ForcedExposureGrid
    {
        [JsonPropertyName("creative_id")] public string CreativeID { get; set; }
        public string AdSetExternalKey { get; set; }
        public string AdSet { get; set; }
        public string SourceMediaExternalKey { get; set; }
        public string Account { get; set; }
        public int AccountID { get; set; }
        public bool IsForcedExposure { get; set; }
        public int SourceMediaID { get; set; }
        public int TestID { get; set; }
        public object TaskID { get; set; }
        public string SegmentKey { get; set; }
        public int Views { get; set; }
        public string AdformatTextIF { get; set; }
        public int? NormFallback { get; set; }
        public string NormSegmentKey { get; set; }
        [JsonPropertyName("norm_sample_size")] public int NormSampleSize { get; set; }
        public float QualityScore { get; set; }
        public int QualityScore_index { get; set; }
        public float AttentiveSeconds { get; set; }
        public int AttentiveSecondsRank { get; set; }
        public float AttentiveSecondsMedian { get; set; }
        public float AttentiveSecondsDiff { get; set; }
        public float AttentiveSecondsVTR { get; set; }
        public int AttentiveSecondsVTRRank { get; set; }
        public float AttentiveSecondsVTRMedian { get; set; }
        public float AttentiveSecondsVTRDiff { get; set; }
        public float VTR { get; set; }
        public int VTRRank { get; set; }
        public float VTRMedian { get; set; }
        public float VTRDiff { get; set; }
        public float PlaybackSeconds { get; set; }
        public int PlaybackSecondsRank { get; set; }
        public float PlaybackSecondsMedian { get; set; }
        public float PlaybackSecondsDiff { get; set; }
        public float AttentionAvg { get; set; }
        public int AttentionAvgRank { get; set; }
        public float AttentionAvgMedian { get; set; }
        public float AttentionAvgDiff { get; set; }
        public float Reactions { get; set; }
        public int ReactionsRank { get; set; }
        public float ReactionsMedian { get; set; }
        public float ReactionsDiff { get; set; }
        public float HappyPeak { get; set; }
        public int HappyPeakRank { get; set; }
        public float HappyPeakMedian { get; set; }
        public float HappyPeakDiff { get; set; }
        public float SurprisePeak { get; set; }
        public int SurprisePeakRank { get; set; }
        public float SurprisePeakMedian { get; set; }
        public float SurprisePeakDiff { get; set; }
        public float ConfusionPeak { get; set; }
        public int ConfusionPeakRank { get; set; }
        public float ConfusionPeakMedian { get; set; }
        public float ConfusionPeakDiff { get; set; }
        public float ContemptPeak { get; set; }
        public int ContemptPeakRank { get; set; }
        public float ContemptPeakMedian { get; set; }
        public float ContemptPeakDiff { get; set; }
        public float DisgustPeak { get; set; }
        public int DisgustPeakRank { get; set; }
        public float DisgustPeakMedian { get; set; }
        public float DisgustPeakDiff { get; set; }
        public float NeutralAttentionAvg { get; set; }
        public int NeutralAttentionAvgRank { get; set; }
        public float NeutralAttentionAvgMedian { get; set; }
        public float DistractionAvg { get; set; }
        public int DistractionAvgRank { get; set; }
        public float DistractionAvgMedian { get; set; }
        public float DistractionAvgDiff { get; set; }
        public float NegativityPeak { get; set; }
        public float NegativityPeakRank { get; set; }
        public float NegativityPeakMedian { get; set; }
        public float AttentionPeak { get; set; }
        public int AttentionPeakRank { get; set; }
        public float AttentionPeakMedian { get; set; }
        public float HappyAvg { get; set; }
        public int HappyAvgRank { get; set; }
        public float HappyAvgMedian { get; set; }
        public float SurpriseAvg { get; set; }
        public int SurpriseAvgRank { get; set; }
        public float SurpriseAvgMedian { get; set; }
        public float ConfusionAvg { get; set; }
        public int ConfusionAvgRank { get; set; }
        public float ConfusionAvgMedian { get; set; }
        public float ContemptAvg { get; set; }
        public int ContemptAvgRank { get; set; }
        public float ContemptAvgMedian { get; set; }
        public float DisgustAvg { get; set; }
        public int DisgustAvgRank { get; set; }
        public float DisgustAvgMedian { get; set; }
        public string Country { get; set; }
        [JsonPropertyName("Country_code")] public string CountryCode { get; set; }
        public string GeographicRegion { get; set; }
        public string SourceMediaThumbnailFileName { get; set; }
        public string SourceMediaConvertedFileName { get; set; }
        public string ParentCreative { get; set; }
        public string SourceMedia { get; set; }
        public string SourceMediaType { get; set; }
        public int Duration { get; set; }
        public string CreationDate { get; set; }
        public string Brand { get; set; }
        public int BrandID { get; set; }
        public string BrandLogoFileName { get; set; }
        public string TopCategory { get; set; }
        public string MidCategory { get; set; }
        public string SubCategory { get; set; }
        public string Platform { get; set; }
        public string Device { get; set; }
        public string Adformat { get; set; }
        public string AdformatText { get; set; }
        public bool IsEnabledForClients { get; set; }
    }


    public class Rootobject
    {
        public string creative_id { get; set; }
        public string AdSetExternalKey { get; set; }
        public string SourceMediaExternalKey { get; set; }
        public string Account { get; set; }
        public int AccountID { get; set; }
        public bool IsForcedExposure { get; set; }
        public int SourceMediaID { get; set; }
        public int TestID { get; set; }
        public object TaskID { get; set; }
        public string SegmentKey { get; set; }
        public int Views { get; set; }
        public string AdformatTextIF { get; set; }
        public int NormFallback { get; set; }
        public object NormSegmentKey { get; set; }
        public object norm_sample_size { get; set; }
        public object QualityScore { get; set; }
        public int QualityScore_index { get; set; }
        public float AttentiveSeconds { get; set; }
        public object AttentiveSecondsRank { get; set; }
        public object AttentiveSecondsMedian { get; set; }
        public object AttentiveSecondsDiff { get; set; }
        public float AttentiveSecondsVTR { get; set; }
        public object AttentiveSecondsVTRRank { get; set; }
        public object AttentiveSecondsVTRMedian { get; set; }
        public object AttentiveSecondsVTRDiff { get; set; }
        public float VTR { get; set; }
        public object VTRRank { get; set; }
        public object VTRMedian { get; set; }
        public object VTRDiff { get; set; }
        public float PlaybackSeconds { get; set; }
        public object PlaybackSecondsRank { get; set; }
        public object PlaybackSecondsMedian { get; set; }
        public object PlaybackSecondsDiff { get; set; }
        public float AttentionAvg { get; set; }
        public object AttentionAvgRank { get; set; }
        public object AttentionAvgMedian { get; set; }
        public object AttentionAvgDiff { get; set; }
        public float Reactions { get; set; }
        public object ReactionsRank { get; set; }
        public object ReactionsMedian { get; set; }
        public object ReactionsDiff { get; set; }
        public float HappyPeak { get; set; }
        public object HappyPeakRank { get; set; }
        public object HappyPeakMedian { get; set; }
        public object HappyPeakDiff { get; set; }
        public float SurprisePeak { get; set; }
        public object SurprisePeakRank { get; set; }
        public object SurprisePeakMedian { get; set; }
        public object SurprisePeakDiff { get; set; }
        public float ConfusionPeak { get; set; }
        public object ConfusionPeakRank { get; set; }
        public object ConfusionPeakMedian { get; set; }
        public object ConfusionPeakDiff { get; set; }
        public float ContemptPeak { get; set; }
        public object ContemptPeakRank { get; set; }
        public object ContemptPeakMedian { get; set; }
        public object ContemptPeakDiff { get; set; }
        public float DisgustPeak { get; set; }
        public object DisgustPeakRank { get; set; }
        public object DisgustPeakMedian { get; set; }
        public object DisgustPeakDiff { get; set; }
        public float NeutralAttentionAvg { get; set; }
        public object NeutralAttentionAvgRank { get; set; }
        public object NeutralAttentionAvgMedian { get; set; }
        public float DistractionAvg { get; set; }
        public object DistractionAvgRank { get; set; }
        public object DistractionAvgMedian { get; set; }
        public object DistractionAvgDiff { get; set; }
        public float NegativityPeak { get; set; }
        public float NegativityPeakRank { get; set; }
        public float NegativityPeakMedian { get; set; }
        public float AttentionPeak { get; set; }
        public object AttentionPeakRank { get; set; }
        public object AttentionPeakMedian { get; set; }
        public float HappyAvg { get; set; }
        public object HappyAvgRank { get; set; }
        public object HappyAvgMedian { get; set; }
        public float SurpriseAvg { get; set; }
        public object SurpriseAvgRank { get; set; }
        public object SurpriseAvgMedian { get; set; }
        public float ConfusionAvg { get; set; }
        public object ConfusionAvgRank { get; set; }
        public object ConfusionAvgMedian { get; set; }
        public float ContemptAvg { get; set; }
        public object ContemptAvgRank { get; set; }
        public object ContemptAvgMedian { get; set; }
        public float DisgustAvg { get; set; }
        public object DisgustAvgRank { get; set; }
        public object DisgustAvgMedian { get; set; }
        public object Country { get; set; }
        public object Country_code { get; set; }
        public object GeographicRegion { get; set; }
        public string SourceMediaThumbnailFileName { get; set; }
        public string SourceMediaConvertedFileName { get; set; }
        public string ParentCreative { get; set; }
        public string SourceMedia { get; set; }
        public string SourceMediaType { get; set; }
        public int Duration { get; set; }
        public string CreationDate { get; set; }
        public string Brand { get; set; }
        public int BrandID { get; set; }
        public string BrandLogoFileName { get; set; }
        public string TopCategory { get; set; }
        public string MidCategory { get; set; }
        public string SubCategory { get; set; }
        public string Platform { get; set; }
        public string Device { get; set; }
        public string Adformat { get; set; }
        public string AdformatText { get; set; }
        public bool IsEnabledForClients { get; set; }
    }

}

