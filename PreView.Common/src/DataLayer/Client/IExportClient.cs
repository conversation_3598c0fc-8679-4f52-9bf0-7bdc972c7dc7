using System.Threading.Tasks;
using DataLayer.Client.DataTransferObjects;
using DataLayer.Clients.DataTransferObjects;

namespace DataLayer.Client;

public interface IExportClient
{
    Task SaveExportConfiguration(string exportFolderPath, ExportConfiguration configData);
    public Task<ExportResult> ChangeExportResultStatus(string exportFolderPath, string newStatus);
    Task<ExportResult> GetExportResult(string bucketName, string exportPath);
}