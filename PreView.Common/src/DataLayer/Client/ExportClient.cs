using System;
using System.IO;
using System.Net;
using System.Text.Json;
using System.Threading.Tasks;
using Amazon.S3;
using Amazon.S3.Model;
using DataLayer.Client.DataTransferObjects;
using DataLayer.Clients;
using DataLayer.Clients.DataTransferObjects;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace DataLayer.Client;

public class ExportClient : IExportClient
{
    private readonly ILogger<ExportClient> _logger;
    private readonly IAmazonS3 _s3Client;
    private readonly ExportSettings _settings;

    public ExportClient(ILogger<ExportClient> logger, IAmazonS3 s3Client, IOptions<ExportSettings> settings)
    {
        _logger = logger;
        _s3Client = s3Client;
        _settings = settings.Value;
    }

    public async Task SaveExportConfiguration(string exportFolderPath, ExportConfiguration configData)
    {
        var configPath = $"{exportFolderPath}/configuration.json";

        var s3Bucket = _settings.ExportBucket;
        configData.ExportFolderPath = exportFolderPath;
        configData.BucketName = s3Bucket;

        var configJson = JsonSerializer.Serialize(configData);

        try
        {
            var configResponse = await UploadFileToS3(s3Bucket, configPath, configJson);

            if (configResponse.HttpStatusCode == HttpStatusCode.OK)
            {
                _logger.LogInformation($"Configuration saved to S3 bucket: {s3Bucket}, key: {configPath}");
                return;
            }

            _logger.LogError($"Failed to save configuration to S3 bucket: {s3Bucket}, key: {configPath}");
        }
        catch (AmazonS3Exception s3Ex)
        {
            _logger.LogError(s3Ex, $"Amazon S3 error saving configuration to S3 bucket: {s3Bucket}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error saving configuration to S3 bucket: {s3Bucket}");
        }
    }

    public Task<ExportResult> ChangeExportResultStatus(string exportFolderPath, string newStatus)
    {
        return ChangeStatus(exportFolderPath, newStatus);
    }

    public Task<ExportResult> GetExportResult(string bucketName, string exportPath)
    {
        return GetResult(bucketName, exportPath);
    }

    private async Task<ExportResult> ChangeStatus(string exportFolderPath, string newStatus)
    {
        var s3Bucket = _settings.ExportBucket;
        var resultPath = $"{exportFolderPath}/result.json";

        var exportResult = new ExportResult
        {
            BucketName = s3Bucket,
            JobStatus = newStatus,
            ExportFolderPath = exportFolderPath
        };

        var jobJson = JsonSerializer.Serialize(exportResult);

        try
        {
            var jobResponse = await UploadFileToS3(s3Bucket, resultPath, jobJson);

            if (jobResponse.HttpStatusCode == HttpStatusCode.OK)
            {
                _logger.LogInformation($"Result saved to S3 bucket: {s3Bucket}, key: {resultPath}");
                return exportResult;
            }

            _logger.LogError($"Failed to save result to S3 bucket: {s3Bucket}, key: {resultPath}");
            return null;
        }
        catch (AmazonS3Exception s3Ex)
        {
            _logger.LogError(s3Ex, $"Amazon S3 error saving result to S3 bucket: {s3Bucket}");
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error saving result to S3 bucket: {s3Bucket}");
            return null;
        }
    }

    private async Task<PutObjectResponse> UploadFileToS3(string bucketName, string key, string content)
    {
        var request = new PutObjectRequest
        {
            BucketName = bucketName,
            Key = key,
            ContentBody = content,
            ContentType = "application/json"
        };

        return await _s3Client.PutObjectAsync(request);
    }

    public async Task<ExportResult> GetResult(string bucketName, string exportPath)
    {
        try
        {
            var response = await _s3Client.GetObjectAsync(new GetObjectRequest
            {
                BucketName = bucketName,
                Key = $"{exportPath}/result.json"
            });

            using var reader = new StreamReader(response.ResponseStream);
            var content = await reader.ReadToEndAsync();

            var configuration = JsonSerializer.Deserialize<ExportResult>(content);

            _logger.LogInformation($"File content received from S3: {content}");
            return configuration;
        }
        catch (Exception ex)
        {
            _logger.LogError($"Error getting file from S3: {ex.Message}");
            throw;
        }
    }
}