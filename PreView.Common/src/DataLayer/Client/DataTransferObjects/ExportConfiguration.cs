using System.Collections.Generic;
using BusinessLayer.Constant;
using BusinessLayer.Model;
using BusinessLayer.Model.Filter;
using BusinessLayer.Model.Response;
using GridFilterItem = DataLayer.Clients.DataTransferObjects.GridFilterItem;
using GridSortingItem = DataLayer.Clients.DataTransferObjects.GridSortingItem;

namespace DataLayer.Client.DataTransferObjects;

public class ExportConfiguration
{
    public ProductType ProductType { get; set; }
    public int AccountId { get; set; }
    public bool IsAdmin { get; set; }
    public List<GridFilterItem> Filters { get; set; } = new();
    public List<string> VisibleColumnsOrder { get; set; } = new();
    public List<GridSortingItem> Sorting { get; set; } = new();
    public List<CurveFilterItem> CurveFilter { get; set; } = new();
    public int? BrandId { get; set; }
    public int? StudyId { get; set; }
    public string Audience { get; set; } = SegmentKeyConstant.AllSegmentKey;
    public bool ProduceEmptyAudiences { get; set; } = true;
    public List<Media> Media { get; set; } = new();
    public List<string> SegmentKeys { get; set; }
    public int MinViewThresholdForScores { get; set; }
    public string ExportFolderPath { get; set; }
    public string BucketName { get; set; }
    public string ExportFileName { get; set; }
}
