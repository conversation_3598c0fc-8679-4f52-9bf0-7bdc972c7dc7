using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using BusinessLayer.Model.Request;
using BusinessLayer.Model.Response;
using BusinessLayer.Repository;
using DataLayer.Dremio.Queries;
using DataLayer.Dremio.QueryParameters;
using DataLayer.Mapper;
using Realeyes.PreView.Infrastructure.Services.Dremio.Extension;

namespace DataLayer.Repository;

public class MediaSegmentRepository : IMediaSegmentRepository
{
    private readonly IQueryExecutor _queryExecutor;

    public MediaSegmentRepository(IQueryExecutor queryExecutor)
    {
        _queryExecutor = queryExecutor;
    }

    public async Task<IEnumerable<MediaSegment>> GetMediaSegmentKeys(GetMediaSegmentKeysRequest request)
    {
        var query = new GetMediaSegmentKeyQuery(new GetMediaSegmentKeysQueryParameters { Media = request.Media});
        var dtos = await _queryExecutor.Execute<Dremio.DataTransferObjects.MediaSegment>(query);

        return dtos.Select(c => c.ToModel());
    }
}