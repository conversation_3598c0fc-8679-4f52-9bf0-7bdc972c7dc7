using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using BusinessLayer.Model.Request;
using BusinessLayer.Model.Response;
using BusinessLayer.Repository;
using DataLayer.Dremio.Queries;
using DataLayer.Dremio.QueryParameters;
using Realeyes.PreView.Infrastructure.Services.Dremio.Extension;
using MediaSegment = DataLayer.Dremio.DataTransferObjects.MediaSegment;

namespace DataLayer.Repository;

public class SegmentRepository : ISegmentRepository
{
    private readonly IQueryExecutor _queryExecutor;

    public SegmentRepository(IQueryExecutor queryExecutor)
    {
        _queryExecutor = queryExecutor;
    }

    public async Task<List<SegmentKeyLabel>> GetSegmentKeys(GetSegmentKeysRequest request)
    {
        var parameters = new GetSegmentKeysQueryParameters
        {
            AccountId = request.AccountId,
            SegmentKeys = request.SegmentKeys,
            ProductType = request.ProductType
        };

        var subjectsQuery = new GetSegmentKeyQuery(parameters);
        var mediaSegmentKeys = await _queryExecutor.Execute<MediaSegment>(subjectsQuery);

        var segmentKeys = mediaSegmentKeys
            .GroupBy(m => m.SegmentKey)
            .Select(m => m.FirstOrDefault())
            .ToList();

        var segmentKeysLabels = segmentKeys
            .Where(s => s.Question != null)
            .OrderBy(s => s.QuestionOrder ?? int.MaxValue)
            .ThenBy(s => s.Question)
            .ThenBy(m => m.AnswerOrder ?? int.MaxValue)
            .ThenBy(m => m.Answer)
            .Select(m => new SegmentKeyLabel { SegmentKey = m.SegmentKey, Question = m.Question, Answer = m.Answer })
            .ToList();

        return segmentKeysLabels;
    }
}