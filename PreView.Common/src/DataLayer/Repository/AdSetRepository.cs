using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using BusinessLayer.Model.Request.Report;
using BusinessLayer.Model.Response;
using DataLayer.Dremio.Queries.Report;
using DataLayer.Dremio.QueryParameters.Report;
using DataLayer.Mapper.Report;
using Realeyes.PreView.Infrastructure.Services.Dremio.Extension;

namespace DataLayer.Repository;

public class AdSetRepository : IAdSetRepository
{
    private readonly IQueryExecutor _queryExecutor;

    public AdSetRepository(IQueryExecutor queryExecutor)
    {
        _queryExecutor = queryExecutor;
    }

    public async Task<IEnumerable<AdSetSearchResult>> SearchAsync(AdSetSearchRequest request)
    {
        var parameters = new AdSetSearchQueryParameters
        {
            SegmentKey = request.SegmentKey,
            AdSetKey = request.AdSetKey,
            AdSetName = request.AdSetName,
            SourceMediaKey = request.SourceMediaKey,
            SourceMediaName = request.SourceMediaName,
            CreationDate = request.CreationDate,
            Duration = request.Duration,
            AccountIds = request.AccountIds
        };

        var query = new AdSetSearchQuery(parameters);
        var dtos = await _queryExecutor.Execute<Dremio.DataTransferObjects.Report.AdSetSearchResult>(query);
        return dtos.Select(c => c.ToModel());
    }

    public async Task<IEnumerable<AdSetSearchDetailsResult>> GetSearchDetailsAsync(AdSetSearchDetailsRequest request)
    {
        var parameters = new AdSetSearchDetailsQueryParameters
        {
            SegmentKey = request.SegmentKey,
            AdSetExternelKeys = request.AdSetKeys,
        };

        var query = new AdSetSearchDetailsQuery(parameters);
        var dtos = await _queryExecutor.Execute<Dremio.DataTransferObjects.Report.AdSetSearchDetailsResult>(query);
        return dtos.Select(c => c.ToModel());
    }
}