using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using BusinessLayer.Model.Request;
using BusinessLayer.Model.Response;
using BusinessLayer.Repository;
using DataLayer.Dremio.Queries;
using DataLayer.Dremio.QueryParameters;
using DataLayer.Mapper;
using Realeyes.PreView.Infrastructure.Services.Dremio.Extension;

namespace DataLayer.Repository;

public class AccountProductRepository : IAccountProductRepository
{
    private readonly IQueryExecutor _queryExecutor;

    public AccountProductRepository(IQueryExecutor queryExecutor)
    {
        _queryExecutor = queryExecutor;
    }

    public async Task<IEnumerable<AccountProduct>> GetAccountProducts(GetAccountProductsRequest request)
    {
        var query = new GetAccountProductsQuery(new GetAccountProductsQueryParameters { IsAdmin = request.IsAdmin, AccountIds = request.AccountIds});
        var dtos = await _queryExecutor.Execute<Dremio.DataTransferObjects.AccountProduct>(query);

        return dtos.Select(c => c.ToModel());
    }
}