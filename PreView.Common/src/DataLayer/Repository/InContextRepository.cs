using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using BusinessLayer.Model.Request;
using BusinessLayer.Model.Response;
using BusinessLayer.Repository;
using DataLayer.Dremio.Queries;
using DataLayer.Dremio.QueryParameters;
using DataLayer.Mapper;
using Realeyes.PreView.Infrastructure.Services.Dremio.Extension;

namespace DataLayer.Repository;

public class InContextRepository : IInContextRepository
{
    private readonly IQueryExecutor _queryExecutor;

    public InContextRepository(IQueryExecutor queryExecutor)
    {
        _queryExecutor = queryExecutor;
    }

    public async Task<IEnumerable<InContextCreative>> GetCreatives(GetCreativesRequest request)
    {
        var parameters = new GetInContextCreativesQueryParameters
        {
            AccountId = request.AccountId,
            SegmentKeys = request.SegmentKeys,
            Media = request.Media?.Select(data => new MediaModelQueryParameters
            {
                TaskID = data.TaskID,
                TestID = data.TestID,
                SourceMediaID = data.SourceMediaID,
                OrderAdSetID = data.OrderAdSetID
            })
        };

        var query = new GetInContextCreativesQuery(parameters);
        var dtos = await _queryExecutor.Execute<Dremio.DataTransferObjects.InContextCreative>(query);
        return dtos.Select(c => c.ToModel());
    }

    public async Task<IEnumerable<InContextCurve>> GetCurves(GetCurvesRequest request)
    {
        var parameters = new GetInContextCurvesQueryParameters
        {
            SegmentKeys = request.SegmentKeys,
            Media = request.Media.Select(data => new MediaModelQueryParameters
            {
                TaskID = data.TaskID,
                TestID = data.TestID,
                SourceMediaID = data.SourceMediaID
            })
        };

        var query = new GetInContextCurvesQuery(parameters);
        var dtos = await _queryExecutor.Execute<Dremio.DataTransferObjects.InContextCurve>(query);
        return dtos.Select(c => c.ToModel());
    }
}