using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using BusinessLayer.Model.Request;
using BusinessLayer.Model.Response;
using BusinessLayer.Repository;
using DataLayer.Dremio.Queries;
using DataLayer.Dremio.QueryParameters;
using DataLayer.Mapper;
using Realeyes.PreView.Infrastructure.Services.Dremio.Extension;

namespace DataLayer.Repository;

public class ForcedExposureRepository : IForcedExposureRepository
{
    private readonly IQueryExecutor _queryExecutor;

    public ForcedExposureRepository(IQueryExecutor queryExecutor)
    {
        _queryExecutor = queryExecutor;
    }

    public async Task<IEnumerable<ForcedExposureCreative>> GetCreatives(GetCreativesRequest request)
    {
        var parameters = new GetForcedExposureCreativesQueryParameters
        {
            AccountId = request.AccountId,
            SegmentKeys = request.SegmentKeys,
            Media = request.Media?.Select(data => new MediaModelQueryParameters
            {
                OrderAdSetID = data.OrderAdSetID,
                TestID = data.TestID,
                SourceMediaID = data.SourceMediaID
            })
        };

        var query = new GetForcedExposureCreativesQuery(parameters);
        var dtos = await _queryExecutor.Execute<Dremio.DataTransferObjects.ForcedExposureCreative>(query);
        return dtos.Select(c => c.ToModel());
    }

    public async Task<IEnumerable<ForcedExposureCurve>> GetCurves(GetCurvesRequest request)
    {
        var parameters = new GetForcedExposureCurvesQueryParameters
        {
            SegmentKeys = request.SegmentKeys,
            Media = request.Media.Select(data => new MediaModelQueryParameters
            {
                TestID = data.TestID,
                SourceMediaID = data.SourceMediaID
            })
        };

        var query = new GetForcedExposureCurvesQuery(parameters);
        var dtos = await _queryExecutor.Execute<Dremio.DataTransferObjects.ForcedExposureCurve>(query);
        return dtos.Select(c => c.ToModel());
    }
}