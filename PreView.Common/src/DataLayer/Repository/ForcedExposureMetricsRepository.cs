using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using BusinessLayer.Model.Request;
using BusinessLayer.Model.Request.Report;
using BusinessLayer.Model.Response;
using BusinessLayer.Repository;
using DataLayer.Dremio.Queries.Report;
using DataLayer.Dremio.QueryParameters;
using DataLayer.Dremio.QueryParameters.Report;
using DataLayer.Mapper.Report;
using Realeyes.PreView.Infrastructure.Services.Dremio.Extension;

namespace DataLayer.Repository;

public class ForcedExposureMetricsRepository : IForcedExposureMetricsRepository
{
    private readonly IQueryExecutor _queryExecutor;

    public ForcedExposureMetricsRepository(IQueryExecutor queryExecutor)
    {
        _queryExecutor = queryExecutor;
    }

    public async Task<IEnumerable<ForcedExposureCurves>> GetCurvesData(GetForcedExposureCurveRequest request)
    {
        var parameters = new GetForcedExposureCurveQueryParameters
        {
            AdSetExternalKey = request.AdSetExternalKey,
            SegmentKey = "all"
        };

        var query = new GetForcedExposureCurveQuery(parameters);
        var dtos = await _queryExecutor.Execute<Dremio.DataTransferObjects.Report.ForcedExposureCurves>(query);
        return dtos.Select(c => c.ToModel());
    }

    public async Task<IEnumerable<ForcedExposureGrid>> GetGridData(GetForcedExposureGridRequest request)
    {
        var parameters = new GetForcedExposureGridQueryParameters
        {
            AdSetExternalKey = request.AdSetExternalKey,
            SegmentKey = "all"
        };

        var query = new GetForcedExposureGridQuery(parameters);
        var dtos = await _queryExecutor.Execute<Dremio.DataTransferObjects.Report.ForcedExposureGrid>(query);
        return dtos.Select(c => c.ToModel());
    }
}