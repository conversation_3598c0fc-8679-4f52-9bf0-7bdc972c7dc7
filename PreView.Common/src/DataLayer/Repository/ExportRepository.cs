using System.Threading.Tasks;
using BusinessLayer.Model.Request;
using BusinessLayer.Model.Response;
using BusinessLayer.Repository;
using DataLayer.Client;
using DataLayer.Mapper;

namespace DataLayer.Repository;

public class ExportRepository : IExportRepository
{
    private readonly IExportClient _exportClient;

    public ExportRepository(IExportClient exportClient)
    {
        _exportClient = exportClient;
    }

    public Task StartExport(StartExportRequest request)
    {
        return _exportClient.SaveExportConfiguration(
            request.ExportFolderPath,
            request.ToDto()
        );
    }

    public async Task<ExportResult> ChangeExportResultStatus(UpdateExportStatusRequest request)
    {
        var result = await _exportClient.ChangeExportResultStatus(
            request.ExportFolderPath,
            request.NewStatus
        );

        return result.ToModel();
    }

    public async Task<ExportResult> GetExportResult(GetExportResultRequest request)
    {
        var result = await _exportClient.GetExportResult(
            request.ExportBucket,
            request.ExportPath
        );

        return result.ToModel();
    }
}