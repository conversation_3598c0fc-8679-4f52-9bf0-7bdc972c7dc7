using BusinessLayer.Model.Response;

namespace DataLayer.Mapper;

public static class InContextCurveMapper
{
    public static InContextCurve ToModel(this Dremio.DataTransferObjects.InContextCurve dto)
    {
        return new InContextCurve
        {
            DistractionIF = dto.DistractionIF,
            DistractionIFNorm = dto.DistractionIFNorm,
            SourceMediaID = dto.SourceMediaID,
            TestID = dto.TestID,
            TaskID = dto.TaskID,
            SegmentKey = dto.SegmentKey,
            Second = dto.Second,
            Attention = dto.Attention,
            AttentionNorm = dto.AttentionNorm,
            AttentionIF = dto.AttentionIF,
            AttentionIFNorm = dto.AttentionIFNorm,
            AllReactions = dto.AllReactions,
            AllReactionsNorm = dto.AllReactionsNorm,
            AllReactionsIC = dto.AllReactionsIC,
            AllReactionsICNorm = dto.AllReactionsICNorm,
            NegativityIC = dto.NegativityIC,
            NegativityICNorm = dto.NegativityICNorm,
            Negativity = dto.Negativity,
            NegativityNorm = dto.NegativityNorm,
            Happiness = dto.Happiness,
            HappinessNorm = dto.HappinessNorm,
            HappinessIC = dto.HappinessIC,
            HappinessICNorm = dto.HappinessICNorm,
            Contempt = dto.Contempt,
            ContemptNorm = dto.ContemptNorm,
            ContemptIC = dto.ContemptIC,
            ContemptICNorm = dto.ContemptICNorm,
            Surprise = dto.Surprise,
            SurpriseNorm = dto.SurpriseNorm,
            SurpriseIC = dto.SurpriseIC,
            SurpriseICNorm = dto.SurpriseICNorm,
            Confusion = dto.Confusion,
            ConfusionNorm = dto.ConfusionNorm,
            ConfusionIC = dto.ConfusionIC,
            ConfusionICNorm = dto.ConfusionICNorm,
            Disgust = dto.Disgust,
            DisgustNorm = dto.DisgustNorm,
            DisgustIC = dto.DisgustIC,
            DisgustICNorm = dto.DisgustICNorm,
            PlaybackIC = dto.PlaybackIC,
            PlaybackICNorm = dto.PlaybackICNorm,
            PlaybackIF = dto.PlaybackIF,
            PlaybackIFNorm = dto.PlaybackIFNorm,
            DistractionIC = dto.DistractionIC,
            DistractionICNorm = dto.DistractionICNorm,
            NeutralAttention = dto.NeutralAttention,
            NeutralAttentionNorm = dto.NeutralAttentionNorm,
            NeutralAttentionIF = dto.NeutralAttentionIF,
            NeutralAttentionIFNorm = dto.NeutralAttentionIFNorm
        };
    }
}