using System;
using DataLayer.Clients.DataTransferObjects;

namespace DataLayer.Mapper;

public static class SortingItemMapper
{
    public static GridSortingItem ToDto(this BusinessLayer.Model.Filter.GridSortingItem source)
    {
        ArgumentNullException.ThrowIfNull(source);

        return new GridSortingItem
        {
            Field = source.Field,
            Direction = source.Direction,
            FirstOrderText = source.FirstOrderText
        };
    }

    public static BusinessLayer.Model.Filter.GridSortingItem ToModel(this GridSortingItem source)
    {
        ArgumentNullException.ThrowIfNull(source);

        return new BusinessLayer.Model.Filter.GridSortingItem
        {
            Field = source.Field,
            Direction = source.Direction,
            FirstOrderText = source.FirstOrderText
        };
    }
}