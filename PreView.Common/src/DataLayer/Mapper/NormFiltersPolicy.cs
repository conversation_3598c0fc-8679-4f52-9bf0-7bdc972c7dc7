using BusinessLayer.Model.Response;
using System.Text.Json;

namespace DataLayer.Mapper;

public class NormFiltersPolicy : JsonNamingPolicy
{
    public override string ConvertName(string name)
    {
        return name switch
        {
            nameof(NormFilters.Country) => "country",
            nameof(NormFilters.Category) => "category",
            nameof(NormFilters.Industry) => "industry",
            nameof(NormFilters.AdFormat) => "ad_format",
            nameof(NormFilters.Environment) => "environment",
            nameof(NormFilters.Region) => "region",
            nameof(NormFilters.IsForcedExposure) => "is_forced_exposure",
            nameof(NormFilters.IsSkippable) => "is_skippable",
            _ => name.ToLowerInvariant()
        };
    }
}