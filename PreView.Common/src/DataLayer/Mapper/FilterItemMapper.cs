using System;
using DataLayer.Clients.DataTransferObjects;

namespace DataLayer.Mapper;

public static class FilterItemMapper
{
    public static GridFilterItem ToDto(
        this BusinessLayer.Model.Filter.GridFilterItem source)
    {
        ArgumentNullException.ThrowIfNull(source);

        return new GridFilterItem
        {
            ColumnField = source.ColumnField,
            Value = source.Value,
            OperatorValue = source.OperatorValue,
            OrQueryGroupId = source.OrQueryGroupId
        };
    }

    public static BusinessLayer.Model.Filter.GridFilterItem ToModel(
        this GridFilterItem source)
    {
        ArgumentNullException.ThrowIfNull(source);

        return new BusinessLayer.Model.Filter.GridFilterItem
        {
            ColumnField = source.ColumnField,
            Value = source.Value,
            OperatorValue = source.OperatorValue,
            OrQueryGroupId = source.OrQueryGroupId
        };
    }
}