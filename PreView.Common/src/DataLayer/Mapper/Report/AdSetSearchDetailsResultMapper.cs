using BusinessLayer.Model.Response;
using System;

namespace DataLayer.Mapper.Report
{
    public static class AdSetSearchDetailsResultMapper
    {
        public static AdSetSearchDetailsResult ToModel(this Dremio.DataTransferObjects.Report.AdSetSearchDetailsResult dto)
        {
            return new AdSetSearchDetailsResult
            {
                AdSetExternalKey = dto.AdSetExternalKey,
                AdSet = dto.AdSet,
                SourceMedia = dto.SourceMedia,
                SourceMediaExternalKey = dto.SourceMediaExternalKey,
                CreationDate = dto.CreationDate,
                Duration = dto.Duration,
                AccountID = dto.AccountID,
            };
        }
    }
}