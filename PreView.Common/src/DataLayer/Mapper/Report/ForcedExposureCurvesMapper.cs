using BusinessLayer.Model.Response;

namespace DataLayer.Mapper.Report
{
    public static class ForcedExposureCurvesMapper
    {
        public static ForcedExposureCurves ToModel(
            this Dremio.DataTransferObjects.Report.ForcedExposureCurves dto)
        {
            return new ForcedExposureCurves
            {
                CreativeID = dto.CreativeID,
                AdSetExternalKey = dto.AdSetExternalKey,
                SourceMediaExternalKey = dto.SourceMediaExternalKey,
                AccountID = dto.AccountID,
                IsForcedExposure = dto.IsForcedExposure,
                SourceMediaID = dto.SourceMediaID,
                TestID = dto.TestID,
                TaskID = dto.TaskID,
                SegmentKey = dto.SegmentKey,
                Second = dto.Second,
                Playback = dto.Playback,
                PlaybackNorm = dto.PlaybackNorm,
                Attention = dto.Attention,
                AttentionNorm = dto.AttentionNorm,
                Distraction = dto.Distraction,
                DistractionNorm = dto.DistractionNorm,
                AllReactions = dto.AllReactions,
                AllReactionsNorm = dto.AllReactionsNorm,
                Negativity = dto.Negativity,
                NegativityNorm = dto.NegativityNorm,
                Happiness = dto.Happiness,
                HappinessNorm = dto.HappinessNorm,
                Confusion = dto.Confusion,
                ConfusionNorm = dto.ConfusionNorm,
                Contempt = dto.Contempt,
                ContemptNorm = dto.ContemptNorm,
                Disgust = dto.Disgust,
                DisgustNorm = dto.DisgustNorm,
                Surprise = dto.Surprise,
                SurpriseNorm = dto.SurpriseNorm
            };
        }
    }
}