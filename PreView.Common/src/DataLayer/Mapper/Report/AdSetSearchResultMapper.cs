using BusinessLayer.Model.Response;

namespace DataLayer.Mapper.Report
{
    public static class AdSetSearchResultMapper
    {
        public static AdSetSearchResult ToModel(this Dremio.DataTransferObjects.Report.AdSetSearchResult dto)
        {
            return new AdSetSearchResult
            {
                AdSetExternalKey = dto.AdSetExternalKey,
                AccountId = dto.AccountId,
                NumberOfSourceMedia = dto.NumberOfSourceMedia,
            };
        }
    }
}