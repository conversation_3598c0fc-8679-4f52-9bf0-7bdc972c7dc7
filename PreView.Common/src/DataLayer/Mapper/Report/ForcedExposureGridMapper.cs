using BusinessLayer.Model.Response;

namespace DataLayer.Mapper.Report
{
    public static class ForcedExposureGridMapper
    {
        public static ForcedExposureGrid ToModel(this Dremio.DataTransferObjects.Report.ForcedExposureGrid dto)
        {
            return new ForcedExposureGrid
            {
                CreativeID = dto.CreativeID,
                AdSetExternalKey = dto.AdSetExternalKey,
                AdSet = dto.AdSet,
                SourceMediaExternalKey = dto.SourceMediaExternalKey,
                Account = dto.Account,
                AccountID = dto.AccountID,
                IsForcedExposure = dto.IsForcedExposure,
                SourceMediaID = dto.SourceMediaID,
                TestID = dto.TestID,
                TaskID = dto.TaskID,
                SegmentKey = dto.SegmentKey,
                Views = dto.Views,
                AdformatTextIF = dto.AdformatTextIF,
                NormFallback = dto.NormFallback,
                NormSegmentKey = dto.NormSegmentKey,
                norm_sample_size = dto.NormSampleSize,
                QualityScore = dto.QualityScore,
                QualityScore_index = dto.QualityScore_index,
                AttentiveSeconds = dto.AttentiveSeconds,
                AttentiveSecondsRank = dto.AttentiveSecondsRank,
                AttentiveSecondsMedian = dto.AttentiveSecondsMedian,
                AttentiveSecondsDiff = dto.AttentiveSecondsDiff,
                AttentiveSecondsVTR = dto.AttentiveSecondsVTR,
                AttentiveSecondsVTRRank = dto.AttentiveSecondsVTRRank,
                AttentiveSecondsVTRMedian = dto.AttentiveSecondsVTRMedian,
                AttentiveSecondsVTRDiff = dto.AttentiveSecondsVTRDiff,
                VTR = dto.VTR,
                VTRRank = dto.VTRRank,
                VTRMedian = dto.VTRMedian,
                VTRDiff = dto.VTRDiff,
                PlaybackSeconds = dto.PlaybackSeconds,
                PlaybackSecondsRank = dto.PlaybackSecondsRank,
                PlaybackSecondsMedian = dto.PlaybackSecondsMedian,
                PlaybackSecondsDiff = dto.PlaybackSecondsDiff,
                AttentionAvg = dto.AttentionAvg,
                AttentionAvgRank = dto.AttentionAvgRank,
                AttentionAvgMedian = dto.AttentionAvgMedian,
                AttentionAvgDiff = dto.AttentionAvgDiff,
                Reactions = dto.Reactions,
                ReactionsRank = dto.ReactionsRank,
                ReactionsMedian = dto.ReactionsMedian,
                ReactionsDiff = dto.ReactionsDiff,
                HappyPeak = dto.HappyPeak,
                HappyPeakRank = dto.HappyPeakRank,
                HappyPeakMedian = dto.HappyPeakMedian,
                HappyPeakDiff = dto.HappyPeakDiff,
                SurprisePeak = dto.SurprisePeak,
                SurprisePeakRank = dto.SurprisePeakRank,
                SurprisePeakMedian = dto.SurprisePeakMedian,
                SurprisePeakDiff = dto.SurprisePeakDiff,
                ConfusionPeak = dto.ConfusionPeak,
                ConfusionPeakRank = dto.ConfusionPeakRank,
                ConfusionPeakMedian = dto.ConfusionPeakMedian,
                ConfusionPeakDiff = dto.ConfusionPeakDiff,
                ContemptPeak = dto.ContemptPeak,
                ContemptPeakRank = dto.ContemptPeakRank,
                ContemptPeakMedian = dto.ContemptPeakMedian,
                ContemptPeakDiff = dto.ContemptPeakDiff,
                DisgustPeak = dto.DisgustPeak,
                DisgustPeakRank = dto.DisgustPeakRank,
                DisgustPeakMedian = dto.DisgustPeakMedian,
                DisgustPeakDiff = dto.DisgustPeakDiff,
                NeutralAttentionAvg = dto.NeutralAttentionAvg,
                NeutralAttentionAvgRank = dto.NeutralAttentionAvgRank,
                NeutralAttentionAvgMedian = dto.NeutralAttentionAvgMedian,
                DistractionAvg = dto.DistractionAvg,
                DistractionAvgRank = dto.DistractionAvgRank,
                DistractionAvgMedian = dto.DistractionAvgMedian,
                DistractionAvgDiff = dto.DistractionAvgDiff,
                NegativityPeak = dto.NegativityPeak,
                NegativityPeakRank = dto.NegativityPeakRank,
                NegativityPeakMedian = dto.NegativityPeakMedian,
                AttentionPeak = dto.AttentionPeak,
                AttentionPeakRank = dto.AttentionPeakRank,
                AttentionPeakMedian = dto.AttentionPeakMedian,
                HappyAvg = dto.HappyAvg,
                HappyAvgRank = dto.HappyAvgRank,
                HappyAvgMedian = dto.HappyAvgMedian,
                SurpriseAvg = dto.SurpriseAvg,
                SurpriseAvgRank = dto.SurpriseAvgRank,
                SurpriseAvgMedian = dto.SurpriseAvgMedian,
                ConfusionAvg = dto.ConfusionAvg,
                ConfusionAvgRank = dto.ConfusionAvgRank,
                ConfusionAvgMedian = dto.ConfusionAvgMedian,
                ContemptAvg = dto.ContemptAvg,
                ContemptAvgRank = dto.ContemptAvgRank,
                ContemptAvgMedian = dto.ContemptAvgMedian,
                DisgustAvg = dto.DisgustAvg,
                DisgustAvgRank = dto.DisgustAvgRank,
                DisgustAvgMedian = dto.DisgustAvgMedian,
                Country = dto.Country,
                Country_code = dto.CountryCode,
                GeographicRegion = dto.GeographicRegion,
                SourceMediaThumbnailFileName = dto.SourceMediaThumbnailFileName,
                SourceMediaConvertedFileName = dto.SourceMediaConvertedFileName,
                ParentCreative = dto.ParentCreative,
                SourceMedia = dto.SourceMedia,
                SourceMediaType = dto.SourceMediaType,
                Duration = dto.Duration,
                CreationDate = dto.CreationDate,
                Brand = dto.Brand,
                BrandID = dto.BrandID,
                BrandLogoFileName = dto.BrandLogoFileName,
                TopCategory = dto.TopCategory,
                MidCategory = dto.MidCategory,
                SubCategory = dto.SubCategory,
                Platform = dto.Platform,
                Device = dto.Device,
                Adformat = dto.Adformat,
                AdformatText = dto.AdformatText,
                IsEnabledForClients = dto.IsEnabledForClients
            };
        }
    }
}