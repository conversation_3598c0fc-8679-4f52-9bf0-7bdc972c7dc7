using System;
using BusinessLayer.Model.Response;

namespace DataLayer.Mapper;

public static class ExportResultMapper
{
    public static ExportResult ToModel(this Clients.DataTransferObjects.ExportResult source)
    {
        ArgumentNullException.ThrowIfNull(source);

        return new ExportResult
        {
            JobStatus = source.JobStatus,
            BucketName = source.BucketName,
            ExportUrl = source.ExportUrl,
            ExportFileName = source.ExportFileName,
            ExportFolderPath = source.ExportFolderPath
        };
    }
}