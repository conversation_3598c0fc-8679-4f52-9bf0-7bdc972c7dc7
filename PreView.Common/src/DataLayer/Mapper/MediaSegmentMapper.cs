using BusinessLayer.Model.Response;

namespace DataLayer.Mapper;

public static class MediaSegmentMapper
{
    public static MediaSegment ToModel(this Dremio.DataTransferObjects.MediaSegment dto)
    {
        return new MediaSegment
        {
            SegmentKey = dto.SegmentKey,
            Question = dto.Question,
            QuestionKey = dto.QuestionKey,
            QuestionOrder = dto.QuestionOrder,
            Answer = dto.Answer,
            AnswerKey = dto.AnswerKey,
            AnswerOrder = dto.AnswerOrder  
        };
    }
}