using System;
using System.Text.Json;
using BusinessLayer.Model.Response;

namespace DataLayer.Mapper;

public static class ForcedExposureCreativeMapper
{

    private static readonly JsonSerializerOptions NormFilterJsonOptions = new JsonSerializerOptions
    {
        PropertyNamingPolicy = new NormFiltersPolicy()
    };

    public static ForcedExposureCreative ToModel(
        this Dremio.DataTransferObjects.ForcedExposureCreative source)
    {
        ArgumentNullException.ThrowIfNull(source);

        return new ForcedExposureCreative
        {
            AdformatTextIF = source.AdformatTextIF,
            SourceMediaID = source.SourceMediaID,
            TestID = source.TestID,
            OrderExternalKey = source.OrderExternalKey,
            OrderAdSetID = source.OrderAdSetID,
            SegmentKey = source.SegmentKey,
            NormFallback = source.NormFallback,
            NormSegmentKey = source.NormSegmentKey,
            Country = source.Country,
            Country_code = source.Country_code,
            Order = source.Order,
            PDProjectID = source.PDProjectID,
            SurveyKeyAlias = source.SurveyKeyAlias,
            CustomAdSets = source.CustomAdSets,
            GeographicRegion = source.GeographicRegion,
            SourceMedia = source.SourceMedia,
            Duration = source.Duration,
            Brand = source.Brand,
            TopCategory = source.TopCategory,
            MidCategory = source.MidCategory,
            SubCategory = source.SubCategory,
            BrandID = source.BrandID,
            Platform = source.Platform,
            Device = source.Device,
            Adformat = source.AdformatTextIF,
            AdformatText = source.AdformatTextIF,
            CreationDate = source.CreationDate,
            IsEnabledForClients = source.IsEnabledForClients,
            BrandLogoFileName = source.BrandLogoFileName,
            SourceMediaThumbnailFileName = source.SourceMediaThumbnailFileName,
            SourceMediaThumbstripFileName = source.SourceMediaThumbstripFileName,
            SourceMediaConvertedFileName = source.SourceMediaConvertedFileName,
            Views = source.Views,
            QualityScore = source.QualityScore,
            QualityScore_index = source.QualityScore_index,
            AttentionAvg = source.AttentionAvg,
            AttentionAvgMedian = source.AttentionAvgMedian,
            AttentionAvgRank = source.AttentionAvgRank,
            AttentionAvgDiff = source.AttentionAvgDiff,
            AttentiveSeconds = source.AttentiveSeconds,
            AttentiveSecondsMedian = source.AttentiveSecondsMedian,
            AttentiveSecondsRank = source.AttentiveSecondsRank,
            AttentiveSecondsDiff = source.AttentiveSecondsDiff,
            AttentiveSecondsVTR = source.AttentiveSecondsVTR,
            AttentiveSecondsVTRMedian = source.AttentiveSecondsVTRMedian,
            AttentiveSecondsVTRRank = source.AttentiveSecondsVTRRank,
            AttentiveSecondsVTRDiff = source.AttentiveSecondsVTRDiff,
            Reactions = source.Reactions,
            ReactionsMedian = source.ReactionsMedian,
            ReactionsRank = source.ReactionsRank,
            ReactionsDiff = source.ReactionsDiff,
            NegativityAvg = source.NegativityAvg,
            NegativityAvgMedian = source.NegativityAvgMedian,
            NegativityAvgDiff = source.NegativityAvgDiff,
            NegativityAvgRank = source.NegativityAvgRank,
            HappyPeak = source.HappyPeak,
            HappyPeakMedian = source.HappyPeakMedian,
            HappyPeakRank = source.HappyPeakRank,
            HappyPeakDiff = source.HappyPeakDiff,
            VTR = source.VTR,
            VTRMedian = source.VTRMedian,
            VTRRank = source.VTRRank,
            VTRDiff = source.VTRDiff,
            PlaybackSeconds = source.PlaybackSeconds,
            PlaybackSecondsMedian = source.PlaybackSecondsMedian,
            PlaybackSecondsRank = source.PlaybackSecondsRank,
            PlaybackSecondsDiff = source.PlaybackSecondsDiff,
            SurprisePeak = source.SurprisePeak,
            SurprisePeakMedian = source.SurprisePeakMedian,
            SurprisePeakRank = source.SurprisePeakRank,
            SurprisePeakDiff = source.SurprisePeakDiff,
            ConfusionPeak = source.ConfusionPeak,
            ConfusionPeakMedian = source.ConfusionPeakMedian,
            ConfusionPeakRank = source.ConfusionPeakRank,
            ConfusionPeakDiff = source.ConfusionPeakDiff,
            ContemptPeak = source.ContemptPeak,
            ContemptPeakMedian = source.ContemptPeakMedian,
            ContemptPeakRank = source.ContemptPeakRank,
            ContemptPeakDiff = source.ContemptPeakDiff,
            DisgustPeak = source.DisgustPeak,
            DisgustPeakMedian = source.DisgustPeakMedian,
            DisgustPeakRank = source.DisgustPeakRank,
            DisgustPeakDiff = source.DisgustPeakDiff,
            DistractionAvg = source.DistractionAvg,
            DistractionAvgMedian = source.DistractionAvgMedian,
            DistractionAvgRank = source.DistractionAvgRank,
            DistractionAvgDiff = source.DistractionAvgDiff,
            AttentionNormFormat = source.AttentionNormFormat,
            ReactionsNormFormat = source.ReactionsNormFormat,
            SurveyNormFormat = source.SurveyNormFormat,

            ReactionsNormDevice = source.ReactionsNormDevice,
            SurveyNormDevice = source.SurveyNormDevice,
            AttentionNormDevice = source.AttentionNormDevice,

            ReactionsNormDuration = source.ReactionsNormDuration,
            SurveyNormDuration = source.SurveyNormDuration,
            AttentionNormDuration = source.AttentionNormDuration,

            ReactionsNormAdFormat = source.ReactionsNormAdFormat,
            SurveyNormAdFormat = source.SurveyNormAdFormat,
            AttentionNormAdFormat = source.AttentionNormAdFormat,

            ReactionsNormAdFormatName = source.ReactionsNormAdFormatName,
            SurveyNormAdFormatName = source.SurveyNormAdFormatName,
            AttentionNormAdFormatName = source.AttentionNormAdFormatName,

            ReactionsNormEnvironmentCategory = source.ReactionsNormEnvironmentCategory,
            SurveyNormEnvironmentCategory = source.SurveyNormEnvironmentCategory,
            AttentionNormEnvironmentCategory = source.AttentionNormEnvironmentCategory,

            ReactionsNormRegion = source.ReactionsNormRegion,
            SurveyNormRegion = source.SurveyNormRegion,
            AttentionNormRegion = source.AttentionNormRegion,

            ReactionsNormSampleSize = source.ReactionsNormSampleSize,
            SurveyNormSampleSize = source.SurveyNormSampleSize,
            AttentionNormSampleSize = source.AttentionNormSampleSize,

            ReactionsNormRefreshDate = source.ReactionsNormRefreshDate,
            SurveyNormRefreshDate = source.SurveyNormRefreshDate,
            AttentionNormRefreshDate = source.AttentionNormRefreshDate,

            ReactionsNormCustomNormFilters = source.ReactionsNormCustomNormFilters is null ? null : JsonSerializer.Deserialize<NormFilters>(source.ReactionsNormCustomNormFilters, NormFilterJsonOptions),
            SurveyNormCustomNormFilters = source.SurveyNormCustomNormFilters is null ? null : JsonSerializer.Deserialize<NormFilters>(source.SurveyNormCustomNormFilters, NormFilterJsonOptions),
            AttentionNormCustomNormFilters = source.AttentionNormCustomNormFilters is null ? null : JsonSerializer.Deserialize<NormFilters>(source.AttentionNormCustomNormFilters, NormFilterJsonOptions)
        };
    }
}
