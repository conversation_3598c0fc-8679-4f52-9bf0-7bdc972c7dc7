using System;
using System.Linq;
using BusinessLayer.Constant;
using BusinessLayer.Model.Request;
using ExportConfiguration = BusinessLayer.Model.Response.ExportPayload;
using GridFilterItem = BusinessLayer.Model.Filter.GridFilterItem;
using GridSortingItem = BusinessLayer.Model.Filter.GridSortingItem;

namespace DataLayer.Mapper;

public static class ExportConfigurationMapper
{
    public static GetExportDataRequest ToRequest(this ExportConfiguration payload)
    {
        return new GetExportDataRequest
        {
            AccountId = payload.AccountId,
            IsAdmin = payload.IsAdmin,
            SegmentKeys = payload.SegmentKeys,
            Audience = payload.Audience ?? SegmentKeyConstant.AllSegmentKey,
            BrandId = payload.BrandId,
            StudyId = payload.StudyId,
            MinViewThresholdForScores = payload.MinViewThresholdForScores,
            ProduceEmptyAudiences = payload.ProduceEmptyAudiences,
            Media = payload.Media.Select(media => new MediaModelRequest
            {
                TestID = media.TestID,
                SourceMediaID = media.SourceMediaID,
                TaskID = media.TaskID,
                OrderAdSetID = media.OrderAdSetID
            }).ToList(),
            Filters = payload.Filters.Select(item => new GridFilterItem
            {
                ColumnField = item.ColumnField,
                Value = item.Value,
                OperatorValue = item.OperatorValue,
                OrQueryGroupId = item.OrQueryGroupId
            }).ToList(),
            VisibleColumnsOrder = payload.VisibleColumnsOrder,
            Sorting = payload.Sorting.Select(item => new GridSortingItem
            {
                Field = item.Field,
                Direction = item.Direction,
                FirstOrderText = item.FirstOrderText
            }).ToList(),
            CurveFilter = payload.CurveFilter
        };
    }

    public static ExportConfiguration ToModel(this Client.DataTransferObjects.ExportConfiguration source)
    {
        ArgumentNullException.ThrowIfNull(source);

        return new ExportConfiguration
        {
            ProductType = source.ProductType,
            AccountId = source.AccountId,
            IsAdmin = source.IsAdmin,
            VisibleColumnsOrder = source.VisibleColumnsOrder,
            ExportFolderPath = source.ExportFolderPath,
            BucketName = source.BucketName,
            ExportFileName = source.ExportFileName,
            Sorting = source.Sorting.Select(item => item.ToModel()).ToList(),
            Filters = source.Filters.Select(item => item.ToModel()).ToList(),
            BrandId = source.BrandId,
            StudyId = source.StudyId,
            Audience = source.Audience,
            ProduceEmptyAudiences = source.ProduceEmptyAudiences,
            Media = source.Media,
            SegmentKeys = source.SegmentKeys,
            MinViewThresholdForScores = source.MinViewThresholdForScores
        };
    }

    public static Client.DataTransferObjects.ExportConfiguration ToDto(this StartExportRequest source)
    {
        ArgumentNullException.ThrowIfNull(source);

        return new Client.DataTransferObjects.ExportConfiguration
        {
            ProductType = source.ProductType,
            AccountId = source.AccountId,
            IsAdmin = source.IsAdmin,
            VisibleColumnsOrder = source.VisibleColumnsOrder,
            ExportFolderPath = source.ExportFolderPath,
            BucketName = source.BucketName,
            ExportFileName = source.ExportFileName,
            Sorting = source.Sorting.Select(item => item.ToDto()).ToList(),
            Filters = source.Filters.Select(item => item.ToDto()).ToList(),
            BrandId = source.BrandId,
            StudyId = source.StudyId,
            Audience = source.Audience,
            ProduceEmptyAudiences = source.ProduceEmptyAudiences,
            Media = source.Media,
            SegmentKeys = source.SegmentKeys,
            MinViewThresholdForScores = source.MinViewThresholdForScores,
            CurveFilter = source.CurveFilter
        };
    }
}