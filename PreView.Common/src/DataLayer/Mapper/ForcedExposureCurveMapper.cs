using System;
using BusinessLayer.Model.Response;

namespace DataLayer.Mapper;

public static class ForcedExposureCurveMapper
{
    public static ForcedExposureCurve ToModel(this Dremio.DataTransferObjects.ForcedExposureCurve dto)
    {
        if (dto == null)
            throw new ArgumentNullException(nameof(dto));

        return new ForcedExposureCurve
        {
            SegmentKey = dto.SegmentKey,
            SourceMediaID = dto.SourceMediaID,
            TestID = dto.TestID,
            Second = dto.Second,
            Playback = dto.Playback,
            PlaybackNorm = dto.PlaybackNorm,
            Attention = dto.Attention,
            AttentionNorm = dto.AttentionNorm,
            Distraction = dto.Distraction,
            DistractionNorm = dto.DistractionNorm,
            AllReactions = dto.AllReactions,
            AllReactionsNorm = dto.AllReactionsNorm,
            Negativity = dto.Negativity,
            NegativityNorm = dto.NegativityNorm,
            Happiness = dto.Happiness,
            HappinessNorm = dto.HappinessNorm,
            Contempt = dto.Contempt,
            ContemptNorm = dto.ContemptNorm,
            Surprise = dto.Surprise,
            SurpriseNorm = dto.SurpriseNorm,
            Confusion = dto.Confusion,
            ConfusionNorm = dto.ConfusionNorm,
            Disgust = dto.Disgust,
            DisgustNorm = dto.DisgustNorm,
            NeutralAttention = dto.NeutralAttention,
            NeutralAttentionNorm = dto.NeutralAttentionNorm,
        };
    }
}