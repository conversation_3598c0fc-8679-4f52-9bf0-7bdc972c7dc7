using BusinessLayer.Model.Response;
using System;

namespace DataLayer.Mapper;

public static class AccountProductMapper
{
    public static AccountProduct ToModel(this Dremio.DataTransferObjects.AccountProduct dto)
    {
        return new AccountProduct
        {
            Id = dto.AccountID,
            Name = dto.AccountName,
            HasREInContext = Convert.ToBoolean(dto.HasREInContext),
            HasCustomNorm = Convert.ToBoolean(dto.HasCustomNorm),
            HasAccessToNewForcedExposure = Convert.ToBoolean(dto.HasAccessToNewForcedExposure)
        };
    }
}