using System;
using System.Text.Json;
using BusinessLayer.Model.Response;

namespace DataLayer.Mapper;

public static class InContextCreativeExtension
{
    private static readonly JsonSerializerOptions NormFilterJsonOptions = new JsonSerializerOptions
    {
        PropertyNamingPolicy = new NormFiltersPolicy()
    };

    public static InContextCreative ToModel(this Dremio.DataTransferObjects.InContextCreative source)
    {
        ArgumentNullException.ThrowIfNull(source);

        return new InContextCreative
        {
            SourceMediaID = source.SourceMediaID,
            TestID = source.TestID,
            InFocusTestID = source.InFocusTestID,
            TaskID = source.TaskID,
            Order = source.Order,
            CustomAdSets = source.CustomAdSets,
            PDProjectID = source.PDProjectID,
            SurveyKeyAlias = source.SurveyKeyAlias,
            OrderExternalKey = source.OrderExternalKey,
            OrderAdSetID = source.OrderAdSetID,
            SegmentKey = source.SegmentKey,
            Audience = null,
            Country = source.Country,
            Country_code = source.Country_code,
            GeographicRegion = source.GeographicRegion,
            SourceMedia = source.SourceMedia,
            Duration = source.Duration,
            NormFallbackIC = source.NormFallbackIC,
            NormFallbackIF = source.NormFallbackIF,
            NormFallbackSurveyIC = source.NormFallbackSurveyIC,
            NormFallbackSurveyIF = source.NormFallbackSurveyIF,
            NormSegmentKeyIC = source.NormSegmentKeyIC,
            NormSegmentKeyIF = source.NormSegmentKeyIF,
            AdformatName = source.AdformatName,
            EnvironmentCategory = source.EnvironmentCategory,
            Brand = source.Brand,
            TopCategory = source.TopCategory,
            MidCategory = source.MidCategory,
            SubCategory = source.SubCategory,
            BrandID = source.BrandID,
            Platform = source.Platform,
            Device = source.Device,
            Adformat = source.Adformat,
            AdformatText = source.AdformatText,
            AdformatTextIF = source.AdformatTextIF,
            CreationDate = source.CreationDate,
            BrandLogoFileName = source.BrandLogoFileName,
            BrandLogoUrl = null,
            SourceMediaThumbnailFileName = source.SourceMediaThumbnailFileName,
            ThumbnailUrl = null,
            SourceMediaConvertedFileName = source.SourceMediaConvertedFileName,
            SourceMediaThumbstripFileName = source.SourceMediaThumbstripFileName,
            IsEnabledForClients = source.IsEnabledForClients,
            Views = source.Views,
            QualityScore = source.QualityScore,
            QualityScore_index = source.QualityScore_index,
            AttentiveSeconds = source.AttentiveSeconds,
            AttentiveSecondsMedian = source.AttentiveSecondsMedian,
            AttentiveSecondsDiff = source.AttentiveSecondsDiff,
            AttentiveSecondsRank = source.AttentiveSecondsRank,
            AttentiveSecondsIF = source.AttentiveSecondsIF,
            AttentiveSecondsIFMedian = source.AttentiveSecondsIFMedian,
            AttentiveSecondsIFDiff = source.AttentiveSecondsIFDiff,
            AttentiveSecondsIFRank = source.AttentiveSecondsIFRank,
            AttentiveSecondsVTR = source.AttentiveSecondsVTR,
            AttentiveSecondsVTRMedian = source.AttentiveSecondsVTRMedian,
            AttentiveSecondsVTRDiff = source.AttentiveSecondsVTRDiff,
            AttentiveSecondsVTRRank = source.AttentiveSecondsVTRRank,
            AttentiveSecondsVTRIF = source.AttentiveSecondsVTRIF,
            AttentiveSecondsVTRIFMedian = source.AttentiveSecondsVTRIFMedian,
            AttentiveSecondsVTRIFDiff = source.AttentiveSecondsVTRIFDiff,
            AttentiveSecondsVTRIFRank = source.AttentiveSecondsVTRIFRank,
            Reactions = source.Reactions,
            ReactionsMedian = source.ReactionsMedian,
            ReactionsDiff = source.ReactionsDiff,
            ReactionsRank = source.ReactionsRank,
            ReactionsIC = source.ReactionsIC,
            ReactionsICMedian = source.ReactionsICMedian,
            ReactionsICDiff = source.ReactionsICDiff,
            ReactionsICRank = source.ReactionsICRank,
            NegativityAvgIC = source.NegativityAvgIC,
            NegativityAvgICMedian = source.NegativityAvgICMedian,
            NegativityAvgICDiff = source.NegativityAvgICDiff,
            NegativityAvgICRank = source.NegativityAvgICRank,
            NegativityAvgIF = source.NegativityAvgIF,
            NegativityAvgIFMedian = source.NegativityAvgIFMedian,
            NegativityAvgIFDiff = source.NegativityAvgIFDiff,
            NegativityAvgIFRank = source.NegativityAvgIFRank,
            AttentionAvgIC = source.AttentionAvgIC,
            AttentionAvgICMedian = source.AttentionAvgICMedian,
            AttentionAvgICDiff = source.AttentionAvgICDiff,
            AttentionAvgICRank = source.AttentionAvgICRank,
            AttentionAvgIF = source.AttentionAvgIF,
            AttentionAvgIFMedian = source.AttentionAvgIFMedian,
            AttentionAvgIFDiff = source.AttentionAvgIFDiff,
            AttentionAvgIFRank = source.AttentionAvgIFRank,
            BrandRecognition = source.BrandRecognition,
            BrandRecognitionMedian = source.BrandRecognitionMedian,
            BrandRecognitionDiff = source.BrandRecognitionDiff,
            BrandRecognitionRank = source.BrandRecognitionRank,
            AdLikeability = source.AdLikeability,
            AdLikeabilityMedian = source.AdLikeabilityMedian,
            AdLikeabilityDiff = source.AdLikeabilityDiff,
            AdLikeabilityRank = source.AdLikeabilityRank,
            HappyPeak = source.HappyPeak,
            HappyPeakMedian = source.HappyPeakMedian,
            HappyPeakDiff = source.HappyPeakDiff,
            HappyPeakRank = source.HappyPeakRank,
            HappyPeakIC = source.HappyPeakIC,
            HappyPeakICMedian = source.HappyPeakICMedian,
            HappyPeakICDiff = source.HappyPeakICDiff,
            HappyPeakICRank = source.HappyPeakICRank,
            VTR = source.VTR,
            VTRRank = source.VTRRank,
            VTRMedian = source.VTRMedian,
            VTRDiff = source.VTRDiff,
            VTRIF = source.VTRIF,
            VTRIFRank = source.VTRIFRank,
            VTRIFMedian = source.VTRIFMedian,
            VTRIFDiff = source.VTRIFDiff,
            PlaybackSeconds = source.PlaybackSeconds,
            PlaybackSecondsRank = source.PlaybackSecondsRank,
            PlaybackSecondsMedian = source.PlaybackSecondsMedian,
            PlaybackSecondsDiff = source.PlaybackSecondsDiff,
            PlaybackSecondsIF = source.PlaybackSecondsIF,
            PlaybackSecondsIFRank = source.PlaybackSecondsIFRank,
            PlaybackSecondsIFMedian = source.PlaybackSecondsIFMedian,
            PlaybackSecondsIFDiff = source.PlaybackSecondsIFDiff,
            AdRecognition = source.AdRecognition,
            AdRecognitionRank = source.AdRecognitionRank,
            AdRecognitionMedian = source.AdRecognitionMedian,
            AdRecognitionDiff = source.AdRecognitionDiff,
            SurprisePeak = source.SurprisePeak,
            SurprisePeakRank = source.SurprisePeakRank,
            SurprisePeakMedian = source.SurprisePeakMedian,
            SurprisePeakDiff = source.SurprisePeakDiff,
            SurprisePeakIC = source.SurprisePeakIC,
            SurprisePeakICRank = source.SurprisePeakICRank,
            SurprisePeakICMedian = source.SurprisePeakICMedian,
            SurprisePeakICDiff = source.SurprisePeakICDiff,
            ConfusionPeak = source.ConfusionPeak,
            ConfusionPeakRank = source.ConfusionPeakRank,
            ConfusionPeakMedian = source.ConfusionPeakMedian,
            ConfusionPeakDiff = source.ConfusionPeakDiff,
            ConfusionPeakIC = source.ConfusionPeakIC,
            ConfusionPeakICRank = source.ConfusionPeakICRank,
            ConfusionPeakICMedian = source.ConfusionPeakICMedian,
            ConfusionPeakICDiff = source.ConfusionPeakICDiff,
            ContemptPeak = source.ContemptPeak,
            ContemptPeakRank = source.ContemptPeakRank,
            ContemptPeakMedian = source.ContemptPeakMedian,
            ContemptPeakDiff = source.ContemptPeakDiff,
            ContemptPeakIC = source.ContemptPeakIC,
            ContemptPeakICRank = source.ContemptPeakICRank,
            ContemptPeakICMedian = source.ContemptPeakICMedian,
            ContemptPeakICDiff = source.ContemptPeakICDiff,
            DisgustPeak = source.DisgustPeak,
            DisgustPeakRank = source.DisgustPeakRank,
            DisgustPeakMedian = source.DisgustPeakMedian,
            DisgustPeakDiff = source.DisgustPeakDiff,
            DisgustPeakIC = source.DisgustPeakIC,
            DisgustPeakICRank = source.DisgustPeakICRank,
            DisgustPeakICMedian = source.DisgustPeakICMedian,
            DisgustPeakICDiff = source.DisgustPeakICDiff,
            BrandTrust = source.BrandTrust,
            BrandTrustRank = source.BrandTrustRank,
            BrandTrustMedian = source.BrandTrustMedian,
            BrandTrustDiff = source.BrandTrustDiff,
            Persuasion = source.Persuasion,
            PersuasionRank = source.PersuasionRank,
            PersuasionMedian = source.PersuasionMedian,
            PersuasionDiff = source.PersuasionDiff,
            DistractionAvgIC = source.DistractionAvgIC,
            DistractionAvgICRank = source.DistractionAvgICRank,
            DistractionAvgICMedian = source.DistractionAvgICMedian,
            DistractionAvgICDiff = source.DistractionAvgICDiff,
            DistractionAvgIF = source.DistractionAvgIF,
            DistractionAvgIFRank = source.DistractionAvgIFRank,
            DistractionAvgIFMedian = source.DistractionAvgIFMedian,
            DistractionAvgIFDiff = source.DistractionAvgIFDiff,

            ReactionsNormDeviceIF = source.ReactionsNormDeviceIF,
            ReactionsNormDeviceIC = source.ReactionsNormDeviceIC,
            SurveyNormDeviceIF = source.SurveyNormDeviceIF,
            SurveyNormDeviceIC = source.SurveyNormDeviceIC,
            AttentionNormDeviceIF = source.AttentionNormDeviceIF,
            AttentionNormDeviceIC = source.AttentionNormDeviceIC,
            ReactionsNormFormatIF = source.ReactionsNormFormatIF,
            ReactionsNormFormatIC = source.ReactionsNormFormatIC,
            SurveyNormFormatIF = source.SurveyNormFormatIF,
            SurveyNormFormatIC = source.SurveyNormFormatIC,
            AttentionNormFormatIF = source.AttentionNormFormatIF,
            AttentionNormFormatIC = source.AttentionNormFormatIC,

            ReactionsNormDurationIF = source.ReactionsNormDurationIF,
            ReactionsNormDurationIC = source.ReactionsNormDurationIC,
            SurveyNormDurationIF = source.SurveyNormDurationIF,
            SurveyNormDurationIC = source.SurveyNormDurationIC,
            AttentionNormDurationIF = source.AttentionNormDurationIF,
            AttentionNormDurationIC = source.AttentionNormDurationIC,

            ReactionsNormAdFormatIF = source.ReactionsNormAdFormatIF,
            ReactionsNormAdFormatIC = source.ReactionsNormAdFormatIC,
            SurveyNormAdFormatIF = source.SurveyNormAdFormatIF,
            SurveyNormAdFormatIC = source.SurveyNormAdFormatIC,
            AttentionNormAdFormatIF = source.AttentionNormAdFormatIF,
            AttentionNormAdFormatIC = source.AttentionNormAdFormatIC,

            ReactionsNormEnvironmentCategoryIF = source.ReactionsNormEnvironmentCategoryIF,
            ReactionsNormEnvironmentCategoryIC = source.ReactionsNormEnvironmentCategoryIC,
            SurveyNormEnvironmentCategoryIF = source.SurveyNormEnvironmentCategoryIF,
            SurveyNormEnvironmentCategoryIC = source.SurveyNormEnvironmentCategoryIC,
            AttentionNormEnvironmentCategoryIF = source.AttentionNormEnvironmentCategoryIF,
            AttentionNormEnvironmentCategoryIC = source.AttentionNormEnvironmentCategoryIC,

            ReactionsNormRegionIF = source.ReactionsNormRegionIF,
            ReactionsNormRegionIC = source.ReactionsNormRegionIC,
            SurveyNormRegionIF = source.SurveyNormRegionIF,
            SurveyNormRegionIC = source.SurveyNormRegionIC,
            AttentionNormRegionIF = source.AttentionNormRegionIF,
            AttentionNormRegionIC = source.AttentionNormRegionIC,

            ReactionsNormSampleSizeIF = source.ReactionsNormSampleSizeIF,
            ReactionsNormSampleSizeIC = source.ReactionsNormSampleSizeIC,
            SurveyNormSampleSizeIF = source.SurveyNormSampleSizeIF,
            SurveyNormSampleSizeIC = source.SurveyNormSampleSizeIC,
            AttentionNormSampleSizeIF = source.AttentionNormSampleSizeIF,
            AttentionNormSampleSizeIC = source.AttentionNormSampleSizeIC,

            ReactionsNormRefreshDateIF = source.ReactionsNormRefreshDateIF,
            ReactionsNormRefreshDateIC = source.ReactionsNormRefreshDateIC,
            SurveyNormRefreshDateIF = source.SurveyNormRefreshDateIF,
            SurveyNormRefreshDateIC = source.SurveyNormRefreshDateIC,
            AttentionNormRefreshDateIF = source.AttentionNormRefreshDateIF,
            AttentionNormRefreshDateIC = source.AttentionNormRefreshDateIC,

            ReactionsNormAdFormatNameIF = source.ReactionsNormAdFormatNameIF,
            ReactionsNormAdFormatNameIC = source.ReactionsNormAdFormatNameIC,
            SurveyNormAdFormatNameIF = source.SurveyNormAdFormatNameIF,
            SurveyNormAdFormatNameIC = source.SurveyNormAdFormatNameIC,
            AttentionNormAdFormatNameIF = source.AttentionNormAdFormatNameIF,
            AttentionNormAdFormatNameIC = source.AttentionNormAdFormatNameIC,

            ReactionsNormCustomNormFilters = source.ReactionsNormCustomNormFilters is null ? null : JsonSerializer.Deserialize<NormFilters>(source.ReactionsNormCustomNormFilters, NormFilterJsonOptions),
            AttentionNormCustomNormFilters = source.AttentionNormCustomNormFilters is null ? null : JsonSerializer.Deserialize<NormFilters>(source.AttentionNormCustomNormFilters, NormFilterJsonOptions),
            SurveyNormCustomNormFilters = source.SurveyNormCustomNormFilters is null ? null : JsonSerializer.Deserialize<NormFilters>(source.SurveyNormCustomNormFilters, NormFilterJsonOptions),
            ReactionsNormCustomNormFiltersIC = source.ReactionsNormCustomNormFiltersIC is null ? null : JsonSerializer.Deserialize<NormFilters>(source.ReactionsNormCustomNormFiltersIC, NormFilterJsonOptions),
            AttentionNormCustomNormFiltersIC = source.AttentionNormCustomNormFiltersIC is null ? null : JsonSerializer.Deserialize<NormFilters>(source.AttentionNormCustomNormFiltersIC, NormFilterJsonOptions),
            SurveyNormCustomNormFiltersIC = source.SurveyNormCustomNormFiltersIC is null ? null : JsonSerializer.Deserialize<NormFilters>(source.SurveyNormCustomNormFiltersIC, NormFilterJsonOptions),
            ReactionsNormCustomNormFiltersIF = source.ReactionsNormCustomNormFiltersIF is null ? null : JsonSerializer.Deserialize<NormFilters>(source.ReactionsNormCustomNormFiltersIF, NormFilterJsonOptions),
            AttentionNormCustomNormFiltersIF = source.AttentionNormCustomNormFiltersIF is null ? null : JsonSerializer.Deserialize<NormFilters>(source.AttentionNormCustomNormFiltersIF, NormFilterJsonOptions),
            SurveyNormCustomNormFiltersIF = source.SurveyNormCustomNormFiltersIF is null ? null : JsonSerializer.Deserialize<NormFilters>(source.SurveyNormCustomNormFiltersIF, NormFilterJsonOptions)
        };
    }
}