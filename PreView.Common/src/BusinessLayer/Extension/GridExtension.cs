using System.Linq.Dynamic.Core;
using BusinessLayer.Interfaces;
using BusinessLayer.Model.Filter;

namespace BusinessLayer.Extensions;

public static class GridExtension
{
    public static ICollection<TDto> ApplyFilter<TDto, TQuery>(this IEnumerable<TDto> rows, TQuery query)
        where TQuery : IFilterQuery
    {
        var result = rows.AsQueryable();

        if (!query.Filters.Any())
        {
            return result.ToList();
        }

        var predicate = ToFilterString(query.Filters);

        if (string.IsNullOrWhiteSpace(predicate))
        {
            return result.ToList();
        }

        var config = new ParsingConfig
        {
            AllowEqualsAndToStringMethodsOnObject = true
        };

        result = result.Where(config, predicate);

        return result.ToList();
    }

    private static string ToFilterString(List<GridFilterItem> filters)
    {
        var andFilters = new List<string>();
        var orFilters = new Dictionary<int, string>();

        foreach (var item in filters)
        {
            var operatorString = OperationConverter(item);
            if (!string.IsNullOrWhiteSpace(operatorString))
            {
                if (item.OrQueryGroupId.HasValue)
                {
                    var orQueryGroupId = (int)item.OrQueryGroupId;
                    if (orFilters.TryGetValue(orQueryGroupId, out var filterStringByGroup))
                        orFilters[orQueryGroupId] = $"{filterStringByGroup} OR {operatorString}";
                    else
                        orFilters.Add(orQueryGroupId, operatorString);
                }
                else
                {
                    andFilters.Add(operatorString);
                }
            }
        }

        andFilters.AddRange(orFilters.Values.Select(of => $"({of})"));

        var filterString = string.Join(" AND ", andFilters);

        return filterString;
    }

    private static string OperationConverter(GridFilterItem item)
    {
        const string separator = ";";
        if (item.OperatorValue.Equals("isAnyOf"))
        {
            if (string.IsNullOrWhiteSpace(item.Value)) return string.Empty;

            return item.Value.Contains(separator)
                ? $"({string.Join(" or ", item.Value.Split(separator).Select(v => $"{item.ColumnField} != null && {item.ColumnField}.ToString().ToLower().Contains(\"{v.ToLower()}\")"))})"
                : $"( {item.ColumnField} != null && {item.ColumnField}.ToString().ToLower().Contains(\"{item.Value.ToLower()}\"))";
        }

        if (item.OperatorValue.Equals("enum"))
            return string.IsNullOrWhiteSpace(item.Value) || !int.TryParse(item.Value, out var number)
                ? $"({item.ColumnField} == null)"
                : $"({item.ColumnField} = \"{number}\")";

        if (item.OperatorValue.Equals("range"))
        {
            if (string.IsNullOrWhiteSpace(item.Value)) return string.Empty;

            var rangeValues = item.Value.Split(separator);
            var rangeQuery =
                $"( \"{rangeValues[0]}\" <= {item.ColumnField} and {item.ColumnField} <= \"{rangeValues[1]}\" )";
            return rangeQuery;
        }

        if (item.OperatorValue.Equals("dateTimeOffset"))
        {
            if (string.IsNullOrWhiteSpace(item.Value)) return string.Empty;

            var rangeValues = item.Value.Split(separator);
            var rangeQuery =
                $"( DateTimeOffset.Parse(\"{rangeValues[0]}\") <= {item.ColumnField} and {item.ColumnField} <= DateTimeOffset.Parse(\"{rangeValues[1]}\") )";
            return rangeQuery;
        }

        if (item.OperatorValue.Equals("dateTime"))
        {
            if (string.IsNullOrWhiteSpace(item.Value)) return string.Empty;

            var rangeValues = item.Value.Split(separator);
            var rangeQuery =
                $"( DateTime.Parse(\"{rangeValues[0]}\") <= {item.ColumnField} and {item.ColumnField} <= DateTime.Parse(\"{rangeValues[1]}\") )";
            return rangeQuery;
        }

        //default
        return string.IsNullOrWhiteSpace(item.Value)
            ? string.Empty
            : $"( {item.ColumnField} != null && {item.ColumnField}.ToString().ToLower().{item.OperatorValue}(\"{item.Value.ToLower()}\") )";
    }

    public static IEnumerable<TDto> Sort<TDto, TQuery>(this IEnumerable<TDto> rows, TQuery query)
        where TQuery : ISortQuery
    {
        var queryableRows = rows.AsQueryable();
        var sortExpression = string.Join(',',
            query.Sorting.Where(sort => !string.IsNullOrEmpty(sort.Field))
                .Select(sort =>
                {
                    var sortFieldExpression = "";

                    if (!string.IsNullOrEmpty(sort.FirstOrderText))
                        sortFieldExpression += $"iif({sort.Field} = \"{sort.FirstOrderText}\", 0, 1),";

                    sortFieldExpression += $"{sort.Field} {sort.Direction ?? "asc"}";

                    return sortFieldExpression;
                })
        );

        var config = new ParsingConfig
        {
            RestrictOrderByToPropertyOrField  = false
        };

        if (!string.IsNullOrEmpty(sortExpression))
            queryableRows = queryableRows.OrderBy(config, sortExpression);


        return queryableRows;
    }

    public static IEnumerable<TDto> GetPage<TDto, TQuery>(this IEnumerable<TDto> rows, TQuery query)
        where TQuery : IGridQuery
    {
        var queryableRows = rows.Sort(query);


        if (query.PageSize > 0)
        {
            var skip = query.CurrentPage * query.PageSize;
            return queryableRows
                .Skip(skip)
                .Take(query.PageSize)
                .ToList();
        }

        return queryableRows.ToList();
    }
}