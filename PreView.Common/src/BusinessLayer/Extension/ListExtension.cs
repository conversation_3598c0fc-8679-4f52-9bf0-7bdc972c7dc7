namespace BusinessLayer.Extensions;

public static class ListExtension
{
    public static IEnumerable<string> CreateCustomFilterOptionList<TSource>(this IEnumerable<TSource> source,
        Func<TSource, string> selector) where TSource : class
    {
        return source
            .Where(x => x != null)
            .Select(selector)
            .Where(x => !string.IsNullOrWhiteSpace(x))
            .Distinct()
            .OrderBy(s => s)
            .ToList();
    }

    public static IEnumerable<string> CreateCustomFilterOptionList<TSource>(this IEnumerable<TSource> source,
        Func<TSource, string[]> selector) where TSource : class
    {
        return source
            .Where(x => x != null)
            .SelectMany(selector)
            .Distinct()
            .OrderBy(s => s)
            .ToList();
    }

    public static IEnumerable<TValue> CreateCustomFilterOptionRange<TSource, TValue>(this IEnumerable<TSource> source,
        Func<TSource, TValue> selector) where TValue : struct
    {
        return new[] { source.Select(selector).Min(), source.Select(selector).Max() }
            .Distinct()
            .ToList();
    }

    public static IEnumerable<TValue> CreateCustomFilterOptionRange<TSource, TValue>(this IEnumerable<TSource> source,
        Func<TSource, TValue?> selector) where TValue : struct
    {
        return new[] { source.Select(selector).Min() ?? default, source.Select(selector).Max() ?? default }
            .Distinct()
            .ToList();
    }

    public static IEnumerable<string> CreateCustomFilterOptionDateString<TSource>(this IEnumerable<TSource> source,
        Func<TSource, DateTimeOffset?> selector)
    {
        return new[]
        {
            source.Select(selector)
                .Min()
                ?.DateTime
                .ToString("yyyy-MM-dd")
        };
    }

    public static IEnumerable<string> CreateCustomFilterOptionDateString<TSource>(this IEnumerable<TSource> source,
        Func<TSource, DateTimeOffset> selector)
    {
        return new[]
        {
            source.Select(selector)
                .Min()
                .DateTime
                .ToString("yyyy-MM-dd")
        };
    }
}