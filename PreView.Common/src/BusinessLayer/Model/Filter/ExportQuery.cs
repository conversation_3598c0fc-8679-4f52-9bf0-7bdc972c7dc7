using BusinessLayer.Interfaces;
using BusinessLayer.Model.Response;

namespace BusinessLayer.Model.Filter;

public class ExportQuery : IExportQuery
{
    public List<GridFilterItem> Filters { get; set; } = [];
    public List<GridSortingItem> Sorting { get; set; } = [];
    public List<string> VisibleColumnsOrder { get; set; }
    public List<Media> Media { get; set; }
    public List<string> SegmentKeys { get; set; }
}