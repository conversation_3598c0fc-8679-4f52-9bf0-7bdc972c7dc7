using BusinessLayer.Constant;
using BusinessLayer.Model.Filter;

namespace BusinessLayer.Model.Request;

public class GetExportDataRequest
{
    public int AccountId { get; set; }
    public bool IsAdmin { get; set; }
    public IEnumerable<string> SegmentKeys { get; set; }
    public string Audience { get; set; } = SegmentKeyConstant.AllSegmentKey;
    public int? BrandId { get; set; }
    public int? StudyId { get; set; }
    public int MinViewThresholdForScores { get; set; }
    public List<GridFilterItem> Filters { get; set; } = new();
    public List<string> VisibleColumnsOrder { get; set; } = new();
    public List<GridSortingItem> Sorting { get; set; } = new();
    public List<MediaModelRequest> Media { get; set; } = new();
    public bool ProduceEmptyAudiences { get; set; } = true;
    public List<CurveFilterItem> CurveFilter { get; set; } = new();
}