using BusinessLayer.Constant;
using BusinessLayer.Model.Filter;
using BusinessLayer.Model.Response;

namespace BusinessLayer.Model.Request;

public class StartExportRequest
{
    public ProductType ProductType { get; set; }
    public int AccountId { get; set; }
    public bool IsAdmin { get; set; }
    public int? BrandId { get; set; }
    public int? StudyId { get; set; }
    public string SegmentKey { get; set; }
    public List<string> SegmentKeys { get; set; } = new();
    public string Audience { get; set; } = SegmentKeyConstant.AllSegmentKey;
    public string ExportFolderPath { get; set; }
    public string BucketName { get; set; }
    public string ExportFileName { get; set; }
    public List<GridFilterItem> Filters { get; set; } = new();
    public List<GridSortingItem> Sorting { get; set; } = new();
    public List<string> VisibleColumnsOrder { get; set; } = new();
    public int MinViewThresholdForScores { get; set; }
    public List<Media> Media { get; set; } = new();
    public bool ProduceEmptyAudiences { get; set; } = true;
    public List<CurveFilterItem> CurveFilter { get; set; } = new();
}