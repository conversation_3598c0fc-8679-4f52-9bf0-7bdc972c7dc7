using BusinessLayer.CustomAttributes;

namespace BusinessLayer.Model.Response;

public class ForcedExposureCurves
{
    [ExcludeFromProperties]
    public string CreativeID { get; set; }
    [ExcludeFromProperties]
    public string AdSetExternalKey { get; set; }
    [ExcludeFromProperties]
    public string SourceMediaExternalKey { get; set; }
    [ExcludeFromProperties]
    public int AccountID { get; set; }
    [ExcludeFromProperties]
    public bool IsForcedExposure { get; set; }
    [ExcludeFromProperties]
    public int SourceMediaID { get; set; }
    [ExcludeFromProperties]
    public int TestID { get; set; }
    [ExcludeFromProperties]
    public object TaskID { get; set; }
    [ExcludeFromProperties]
    public string SegmentKey { get; set; }
    [ExcludeFromProperties]
    public int Second { get; set; }
    public float Playback { get; set; }
    public object PlaybackNorm { get; set; }
    public float Attention { get; set; }
    public object AttentionNorm { get; set; }
    public float Distraction { get; set; }
    public object DistractionNorm { get; set; }
    public float AllReactions { get; set; }
    public object AllReactionsNorm { get; set; }
    public float Negativity { get; set; }
    public object NegativityNorm { get; set; }
    public float Happiness { get; set; }
    public object HappinessNorm { get; set; }
    public float Confusion { get; set; }
    public object ConfusionNorm { get; set; }
    public float Contempt { get; set; }
    public object ContemptNorm { get; set; }
    public float Disgust { get; set; }
    public object DisgustNorm { get; set; }
    public float Surprise { get; set; }
    public object SurpriseNorm { get; set; }
}