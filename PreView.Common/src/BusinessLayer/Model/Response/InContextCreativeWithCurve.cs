using BusinessLayer.Model.Interface;

namespace BusinessLayer.Model.Response;

public class InContextCreativeWithCurve : ICurve
{
    public DateTimeOffset? CreationDate { get; set; }
    public string TopCategory { get; set; }
    public string MidCategory { get; set; }
    public string SubCategory { get; set; }
    public string Brand { get; set; }
    public string SourceMedia { get; set; }
    public int? Duration { get; set; }
    public string Device { get; set; }
    public string GeographicRegion { get; set; }
    public string Country { get; set; }
    public string Audience { get; set; }
    public int? Views { get; set; }
    public int Second { get; set; }
    public string AdformatText { get; set; }
    public decimal? PlaybackIC { get; set; }
    public decimal? PlaybackICNorm { get; set; }
    public decimal? DistractionIC { get; set; }
    public decimal? DistractionICNorm { get; set; }
    public decimal? Attention { get; set; }
    public decimal? AttentionNorm { get; set; }
    public decimal? NeutralAttention { get; set; }
    public decimal? NeutralAttentionNorm { get; set; }
    public decimal? AllReactionsIC { get; set; }
    public decimal? AllReactionsICNorm { get; set; }
    public decimal? NegativityIC { get; set; }
    public decimal? NegativityICNorm { get; set; }
    public decimal? HappinessIC { get; set; }
    public decimal? HappinessICNorm { get; set; }
    public decimal? ContemptIC { get; set; }
    public decimal? ContemptICNorm { get; set; }
    public decimal? SurpriseIC { get; set; }
    public decimal? SurpriseICNorm { get; set; }
    public decimal? ConfusionIC { get; set; }
    public decimal? ConfusionICNorm { get; set; }
    public decimal? DisgustIC { get; set; }
    public decimal? DisgustICNorm { get; set; }
    public string AdformatTextIF { get; set; }
    public decimal? PlaybackIF { get; set; }
    public decimal? PlaybackIFNorm { get; set; }
    public decimal? DistractionIF { get; set; }
    public decimal? DistractionIFNorm { get; set; }
    public decimal? AttentionIF { get; set; }
    public decimal? AttentionIFNorm { get; set; }
    public decimal? NeutralAttentionIF { get; set; }
    public decimal? NeutralAttentionIFNorm { get; set; }
    public decimal? AllReactions { get; set; }
    public decimal? AllReactionsNorm { get; set; }
    public decimal? Negativity { get; set; }
    public decimal? NegativityNorm { get; set; }
    public decimal? Happiness { get; set; }
    public decimal? HappinessNorm { get; set; }
    public decimal? Contempt { get; set; }
    public decimal? ContemptNorm { get; set; }
    public decimal? Surprise { get; set; }
    public decimal? SurpriseNorm { get; set; }
    public decimal? Confusion { get; set; }
    public decimal? ConfusionNorm { get; set; }
    public decimal? Disgust { get; set; }
    public decimal? DisgustNorm { get; set; }
}