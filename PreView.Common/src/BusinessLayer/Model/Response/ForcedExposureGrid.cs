using BusinessLayer.CustomAttributes;

namespace BusinessLayer.Model.Response;

public class ForcedExposureGrid
{
    [ExcludeFromProperties]
    public string CreativeID { get; set; }
    [ExcludeFromProperties]
    public string AdSetExternalKey { get; set; }
    [ExcludeFromProperties]
    public string AdSet { get; set; }
    [ExcludeFromProperties]
    public string SourceMediaExternalKey { get; set; }
    [ExcludeFromProperties]
    public string Account { get; set; }
    [ExcludeFromProperties]
    public int AccountID { get; set; }
    [ExcludeFromProperties]
    public bool IsForcedExposure { get; set; }
    [ExcludeFromProperties]
    public int SourceMediaID { get; set; }
    [ExcludeFromProperties]
    public int TestID { get; set; }
    [ExcludeFromProperties]
    public object TaskID { get; set; }
    [ExcludeFromProperties]
    public string SegmentKey { get; set; }
    [ExcludeFromProperties]
    public int Views { get; set; }
    [ExcludeFromProperties]
    public string AdformatTextIF { get; set; }
    [ExcludeFromProperties]
    public int? NormFallback { get; set; }
    [ExcludeFromProperties]
    public string NormSegmentKey { get; set; }
    [ExcludeFromProperties]
    public int norm_sample_size { get; set; }
    [ExcludeFromProperties]
    public float QualityScore { get; set; }
    [ExcludeFromProperties]
    public int QualityScore_index { get; set; }
    public float AttentiveSeconds { get; set; }
    [ExcludeFromProperties]
    public int AttentiveSecondsRank { get; set; }
    public float AttentiveSecondsMedian { get; set; }
    [ExcludeFromProperties]
    public float AttentiveSecondsDiff { get; set; }
    public float AttentiveSecondsVTR { get; set; }
    [ExcludeFromProperties]
    public int AttentiveSecondsVTRRank { get; set; }
    public float AttentiveSecondsVTRMedian { get; set; }
    [ExcludeFromProperties]
    public float AttentiveSecondsVTRDiff { get; set; }
    public float VTR { get; set; }
    [ExcludeFromProperties]
    public int VTRRank { get; set; }
    public float VTRMedian { get; set; }
    [ExcludeFromProperties]
    public float VTRDiff { get; set; }
    public float PlaybackSeconds { get; set; }
    [ExcludeFromProperties]
    public int PlaybackSecondsRank { get; set; }
    public float PlaybackSecondsMedian { get; set; }
    [ExcludeFromProperties]
    public float PlaybackSecondsDiff { get; set; }
    public float AttentionAvg { get; set; }
    [ExcludeFromProperties]
    public int AttentionAvgRank { get; set; }
    public float AttentionAvgMedian { get; set; }
    [ExcludeFromProperties]
    public float AttentionAvgDiff { get; set; }
    public float Reactions { get; set; }
    [ExcludeFromProperties]
    public int ReactionsRank { get; set; }
    public float ReactionsMedian { get; set; }
    [ExcludeFromProperties]
    public float ReactionsDiff { get; set; }
    public float HappyPeak { get; set; }
    [ExcludeFromProperties]
    public int HappyPeakRank { get; set; }
    public float HappyPeakMedian { get; set; }
    [ExcludeFromProperties]
    public float HappyPeakDiff { get; set; }
    public float SurprisePeak { get; set; }
    [ExcludeFromProperties]
    public int SurprisePeakRank { get; set; }
    public float SurprisePeakMedian { get; set; }
    [ExcludeFromProperties]
    public float SurprisePeakDiff { get; set; }
    public float ConfusionPeak { get; set; }
    [ExcludeFromProperties]
    public int ConfusionPeakRank { get; set; }
    public float ConfusionPeakMedian { get; set; }
    [ExcludeFromProperties]
    public float ConfusionPeakDiff { get; set; }
    [ExcludeFromProperties]
    public float ContemptPeak { get; set; }
    public int ContemptPeakRank { get; set; }
    public float ContemptPeakMedian { get; set; }
    [ExcludeFromProperties]
    public float ContemptPeakDiff { get; set; }
    public float DisgustPeak { get; set; }
    [ExcludeFromProperties]
    public int DisgustPeakRank { get; set; }
    public float DisgustPeakMedian { get; set; }
    [ExcludeFromProperties]
    public float DisgustPeakDiff { get; set; }
    public float NeutralAttentionAvg { get; set; }
    [ExcludeFromProperties]
    public int NeutralAttentionAvgRank { get; set; }
    public float NeutralAttentionAvgMedian { get; set; }
    public float DistractionAvg { get; set; }
    [ExcludeFromProperties]
    public int DistractionAvgRank { get; set; }
    public float DistractionAvgMedian { get; set; }
    [ExcludeFromProperties]
    public float DistractionAvgDiff { get; set; }
    public float NegativityPeak { get; set; }
    [ExcludeFromProperties]
    public float NegativityPeakRank { get; set; }
    public float NegativityPeakMedian { get; set; }
    public float AttentionPeak { get; set; }
    [ExcludeFromProperties]
    public int AttentionPeakRank { get; set; }
    public float AttentionPeakMedian { get; set; }
    public float HappyAvg { get; set; }
    [ExcludeFromProperties]
    public int HappyAvgRank { get; set; }
    public float HappyAvgMedian { get; set; }
    public float SurpriseAvg { get; set; }
    [ExcludeFromProperties]
    public int SurpriseAvgRank { get; set; }
    public float SurpriseAvgMedian { get; set; }
    public float ConfusionAvg { get; set; }
    [ExcludeFromProperties]
    public int ConfusionAvgRank { get; set; }
    public float ConfusionAvgMedian { get; set; }
    public float ContemptAvg { get; set; }
    [ExcludeFromProperties]
    public int ContemptAvgRank { get; set; }
    public float ContemptAvgMedian { get; set; }
    public float DisgustAvg { get; set; }
    [ExcludeFromProperties]
    public int DisgustAvgRank { get; set; }
    public float DisgustAvgMedian { get; set; }
    public string Country { get; set; }
    [ExcludeFromProperties]
    public string Country_code { get; set; }
    public string GeographicRegion { get; set; }
    [ExcludeFromProperties]
    public string SourceMediaThumbnailFileName { get; set; }
    [ExcludeFromProperties]
    public string SourceMediaConvertedFileName { get; set; }
    [ExcludeFromProperties]
    public string ParentCreative { get; set; }
    public string SourceMedia { get; set; }
    public string SourceMediaType { get; set; }
    public int Duration { get; set; }
    public string CreationDate { get; set; }
    public string Brand { get; set; }
    [ExcludeFromProperties]
    public int BrandID { get; set; }
    [ExcludeFromProperties]
    public string BrandLogoFileName { get; set; }
    public string TopCategory { get; set; }
    public string MidCategory { get; set; }
    public string SubCategory { get; set; }
    [ExcludeFromProperties]
    public string Platform { get; set; }
    public string Device { get; set; }
    public string Adformat { get; set; }
    public string AdformatText { get; set; }
    [ExcludeFromProperties]
    public bool IsEnabledForClients { get; set; }
}