using BusinessLayer.Model;
using BusinessLayer.Model.Filter;
using BusinessLayer.Model.Response;

namespace BusinessLayer.Services;

public static class CurveFilterService
{
    public static List<InContextCurve> FilterCurves(
        IEnumerable<InContextCurve> curves, 
        IEnumerable<CurveFilterItem> curveFilters)
    {
        if (curves == null)
            return new List<InContextCurve>();

        if (curveFilters == null || !curveFilters.Any())
            return curves.ToList();

        var allowedCurveTypes = curveFilters.Select(cf => cf.Type).ToHashSet();
        
        return curves.Select(curve => FilterSingleCurve(curve, allowedCurveTypes)).ToList();
    }
    
    private static InContextCurve FilterSingleCurve(InContextCurve originalCurve, HashSet<CurveType> allowedCurveTypes)
    {
        var filteredCurve = new InContextCurve
        {
            SourceMediaID = originalCurve.SourceMediaID,
            TestID = originalCurve.TestID,
            TaskID = originalCurve.TaskID,
            Second = originalCurve.Second,
            SegmentKey = originalCurve.SegmentKey,
            
            Attention = allowedCurveTypes.Contains(CurveType.Attention) ? originalCurve.Attention : null,
            AttentionNorm = allowedCurveTypes.Contains(CurveType.Attention) ? originalCurve.AttentionNorm : null,
            AttentionIF = allowedCurveTypes.Contains(CurveType.Attention) ? originalCurve.AttentionIF : null,
            AttentionIFNorm = allowedCurveTypes.Contains(CurveType.Attention) ? originalCurve.AttentionIFNorm : null,
            
            AllReactions = allowedCurveTypes.Contains(CurveType.AllReactions) ? originalCurve.AllReactions : null,
            AllReactionsNorm = allowedCurveTypes.Contains(CurveType.AllReactions) ? originalCurve.AllReactionsNorm : null,
            AllReactionsIC = allowedCurveTypes.Contains(CurveType.AllReactions) ? originalCurve.AllReactionsIC : null,
            AllReactionsICNorm = allowedCurveTypes.Contains(CurveType.AllReactions) ? originalCurve.AllReactionsICNorm : null,
            
            NegativityIC = allowedCurveTypes.Contains(CurveType.Negativity) ? originalCurve.NegativityIC : null,
            NegativityICNorm = allowedCurveTypes.Contains(CurveType.Negativity) ? originalCurve.NegativityICNorm : null,
            Negativity = allowedCurveTypes.Contains(CurveType.Negativity) ? originalCurve.Negativity : null,
            NegativityNorm = allowedCurveTypes.Contains(CurveType.Negativity) ? originalCurve.NegativityNorm : null,
            
            Happiness = allowedCurveTypes.Contains(CurveType.Happiness) ? originalCurve.Happiness : null,
            HappinessNorm = allowedCurveTypes.Contains(CurveType.Happiness) ? originalCurve.HappinessNorm : null,
            HappinessIC = allowedCurveTypes.Contains(CurveType.Happiness) ? originalCurve.HappinessIC : null,
            HappinessICNorm = allowedCurveTypes.Contains(CurveType.Happiness) ? originalCurve.HappinessICNorm : null,
            
            Contempt = allowedCurveTypes.Contains(CurveType.Contempt) ? originalCurve.Contempt : null,
            ContemptNorm = allowedCurveTypes.Contains(CurveType.Contempt) ? originalCurve.ContemptNorm : null,
            ContemptIC = allowedCurveTypes.Contains(CurveType.Contempt) ? originalCurve.ContemptIC : null,
            ContemptICNorm = allowedCurveTypes.Contains(CurveType.Contempt) ? originalCurve.ContemptICNorm : null,
            
            Surprise = allowedCurveTypes.Contains(CurveType.Surprise) ? originalCurve.Surprise : null,
            SurpriseNorm = allowedCurveTypes.Contains(CurveType.Surprise) ? originalCurve.SurpriseNorm : null,
            SurpriseIC = allowedCurveTypes.Contains(CurveType.Surprise) ? originalCurve.SurpriseIC : null,
            SurpriseICNorm = allowedCurveTypes.Contains(CurveType.Surprise) ? originalCurve.SurpriseICNorm : null,
            
            Confusion = allowedCurveTypes.Contains(CurveType.Confusion) ? originalCurve.Confusion : null,
            ConfusionNorm = allowedCurveTypes.Contains(CurveType.Confusion) ? originalCurve.ConfusionNorm : null,
            ConfusionIC = allowedCurveTypes.Contains(CurveType.Confusion) ? originalCurve.ConfusionIC : null,
            ConfusionICNorm = allowedCurveTypes.Contains(CurveType.Confusion) ? originalCurve.ConfusionICNorm : null,
            
            Disgust = allowedCurveTypes.Contains(CurveType.Disgust) ? originalCurve.Disgust : null,
            DisgustNorm = allowedCurveTypes.Contains(CurveType.Disgust) ? originalCurve.DisgustNorm : null,
            DisgustIC = allowedCurveTypes.Contains(CurveType.Disgust) ? originalCurve.DisgustIC : null,
            DisgustICNorm = allowedCurveTypes.Contains(CurveType.Disgust) ? originalCurve.DisgustICNorm : null,
            
            PlaybackIC = allowedCurveTypes.Contains(CurveType.Playback) ? originalCurve.PlaybackIC : null,
            PlaybackICNorm = allowedCurveTypes.Contains(CurveType.Playback) ? originalCurve.PlaybackICNorm : null,
            PlaybackIF = allowedCurveTypes.Contains(CurveType.Playback) ? originalCurve.PlaybackIF : null,
            PlaybackIFNorm = allowedCurveTypes.Contains(CurveType.Playback) ? originalCurve.PlaybackIFNorm : null,
            
            DistractionIC = allowedCurveTypes.Contains(CurveType.Distraction) ? originalCurve.DistractionIC : null,
            DistractionICNorm = allowedCurveTypes.Contains(CurveType.Distraction) ? originalCurve.DistractionICNorm : null,
            DistractionIF = allowedCurveTypes.Contains(CurveType.Distraction) ? originalCurve.DistractionIF : null,
            DistractionIFNorm = allowedCurveTypes.Contains(CurveType.Distraction) ? originalCurve.DistractionIFNorm : null,
            
            NeutralAttention = allowedCurveTypes.Contains(CurveType.NeutralAttention) ? originalCurve.NeutralAttention : null,
            NeutralAttentionNorm = allowedCurveTypes.Contains(CurveType.NeutralAttention) ? originalCurve.NeutralAttentionNorm : null,
            NeutralAttentionIF = allowedCurveTypes.Contains(CurveType.NeutralAttention) ? originalCurve.NeutralAttentionIF : null,
            NeutralAttentionIFNorm = allowedCurveTypes.Contains(CurveType.NeutralAttention) ? originalCurve.NeutralAttentionIFNorm : null
        };

        return filteredCurve;
    }

    /// <summary>
    /// Filters ForcedExposureCurve objects based on the provided curve filter items.
    /// Only keeps the curve data for the specified curve types, setting other curve properties to null.
    /// </summary>
    /// <param name="curves">The list of ForcedExposureCurve objects to filter</param>
    /// <param name="curveFilters">The list of CurveFilterItem specifying which curve types to keep</param>
    /// <returns>A new list of ForcedExposureCurve objects with filtered curve data</returns>
    public static List<ForcedExposureCurve> FilterForcedExposureCurves(
        IEnumerable<ForcedExposureCurve> curves,
        IEnumerable<CurveFilterItem> curveFilters)
    {
        if (curves == null)
            return new List<ForcedExposureCurve>();

        if (curveFilters == null || !curveFilters.Any())
            return curves.ToList();

        var allowedCurveTypes = curveFilters.Select(cf => cf.Type).ToHashSet();

        return curves.Select(curve => FilterSingleForcedExposureCurve(curve, allowedCurveTypes)).ToList();
    }

    /// <summary>
    /// Filters a single ForcedExposureCurve object based on allowed curve types.
    /// </summary>
    /// <param name="originalCurve">The original curve to filter</param>
    /// <param name="allowedCurveTypes">Set of allowed curve types</param>
    /// <returns>A new ForcedExposureCurve with only the allowed curve data</returns>
    private static ForcedExposureCurve FilterSingleForcedExposureCurve(ForcedExposureCurve originalCurve, HashSet<CurveType> allowedCurveTypes)
    {
        var filteredCurve = new ForcedExposureCurve
        {
            // Always keep the identifying properties
            SourceMediaID = originalCurve.SourceMediaID,
            TestID = originalCurve.TestID,
            Second = originalCurve.Second,
            SegmentKey = originalCurve.SegmentKey,

            // Filter curve data based on allowed types
            Attention = allowedCurveTypes.Contains(CurveType.Attention) ? originalCurve.Attention : null,
            AttentionNorm = allowedCurveTypes.Contains(CurveType.Attention) ? originalCurve.AttentionNorm : null,

            AllReactions = allowedCurveTypes.Contains(CurveType.AllReactions) ? originalCurve.AllReactions : null,
            AllReactionsNorm = allowedCurveTypes.Contains(CurveType.AllReactions) ? originalCurve.AllReactionsNorm : null,

            Negativity = allowedCurveTypes.Contains(CurveType.Negativity) ? originalCurve.Negativity : null,
            NegativityNorm = allowedCurveTypes.Contains(CurveType.Negativity) ? originalCurve.NegativityNorm : null,

            Happiness = allowedCurveTypes.Contains(CurveType.Happiness) ? originalCurve.Happiness : null,
            HappinessNorm = allowedCurveTypes.Contains(CurveType.Happiness) ? originalCurve.HappinessNorm : null,

            Contempt = allowedCurveTypes.Contains(CurveType.Contempt) ? originalCurve.Contempt : null,
            ContemptNorm = allowedCurveTypes.Contains(CurveType.Contempt) ? originalCurve.ContemptNorm : null,

            Surprise = allowedCurveTypes.Contains(CurveType.Surprise) ? originalCurve.Surprise : null,
            SurpriseNorm = allowedCurveTypes.Contains(CurveType.Surprise) ? originalCurve.SurpriseNorm : null,

            Confusion = allowedCurveTypes.Contains(CurveType.Confusion) ? originalCurve.Confusion : null,
            ConfusionNorm = allowedCurveTypes.Contains(CurveType.Confusion) ? originalCurve.ConfusionNorm : null,

            Disgust = allowedCurveTypes.Contains(CurveType.Disgust) ? originalCurve.Disgust : null,
            DisgustNorm = allowedCurveTypes.Contains(CurveType.Disgust) ? originalCurve.DisgustNorm : null,

            Playback = allowedCurveTypes.Contains(CurveType.Playback) ? originalCurve.Playback : null,
            PlaybackNorm = allowedCurveTypes.Contains(CurveType.Playback) ? originalCurve.PlaybackNorm : null,

            Distraction = allowedCurveTypes.Contains(CurveType.Distraction) ? originalCurve.Distraction : null,
            DistractionNorm = allowedCurveTypes.Contains(CurveType.Distraction) ? originalCurve.DistractionNorm : null,

            NeutralAttention = allowedCurveTypes.Contains(CurveType.NeutralAttention) ? originalCurve.NeutralAttention : null,
            NeutralAttentionNorm = allowedCurveTypes.Contains(CurveType.NeutralAttention) ? originalCurve.NeutralAttentionNorm : null
        };

        return filteredCurve;
    }
}
