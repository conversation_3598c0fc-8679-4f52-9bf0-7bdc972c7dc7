using BusinessLayer.Model.Filter;
using BusinessLayer.Model.Request;

namespace BusinessLayer.Mapper;

public static class ExportQueryMapper
{
    public static ExportQuery ToExportQuery(this GetExportDataRequest source)
    {
        ArgumentNullException.ThrowIfNull(source);

        return new ExportQuery
        {
            Filters = source.Filters.Select(item => item.ToDto()).ToList(),
            Sorting = source.Sorting.Select(item => item.ToDto()).ToList(),
            VisibleColumnsOrder = source.VisibleColumnsOrder
        };
    }
}