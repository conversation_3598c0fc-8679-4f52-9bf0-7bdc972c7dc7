using BusinessLayer.Model.Filter;

namespace BusinessLayer.Mapper;

public static class FilterItemMapper
{
    public static GridFilterItem ToDto(this GridFilterItem source)
    {
        ArgumentNullException.ThrowIfNull(source);

        return new GridFilterItem
        {
            ColumnField = source.ColumnField,
            Value = source.Value,
            OperatorValue = source.OperatorValue,
            OrQueryGroupId = source.OrQueryGroupId
        };
    }
}