using BusinessLayer.Model.Filter;

namespace BusinessLayer.Mapper;

public static class SortingItemMapper
{
    public static GridSortingItem ToDto(this GridSortingItem source)
    {
        ArgumentNullException.ThrowIfNull(source);

        return new GridSortingItem
        {
            Field = source.Field,
            Direction = source.Direction,
            FirstOrderText = source.FirstOrderText
        };
    }
}