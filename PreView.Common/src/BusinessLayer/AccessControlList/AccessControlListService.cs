using BusinessLayer.Repository;

namespace BusinessLayer.AccessControlList;

public class AccessControlListService : IAccessControlListService
{
    private readonly IAdSetToAccountRepository _adSetToAccountRepository;

    public AccessControlListService(IAdSetToAccountRepository adSetToAccountRepository)
    {
        _adSetToAccountRepository = adSetToAccountRepository;
    }

    public async Task<IEnumerable<int>> GetAccountIdsForAdSetAsync(string adSetKey)
    {
        var accountIds = await _adSetToAccountRepository.GetAccountIdsByAdSetExternalKey(adSetKey).ConfigureAwait(false);
        return accountIds;
    }

    public async Task<IEnumerable<string>> GetAdSetKeysByAccountsAsync(IEnumerable<int> accountIds)
    {
        var externalKeys = await _adSetToAccountRepository.GetAdSetExternalKeysByAccounts(accountIds).ConfigureAwait(false);
        return externalKeys;
    }
}
