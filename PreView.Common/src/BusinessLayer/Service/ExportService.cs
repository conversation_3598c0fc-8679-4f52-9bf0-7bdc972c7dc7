using BusinessLayer.Model.Request;
using BusinessLayer.Model.Response;
using BusinessLayer.Repository;

namespace BusinessLayer.Service;

public class ExportService : IExportService
{
    private readonly IExportRepository _exportRepository;

    public ExportService(IExportRepository exportRepository)
    {
        _exportRepository = exportRepository;
    }

    public async Task<ExportResult> StartExport(StartExportRequest request)
    {
        await _exportRepository.StartExport(request);
        
        return await UpdateExportStatus(new UpdateExportStatusRequest
        {
            ExportFolderPath = request.ExportFolderPath,
            NewStatus = "Pending",
        });
    }

    private async Task<ExportResult> UpdateExportStatus(UpdateExportStatusRequest request)
    {
        return await _exportRepository.ChangeExportResultStatus(request);
    }

    public async Task<ExportResult> GetExportResult(GetExportResultRequest request)
    {
        return await _exportRepository.GetExportResult(request);
    }
}