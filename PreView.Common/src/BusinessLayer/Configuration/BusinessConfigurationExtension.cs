using BusinessLayer.AccessControlList;
using BusinessLayer.Collector;
using BusinessLayer.Collector.Report;
using BusinessLayer.Service;
using Microsoft.Extensions.DependencyInjection;

namespace BusinessLayer.Configuration;

public static class BusinessConfigurationExtension
{
    public static IServiceCollection AddBusinessConfiguration(this IServiceCollection services)
    {
        services.AddScoped<IForcedExposureCollector, ForcedExposureCollector>();
        services.AddScoped<IInContextCollector, InContextCollector>();
        services.AddScoped<IExportDataCollector, ForcedExposureExportDataCollector>();
        services.AddScoped<IExportDataCollector, InContextExportDataCollector>();
        services.AddScoped<IForcedExposureMetricsCollector, ForcedExposureMetricsCollector>();
        services.AddScoped<IExportService, ExportService>();
        services.AddScoped<IAccessControlListService, AccessControlListService>();

        return services;
    }
}