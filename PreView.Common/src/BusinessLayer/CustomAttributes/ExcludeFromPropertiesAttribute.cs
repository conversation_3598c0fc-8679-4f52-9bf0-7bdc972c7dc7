using System.Reflection;

namespace BusinessLayer.CustomAttributes;

public class ExcludeFromPropertiesAttribute : Attribute
{
    public static IEnumerable<PropertyInfo> GetPublicPropertiesWithoutAttribute<T>()
    {
        return typeof(T)
            .GetProperties(BindingFlags.Instance | BindingFlags.Public)
            .Where(p => p.GetCustomAttribute<ExcludeFromPropertiesAttribute>() == null);
    }
}
