using System.Reflection;

namespace BusinessLayer.CustomAttributes;

public static class PropertyInfoExtensions
{
    public static string GetName(this PropertyInfo propertyInfo)
    {
        var attribute = propertyInfo.GetCustomAttribute<PropertyNameReplacementAttribute>();
        return attribute == null || string.IsNullOrWhiteSpace(attribute.NameReplacementValue)
               ? propertyInfo.Name
               : attribute.NameReplacementValue;
    }
}
