using BusinessLayer.Constant;
using BusinessLayer.Extensions;
using BusinessLayer.Mapper;
using BusinessLayer.Model;
using BusinessLayer.Model.Filter;
using BusinessLayer.Model.Request;
using BusinessLayer.Model.Response;
using BusinessLayer.Repository;
using BusinessLayer.Services;

namespace BusinessLayer.Collector;

public class ForcedExposureExportDataCollector : IExportDataCollector
{
    private readonly IForcedExposureCollector _forcedExposureCollector;
    private readonly ISegmentRepository _segmentRepository;

    public ForcedExposureExportDataCollector(
        IForcedExposureCollector forcedExposureCollector,
        ISegmentRepository segmentRepository)
    {
        _forcedExposureCollector = forcedExposureCollector;
        _segmentRepository = segmentRepository;
    }

    public bool CanHandleProductType(ProductType type)
    {
        return type == ProductType.NewForcedExposure;
    }

    public async Task<ExportData> GetExportData(GetExportDataRequest request)
    {
        ArgumentNullException.ThrowIfNull(request);

        var creatives = await _forcedExposureCollector.GetCreativesAsync(new GetCreativesRequest
        {
            AccountId = request.AccountId,
            Media = request.Media,
            IsAdmin = request.IsAdmin,
            SegmentKeys = request.SegmentKeys,
            MinViewThresholdForScores = request.MinViewThresholdForScores
        });

        ApplyDefaultSorting(request);

        var dataModifier = request.ToExportQuery();
        var creativesToExport = FilterAndSortCreatives(creatives, dataModifier);

        var filteredMedia = GetFilteredMedia(creativesToExport);

        var mediaSegmentsTask = GetMediaSegments(request);

        var curvesTask = _forcedExposureCollector.GetCurvesAsync(new GetCurvesRequest { Media = filteredMedia, SegmentKeys = request.SegmentKeys });

        await Task.WhenAll(mediaSegmentsTask, curvesTask).ConfigureAwait(false);

        var mediaSegments = await mediaSegmentsTask.ConfigureAwait(false);
        var curves = await curvesTask.ConfigureAwait(false);

        var filteredCurves = ApplyCurveFiltering(curves, request.CurveFilter);

        UpdateCreativeAudiences(creativesToExport, mediaSegments);

        creativesToExport = FilterByValidAudience(creativesToExport);

        var curvesToExport = CombineCreativesAndCurves(creativesToExport, filteredCurves);

        AddAudienceToVisibleColumns(request);

        return BuildExportData(request.VisibleColumnsOrder, creativesToExport, curvesToExport, filteredMedia.Count);
    }

    private void ApplyDefaultSorting(GetExportDataRequest request)
    {
        request.Sorting.Add(new GridSortingItem
        {
            Field = "segmentKey",
            Direction = "asc",
            FirstOrderText = SegmentKeyConstant.AllSegmentKey
        });
    }

    private IEnumerable<ForcedExposureCreative> FilterAndSortCreatives(
        IEnumerable<ForcedExposureCreative> creatives,
        ExportQuery dataModifier)
    {
        return creatives.ApplyFilter(dataModifier).Sort(dataModifier);
    }

    private List<MediaModelRequest> GetFilteredMedia(IEnumerable<ForcedExposureCreative> creativesToExport)
    {
        return creativesToExport
            .GroupBy(s => new { s.SourceMediaID.Value, s.TestID, s.OrderAdSetID })
            .Select(cp => cp.First())
            .Select(creative => new MediaModelRequest
            {
                SourceMediaID = creative.SourceMediaID.Value,
                TestID = creative.TestID,
                OrderAdSetID = creative.OrderAdSetID ?? default
            })
            .ToList();
    }

    private Task<List<SegmentKeyLabel>> GetMediaSegments(GetExportDataRequest request)
    {
        return _segmentRepository.GetSegmentKeys(new GetSegmentKeysRequest
        {
            AccountId = request.AccountId,
            ProductType = ProductType.NewForcedExposure,
            SegmentKeys = request.SegmentKeys
        });
    }

    private void UpdateCreativeAudiences(
        IEnumerable<ForcedExposureCreative> creativesToExport,
        IEnumerable<SegmentKeyLabel> mediaSegments)
    {
        var segmentsInfoDict = mediaSegments.ToDictionary(
            l => l.SegmentKey,
            l => new { l.Answer, l.Question });

        foreach (var creative in creativesToExport)
        {
            var segmentInfo = segmentsInfoDict.GetValueOrDefault(creative.SegmentKey);
            creative.Audience = creative.SegmentKey == SegmentKeyConstant.AllSegmentKey
                ? SegmentKeyConstant.AllSegmentLabel
                : segmentInfo != null && !string.IsNullOrEmpty(segmentInfo.Question) &&
                  !string.IsNullOrEmpty(segmentInfo.Answer)
                    ? $"{segmentInfo.Question} ({segmentInfo.Answer})"
                    : null;
        }
    }

    private IEnumerable<ForcedExposureCreative> FilterByValidAudience(
        IEnumerable<ForcedExposureCreative> creativesToExport)
    {
        return creativesToExport.Where(c => c.Audience != null);
    }

    private IEnumerable<ForcedExposureCreativeWithCurve> CombineCreativesAndCurves(
        IEnumerable<ForcedExposureCreative> creatives,
        IEnumerable<ForcedExposureCurve> curves)
    {
        return creatives.Join(curves,
            ad => (ad.SourceMediaID, ad.TestID, ad.SegmentKey),
            curve => (curve.SourceMediaID, curve.TestID, curve.SegmentKey),
            (creative, curve) => new ForcedExposureCreativeWithCurve
            {
                CreationDate = creative.CreationDate,
                TopCategory = creative.TopCategory,
                MidCategory = creative.MidCategory,
                SubCategory = creative.SubCategory,
                Brand = creative.Brand,
                SourceMedia = creative.SourceMedia,
                Duration = creative.Duration,
                Device = creative.Device,
                GeographicRegion = creative.GeographicRegion,
                Country = creative.Country,
                Audience = creative.Audience,
                Views = creative.Views,
                Second = curve.Second + 1,
                AdformatTextIF = creative.AdformatTextIF,
                Playback = (decimal?)curve.Playback,
                PlaybackNorm = (decimal?)curve.PlaybackNorm,
                Attention = (decimal?)curve.Attention,
                AttentionNorm = (decimal?)curve.AttentionNorm,
                Distraction = (decimal?)curve.Distraction,
                DistractionNorm = (decimal?)curve.DistractionNorm,
                AllReactions = (decimal?)curve.AllReactions,
                AllReactionsNorm = (decimal?)curve.AllReactionsNorm,
                Negativity = (decimal?)curve.Negativity,
                NegativityNorm = (decimal?)curve.NegativityNorm,
                Happiness = (decimal?)curve.Happiness,
                HappinessNorm = (decimal?)curve.HappinessNorm,
                Contempt = (decimal?)curve.Contempt,
                ContemptNorm = (decimal?)curve.ContemptNorm,
                Surprise = (decimal?)curve.Surprise,
                SurpriseNorm = (decimal?)curve.SurpriseNorm,
                Confusion = (decimal?)curve.Confusion,
                ConfusionNorm = (decimal?)curve.ConfusionNorm,
                Disgust = (decimal?)curve.Disgust,
                DisgustNorm = (decimal?)curve.DisgustNorm,
                NeutralAttention = (decimal?)curve.NeutralAttention,
                NeutralAttentionNorm = (decimal?)curve.NeutralAttentionNorm
            });
    }

    private void AddAudienceToVisibleColumns(GetExportDataRequest request)
    {
        request.VisibleColumnsOrder.Insert(
            request.VisibleColumnsOrder.FindIndex(c => c == "views"),
            "audience");
    }

    private ExportData BuildExportData(
        List<String> visibleColumnsOrder,
        IEnumerable<ForcedExposureCreative> creativesToExport,
        IEnumerable<ForcedExposureCreativeWithCurve> curvesToExport,
        int mediaCount)
    {
        return new ExportData
        {
            CreativesPayload = creativesToExport,
            CurvesPayload = curvesToExport,
            MediaCount = mediaCount,
            VisibleColumnsOrder = visibleColumnsOrder
        };
    }
    
    private static IEnumerable<ForcedExposureCurve> ApplyCurveFiltering(
        IEnumerable<ForcedExposureCurve> curves,
        List<CurveFilterItem> curveFilters)
    {
        if (curveFilters == null || !curveFilters.Any())
        {
            return curves;
        }

        return CurveFilterService.FilterForcedExposureCurves(curves, curveFilters);
    }
}