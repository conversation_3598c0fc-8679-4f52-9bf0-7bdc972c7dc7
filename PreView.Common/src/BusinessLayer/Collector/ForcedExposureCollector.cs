using BusinessLayer.Model.Request;
using BusinessLayer.Model.Response;
using BusinessLayer.Repository;

namespace BusinessLayer.Collector;

public class ForcedExposureCollector : IForcedExposureCollector
{
    private readonly IForcedExposureRepository _forcedExposureRepository;

    public ForcedExposureCollector(IForcedExposureRepository forcedExposureRepository)
    {
        _forcedExposureRepository = forcedExposureRepository;
    }

    public async Task<IEnumerable<ForcedExposureCreative>> GetCreativesAsync(GetCreativesRequest request)
    {
        ArgumentNullException.ThrowIfNull(request);

        var creatives = await _forcedExposureRepository.GetCreatives(request);

        return creatives
            .Where(c => request.IsAdmin || c.IsEnabledForClients == true)
            .GroupBy(cp => new { cp.SourceMediaID, cp.TestID, cp.OrderAdSetID, cp.SegmentKey })
            .Select(cp => cp.FirstOrDefault())
            .Select(item => ApplyThreshold(request, item));
    }

    public async Task<IEnumerable<ForcedExposureCurve>> GetCurvesAsync(GetCurvesRequest request)
    {
        return await _forcedExposureRepository.GetCurves(request);
    }

    private static ForcedExposureCreative ApplyThreshold(GetCreativesRequest request, ForcedExposureCreative creative)
    {
        if (creative.Views is null || creative.Views < request.MinViewThresholdForScores)
        {
            creative.QualityScore = null;
            creative.AttentionAvg = null;
            creative.AttentionAvgRank = null;
            creative.AttentionAvgMedian = null;
            creative.AttentionAvgDiff = null;
            creative.AttentiveSeconds = null;
            creative.AttentiveSecondsRank = null;
            creative.AttentiveSecondsMedian = null;
            creative.AttentiveSecondsDiff = null;
            creative.AttentiveSecondsVTR = null;
            creative.AttentiveSecondsVTRRank = null;
            creative.AttentiveSecondsVTRMedian = null;
            creative.AttentiveSecondsVTRDiff = null;
            creative.Reactions = null;
            creative.ReactionsMedian = null;
            creative.ReactionsRank = null;
            creative.ReactionsDiff = null;
            creative.NegativityAvg = null;
            creative.NegativityAvgMedian = null;
            creative.NegativityAvgRank = null;
            creative.NegativityAvgDiff = null;
            creative.HappyPeak = null;
            creative.HappyPeakRank = null;
            creative.HappyPeakMedian = null;
            creative.HappyPeakDiff = null;
            creative.VTR = null;
            creative.VTRRank = null;
            creative.VTRMedian = null;
            creative.VTRDiff = null;
            creative.PlaybackSeconds = null;
            creative.PlaybackSecondsRank = null;
            creative.PlaybackSecondsMedian = null;
            creative.PlaybackSecondsDiff = null;
            creative.SurprisePeak = null;
            creative.SurprisePeakRank = null;
            creative.SurprisePeakMedian = null;
            creative.SurprisePeakDiff = null;
            creative.ConfusionPeak = null;
            creative.ConfusionPeakRank = null;
            creative.ConfusionPeakMedian = null;
            creative.ConfusionPeakDiff = null;
            creative.ContemptPeak = null;
            creative.ContemptPeakRank = null;
            creative.ContemptPeakMedian = null;
            creative.ContemptPeakDiff = null;
            creative.DisgustPeak = null;
            creative.DisgustPeakRank = null;
            creative.DisgustPeakMedian = null;
            creative.DisgustPeakDiff = null;
            creative.DistractionAvg = null;
            creative.DistractionAvgRank = null;
            creative.DistractionAvgMedian = null;
            creative.DistractionAvgDiff = null;
        }

        return creative;
    }
}