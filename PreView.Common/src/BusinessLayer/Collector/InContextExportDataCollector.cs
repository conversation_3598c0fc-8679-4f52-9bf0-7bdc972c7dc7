using BusinessLayer.Constant;
using BusinessLayer.Extensions;
using BusinessLayer.Mapper;
using BusinessLayer.Model;
using BusinessLayer.Model.Filter;
using BusinessLayer.Model.Request;
using BusinessLayer.Model.Response;
using BusinessLayer.Repository;
using BusinessLayer.Services;

namespace BusinessLayer.Collector;

public class InContextExportDataCollector : IExportDataCollector
{
    private readonly IInContextCollector _inContextCollector;
    private readonly ISegmentRepository _segmentRepository;

    public InContextExportDataCollector(IInContextCollector inContextCollector,
        ISegmentRepository segmentRepository)
    {
        _inContextCollector = inContextCollector;
        _segmentRepository = segmentRepository;
    }

    public bool CanHandleProductType(ProductType type)
    {
        return type == ProductType.InContext;
    }

    public async Task<ExportData> GetExportData(GetExportDataRequest request)
    {
        ArgumentNullException.ThrowIfNull(request);

        var creatives = await _inContextCollector.GetCreativesAsync(new GetCreativesRequest
        {
            AccountId = request.AccountId,
            Media = request.Media,
            IsAdmin = request.IsAdmin,
            SegmentKeys = request.SegmentKeys,
            MinViewThresholdForScores = request.MinViewThresholdForScores
        });

        request.Sorting.Add(new GridSortingItem
            { Field = "segmentKey", Direction = "asc", FirstOrderText = SegmentKeyConstant.AllSegmentKey });

        var dataModifier = request.ToExportQuery();
        var creativesToExport = creatives.ApplyFilter(dataModifier).Sort(dataModifier);
        var filteredMedia = ExtractFilteredMedia(creativesToExport);

        var mediaSegmentsTask = GetMediaSegmentsAsync(request.AccountId, request.SegmentKeys);
        var curvesTask = GetCurvesAsync(new GetCurvesRequest { Media = filteredMedia, SegmentKeys = request.SegmentKeys});

        await Task.WhenAll(mediaSegmentsTask, curvesTask).ConfigureAwait(false);

        var mediaSegments = await mediaSegmentsTask.ConfigureAwait(false);
        var curves = await curvesTask.ConfigureAwait(false);

        var filteredCurves = ApplyCurveFiltering(curves, request.CurveFilter);

        ApplyAudienceToCreatives(creativesToExport, mediaSegments);

        creativesToExport = creativesToExport.Where(c => c.Audience != null);

        var curvesToExport = MergeCreativesWithCurves(creativesToExport, filteredCurves);

        InsertAudienceColumn(request);

        return new ExportData
        {
            CreativesPayload = creativesToExport,
            CurvesPayload = curvesToExport,
            MediaCount = filteredMedia.Count(),
            VisibleColumnsOrder = request.VisibleColumnsOrder
        };
    }

    private static IEnumerable<MediaModelRequest> ExtractFilteredMedia(IEnumerable<InContextCreative> creatives)
    {
        return creatives
            .GroupBy(s => new { s.SourceMediaID.Value, s.TestID, s.TaskID, s.OrderAdSetID })
            .Select(cp => cp.FirstOrDefault())
            .Select(s => new MediaModelRequest
            {
                SourceMediaID = s.SourceMediaID.Value,
                TestID = s.TestID,
                TaskID = s.TaskID,
                OrderAdSetID = s.OrderAdSetID ?? default
            })
            .ToList();
    }

    private Task<List<SegmentKeyLabel>> GetMediaSegmentsAsync(int accountId, IEnumerable<string> segmentKeys)
    {
        return _segmentRepository.GetSegmentKeys(new GetSegmentKeysRequest
        {
            AccountId = accountId,
            ProductType = ProductType.InContext,
            SegmentKeys = segmentKeys
        });
    }

    public async Task<IEnumerable<InContextCurve>> GetCurvesAsync(GetCurvesRequest request)
    {
        return await _inContextCollector.GetCurvesAsync(request);
    }

    private static void ApplyAudienceToCreatives(IEnumerable<InContextCreative> creatives,
        IEnumerable<SegmentKeyLabel> mediaSegments)
    {
        var segmentsInfoDict = mediaSegments.ToDictionary(l => l.SegmentKey, l => new { l.Answer, l.Question });

        foreach (var creative in creatives)
        {
            var segmentInfo = segmentsInfoDict.GetValueOrDefault(creative.SegmentKey);

            creative.Audience = creative.SegmentKey == SegmentKeyConstant.AllSegmentKey
                ? SegmentKeyConstant.AllSegmentLabel
                : segmentInfo != null && !string.IsNullOrEmpty(segmentInfo.Question) &&
                  !string.IsNullOrEmpty(segmentInfo.Answer)
                    ? $"{segmentInfo.Question} ({segmentInfo.Answer})"
                    : null;
        }
    }

    private static IEnumerable<InContextCreativeWithCurve> MergeCreativesWithCurves(
        IEnumerable<InContextCreative> creatives, IEnumerable<InContextCurve> curves)
    {
        return creatives.Join(curves,
            ad => (ad.SourceMediaID, ad.TaskID, ad.TestID, ad.SegmentKey),
            adCurves => (adCurves.SourceMediaID, adCurves.TaskID, adCurves.TestID, adCurves.SegmentKey),
            (ad, adCurves) => new InContextCreativeWithCurve
            {
                CreationDate = ad.CreationDate,
                TopCategory = ad.TopCategory,
                MidCategory = ad.MidCategory,
                SubCategory = ad.SubCategory,
                Brand = ad.Brand,
                SourceMedia = ad.SourceMedia,
                Duration = ad.Duration,
                Device = ad.Device,
                GeographicRegion = ad.GeographicRegion,
                Country = ad.Country,
                Audience = ad.Audience,
                Views = ad.Views,
                Second = adCurves.Second + 1,
                AdformatText = ad.AdformatText,
                PlaybackIC = (decimal?)adCurves.PlaybackIC,
                PlaybackICNorm = (decimal?)adCurves.PlaybackICNorm,
                Attention = (decimal?)adCurves.Attention,
                AttentionNorm = (decimal?)adCurves.AttentionNorm,
                DistractionIC = (decimal?)adCurves.DistractionIC,
                DistractionICNorm = (decimal?)adCurves.DistractionICNorm,
                DistractionIF = (decimal?)adCurves.DistractionIF,
                DistractionIFNorm = (decimal?)adCurves.DistractionIFNorm,
                AllReactionsIC = (decimal?)adCurves.AllReactionsIC,
                AllReactionsICNorm = (decimal?)adCurves.AllReactionsICNorm,
                NegativityIC = (decimal?)adCurves.NegativityIC,
                NegativityICNorm = (decimal?)adCurves.NegativityICNorm,
                HappinessIC = (decimal?)adCurves.HappinessIC,
                HappinessICNorm = (decimal?)adCurves.HappinessICNorm,
                ContemptIC = (decimal?)adCurves.ContemptIC,
                ContemptICNorm = (decimal?)adCurves.ContemptICNorm,
                SurpriseIC = (decimal?)adCurves.SurpriseIC,
                SurpriseICNorm = (decimal?)adCurves.SurpriseICNorm,
                ConfusionIC = (decimal?)adCurves.ConfusionIC,
                ConfusionICNorm = (decimal?)adCurves.ConfusionICNorm,
                DisgustIC = (decimal?)adCurves.DisgustIC,
                DisgustICNorm = (decimal?)adCurves.DisgustICNorm,
                AdformatTextIF = ad.AdformatTextIF,
                PlaybackIF = (decimal?)adCurves.PlaybackIF,
                PlaybackIFNorm = (decimal?)adCurves.PlaybackIFNorm,
                AttentionIF = (decimal?)adCurves.AttentionIF,
                AttentionIFNorm = (decimal?)adCurves.AttentionIFNorm,
                AllReactions = (decimal?)adCurves.AllReactions,
                AllReactionsNorm = (decimal?)adCurves.AllReactionsNorm,
                Negativity = (decimal?)adCurves.Negativity,
                NegativityNorm = (decimal?)adCurves.NegativityNorm,
                Happiness = (decimal?)adCurves.Happiness,
                HappinessNorm = (decimal?)adCurves.HappinessNorm,
                Contempt = (decimal?)adCurves.Contempt,
                ContemptNorm = (decimal?)adCurves.ContemptNorm,
                Surprise = (decimal?)adCurves.Surprise,
                SurpriseNorm = (decimal?)adCurves.SurpriseNorm,
                Confusion = (decimal?)adCurves.Confusion,
                ConfusionNorm = (decimal?)adCurves.ConfusionNorm,
                Disgust = (decimal?)adCurves.Disgust,
                DisgustNorm = (decimal?)adCurves.DisgustNorm,
                NeutralAttention = (decimal?)adCurves.NeutralAttention,
                NeutralAttentionNorm = (decimal?)adCurves.NeutralAttentionNorm,
                NeutralAttentionIF = (decimal?)adCurves.NeutralAttentionIF,
                NeutralAttentionIFNorm = (decimal?)adCurves.NeutralAttentionIFNorm
            });
    }

    private static void InsertAudienceColumn(GetExportDataRequest request)
    {
        var viewsIndex = request.VisibleColumnsOrder.FindIndex(c => c == "views");
        request.VisibleColumnsOrder.Insert(viewsIndex, "audience");
    }
    
    private static IEnumerable<InContextCurve> ApplyCurveFiltering(
        IEnumerable<InContextCurve> curves,
        List<CurveFilterItem> curveFilters)
    {
        if (curveFilters == null || !curveFilters.Any())
        {
            return curves;
        }

        return CurveFilterService.FilterCurves(curves, curveFilters);
    }
}