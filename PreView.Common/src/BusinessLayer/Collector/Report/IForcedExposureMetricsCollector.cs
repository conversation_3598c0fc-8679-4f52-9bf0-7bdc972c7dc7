using BusinessLayer.Model.Request;
using BusinessLayer.Model.Request.Report;
using BusinessLayer.Model.Response;

namespace BusinessLayer.Collector.Report;

public interface IForcedExposureMetricsCollector
{
    // Response Model from curves.preview_curves_forced_exposure_with_adset
    Task<IEnumerable<ForcedExposureCurves>> GetCurvesData(GetForcedExposureCurveRequest request);

    // Response Model from app.creative_performance_portfolio_forced_exposure_grid_with_adset
    Task<IEnumerable<ForcedExposureGrid>> GetGridData(GetForcedExposureGridRequest request);
}