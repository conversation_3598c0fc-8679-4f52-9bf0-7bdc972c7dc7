using BusinessLayer.Model.Request;
using BusinessLayer.Model.Request.Report;
using BusinessLayer.Model.Response;
using BusinessLayer.Repository;

namespace BusinessLayer.Collector.Report;

public class ForcedExposureMetricsCollector : IForcedExposureMetricsCollector
{
    private readonly IForcedExposureMetricsRepository _forcedExposureMetricsRepository;

    public ForcedExposureMetricsCollector(IForcedExposureMetricsRepository forcedExposureMetricsRepository)
    {
        _forcedExposureMetricsRepository = forcedExposureMetricsRepository;
    }

    public Task<IEnumerable<ForcedExposureCurves>> GetCurvesData(GetForcedExposureCurveRequest request)
    {
        return _forcedExposureMetricsRepository.GetCurvesData(request);
    }

    public Task<IEnumerable<ForcedExposureGrid>> GetGridData(GetForcedExposureGridRequest request)
    {
        return _forcedExposureMetricsRepository.GetGridData(request);
    }
}