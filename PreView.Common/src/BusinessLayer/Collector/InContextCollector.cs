using BusinessLayer.Model.Request;
using BusinessLayer.Model.Response;
using BusinessLayer.Repository;

namespace BusinessLayer.Collector;

public class InContextCollector : IInContextCollector
{
    private readonly IInContextRepository _inContextRepository;

    public InContextCollector(IInContextRepository inContextRepository)
    {
        _inContextRepository = inContextRepository;
    }

    public async Task<IEnumerable<InContextCreative>> GetCreativesAsync(GetCreativesRequest request)
    {
        ArgumentNullException.ThrowIfNull(request);

        var creatives = await _inContextRepository.GetCreatives(request);
        return creatives
            .Where(c => request.IsAdmin || c.IsEnabledForClients is true)
            .GroupBy(cp => new { cp.SourceMediaID, cp.TestID, cp.TaskID, cp.OrderAdSetID, cp.SegmentKey })
            .Select(cp => cp.FirstOrDefault())
            .Select(item => ApplyThreshold(request, item));
    }

    public async Task<IEnumerable<InContextCurve>> GetCurvesAsync(GetCurvesRequest request)
    {
        return await _inContextRepository.GetCurves(request);
    }

    private static InContextCreative ApplyThreshold(GetCreativesRequest request, InContextCreative creative)
    {
        if (creative.Views is not null && creative.Views >= request.MinViewThresholdForScores) return creative;

        creative.QualityScore = null;
        creative.AttentiveSeconds = null;
        creative.AttentiveSecondsRank = null;
        creative.AttentiveSecondsMedian = null;
        creative.AttentiveSecondsDiff = null;
        creative.AttentiveSecondsIF = null;
        creative.AttentiveSecondsIFRank = null;
        creative.AttentiveSecondsIFMedian = null;
        creative.AttentiveSecondsIFDiff = null;
        creative.AttentiveSecondsVTR = null;
        creative.AttentiveSecondsVTRRank = null;
        creative.AttentiveSecondsVTRMedian = null;
        creative.AttentiveSecondsVTRDiff = null;
        creative.AttentiveSecondsVTRIF = null;
        creative.AttentiveSecondsVTRIFRank = null;
        creative.AttentiveSecondsVTRIFMedian = null;
        creative.AttentiveSecondsVTRIFDiff = null;
        creative.AttentionAvgIC = null;
        creative.AttentionAvgICRank = null;
        creative.AttentionAvgICMedian = null;
        creative.AttentionAvgICDiff = null;
        creative.AttentionAvgIF = null;
        creative.AttentionAvgIFRank = null;
        creative.AttentionAvgIFMedian = null;
        creative.AttentionAvgIFDiff = null;
        creative.Reactions = null;
        creative.ReactionsMedian = null;
        creative.ReactionsRank = null;
        creative.ReactionsDiff = null;
        creative.ReactionsIC = null;
        creative.ReactionsICMedian = null;
        creative.ReactionsICRank = null;
        creative.ReactionsICDiff = null;
        creative.NegativityAvgIC = null;
        creative.NegativityAvgICMedian = null;
        creative.NegativityAvgICRank = null;
        creative.NegativityAvgICDiff = null;
        creative.NegativityAvgIF = null;
        creative.NegativityAvgIFMedian = null;
        creative.NegativityAvgIFRank = null;
        creative.NegativityAvgIFDiff = null;
        creative.BrandRecognition = null;
        creative.BrandRecognitionMedian = null;
        creative.BrandRecognitionRank = null;
        creative.BrandRecognitionDiff = null;
        creative.AdLikeability = null;
        creative.AdLikeabilityRank = null;
        creative.AdLikeabilityMedian = null;
        creative.AdLikeabilityDiff = null;
        creative.HappyPeak = null;
        creative.HappyPeakRank = null;
        creative.HappyPeakMedian = null;
        creative.HappyPeakDiff = null;
        creative.HappyPeakIC = null;
        creative.HappyPeakICRank = null;
        creative.HappyPeakICMedian = null;
        creative.HappyPeakICDiff = null;
        creative.VTR = null;
        creative.VTRRank = null;
        creative.VTRMedian = null;
        creative.VTRDiff = null;
        creative.VTRIF = null;
        creative.VTRIFRank = null;
        creative.VTRIFMedian = null;
        creative.VTRIFDiff = null;
        creative.PlaybackSeconds = null;
        creative.PlaybackSecondsRank = null;
        creative.PlaybackSecondsMedian = null;
        creative.PlaybackSecondsDiff = null;
        creative.PlaybackSecondsIF = null;
        creative.PlaybackSecondsIFRank = null;
        creative.PlaybackSecondsIFMedian = null;
        creative.PlaybackSecondsIFDiff = null;
        creative.AdRecognition = null;
        creative.AdRecognitionRank = null;
        creative.AdRecognitionMedian = null;
        creative.AdRecognitionDiff = null;
        creative.SurprisePeak = null;
        creative.SurprisePeakRank = null;
        creative.SurprisePeakMedian = null;
        creative.SurprisePeakDiff = null;
        creative.SurprisePeakIC = null;
        creative.SurprisePeakICRank = null;
        creative.SurprisePeakICMedian = null;
        creative.SurprisePeakICDiff = null;
        creative.ConfusionPeak = null;
        creative.ConfusionPeakRank = null;
        creative.ConfusionPeakMedian = null;
        creative.ConfusionPeakDiff = null;
        creative.ConfusionPeakIC = null;
        creative.ConfusionPeakICRank = null;
        creative.ConfusionPeakICMedian = null;
        creative.ConfusionPeakICDiff = null;
        creative.ContemptPeak = null;
        creative.ContemptPeakRank = null;
        creative.ContemptPeakMedian = null;
        creative.ContemptPeakDiff = null;
        creative.ContemptPeakIC = null;
        creative.ContemptPeakICRank = null;
        creative.ContemptPeakICMedian = null;
        creative.ContemptPeakICDiff = null;
        creative.DisgustPeak = null;
        creative.DisgustPeakRank = null;
        creative.DisgustPeakMedian = null;
        creative.DisgustPeakDiff = null;
        creative.DisgustPeakIC = null;
        creative.DisgustPeakICRank = null;
        creative.DisgustPeakICMedian = null;
        creative.DisgustPeakICDiff = null;
        creative.BrandTrust = null;
        creative.BrandTrustRank = null;
        creative.BrandTrustMedian = null;
        creative.BrandTrustDiff = null;
        creative.Persuasion = null;
        creative.PersuasionRank = null;
        creative.PersuasionMedian = null;
        creative.PersuasionDiff = null;
        creative.DistractionAvgIC = null;
        creative.DistractionAvgICRank = null;
        creative.DistractionAvgICMedian = null;
        creative.DistractionAvgICDiff = null;
        creative.DistractionAvgIF = null;
        creative.DistractionAvgIFRank = null;
        creative.DistractionAvgIFMedian = null;
        creative.DistractionAvgIFDiff = null;

        return creative;
    }
}