using EntityFramework.Entities;
using Microsoft.EntityFrameworkCore;

namespace EntityFramework;

public class StudyDbContext : DbContext
{
    public StudyDbContext() { }

    public StudyDbContext(DbContextOptions options) : base(options) { }

    public DbSet<Account> Accounts { get; set; }
    public DbSet<AdSet> AdSets { get; set; }
    public DbSet<AdSetToAccount> AdSetToAccounts { get; set; }
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.Entity<AdSetToAccount>()
                    .<PERSON><PERSON><PERSON>(adSetToAccount => new { adSetToAccount.AdSetID, adSetToAccount.AccountID });
    }
}
