using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.EntityFrameworkCore;

namespace EntityFramework;

public static class EntityFrameworkConfigurationExtension
{
    public static IServiceCollection AddDbContext(this IServiceCollection services, IConfiguration configuration)
    {
        var connectionString = configuration.GetConnectionString("StudyDatabase");

        return services.AddDbContext<StudyDbContext>((serviceProvider, options) =>
        {
            options.UseSqlServer(connectionString);
        });
    }
}
