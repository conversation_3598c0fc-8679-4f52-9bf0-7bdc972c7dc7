using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace EntityFramework.Entities;

[Table("AdSet")]
public class AdSet
{
    [Key]
    public int ID { get; set; }
    [Required]
    public string Name { get; set; }
    [Required]
    public string ExternalKey { get; set; }
    [Required]
    public int AdSetTypeID { get; set; }
    [Required]
    public DateTime CreateTime { get; set; } = DateTime.Now;
   
    public virtual ICollection<AdSetToAccount> Accounts { get; set; }
}

