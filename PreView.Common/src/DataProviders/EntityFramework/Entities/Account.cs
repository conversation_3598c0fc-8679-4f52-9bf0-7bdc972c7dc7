using System.ComponentModel.DataAnnotations.Schema;

namespace EntityFramework.Entities;

[Table("Account")]
public class Account
{
    public int Id { get; set; }

    public string Name { get; set; }

    public string Hash { get; set; }

    public int? IndustryVerticalId { get; set; }

    public string Domain { get; set; }

    public virtual ICollection<AdSetToAccount> AdSetToAccounts { get; set; }
}
