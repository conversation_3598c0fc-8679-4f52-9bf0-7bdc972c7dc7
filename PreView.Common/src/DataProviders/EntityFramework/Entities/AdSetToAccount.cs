using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace EntityFramework.Entities;

[Table("AdSet_Account")]
public class AdSetToAccount
{
    [Required]
    public int AdSetID { get; set; }
    [Required]
    public int AccountID { get; set; }

    [ForeignKey(nameof(AdSetID))]
    public virtual AdSet AdSet { get; set; }
    [ForeignKey(nameof(AccountID))]
    public virtual Account Account { get; set; }
}