using BusinessLayer.Repository;
using Microsoft.EntityFrameworkCore;

namespace EntityFramework;

public class AdSetToAccountRepository : IAdSetToAccountRepository
{
    private readonly StudyDbContext _studyDBContext;

    public AdSetToAccountRepository(StudyDbContext studyDBContext)
    {
        _studyDBContext = studyDBContext;
    }

    public async Task<IEnumerable<int>> GetAccountIdsByAdSetExternalKey(string adSetExternalKey)
    {
        if (string.IsNullOrWhiteSpace(adSetExternalKey))
        {
            return await Task.FromResult(Enumerable.Empty<int>());
        }

        var ids = await _studyDBContext.AdSetToAccounts
            .Include(adsetToAccount => adsetToAccount.AdSet)
            .Where(adSetToAccount => adSetToAccount.AdSet.ExternalKey == adSetExternalKey)
            .Select(adSetAccount => adSetAccount.AccountID)
            .ToListAsync()
            .ConfigureAwait(false);

        return ids;
    }

    public async Task<IEnumerable<string>> GetAdSetExternalKeysByAccounts(IEnumerable<int> accountIds)
    {
        if (!accountIds?.Any() ?? true) 
        {
            return await Task.FromResult(Enumerable.Empty<string>());
        }

        var externalKeys = await _studyDBContext.AdSetToAccounts
            .Include(adSetAccount => adSetAccount.AdSet)
            .Where(adSetAccount => accountIds!.Contains(adSetAccount.AccountID))
            .Select(adSetAccount => adSetAccount.AdSet.ExternalKey)
            .ToListAsync()
            .ConfigureAwait(false);

        return externalKeys;
    }
}
