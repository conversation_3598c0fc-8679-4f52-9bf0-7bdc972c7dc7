@token = eyJraWQiOiJ0SXV3TnJcL1I5eGhjbEk0cWZcL3FnSmwyYm1yV01vaHFGU1wvU1RwRkRLeVp3PSIsImFsZyI6IlJTMjU2In0.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.w6G7wUp_8Y5NMYokYOD8rLI3D0qT1EYw-LPfM45tcHSyHx4aKE9JcKKaIZ_KtsTCAPR-pHE51W7lz-yzbQF5zoPzXKckqfybIms_vObQJNhmYKBWxJyLyp-lOsUtgCWLCazFL7WgTws3EwQtCykUFQ18vD7iU3zhR45ALZ8hyVdZ_I7S9BQk0pccHs10G73Ufcgvm8yBvVmaQP5k7bfIOlBCzZwWkgs59ISYYUkJsEjgN2yqvWyIuX4yfYCSpjwEQ6f99Ef9AJumMIsF4mGNzjYek1ptx7If19WbgpvvB1h948VTwTQzcukRbhiQEgbBagTtxQz2jHQVGExZsUOCBg
@port = 50116

### Lambda Authorizer Test

GET https://kdnoewvudk.execute-api.eu-west-1.amazonaws.com/dev/api-demo?ProjectId=2023-05_RE0422
Authorization: Bearer {{token}}

### media-measurements test by ProjectId

GET https://localhost:{{port}}/eye-square/v1/report/media-measurements?ProjectId=2023-03_MBK083

### subject-measurements test by ProjectId

GET https://localhost:{{port}}/eye-square/v1/report/subject-measurements?ProjectId=2023-03_MBK083

### media-measurements test by all parameters

GET https://localhost:{{port}}/eye-square/v1/report/media-measurements?ProjectId=2023-03_MBK083&SubjectGroupId=1&TaskID=1

### subject-measurements test by all parameters

GET https://localhost:{{port}}/eye-square/v1/report/subject-measurements?ProjectId=2023-03_MBK083&SubjectId=8_3489257068&SubjectGroupId=1&TaskId=1

### Fetch JSON Web Key Set (JWKS) from cognito userpool (id on dev: eu-west-1_qWaaxaSZE)

GET https://cognito-idp.eu-west-1.amazonaws.com/eu-west-1_qWaaxaSZE/.well-known/jwks.json