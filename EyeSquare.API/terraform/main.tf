terraform {
  backend "s3" {
    bucket = "preview-terraform-workspaces"
    key = "eye-square-api"
	  region = "eu-west-1"
  }
}

provider "aws" {
  region = var.region
  # This is needed only for local deployment
  # ignore_tags {
  #   keys = ["AutoTag_CreateTime", "AutoTag_Creator"]
  # }
  default_tags {
    tags = {
      Costgroup   = var.cost_group
      product     = "preview"
      stage       = var.environment
    }
  }
}

locals {
  api_gateway_url = "${aws_api_gateway_rest_api.api.id}.execute-api.${var.region}.amazonaws.com"
  service_prefix_with_enviroment = "${var.environment}-${var.service_prefix}"
  authorizer_lambda_name = "preview-eye-square-authorizer-${var.region}-${var.environment}-lambda"
  authorizer_lambda_arn = "arn:aws:lambda:${var.region}:249265253269:function:${local.authorizer_lambda_name}"
}

data "aws_subnet" "subnet_1" {
  id = "subnet-5312240a"  
}

data "aws_subnet" "subnet_2" {
  id = "subnet-1dae5e79" 
}

data "aws_security_group" "sec_group" {
  id = "sg-8b383cef" 
}

resource "aws_iam_role" "lambda_role" {
  name = "${local.service_prefix_with_enviroment}-lambda-execution-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action    = "sts:AssumeRole"
        Effect    = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "lambda_execution_policy" {
  role       = aws_iam_role.lambda_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

resource "aws_iam_role_policy_attachment" "AWSLambdaVPCAccessExecutionRole" {
    role       = aws_iam_role.lambda_role.name
    policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole"
}

resource "aws_iam_role_policy" "lambda_ssm_policy" {
  name   = "${aws_iam_role.lambda_role.name}-ssm-policy"
  role   =  aws_iam_role.lambda_role.name

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ssm:GetParametersByPath",
          "ssm:GetParameters",
          "ssm:GetParameter"
        ]
        Resource = [
          "arn:aws:ssm:eu-west-1:249265253269:parameter/live/administration/dremio/preview/*",
          "arn:aws:ssm:eu-west-1:249265253269:parameter/live/api/featureApi/apiKey"
        ]
      }
    ]
  })
}

resource "aws_lambda_function" "my_lambda" {
  function_name = "${local.service_prefix_with_enviroment}-lambda"
  role          = aws_iam_role.lambda_role.arn
  handler       = "Realeyes.PreView.API.EyeSquare"
  runtime       = "dotnet8"
  memory_size      = 512
  timeout          = 30

  filename        = "../src/API/publish/eyesquare-api.zip"
  source_code_hash = filebase64sha256("../src/API/publish/eyesquare-api.zip")

  environment {
    variables = {
      ASPNETCORE_ENVIRONMENT                 = var.dotnet_enviroment
      Dremio__BaseUrl                        = "$${ssm:/live/administration/dremio/preview/baseUrl}"
      Dremio__Username                       = "$${ssm:/live/administration/dremio/preview/username}"
      Dremio__Password                       = "$${ssm:/live/administration/dremio/preview/password}"
      FeatureCalculationPlatform__FeatureApiUrl = "https://feature-reporting.realeyesit.com/v1/"
      FeatureCalculationPlatform__FeatureApiKey = "$${ssm:/live/api/featureApi/apiKey}"
      FeatureCalculationPlatform__Algorithm     = "algorithm-v6.0.0-nelSDK"
    }
  }

  vpc_config {
    subnet_ids          = [data.aws_subnet.subnet_1.id, data.aws_subnet.subnet_2.id]
    security_group_ids  = [data.aws_security_group.sec_group.id]
  }
}

resource "aws_cloudwatch_log_group" "lambda_log_group" {
  name = "/aws/lambda/${aws_lambda_function.my_lambda.function_name}"
  retention_in_days = 30 
}

resource "aws_api_gateway_rest_api" "api" {
  name        =  "${local.service_prefix_with_enviroment}-gateway"
  description = "API Gateway for .NET Lambda"
}

resource "aws_api_gateway_resource" "proxy_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "{proxy+}"  
}

resource "aws_api_gateway_resource" "token_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "token"  
}

resource "aws_api_gateway_authorizer" "lambda_authorizer" {
  name                    = "${local.service_prefix_with_enviroment}-lambda-authorizer"
  authorizer_uri          = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/${local.authorizer_lambda_arn}/invocations"
  type                    = "REQUEST"
  identity_source         = "method.request.header.Authorization"
  authorizer_result_ttl_in_seconds =  300
  rest_api_id             = aws_api_gateway_rest_api.api.id
}

resource "aws_api_gateway_method" "proxy_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.proxy_resource.id
  http_method   = "ANY"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_method" "token_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.token_resource.id
  http_method   = "POST"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "token_post_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.token_resource.id
  http_method             = aws_api_gateway_method.token_method.http_method
  integration_http_method = "POST"  
  type                    = "HTTP_PROXY"  
  uri                     = "https://${var.environment == "live" ? "" : "stage-"}preview-authentication.${var.environment}.realeyesit.com/oauth2/token" 
}

resource "aws_api_gateway_integration" "lambda_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.proxy_resource.id
  http_method             = aws_api_gateway_method.proxy_method.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = aws_lambda_function.my_lambda.invoke_arn
}

resource "aws_lambda_permission" "allow_api_gateway" {
  statement_id  = "AllowApiGatewayInvoke"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.my_lambda.function_name
  principal     = "apigateway.amazonaws.com"
}


resource "aws_lambda_permission" "allow_authorizer_from_gateway" {
  statement_id  = "AllowExecutionFromAPIGateway-${aws_api_gateway_rest_api.api.id}"
  action        = "lambda:InvokeFunction"
  function_name = local.authorizer_lambda_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "arn:aws:execute-api:eu-west-1:249265253269:${aws_api_gateway_rest_api.api.id}/authorizers/${aws_api_gateway_authorizer.lambda_authorizer.id}"
}

resource "aws_api_gateway_deployment" "api_deployment" {
  depends_on = [
    aws_api_gateway_integration.lambda_integration,
    aws_api_gateway_integration.token_post_integration
  ]
  rest_api_id = aws_api_gateway_rest_api.api.id
}

resource "aws_cognito_resource_server" "eye_square_api_resource_server" {
  name         = "${var.service_prefix}-resource-server-${var.environment}"
  user_pool_id =  var.user_pool_id
  identifier   = "https://${var.environment == "live" ? "" : "stage-"}preview-api.realeyesit.com"

  scope {
    scope_name        = "account-id:${var.eyesquare_account_id}"
    scope_description = "eye square scope" 
  }

  scope {
    scope_name        = "account-id:10"
    scope_description = "realeyes scope" 
  }
}

resource "aws_api_gateway_stage" "api_stage" {
  rest_api_id  = aws_api_gateway_rest_api.api.id
  deployment_id = aws_api_gateway_deployment.api_deployment.id
  stage_name   =  var.environment
  description  = "API Gateway Stage"
}

resource "aws_ssm_parameter" "ssm_gateway_url" {
  name  = "/preview/api/eye-square/${var.environment}/gatewayUrl"
  type  = "String"
  value = local.api_gateway_url
}

output "api_url" {
  value = local.api_gateway_url
}