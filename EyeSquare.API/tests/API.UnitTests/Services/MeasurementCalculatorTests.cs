using Realeyes.PreView.API.EyeSquare.Model;
using Realeyes.PreView.API.EyeSquare.Model.Responses;
using Realeyes.PreView.API.EyeSquare.Services;
using Realeyes.PreView.API.EyeSquare.Services.Calculation;
using Realeyes.PreView.API.EyeSquare.Services.DremioQueries.DataTransferObjects;
using Realeyes.PreView.API.EyeSquare.Services.FcpApi.DataTransferObjects;

namespace Realeyes.PreView.API.EyeSquare.UnitTests.Services;

public class MeasurementCalculatorTests
{
    private IMeasurementCalculator underTest;
    private IMeasurementRepository repository;
    private IFeatureCalculator featureCalculation;
    private IMediaClassifierGroupBuilder mediaClassifierGroupBuilder;

    [SetUp]
    public void Setup()
    {
        this.repository = Substitute.For<IMeasurementRepository>();
        this.featureCalculation = Substitute.For<IFeatureCalculator>();
        this.mediaClassifierGroupBuilder = Substitute.For<IMediaClassifierGroupBuilder>();
        underTest = new MeasurementCalculator(this.repository, this.featureCalculation, this.mediaClassifierGroupBuilder);
    }

    [Test]
    public void CanConstruct()
    {
        // Arrange

        // Act

        // Assert
        Assert.That(this.underTest, Is.Not.Null);
    }

    [Test]
    public void GivenNullRequest_WhenCalculateMediaMeasurementsCalled_ThenThrowException()
    {
        // Arrange
        MediaMeasurementCalculationDetails details = null!;

        // Act
        async Task action() => await this.underTest.CalculateMediaMeasurements(details);

        // Assert
        var exception = Assert.ThrowsAsync<ArgumentNullException>(action);
        Assert.That(exception.Message, Is.EqualTo($"Value cannot be null. (Parameter '{nameof(details)}')"));
    }


    [Test]
    public async Task GivenNoneNullParameters_WhenCalculateMediaMeasurementsCalled_ThenResponseIsNotNull()
    {
        // Arrange
        var details = new MediaMeasurementCalculationDetails("", "", "", new List<string>());

        this.repository.GetProjectMappings(details)
            .Returns(new List<InContextProjectMapping>() { new InContextProjectMapping() });
        this.repository.GetSessionIdsForTest(details, new List<int> { 0, 1, 2})
            .Returns(new List<Session>() { new Session() });
        this.repository.GetSubjectsInCalculation(details)
            .Returns(new List<Subject>() { new Subject() });

        this.featureCalculation.CalculateMediaClassifiersCreatives(Arg.Any<SessionInfo>())
            .Returns(new Creatives(new List<StatsMediaClassifier>(), new List<MeanCountsMediaClassifier>(), new List<MeanMediaTimeClassifier>()));

        // Act
        var response = await this.underTest.CalculateMediaMeasurements(details);

        // Assert
        Assert.That(response, Is.Not.Null);
    }


    [Test]
    public async Task GivenValidDataByDataProviders_WhenCalculateMediaMeasurementsCalled_ThenResponseIsValid()
    {
        // Arrange
        string projectId = Guid.NewGuid().ToString();
        string subjectGroupId = Guid.NewGuid().ToString();
        string firstTaskId = Guid.NewGuid().ToString();
        string secondTaskId = Guid.NewGuid().ToString();
        var testIds = new List<int>() { 1,2,3 };
        string elementId = Guid.NewGuid().ToString();
        int sourceMediaId = 123;
        var subjectIds = new List<string> { Guid.NewGuid().ToString(), Guid.NewGuid().ToString(), Guid.NewGuid().ToString() };

        var details = new MediaMeasurementCalculationDetails(projectId, subjectGroupId, firstTaskId, subjectIds);        

        this.InitializeProjectMappings(projectId, subjectGroupId, firstTaskId, secondTaskId, testIds.First(), elementId, sourceMediaId, details);
        this.InitializeSessionIds(testIds, details);
        this.InitializeSubjects(details);
        this.InitializeFeatureCalculation(firstTaskId, secondTaskId, testIds.First(), sourceMediaId);
        
        // Act
        var response = await this.underTest.CalculateMediaMeasurements(details);

        // Assert
        Assert.That(response.SubjectIdsUsedInCalculation.Count(), Is.GreaterThan(0));
        Assert.That(response.Measurements.Count(), Is.GreaterThan(0));
        Assert.Multiple(() =>
        {
            this.AssertMeasurements(projectId, subjectGroupId, firstTaskId, secondTaskId, elementId, response);
        });
    }

    private void InitializeProjectMappings(string projectId, string subjectGroupId, string firstTaskId, string secondTaskId, int testId, string elementId, int sourceMediaId, MediaMeasurementCalculationDetails details)
    {
        this.repository.GetProjectMappings(details).Returns(new List<InContextProjectMapping>()
        {
            new InContextProjectMapping()
            {
                ProjectId = projectId,
                ElementId = elementId,
                SourceMediaId = sourceMediaId,
                SubjectGroupId = subjectGroupId,
                TaskId = firstTaskId,
                TestId = testId,
            },
            new InContextProjectMapping()
            {
                ProjectId = projectId,
                ElementId = elementId,
                SourceMediaId = sourceMediaId,
                SubjectGroupId = subjectGroupId,
                TaskId = secondTaskId,
                TestId = testId,
            }
        });
    }

    private void InitializeSessionIds(IEnumerable<int> testIds, MediaMeasurementCalculationDetails details)
    {
        this.repository.GetSessionIdsForTest(details, testIds).Returns(new List<Session>()
        {
            new Session() { Id = 1 },
            new Session() { Id = 2 },
            new Session() { Id = 3 },
            new Session() { Id = 4 }
        });
    }

    private void InitializeSubjects(MediaMeasurementCalculationDetails details)
    {
        this.repository.GetSubjectsInCalculation(details).Returns(new List<Subject>()
        {
            new Subject() { SubjectId = Guid.NewGuid().ToString() },
            new Subject() { SubjectId = Guid.NewGuid().ToString() },
            new Subject() { SubjectId = Guid.NewGuid().ToString() },
            new Subject() { SubjectId = Guid.NewGuid().ToString() },
            new Subject() { SubjectId = Guid.NewGuid().ToString() },
        });
    }
    private void InitializeFeatureCalculation(string firstTaskId, string secondTaskId, int testId, int sourceMediaId)
    {
        var stats = new List<StatsMediaClassifier>()
        {
            new StatsMediaClassifier() { taskid = firstTaskId, testid = testId, sourcemediaid = sourceMediaId },
            new StatsMediaClassifier() { taskid = firstTaskId, testid = testId, sourcemediaid = sourceMediaId },
            new StatsMediaClassifier() { taskid = secondTaskId, testid = testId, sourcemediaid = sourceMediaId },
            new StatsMediaClassifier() { taskid = secondTaskId, testid = testId, sourcemediaid = sourceMediaId }
        };

        var meanCounts = new List<MeanCountsMediaClassifier>()
        {
            new MeanCountsMediaClassifier() { taskid = firstTaskId, testid = testId, sourcemediaid = sourceMediaId },
            new MeanCountsMediaClassifier() { taskid = firstTaskId, testid = testId, sourcemediaid = sourceMediaId },
            new MeanCountsMediaClassifier() { taskid = secondTaskId, testid = testId, sourcemediaid = sourceMediaId },
            new MeanCountsMediaClassifier() { taskid = secondTaskId, testid = testId, sourcemediaid = sourceMediaId },
            new MeanCountsMediaClassifier() { taskid = secondTaskId, testid = testId, sourcemediaid = sourceMediaId },
        };

        var meanMediaTimes = new List<MeanMediaTimeClassifier>()
        {
            new MeanMediaTimeClassifier() { taskid = firstTaskId, testid = testId, sourcemediaid = sourceMediaId },
            new MeanMediaTimeClassifier() { taskid = secondTaskId, testid = testId, sourcemediaid = sourceMediaId }
        };

        this.featureCalculation.CalculateMediaClassifiersCreatives(Arg.Any<SessionInfo>()).Returns(new Creatives(stats, meanCounts, meanMediaTimes));

        this.mediaClassifierGroupBuilder.Build().Returns(new Dictionary<MediaClassifierGroupKey, MediaClassifierGroup>()
        {
            { 
                new MediaClassifierGroupKey(testId, firstTaskId, sourceMediaId), 
                new MediaClassifierGroup() 
                { 
                    Stats = stats.Where(x => x.taskid == firstTaskId).ToList(),
                    MeanCounts = meanCounts.Where(x => x.taskid ==firstTaskId).ToList(),
                    MeanMediaTimes = meanMediaTimes.Where(x => x.taskid ==firstTaskId).ToList(),
                } 
            },
            {
                new MediaClassifierGroupKey(testId, secondTaskId, sourceMediaId),
                new MediaClassifierGroup()
                {
                    Stats = stats.Where(x => x.taskid == secondTaskId).ToList(),
                    MeanCounts = meanCounts.Where(x => x.taskid == secondTaskId).ToList(),
                    MeanMediaTimes = meanMediaTimes.Where(x => x.taskid == secondTaskId).ToList(),
                }
            },
        });
    }

    private void AssertMeasurements(string projectId, string subjectGroupId, string firstTaskId, string secondTaskId, string elementId, MediaMeasurementCalculationResponse response)
    {
        foreach (var measurement in response.Measurements)
        {
            Assert.That(measurement, Is.Not.Null);
            Assert.That(measurement.ProjectId, Is.EqualTo(projectId));
            Assert.That(measurement.SubjectGroupId, Is.EqualTo(subjectGroupId));
            Assert.That(measurement.TaskId, Is.EqualTo(firstTaskId).Or.EqualTo(secondTaskId));
            Assert.That(measurement.Elements.Count(), Is.GreaterThan(0));

            this.AssertElements(elementId, measurement);
        }
    }

    private void AssertElements(string elementId, MediaMeasurement measurement)
    {
        foreach (var element in measurement.Elements)
        {
            Assert.That(element, Is.Not.Null);
            Assert.That(element.Id, Is.EqualTo(elementId));
            Assert.That(element.Metrics.Count(), Is.GreaterThan(0));
            Assert.That(element.TimeSeries.Count(), Is.GreaterThan(0));
        }
    }
}