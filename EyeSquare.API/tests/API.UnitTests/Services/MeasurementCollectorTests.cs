using Realeyes.PreView.API.EyeSquare.Model.Requests;
using Realeyes.PreView.API.EyeSquare.Services;
using Realeyes.PreView.API.EyeSquare.Services.DremioQueries.DataTransferObjects;

namespace Realeyes.PreView.API.EyeSquare.UnitTests.Services;

internal class MeasurementCollectorTests
{
    private IMeasurementCollector underTest;
    private IMeasurementRepository repository;

    [SetUp]
    public void Setup()
    {
        this.repository = Substitute.For<IMeasurementRepository>();
        this.underTest = new MeasurementCollector(this.repository);
    }

    [Test]
    public void CanConstruct()
    {
        // Arrange

        // Act

        // Assert
        Assert.That(this.underTest, Is.Not.Null);
    }

    [Test]
    public void GivenNullRequest_WhenCollectMediaMeasurementsCalled_ThenThrowException()
    {
        // Arrange
        MediaMeasurementRequest request = null!;

        // Act
        async Task action() => await this.underTest.CollectMediaMeasurements(request);

        // Assert
        var exception = Assert.ThrowsAsync<ArgumentNullException>(action);
        Assert.That(exception.Message, Is.EqualTo($"Value cannot be null. (Parameter '{nameof(request)}')"));
    }

    [Test]
    public void GivenNullRequest_WhenCollectSubjectMeasurementsCalled_ThenThrowException()
    {
        // Arrange
        SubjectMeasurementRequest request = null!;

        // Act
        async Task action() => await this.underTest.CollectSubjectMeasurements(request);

        // Assert
        var exception = Assert.ThrowsAsync<ArgumentNullException>(action);
        Assert.That(exception.Message, Is.EqualTo($"Value cannot be null. (Parameter '{nameof(request)}')"));
    }

    [Test]
    public async Task GivenNoneNullParameters_WhenCollectMediaMeasurementsCalled_ThenResponseIsNotNull()
    {
        // Arrange
        var request = new MediaMeasurementRequest
        {
            ProjectId = "",
            SubjectGroupId = "",
            TaskId = ""
        };

        this.repository.GetMediaMetricsBySession(request).Returns(new List<MediaMetricsBySession>() { new MediaMetricsBySession() });
        this.repository.GetMediaMetricsByTime(request).Returns(new List<MediaMetricsByTime>() { new MediaMetricsByTime() });

        // Act
        var response = await this.underTest.CollectMediaMeasurements(request);

        // Assert
        Assert.That(response, Is.Not.Null);
    }

    [Test]
    public async Task GivenNoneNullParameters_WhenCollectSubjectMeasurementsCalled_ThenResponseIsNotNull()
    {
        // Arrange
        var request = new SubjectMeasurementRequest
        {
            ProjectId = "",
            SubjectGroupId = "",
            TaskId = "",
            SubjectId = ""
        };

        this.repository.GetSubjectMetricsBySession(request).Returns(new List<SubjectMetricsBySession>() { new SubjectMetricsBySession() });
        this.repository.GetSubjectMetricsByTime(request).Returns(new List<SubjectMetricsByTime>() { new SubjectMetricsByTime() });

        // Act
        var response = await this.underTest.CollectSubjectMeasurements(request);

        // Assert
        Assert.That(response, Is.Not.Null);
    }

    [Test]
    public async Task GivenValidDataByDataProviders_WhenCollectMediaMeasurementsCalled_ThenResponseIsValid()
    {
        var projectId = Guid.NewGuid().ToString();
        var subjectGroupId = Guid.NewGuid().ToString();
        var taskId = Guid.NewGuid().ToString();
        var elementId = Guid.NewGuid().ToString();

        // Arrange
        var request = new MediaMeasurementRequest
        {
            ProjectId = projectId,
            SubjectGroupId = subjectGroupId,
            TaskId = taskId
        };

        this.repository.GetMediaMetricsBySession(request).Returns(new List<MediaMetricsBySession>()
        {
            new MediaMetricsBySession { EyeSquareProjectID = projectId, EyeSquareSubjectGroupID = subjectGroupId, TaskID = taskId, ElementID = elementId, visible_viewer_share_avg = 0.1 },            
        });
        this.repository.GetMediaMetricsByTime(request).Returns(new List<MediaMetricsByTime>() 
        { 
            new MediaMetricsByTime { EyeSquareProjectID = projectId, EyeSquareSubjectGroupID = subjectGroupId, TaskID = taskId, ElementID = elementId, second = 0, happiness_viewer_share = 0.1 },
            new MediaMetricsByTime { EyeSquareProjectID = projectId, EyeSquareSubjectGroupID = subjectGroupId, TaskID = taskId, ElementID = elementId, second = 1, happiness_viewer_share = 0.2 },
            new MediaMetricsByTime { EyeSquareProjectID = projectId, EyeSquareSubjectGroupID = subjectGroupId, TaskID = taskId, ElementID = elementId, second = 2, happiness_viewer_share = 0.3 },
        });

        // Act
        var response = await this.underTest.CollectMediaMeasurements(request);

        // Assert
        Assert.That(response.Count(), Is.EqualTo(1));
        foreach (var measurement in response)
        {
            Assert.That(measurement.ProjectId, Is.EqualTo(projectId));
            Assert.That(measurement.SubjectGroupId, Is.EqualTo(subjectGroupId));
            Assert.That(measurement.TaskId, Is.EqualTo(taskId));
            Assert.That(measurement.Elements.Count(), Is.EqualTo(1));
            foreach(var element in measurement.Elements)
            {
                Assert.That(element.Id, Is.EqualTo(elementId));
                Assert.That(element.Metrics.Count(), Is.GreaterThan(0));
                Assert.That(element.Metrics.Where(m => m.Name == "visible_viewer_share_avg").Select(m => m.Value), Is.Not.Null);
                Assert.That(element.TimeSeries.Count(), Is.GreaterThan(0));
                Assert.That(element.TimeSeries.Single(t => t.Name == "happiness_viewer_share").TimeSpans.Count(t => t != null), Is.EqualTo(3));
                Assert.That(element.TimeSeries.Single(t => t.Name == "happiness_viewer_share").Values.Count(v => v != null), Is.EqualTo(3));
            }
        }
    }

    [Test]
    public async Task GivenValidDataByDataProviders_WhenCollectSubjectMeasurementsCalled_ThenResponseIsValid()
    {
        var projectId = Guid.NewGuid().ToString();
        var subjectGroupId = Guid.NewGuid().ToString();
        var subjectId = Guid.NewGuid().ToString();
        var taskId = Guid.NewGuid().ToString();
        var elementId = Guid.NewGuid().ToString();

        // Arrange
        var request = new SubjectMeasurementRequest
        {
            ProjectId = projectId,
            SubjectGroupId = subjectGroupId,
            SubjectId = subjectGroupId,
            TaskId = taskId
        };

        this.repository.GetSubjectMetricsBySession(request).Returns(new List<SubjectMetricsBySession>()
        {
            new SubjectMetricsBySession { EyeSquareProjectID = projectId, EyeSquareSubjectGroupID = subjectGroupId, SubjectID = subjectId, TaskID = taskId, ElementID = elementId, happiness_seconds = 0.1 },
        });
        this.repository.GetSubjectMetricsByTime(request).Returns(new List<SubjectMetricsByTime>()
        {
            new SubjectMetricsByTime { EyeSquareProjectID = projectId, EyeSquareSubjectGroupID = subjectGroupId, SubjectID = subjectId, TaskID = taskId, ElementID = elementId, second = 0, happiness = 0.1 },
            new SubjectMetricsByTime { EyeSquareProjectID = projectId, EyeSquareSubjectGroupID = subjectGroupId, SubjectID = subjectId, TaskID = taskId, ElementID = elementId, second = 1, happiness = 0.2 },
            new SubjectMetricsByTime { EyeSquareProjectID = projectId, EyeSquareSubjectGroupID = subjectGroupId, SubjectID = subjectId, TaskID = taskId, ElementID = elementId, second = 2, happiness = 0.3 },
        });

        // Act
        var response = await this.underTest.CollectSubjectMeasurements(request);

        // Assert
        Assert.That(response.Count(), Is.EqualTo(1));
        foreach (var measurement in response)
        {
            Assert.That(measurement.ProjectId, Is.EqualTo(projectId));
            Assert.That(measurement.SubjectGroupId, Is.EqualTo(subjectGroupId));
            Assert.That(measurement.TaskId, Is.EqualTo(taskId));
            Assert.That(measurement.Elements.Count(), Is.EqualTo(1));
            foreach (var element in measurement.Elements)
            {
                Assert.That(element.Id, Is.EqualTo(elementId));
                Assert.That(element.Metrics.Count(), Is.GreaterThan(0));
                Assert.That(element.Metrics.Where(m => m.Name == "happiness_seconds").Select(m => m.Value), Is.Not.Null);
                Assert.That(element.TimeSeries.Count(), Is.GreaterThan(0));
                Assert.That(element.TimeSeries.Single(t => t.Name == "happiness").TimeSpans.Count(t => t != null), Is.EqualTo(3));
                Assert.That(element.TimeSeries.Single(t => t.Name == "happiness").Values.Count(v => v != null), Is.EqualTo(3));
            }
        }
    }
}
