
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.6.33829.357
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "API", "src\API\API.csproj", "{D9E5E09C-0D6A-46FA-AA0C-A662F628CB45}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Dremio", "..\PreViewPortal.API\src\Dremio\Dremio.csproj", "{6E2C9AD5-663C-4D03-BBD1-6F693F0AFB70}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "SolutionItems", "SolutionItems", "{FC9D2008-7198-4769-8F1A-6CEEC50FD1B8}"
	ProjectSection(SolutionItems) = preProject
		.gitignore = .gitignore
		..\.github\workflows\eye-square-api-ci-cd.yml = ..\.github\workflows\eye-square-api-ci-cd.yml
		serverless.yaml = serverless.yaml
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "HttpRequests", "HttpRequests", "{51229D0F-7F45-48F9-9976-57876B6EF9AE}"
	ProjectSection(SolutionItems) = preProject
		tests.http = tests.http
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "API.UnitTests", "tests\API.UnitTests\API.UnitTests.csproj", "{C5A4421E-8459-4F34-920B-C18ECCDEC3E2}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Tests", "Tests", "{428D73C1-AAFE-4678-AEB0-0B0552DB006E}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{D9E5E09C-0D6A-46FA-AA0C-A662F628CB45}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D9E5E09C-0D6A-46FA-AA0C-A662F628CB45}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D9E5E09C-0D6A-46FA-AA0C-A662F628CB45}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D9E5E09C-0D6A-46FA-AA0C-A662F628CB45}.Release|Any CPU.Build.0 = Release|Any CPU
		{6E2C9AD5-663C-4D03-BBD1-6F693F0AFB70}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6E2C9AD5-663C-4D03-BBD1-6F693F0AFB70}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6E2C9AD5-663C-4D03-BBD1-6F693F0AFB70}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6E2C9AD5-663C-4D03-BBD1-6F693F0AFB70}.Release|Any CPU.Build.0 = Release|Any CPU
		{C5A4421E-8459-4F34-920B-C18ECCDEC3E2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C5A4421E-8459-4F34-920B-C18ECCDEC3E2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C5A4421E-8459-4F34-920B-C18ECCDEC3E2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C5A4421E-8459-4F34-920B-C18ECCDEC3E2}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{C5A4421E-8459-4F34-920B-C18ECCDEC3E2} = {428D73C1-AAFE-4678-AEB0-0B0552DB006E}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {32003958-5D5F-4F28-8ACD-C3DF1BAF9FF4}
	EndGlobalSection
EndGlobal
