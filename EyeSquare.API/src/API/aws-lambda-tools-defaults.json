{"Information": ["This file provides default values for the deployment wizard inside Visual Studio and the AWS Lambda commands added to the .NET Core CLI.", "To learn more about the Lambda commands with the .NET Core CLI execute the following command at the command line in the project root directory.", "dotnet lambda help", "All the command line options for the Lambda command can be specified in this file."], "profile": "default", "region": "eu-west-1", "configuration": "Release", "function-runtime": "dotnet8", "function-memory-size": 2048, "function-timeout": 30, "funtion-handler": "Realeyes.PreView.API.EyeSquare", "framework": "net8.0", "function-name": "preview-eye-square-api-eu-west-1-stage-lambda", "function-description": "", "package-type": "Zip", "function-handler": "Realeyes.PreView.API.EyeSquare", "function-role": "arn:aws:iam::249265253269:role/stage-preview-eye-square-api-role", "function-architecture": "x86_64", "function-subnets": "subnet-1dae5e79,subnet-5312240a", "function-security-groups": "sg-8b383cef", "tracing-mode": "PassThrough", "environment-variables": "\"Dremio__BaseUrl\"=\"https://preview-data.realeyesit.com:9047\";\"ASPNETCORE_ENVIRONMENT\"=\"Stage\";\"Dremio__Password\"=\"6TcO2HD7Rj2iZUIHLhMuB7cFaBv/cllJgweTc4hnsXddyarL8mrfrZrZPFAxPQ==\";\"Dremio__Username\"=\"<EMAIL>\";\"FeatureCalculationPlatform__Algorithm\"=\"algorithm-v6.0.0-nelSDK\";\"FeatureCalculationPlatform__FeatureApiKey\"=\"8bNp7rU2aS7yYddJoWjU65bp3CqLHH309qpCiQjF\";\"FeatureCalculationPlatform__FeatureApiUrl\"=\"https://feature-reporting.realeyesit.com/v1/\"", "image-tag": ""}