using Realeyes.PreView.Infrastructure.Services.Dremio;
using Realeyesit.Extensions.Configuration;
using Realeyes.PreView.API.EyeSquare.Filters;
using Microsoft.AspNetCore.Mvc.Filters;
using Asp.Versioning;
using Microsoft.AspNetCore.Mvc.ApplicationModels;
using Realeyes.PreView.API.EyeSquare;
using Realeyes.PreView.API.EyeSquare.Services.FcpApi;
using Realeyes.PreView.API.EyeSquare.Services.Dremio;
using Microsoft.Extensions.Options;
using Realeyes.PreView.API.EyeSquare.Services;
using Realeyes.PreView.API.EyeSquare.Services.Calculation;
using Microsoft.AspNetCore.ResponseCompression;
using System.IO.Compression;
using Realeyes.PreView.Infrastructure.Services.Dremio.Configuration;
using Realeyes.PreView.Infrastructure.Services.Dremio.Extension;

var builder = WebApplication.CreateBuilder(args);

//Configuration
builder.Configuration
    .SetBasePath(builder.Environment.ContentRootPath)
    .AddJsonFile("appsettings.json")
    .AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json")
    .AddEnvironmentVariables()
    .Build();

IConfiguration configuration = builder.Configuration;
var shouldResolveSecrets = configuration.GetValue<bool>("Startup:ShouldResolveSecrets");
if (shouldResolveSecrets)
{
    configuration = configuration.ResolveSecrets(new ConfigurationSecretResolverSettings { ShouldThrow = true });
}

// Compression
builder.Services.AddResponseCompression(options =>
{
    options.EnableForHttps = true;
    options.Providers.Add<BrotliCompressionProvider>();
    options.Providers.Add<GzipCompressionProvider>();
});

builder.Services.Configure<BrotliCompressionProviderOptions>(options =>
{
    options.Level = CompressionLevel.Fastest;
});

builder.Services.Configure<GzipCompressionProviderOptions>(options =>
{
    options.Level = CompressionLevel.SmallestSize;
});

//Versioning and Documentation
var V1 = new ApiVersion(1);
builder.Services.AddSwaggerGen(options =>
{
    options.SwaggerDoc($"v{V1}", new()
    {
        Title = "eye square API",
        Version = V1.ToString(),
    });
});

builder.Services.AddApiVersioning(options =>
{
    options.DefaultApiVersion = V1;
    options.ApiVersionReader = new UrlSegmentApiVersionReader();
})
.AddApiExplorer(options =>
{
    options.GroupNameFormat = "'v'VVV";
    options.SubstituteApiVersionInUrl = true;
});

//Other Services
builder.Services.AddControllers(options =>
{
    options.AllowEmptyInputInBodyModelBinding = true;
    options.Conventions.Add(new RouteTokenTransformerConvention(new KebabParameterTransformer()));
    options.Filters.Add<GlobalExceptionFilter>();
});
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddAWSLambdaHosting(LambdaEventSource.RestApi);

builder.Services.AddDremioSettingsFrom(configuration);
builder.Services.AddDremioService();

builder.Services.Configure<FcpApiSettings>(configuration.GetSection(FcpApiSettings.SectionName));
builder.Services.AddHttpClient<IFeatureCalculationPlatform, FeatureCalculationPlatform>((serviceProvider, httpClient) => 
{
    var settings = serviceProvider.GetRequiredService<IOptions<FcpApiSettings>>().Value;
    httpClient.BaseAddress = new Uri(settings.FeatureApiUrl);
    httpClient.DefaultRequestHeaders.Add("x-api-key", settings.FeatureApiKey);
});

builder.Services.AddScoped<IActionFilter, ValidationFilter>();
builder.Services.AddScoped<IDremioDataProvider, DremioDataProvider>();
builder.Services.AddScoped<IMeasurementCollector, MeasurementCollector>();
builder.Services.AddScoped<IMediaClassifierGroupBuilder, MediaClassifierGroupBuilder>();
builder.Services.AddScoped<IMeasurementCalculator, MeasurementCalculator>();
builder.Services.AddScoped<IMeasurementRepository, MeasurementRepository>();
builder.Services.AddScoped<IQueryExecutor, DremioQueryExecutor>();
builder.Services.AddScoped<IFeatureCalculator, FeatureCalculator>();


//Build & Run Application
var app = builder.Build();

var versionSet = app.NewApiVersionSet()
    .HasApiVersion(V1)
    .ReportApiVersions()
    .Build();

if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(options =>
    {
        var descriptions = app.DescribeApiVersions();
        foreach (var description in descriptions)
        {
            options.SwaggerEndpoint($"/swagger/{description.GroupName}/swagger.json", $"{builder.Environment.ApplicationName} {description.GroupName}");
        }
    });
}

app.UseResponseCompression();
app.UseHttpsRedirection();
app.MapControllers().WithApiVersionSet(versionSet);
app.Run();
