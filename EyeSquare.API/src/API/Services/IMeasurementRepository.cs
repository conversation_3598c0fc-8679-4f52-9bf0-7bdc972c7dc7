using Realeyes.PreView.API.EyeSquare.Model;
using Realeyes.PreView.API.EyeSquare.Model.Requests;
using Realeyes.PreView.API.EyeSquare.Services.DremioQueries.DataTransferObjects;

namespace Realeyes.PreView.API.EyeSquare.Services;

public interface IMeasurementRepository
{
    public Task<IEnumerable<Subject>> GetSubjectsInCalculation(MediaMeasurementCalculationDetails details);
    public Task<IEnumerable<Session>> GetSessionIdsForTest(MediaMeasurementCalculationDetails details, IEnumerable<int> testIds);
    public Task<IEnumerable<InContextProjectMapping>> GetProjectMappings(MediaMeasurementCalculationDetails details);
    public Task<IEnumerable<SubjectMetricsBySession>> GetSubjectMetricsBySession(SubjectMeasurementRequest request);
    public Task<IEnumerable<SubjectMetricsByTime>> GetSubjectMetricsByTime(SubjectMeasurementRequest request);
    public Task<IEnumerable<MediaMetricsBySession>> GetMediaMetricsBySession(MediaMeasurementRequest request);
    public Task<IEnumerable<MediaMetricsByTime>> GetMediaMetricsByTime(MediaMeasurementRequest request);
}