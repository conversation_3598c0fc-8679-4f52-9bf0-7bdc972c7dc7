using Realeyes.PreView.API.EyeSquare.Model.Requests;
using Realeyes.PreView.API.EyeSquare.Model;

namespace Realeyes.PreView.API.EyeSquare.Services;

public interface IMeasurementCollector
{
    public Task<IEnumerable<SubjectMeasurement>> CollectSubjectMeasurements(SubjectMeasurementRequest request);
    public Task<IEnumerable<MediaMeasurement>> CollectMediaMeasurements(MediaMeasurementRequest request);
}
