using Realeyes.PreView.API.EyeSquare.Model;
using Realeyes.PreView.API.EyeSquare.Model.Requests;
using Realeyes.PreView.API.EyeSquare.Services.Dremio;
using Realeyes.PreView.API.EyeSquare.Services.Dremio.Queries;
using Realeyes.PreView.API.EyeSquare.Services.Dremio.QueryParameters;
using Realeyes.PreView.API.EyeSquare.Services.DremioQueries.DataTransferObjects;
using Realeyes.PreView.Infrastructure.Services.Dremio.Extension;

namespace Realeyes.PreView.API.EyeSquare.Services;

public class MeasurementRepository : IMeasurementRepository
{
    private readonly IQueryExecutor queryExecutor;

    public MeasurementRepository(IQueryExecutor queryExecutor)
    {
        this.queryExecutor = queryExecutor;
    }

    public Task<IEnumerable<Subject>> GetSubjectsInCalculation(MediaMeasurementCalculationDetails details)
    {
        var parameters = new SubjectsInCalculationQueryParameters
        {
            IdentifierSet = new IdentifierSetParameters
            {
                ProjectId = details.ProjectId,
                SubjectGroupId = details.SubjectGroupId,
                TaskId = details.TaskId
            },
            SubjectIds = details.SubjectIds
        };

        var subjectsQuery = new SubjectsInCalculationQuery(parameters);
        return queryExecutor.Execute<Subject>(subjectsQuery);
    }

    public Task<IEnumerable<Session>> GetSessionIdsForTest(MediaMeasurementCalculationDetails details, IEnumerable<int> testIds)
    {
        var sessionsQuery = new SessionsQuery(new SessionsQueryParameters
        {
            SubjectIds = details.SubjectIds,
            TestIds = testIds
        });
        return queryExecutor.Execute<Session>(sessionsQuery);
    }

    public Task<IEnumerable<InContextProjectMapping>> GetProjectMappings(MediaMeasurementCalculationDetails details)
    {
        var parameters = new IdentifierSetParameters
        {
            ProjectId = details.ProjectId,
            SubjectGroupId = details.SubjectGroupId,
            TaskId = details.TaskId
        };

        var sessionInfoQuery = new SessionInfoQuery(parameters);
        return queryExecutor.Execute<InContextProjectMapping>(sessionInfoQuery);
    }

    public Task<IEnumerable<SubjectMetricsBySession>> GetSubjectMetricsBySession(SubjectMeasurementRequest request)
    {
        var parameters = new SubjectMetricsQueryParameters
        {
            IdentifierSet = new IdentifierSetParameters
            {
                ProjectId = request.ProjectId,
                SubjectGroupId = request.SubjectGroupId,
                TaskId = request.TaskId
            },
            SubjectId = request.SubjectId
        };

        var subjectMetricsQuery = new SubjectMetricsBySessionQuery(parameters);
        return queryExecutor.Execute<SubjectMetricsBySession>(subjectMetricsQuery);
    }

    public Task<IEnumerable<SubjectMetricsByTime>> GetSubjectMetricsByTime(SubjectMeasurementRequest request)
    {
        var parameters = new SubjectMetricsQueryParameters
        {
            IdentifierSet = new IdentifierSetParameters
            {
                ProjectId = request.ProjectId,
                SubjectGroupId = request.SubjectGroupId,
                TaskId = request.TaskId
            },
            SubjectId = request.SubjectId
        };

        var subjectMetricsQuery = new SubjectMetricsByTimeQuery(parameters);
        return queryExecutor.Execute<SubjectMetricsByTime>(subjectMetricsQuery);
    }

    public Task<IEnumerable<MediaMetricsBySession>> GetMediaMetricsBySession(MediaMeasurementRequest request)
    {
        var parameters = new IdentifierSetParameters
        {
            ProjectId = request.ProjectId,
            SubjectGroupId = request.SubjectGroupId,
            TaskId = request.TaskId
        };

        var mediaMetricsBySessionQuery = new MediaMetricsBySessionQuery(parameters);
        return queryExecutor.Execute<MediaMetricsBySession>(mediaMetricsBySessionQuery);
    }

    public Task<IEnumerable<MediaMetricsByTime>> GetMediaMetricsByTime(MediaMeasurementRequest request)
    {
        var parameters = new IdentifierSetParameters
        {
            ProjectId = request.ProjectId,
            SubjectGroupId = request.SubjectGroupId,
            TaskId = request.TaskId
        };

        var timeSeriesQuery = new MediaMetricsByTimeQuery(parameters);
        return queryExecutor.Execute<MediaMetricsByTime>(timeSeriesQuery);
    }
}
