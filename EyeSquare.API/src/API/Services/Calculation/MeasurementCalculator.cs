using Realeyes.PreView.API.EyeSquare.Model;
using Realeyes.PreView.API.EyeSquare.Services.DremioQueries.DataTransferObjects;
using Realeyes.PreView.API.EyeSquare.Services.CustomAttributes;
using Realeyes.PreView.API.EyeSquare.Services.FcpApi.DataTransferObjects;
using Realeyes.PreView.API.EyeSquare.Model.Responses;

namespace Realeyes.PreView.API.EyeSquare.Services.Calculation;

public class MeasurementCalculator : IMeasurementCalculator
{
    private readonly IMeasurementRepository repository;
    private readonly IFeatureCalculator featureCalculator;
    private readonly IMediaClassifierGroupBuilder mediaClassifierGroupBuilder;

    public MeasurementCalculator(IMeasurementRepository repository, IFeatureCalculator featureCalculator, IMediaClassifierGroupBuilder mediaClassifierGroupBuilder)
    {
        this.repository = repository;
        this.featureCalculator = featureCalculator;
        this.mediaClassifierGroupBuilder = mediaClassifierGroupBuilder;
    }

    public async Task<MediaMeasurementCalculationResponse> CalculateMediaMeasurements(MediaMeasurementCalculationDetails details)
    {
        ArgumentNullException.ThrowIfNull(details);

        SessionInfo sessionInfo = await this.GatherSessionInfo(details);

        Creatives creatives = await this.featureCalculator.CalculateMediaClassifiersCreatives(sessionInfo);

        Dictionary<MediaClassifierGroupKey, MediaClassifierGroup> mediaClassifierGroups = BuildClassifierGroups(details, creatives);

        MediaMeasurementCalculationResponse response = this.ConstructMeasurementResponse(sessionInfo, mediaClassifierGroups);

        return response;
    }

    private Dictionary<MediaClassifierGroupKey, MediaClassifierGroup> BuildClassifierGroups(MediaMeasurementCalculationDetails details, Creatives creatives)
    {
        Func<StatsMediaClassifier, bool> statsMediaClassifierFilter = (StatsMediaClassifier stat) => 
            details.duration == null ? 
            stat.is_max_second : 
            (stat.second == details.duration || (stat.second < details.duration && stat.is_max_second));

        Func<MeanCountsMediaClassifier, bool> meanCountsMediaClassifierFilter = (MeanCountsMediaClassifier meanCounts) =>
            details.duration == null ? 
            meanCounts.is_max_second : 
            (meanCounts.second == details.duration || (meanCounts.second < details.duration && meanCounts.is_max_second));


        this.mediaClassifierGroupBuilder.Set(creatives);
        this.mediaClassifierGroupBuilder.Set(statsMediaClassifierFilter);
        this.mediaClassifierGroupBuilder.Set(meanCountsMediaClassifierFilter);

        Dictionary<MediaClassifierGroupKey, MediaClassifierGroup> mediaClassifierGroups = this.mediaClassifierGroupBuilder.Build();
        this.mediaClassifierGroupBuilder.Clear();

        return mediaClassifierGroups;
    }

    private async Task<SessionInfo> GatherSessionInfo(MediaMeasurementCalculationDetails details)
    {
        var inContextMappings = await repository.GetProjectMappings(details);
        if (inContextMappings == null || !inContextMappings.Any())
        {
            throw new InvalidOperationException("In-context project mapping cannot be found by the given parameters.");
        }

        IEnumerable<int> testIds = inContextMappings.Where(mapping => mapping?.TestId != null).Select(mapping => mapping.TestId!.Value).Distinct();
        Task<IEnumerable<Session>> sessionsTask = repository.GetSessionIdsForTest(details, testIds);
        Task<IEnumerable<Subject>> subjectsTask = repository.GetSubjectsInCalculation(details);

        await Task.WhenAll(sessionsTask, subjectsTask).ConfigureAwait(false);
        var sessions = await sessionsTask.ConfigureAwait(false);
        var subjects = await subjectsTask.ConfigureAwait(false);

        SessionInfo sessionInfo = CreateSessionInfo(inContextMappings, sessions, subjects);

        return sessionInfo;
    }

    private SessionInfo CreateSessionInfo(IEnumerable<InContextProjectMapping> inContextMapping, IEnumerable<Session> sessions, IEnumerable<Subject> subjects)
    {
        return new SessionInfo
        {
            IndentifierSets = inContextMapping
                    .GroupBy(mapping => new { mapping.TestId, mapping.ProjectId, mapping.SubjectGroupId, mapping.TaskId })
                    .Select(item => new Identifiers
                    {
                        SubjectGroupId = item.Key.SubjectGroupId,
                        TestId = item.Key.TestId,
                        ProjectId = item.Key.ProjectId,
                        TaskId = item.Key.TaskId,
                        Medias = item.Select(i => new Media
                        {
                            ElementId = i.ElementId,
                            SourceMediaId = i.SourceMediaId
                        })
                    }),
            SessionIds = sessions.Select(session => session.Id),
            ExpectedSubjectIds = subjects.Where(subject => !string.IsNullOrWhiteSpace(subject.SubjectId)).Select(subject => subject.SubjectId)!
        };
    }

    private MediaMeasurementCalculationResponse ConstructMeasurementResponse(SessionInfo sessionInfo, Dictionary<MediaClassifierGroupKey, MediaClassifierGroup> mediaClassifierGroupDicionary)
    {
        return new MediaMeasurementCalculationResponse
        {
            Measurements = sessionInfo.IndentifierSets.Select(identifierSet => new MediaMeasurement
            {
                ProjectId = identifierSet.ProjectId,
                SubjectGroupId = identifierSet.SubjectGroupId,
                TaskId = identifierSet.TaskId,
                Elements = CalculateMediaElements(identifierSet, mediaClassifierGroupDicionary)
            }),
            SubjectIdsUsedInCalculation = sessionInfo.ExpectedSubjectIds
        };
    }

    private IEnumerable<MediaElement> CalculateMediaElements(Identifiers identifierSet, Dictionary<MediaClassifierGroupKey, MediaClassifierGroup> dictionary)
    {
        return identifierSet.Medias.Select(media =>
        {
            var key = new MediaClassifierGroupKey(identifierSet.TestId, identifierSet.TaskId, media.SourceMediaId);            
            MediaClassifierGroup mediaClassifierGroup = dictionary.ContainsKey(key) ? dictionary[key] : new MediaClassifierGroup();

            var element = new MediaElement
            {
                Id = media.ElementId?.ToString(),
                Metrics = CalculateMetrics(mediaClassifierGroup.Stats, mediaClassifierGroup.MeanCounts),
                TimeSeries = CalculateTimeSeries(mediaClassifierGroup.MeanMediaTimes)
            };
            return element;
        });
    }

    private IEnumerable<Metric> CalculateMetrics(IEnumerable<StatsMediaClassifier> statsMediaClassifiers, IEnumerable<MeanCountsMediaClassifier> meanCountsMediaClassifiers)
    {
        var statProperties = ExcludeFromAggregationAttribute.GetPublicPropertiesWithoutAttribute<StatsMediaClassifier>();
        var meanCountProperties = ExcludeFromAggregationAttribute.GetPublicPropertiesWithoutAttribute<MeanCountsMediaClassifier>();

        var statsMediaMetrics = statsMediaClassifiers.SelectMany(stat => statProperties.Select(property => new Metric { Name = property.GetName(), Value = property.GetValue(stat) }));
        var meanCountMediaMetrics = meanCountsMediaClassifiers.SelectMany(meancount => meanCountProperties.Select(property => new Metric { Name = property.GetName(), Value = property.GetValue(meancount) }));

        return statsMediaMetrics.Union(meanCountMediaMetrics, new MetricComparer());
    }

    private IEnumerable<TimeSeries> CalculateTimeSeries(IEnumerable<MeanMediaTimeClassifier> meanMediaTimeClassifiers)
    {
        var meanMediaTimeProperties = ExcludeFromAggregationAttribute.GetPublicPropertiesWithoutAttribute<MeanMediaTimeClassifier>();
        return meanMediaTimeProperties.Select(property => new TimeSeries
        {
            Name = property.GetName(),
            TimeSpans = meanMediaTimeClassifiers.Select(model => model.second),
            Values = meanMediaTimeClassifiers.Select(model => property.GetValue(model))
        });
    }
}
