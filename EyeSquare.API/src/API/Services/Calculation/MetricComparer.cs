using Realeyes.PreView.API.EyeSquare.Model;
using System.Diagnostics.CodeAnalysis;

namespace Realeyes.PreView.API.EyeSquare.Services.Calculation;

public class MetricComparer : IEqualityComparer<Metric>
{
    public bool Equals(Metric? x, Metric? y)
    {
        return x?.Name == y?.Name;
    }

    public int GetHashCode([DisallowNull] Metric obj)
    {
        return HashCode.Combine(obj?.Name);
    }
}
