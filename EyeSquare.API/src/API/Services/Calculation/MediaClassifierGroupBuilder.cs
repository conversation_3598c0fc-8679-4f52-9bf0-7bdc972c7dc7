using Realeyes.PreView.API.EyeSquare.Services.FcpApi.DataTransferObjects;

namespace Realeyes.PreView.API.EyeSquare.Services.Calculation;

public class MediaClassifierGroupBuilder : IMediaClassifierGroupBuilder
{
    private Creatives? creatives;
    private Func<StatsMediaClassifier, bool>? statsFilter;
    private Func<MeanCountsMediaClassifier, bool>? meanCountsFilter;

    public void Set(Creatives creatives)
    {
        ArgumentNullException.ThrowIfNull(creatives);

        this.creatives = creatives;
    }

    public void Set(Func<StatsMediaClassifier,bool> whereClause)
    {
        this.statsFilter = whereClause;
    }

    public void Set(Func<MeanCountsMediaClassifier, bool> whereClause)
    {
        this.meanCountsFilter = whereClause;
    }

    public Dictionary<MediaClassifierGroupKey, MediaClassifierGroup> Build()
    {
        var dictionary = new Dictionary<MediaClassifierGroupKey, MediaClassifierGroup>();

        if (this.creatives == null)
        {
            return dictionary;
        }

        this.AddClassifiersToDictionary(
            this.statsFilter == null ? this.creatives.Stats : this.creatives.Stats.Where(this.statsFilter), 
            x => new MediaClassifierGroupKey(x.testid, x.taskid, x.sourcemediaid), 
            dictionary);

        this.AddClassifiersToDictionary(
            this.meanCountsFilter == null ? this.creatives.MeanCounts : this.creatives.MeanCounts.Where(this.meanCountsFilter), 
            x => new MediaClassifierGroupKey(x.testid, x.taskid, x.sourcemediaid),
            dictionary);

        this.AddClassifiersToDictionary(
            this.creatives.MeanMediaTime, 
            x => new MediaClassifierGroupKey(x.testid, x.taskid, x.sourcemediaid), dictionary);

        return dictionary;
    }

    private void AddClassifiersToDictionary<T>(IEnumerable<T> classifiers, Func<T, MediaClassifierGroupKey> createKeyFrom, Dictionary<MediaClassifierGroupKey, MediaClassifierGroup> dictionary)
    {
        if (classifiers == null) { return; }

        foreach (var classifier in classifiers)
        {
            var key = createKeyFrom(classifier);
            if (!dictionary.ContainsKey(key))
            {
                dictionary[key] = new MediaClassifierGroup();
            }

            dictionary[key].Add(classifier);
        }
    }

    public void Clear()
    {
        this.creatives = null;
        this.statsFilter = null;
        this.meanCountsFilter = null;
    }
}
