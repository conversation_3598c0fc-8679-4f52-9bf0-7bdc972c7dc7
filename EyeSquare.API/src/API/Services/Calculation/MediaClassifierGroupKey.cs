namespace Realeyes.PreView.API.EyeSquare.Services.Calculation;

public class MediaClassifierGroupKey
{
    public MediaClassifierGroupKey(long? testId, string? taskId, long? sourceMediaId)
    {
        TestId = testId;
        TaskId = taskId;
        SourceMediaId = sourceMediaId;
    }

    public long? TestId { get; set; }
    public string? TaskId { get; set; }
    public long? SourceMediaId { get; set; }

    public override bool Equals(object? obj)
    {
        return obj is MediaClassifierGroupKey key &&
            TestId == key.TestId &&
            TaskId == key.TaskId &&
            SourceMediaId == key.SourceMediaId;
    }

    public override int GetHashCode()
    {
        return HashCode.Combine(TestId, TaskId, SourceMediaId);
    }
}
