using Realeyes.PreView.API.EyeSquare.Services.FcpApi.DataTransferObjects;

namespace Realeyes.PreView.API.EyeSquare.Services.Calculation;

public interface IMediaClassifierGroupBuilder
{
    public void Set(Creatives mediaClassifiers);
    public void Set(Func<StatsMediaClassifier, bool> whereClause);
    public void Set(Func<MeanCountsMediaClassifier, bool> whereClause);
    public Dictionary<MediaClassifierGroupKey, MediaClassifierGroup> Build();
    public void Clear();
}
