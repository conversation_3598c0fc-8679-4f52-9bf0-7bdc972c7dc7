using Realeyes.PreView.API.EyeSquare.Services.FcpApi.DataTransferObjects;

namespace Realeyes.PreView.API.EyeSquare.Services.Calculation;

public class MediaClassifierGroup
{
    public IList<StatsMediaClassifier> Stats { get; init; } = new List<StatsMediaClassifier>();
    public IList<MeanCountsMediaClassifier> MeanCounts { get; init; } = new List<MeanCountsMediaClassifier>();
    public IList<MeanMediaTimeClassifier> MeanMediaTimes { get; init; } = new List<MeanMediaTimeClassifier>();

    public void Add<T>(T classifier)
    {
        ArgumentNullException.ThrowIfNull(classifier);

        switch (classifier)
        {
            case StatsMediaClassifier stat:
                Stats.Add(stat);
                break;
            case MeanCountsMediaClassifier meanCounts:
                MeanCounts.Add(meanCounts);
                break;
            case MeanMediaTimeClassifier meanMediaTimes:
                MeanMediaTimes.Add(meanMediaTimes);
                break;
            default:
                throw new InvalidOperationException($"Unsupported type: {typeof(T)}");
        }
    }
}