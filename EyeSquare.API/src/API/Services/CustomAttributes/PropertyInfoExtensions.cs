using System.Reflection;

namespace Realeyes.PreView.API.EyeSquare.Services.CustomAttributes;

public static class PropertyInfoExtensions
{
    public static string GetName(this PropertyInfo propertyInfo)
    {
        var attribute = propertyInfo.GetCustomAttribute<PropertyNameInAggregationAttribute>();
        return attribute == null || string.IsNullOrWhiteSpace(attribute.NameInAggregation) 
               ? propertyInfo.Name 
               : attribute.NameInAggregation; 
    }
}
