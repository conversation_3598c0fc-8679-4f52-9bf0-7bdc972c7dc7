using System.Reflection;

namespace Realeyes.PreView.API.EyeSquare.Services.CustomAttributes
{
    public class ExcludeFromAggregationAttribute : Attribute
    {
        public static IEnumerable<PropertyInfo> GetPublicPropertiesWithoutAttribute<T>()
        {
            return typeof(T)
                .GetProperties(BindingFlags.Instance | BindingFlags.Public)
                .Where(p => p.GetCustomAttribute<ExcludeFromAggregationAttribute>() == null);
        }
    }
}
