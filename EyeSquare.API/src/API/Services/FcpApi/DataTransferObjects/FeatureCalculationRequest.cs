using System.Text.Json.Serialization;

namespace Realeyes.PreView.API.EyeSquare.Services.FcpApi.DataTransferObjects;

public class FeatureCalculationRequest
{
    [JsonPropertyName("dimensions")]
    public List<CalculationRequestDimension>? Dimensions { get; set; }

    [JsonPropertyName("filters")]
    public List<Filter>? Filters { get; set; }

    [JsonPropertyName("data-version")]
    public string? DataVersion { get; set; }

    /// <summary>
    /// Note: FCP does not return cached results! Caching is used for reporting(freezing) internally by FCP
    /// Always send False for now
    /// </summary>
    [JsonPropertyName("skip_cache_results")]
    public int SkipCacheResults { get; set; } = 0;

    /// <summary>
    /// Use True to ask for non cached results in case result was frozen
    /// </summary>
    [JsonPropertyName("force")]
    public bool Force { get; set; } = false;
}
