using Realeyes.PreView.API.EyeSquare.Services.CustomAttributes;

namespace Realeyes.PreView.API.EyeSquare.Services.FcpApi.DataTransferObjects;

public class MeanCountsMediaClassifier
{
    [ExcludeFromAggregation]
    public string? algorithmid { get; set; }
    [ExcludeFromAggregation]
    public double? attentive_seconds_all_exposures_avg { get; set; }
    [ExcludeFromAggregation]
    public double? attentive_seconds_avg { get; set; }
    [ExcludeFromAggregation]
    public double? attentive_seconds_from_duration_avg { get; set; }
    [ExcludeFromAggregation]
    public double? attentive_viewthrough_avg { get; set; }
    public double? confusion_seconds_all_exposures_avg { get; set; }
    public double? confusion_seconds_avg { get; set; }    
    public double? contempt_seconds_all_exposures_avg { get; set; }
    public double? contempt_seconds_avg { get; set; }
    public double? disgust_seconds_all_exposures_avg { get; set; }
    public double? disgust_seconds_avg { get; set; }
    [ExcludeFromAggregation]
    public long? exposure { get; set; }
    public long? exposures { get; set; }
    [PropertyNameInAggregation("attentive_seconds_avg")]
    public double? eyeson_seconds_avg { get; set; }
    [PropertyNameInAggregation("attentive_seconds_from_duration_avg")]
    public double? eyeson_seconds_from_duration_avg { get; set; }
    [PropertyNameInAggregation("attentive_seconds_all_exposures_avg")]
    public double? eyeson_seconds_all_exposures_avg { get; set; }
    public double? happiness_seconds_all_exposures_avg { get; set; }
    public double? happiness_seconds_avg { get; set; }
    public double? inattentive_visible_seconds_all_exposures_avg { get; set; }
    public double? inattentive_visible_seconds_avg { get; set; }
    [ExcludeFromAggregation]
    public bool is_max_second { get; set; }
    public double? negativity_seconds_all_exposures_avg { get; set; }
    [ExcludeFromAggregation]
    public double? percentage_with_attentive_seconds { get; set; }
    public double? percentage_with_confusion_seconds { get; set; }
    public double? percentage_with_contempt_seconds { get; set; }
    public double? percentage_with_disgust_seconds { get; set; }
    [PropertyNameInAggregation("percentage_with_attentive_seconds")]
    public double? percentage_with_eyeson_seconds { get; set; }
    public double? percentage_with_happiness_seconds { get; set; }
    public double? percentage_with_inattentive_visible_seconds { get; set; }
    public double? percentage_with_negativity_seconds { get; set; }
    public double? percentage_with_reactions_seconds { get; set; }
    public double? percentage_with_surprise_seconds { get; set; }
    public double? negativity_seconds_avg { get; set; }    
    public double? reactions_seconds_all_exposures_avg { get; set; }
    public double? reactions_seconds_avg { get; set; }
    public long? second { get; set; }
    [ExcludeFromAggregation]
    public string? segment_key { get; set; }
    [ExcludeFromAggregation]
    public long? sourcemediaid { get; set; }
    public double? surprise_seconds_all_exposures_avg { get; set; }
    public double? surprise_seconds_avg { get; set; }
    [ExcludeFromAggregation]
    public string? taskid { get; set; }
    [ExcludeFromAggregation]
    public long? testid { get; set; }
    public long? views { get; set; }
    public double? visible_seconds_all_exposures_avg { get; set; }
    public double? visible_seconds_avg { get; set; }
}
