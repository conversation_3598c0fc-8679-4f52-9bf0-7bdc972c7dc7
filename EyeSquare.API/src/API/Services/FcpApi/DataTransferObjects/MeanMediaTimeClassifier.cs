using Realeyes.PreView.API.EyeSquare.Services.CustomAttributes;

namespace Realeyes.PreView.API.EyeSquare.Services.FcpApi.DataTransferObjects;

public class MeanMediaTimeClassifier
{
    [ExcludeFromAggregation]
    public string? algorithmid { get; set; }
    [ExcludeFromAggregation]
    public double? attentive_viewer_share { get; set; }
    public double? confusion_viewer_share { get; set; }
    public double? contempt_viewer_share { get; set; }
    public double? disgust_viewer_share { get; set; }
    public int? exposure { get; set; }
    [PropertyNameInAggregation("attentive_viewer_share")]
    public double? eyeson_viewer_share { get; set; }
    public double? happiness_viewer_share { get; set; }
    public double? inattentive_visible_viewer_share { get; set; }
    public double? negativity_viewer_share { get; set; }
    public double? not_visible_viewer_share { get; set; }
    public double? reactions_viewer_share { get; set; }
    public int? second { get; set; }
    [ExcludeFromAggregation]
    public string? segment_key { get; set; }
    [ExcludeFromAggregation]
    public long? sourcemediaid { get; set; }
    public double? surprise_viewer_share { get; set; }
    [ExcludeFromAggregation]
    public string? taskid { get; set; }
    [ExcludeFromAggregation]
    public int? testid { get; set; }
    public int? views { get; set; }
    public double? visible_viewer_share { get; set; }
}

