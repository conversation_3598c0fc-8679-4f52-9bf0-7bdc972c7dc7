using Realeyes.PreView.API.EyeSquare.Services.CustomAttributes;

namespace Realeyes.PreView.API.EyeSquare.Services.FcpApi.DataTransferObjects;

public class StatsMediaClassifier
{
    [ExcludeFromAggregation]
    public string? algorithmid { get; set; }
    [ExcludeFromAggregation]
    public double? attentive_viewer_share_avg { get; set; }
    [ExcludeFromAggregation]
    public double? attentive_viewer_share_std { get; set; }
    [ExcludeFromAggregation]
    public double? attentive_viewer_share_max { get; set; }
    public double? confusion_viewer_share_avg { get; set; }
    public double? confusion_viewer_share_std { get; set; }
    public double? confusion_viewer_share_max { get; set; }
    public double? contempt_viewer_share_avg { get; set; }
    public double? contempt_viewer_share_std { get; set; }
    public double? contempt_viewer_share_max { get; set; }
    public double? disgust_viewer_share_avg { get; set; }
    public double? disgust_viewer_share_std { get; set; }
    public double? disgust_viewer_share_max { get; set; }
    public long exposure { get; set; }
    [PropertyNameInAggregation("attentive_viewer_share_avg")]
    public double? eyeson_viewer_share_avg { get; set; }
    [PropertyNameInAggregation("attentive_viewer_share_std")]
    public double? eyeson_viewer_share_std { get; set; }
    [PropertyNameInAggregation("attentive_viewer_share_max")]
    public double? eyeson_viewer_share_max { get; set; }

    public double? happiness_viewer_share_avg { get; set; }
    public double? happiness_viewer_share_std { get; set; }
    public double? happiness_viewer_share_max { get; set; }
    public double? inattentive_visible_viewer_share_avg { get; set; }
    public double? inattentive_visible_viewer_share_std { get; set; }
    public double? inattentive_visible_viewer_share_max { get; set; }
    [ExcludeFromAggregation]
    public bool is_max_second { get; set; }
    public double? negativity_viewer_share_avg { get; set; }
    public double? negativity_viewer_share_std { get; set; }
    public double? negativity_viewer_share_max { get; set; }
    [ExcludeFromAggregation]
    public double? neutral_attention_viewer_share { get; set; }
    [ExcludeFromAggregation]
    public double? neutral_attention_viewer_share_max { get; set; }
    [ExcludeFromAggregation]
    public double? neutral_attention_viewer_share_std { get; set; }
    [PropertyNameInAggregation("neutral_attentive_viewer_share_avg")]
    public double? neutral_eyeson_viewer_share_avg { get; set; }
    [PropertyNameInAggregation("neutral_attentive_viewer_share_max")]
    public double? neutral_eyeson_viewer_share_max { get; set; }
    [PropertyNameInAggregation("neutral_attentive_viewer_share_std")]
    public double? neutral_eyeson_viewer_share_std { get; set; }
    public double? not_visible_viewer_share_avg { get; set; }
    public double? not_visible_viewer_share_std { get; set; }
    public double? not_visible_viewer_share_max { get; set; }
    public double? reactions_viewer_share_avg { get; set; }
    public double? reactions_viewer_share_std { get; set; }
    public double? reactions_viewer_share_max { get; set; }
    public long? second { get; set; }
    [ExcludeFromAggregation]
    public string? segment_key { get; set; }
    [ExcludeFromAggregation]
    public long? sourcemediaid { get; set; }
    public double? surprise_viewer_share_avg { get; set; }
    public double? surprise_viewer_share_std { get; set; }
    public double? surprise_viewer_share_max { get; set; }
    [ExcludeFromAggregation]
    public string? taskid { get; set; }
    [ExcludeFromAggregation]
    public long? testid { get; set; }
    public double? views { get; set; }
    public double? visible_viewer_share_avg { get; set; }
    public double? visible_viewer_share_std { get; set; }
    public double? visible_viewer_share_max { get; set; }
}

