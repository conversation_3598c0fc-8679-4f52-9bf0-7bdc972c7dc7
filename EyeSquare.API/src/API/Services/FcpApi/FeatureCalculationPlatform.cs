using System.Diagnostics;
using System.Text.Json;

namespace Realeyes.PreView.API.EyeSquare.Services.FcpApi;

public class FeatureCalculationPlatform : IFeatureCalculationPlatform
{
    private readonly HttpClient client;
    private readonly ILogger<FeatureCalculationPlatform> logger;

    public FeatureCalculationPlatform(HttpClient client, ILogger<FeatureCalculationPlatform> logger)
    {
        this.client = client;
        this.logger = logger;
    }

    public async Task<T?> Calculate<T>(IFeatureCalculation feature)
    {
        ArgumentNullException.ThrowIfNull(feature);
        ArgumentNullException.ThrowIfNull(feature.Request);
        ArgumentNullException.ThrowIfNull(feature.Name);

        try
        {
            var sw = Stopwatch.StartNew();

            var response = await client.PostAsJsonAsync($"features/{feature}/calculate", feature.Request);

            logger.LogInformation("Feature calculation response for {feature} arrived in: {totalSeconds} seconds.", feature.Name, sw.Elapsed.TotalSeconds);

            response.EnsureSuccessStatusCode();

            return await response.Content.ReadFromJsonAsync<T>(new JsonSerializerOptions { NumberHandling = System.Text.Json.Serialization.JsonNumberHandling.AllowNamedFloatingPointLiterals });
        }
        catch (Exception e)
        {
            logger.LogError(e, "FeatureAPI error");
            throw;
        }
    }
}
