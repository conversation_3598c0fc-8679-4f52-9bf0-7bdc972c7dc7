using Realeyes.PreView.API.EyeSquare.Services.FcpApi.DataTransferObjects;

namespace Realeyes.PreView.API.EyeSquare.Services.FcpApi;

public class FeatureCalculation : IFeatureCalculation
{
    public string Name { get; init; } = "default";
    public FeatureCalculationRequest Request { get; init; } = new FeatureCalculationRequest();

    public override string ToString()
    {
        return this.Name;
    }
}
