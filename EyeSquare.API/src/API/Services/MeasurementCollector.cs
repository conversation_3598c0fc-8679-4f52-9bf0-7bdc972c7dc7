using Realeyes.PreView.API.EyeSquare.Model.Requests;
using Realeyes.PreView.API.EyeSquare.Model;
using Realeyes.PreView.API.EyeSquare.Services.DremioQueries.DataTransferObjects;
using Realeyes.PreView.API.EyeSquare.Services.CustomAttributes;

namespace Realeyes.PreView.API.EyeSquare.Services;

public class MeasurementCollector : IMeasurementCollector
{
    private readonly IMeasurementRepository repository;

    public MeasurementCollector(IMeasurementRepository repository)
    {
        this.repository = repository;
    }

    public async Task<IEnumerable<SubjectMeasurement>> CollectSubjectMeasurements(SubjectMeasurementRequest request)
    {
        ArgumentNullException.ThrowIfNull(request);

        var subjectMetricsTask = this.repository.GetSubjectMetricsBySession(request);
        var timeSeriesTask = this.repository.GetSubjectMetricsByTime(request);

        var result = await CollectMeasurements(
            subjectMetricsTask,
            timeSeriesTask,
            session => new { session.EyeSquareProjectID, session.EyeSquareSubjectGroupID, session.SubjectID, session.TaskID, session.ElementID },
            timeSeries => new { timeSeries.EyeSquareProjectID, timeSeries.EyeSquareSubjectGroupID, timeSeries.SubjectID, timeSeries.TaskID, timeSeries.ElementID },
            (session, timeSeries) =>
            {
                var measurement = new SubjectMeasurement()
                {
                    ProjectId = session.EyeSquareProjectID,
                    SubjectGroupId = session.EyeSquareSubjectGroupID,
                    SubjectId = session.SubjectID,
                    TaskId = session.TaskID,
                    Elements = GetElements(session, timeSeries)
                };

                return measurement;
            });

        return result;
    }

    public async Task<IEnumerable<MediaMeasurement>> CollectMediaMeasurements(MediaMeasurementRequest request)
    {
        ArgumentNullException.ThrowIfNull(request);

        Task<IEnumerable<MediaMetricsBySession>> mediaMetricsBySessionTask = this.repository.GetMediaMetricsBySession(request);
        Task<IEnumerable<MediaMetricsByTime>> timeSeriesResponseTask = this.repository.GetMediaMetricsByTime(request);       

        var result = await CollectMeasurements(
            mediaMetricsBySessionTask,
            timeSeriesResponseTask,
            session => new { session.EyeSquareProjectID, session.EyeSquareSubjectGroupID, session.TaskID, session.ElementID },
            timeSeries => new { timeSeries.EyeSquareProjectID, timeSeries.EyeSquareSubjectGroupID, timeSeries.TaskID, timeSeries.ElementID },
            (session, timeSeries) =>
            {
                var measurement = new MediaMeasurement()
                {
                    ProjectId = session.EyeSquareProjectID,
                    SubjectGroupId = session.EyeSquareSubjectGroupID,
                    TaskId = session.TaskID,
                    Elements = GetElements(session, timeSeries)
                };

                return measurement;
            });

        return result;
    }

    private async Task<IEnumerable<TModel>> CollectMeasurements<TModel, TSession, TTimeSeries, TKey>(Task<IEnumerable<TSession>> sessionsResponseTask, Task<IEnumerable<TTimeSeries>> timeSeriesResponseTask, Func<TSession, TKey> innerSelector, Func<TTimeSeries, TKey> outerSelector, Func<TSession, IEnumerable<TTimeSeries>, TModel> resultSelector)
        where TModel : Measurement
    {
        // Fething data
        await Task.WhenAll(timeSeriesResponseTask, sessionsResponseTask).ConfigureAwait(false);
        var sessionsResponse = await sessionsResponseTask.ConfigureAwait(false);
        var timeSeriesResponse = await timeSeriesResponseTask.ConfigureAwait(false);

        // Mapping
        IEnumerable<TModel> mergedMeasurements = sessionsResponse.GroupJoin(timeSeriesResponse, innerSelector, outerSelector, resultSelector);
        return mergedMeasurements;
    }

    private IEnumerable<MediaElement> GetElements<TSession, TTimeSeries>(TSession session, IEnumerable<TTimeSeries> timeSeries)
        where TSession : MetricsBySession
        where TTimeSeries : MetricsByTime
    {
        yield return new MediaElement
        {
            Id = session.ElementID,
            Metrics = GetMetrics(session),
            TimeSeries = GetTimeSeries(timeSeries)
        };
    }

    private IEnumerable<Metric> GetMetrics<TModel>(TModel model)
    {
        IEnumerable<Metric> metrics = ExcludeFromAggregationAttribute.GetPublicPropertiesWithoutAttribute<TModel>()
            .Select(property => new Metric
            {
                Name = property.GetName(),
                Value = property.GetValue(model)
            });
        return metrics;
    }

    private IEnumerable<TimeSeries> GetTimeSeries<TModel>(IEnumerable<TModel> models) where TModel : MetricsByTime
    {
        IEnumerable<TimeSeries> timeSeries = ExcludeFromAggregationAttribute.GetPublicPropertiesWithoutAttribute<TModel>()
            .Select(property => new TimeSeries
            {
                Name = property.GetName(),
                TimeSpans = models.Select(model => model.second),
                Values = models.Select(model => property.GetValue(model))
            });
        return timeSeries;
    }
}
