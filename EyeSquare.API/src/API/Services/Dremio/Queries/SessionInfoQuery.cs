using Realeyes.PreView.API.EyeSquare.Services.Dremio.QueryParameters;
using Realeyes.PreView.Infrastructure.Services.Dremio.Extension;

namespace Realeyes.PreView.API.EyeSquare.Services.Dremio.Queries;

public class SessionInfoQuery : IDremioQuery
{
    private readonly IdentifierSetParameters parameters;

    public SessionInfoQuery(IdentifierSetParameters parameters)
    {
        this.parameters = parameters;
    }

    public string Build()
    {
        string sessionInfoQuery = @$"SELECT InContextTestID as TestId, EyeSquareProjectID as ProjectId, EyeSquareSubjectGroupID as SubjectGroupId, SourceMediaID as SourceMediaId, ElementID as ElementId, TaskID as TaskId FROM StudyDatabaseReadonly.StudyDatabase.dbo.InContextProjectMapping WHERE InContextTestID is not null AND EyeSquareProjectID = '{this.parameters.ProjectId}'";

        if (!string.IsNullOrWhiteSpace(this.parameters.SubjectGroupId))
        {
            sessionInfoQuery += $" AND EyeSquareSubjectGroupID = '{this.parameters.SubjectGroupId}'";
        }

        if (!string.IsNullOrWhiteSpace(this.parameters.TaskId))
        {
            sessionInfoQuery += $" AND TaskID = '{this.parameters.TaskId}'";
        }

        sessionInfoQuery += " GROUP BY InContextTestID, EyeSquareProjectID, EyeSquareSubjectGroupID, SourceMediaID, ElementID, TaskID";

        return sessionInfoQuery;
    }
}
