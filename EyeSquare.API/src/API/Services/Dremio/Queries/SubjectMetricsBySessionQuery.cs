using Realeyes.PreView.API.EyeSquare.Services.Dremio.QueryParameters;
using Realeyes.PreView.Infrastructure.Services.Dremio.Extension;

namespace Realeyes.PreView.API.EyeSquare.Services.Dremio.Queries;

public class SubjectMetricsBySessionQuery : IDremioQuery
{
    private readonly SubjectMetricsQueryParameters parameters;

    public SubjectMetricsBySessionQuery(SubjectMetricsQueryParameters parameters)
    {
        this.parameters = parameters;
    }

    public string Build()
    {
        string sessionsQuery = @$"SELECT * FROM virtual.data.preview.api.preview_api_counts_session_duration_metrics WHERE EyeSquareProjectID = '{this.parameters.IdentifierSet?.ProjectId}'";

        if (!string.IsNullOrWhiteSpace(this.parameters.IdentifierSet?.SubjectGroupId))
        {
            sessionsQuery += $" AND EyeSquareSubjectGroupID = '{this.parameters.IdentifierSet?.SubjectGroupId}'";
        }

        if (!string.IsNullOrWhiteSpace(this.parameters.SubjectId))
        {
            sessionsQuery += $" AND SubjectID = '{this.parameters.SubjectId}'";
        }

        if (!string.IsNullOrWhiteSpace(this.parameters.IdentifierSet?.TaskId))
        {
            sessionsQuery += $" AND TaskID = '{this.parameters.IdentifierSet?.TaskId}'";
        }

        return sessionsQuery;
    }
}
