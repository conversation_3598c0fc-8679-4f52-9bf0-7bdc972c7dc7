using Realeyes.PreView.API.EyeSquare.Model.Requests;
using Realeyes.PreView.API.EyeSquare.Services.Dremio.QueryParameters;
using Realeyes.PreView.Infrastructure.Services.Dremio.Extension;

namespace Realeyes.PreView.API.EyeSquare.Services.Dremio.Queries;

public class SubjectMetricsByTimeQuery : IDremioQuery
{
    private readonly SubjectMetricsQueryParameters parameters;

    public SubjectMetricsByTimeQuery(SubjectMetricsQueryParameters parameters)
    {
        this.parameters = parameters;
    }

    public string Build()
    {
        string timeSeriesQuery = @$"SELECT * FROM virtual.data.preview.api.preview_api_time_series_session_time_metrics WHERE EyeSquareProjectID = '{this.parameters.IdentifierSet?.ProjectId}'";

        if (!string.IsNullOrWhiteSpace(this.parameters.IdentifierSet?.SubjectGroupId))
        {
            timeSeriesQuery += $" AND EyeSquareSubjectGroupID = '{this.parameters.IdentifierSet?.SubjectGroupId}'";
        }

        if (!string.IsNullOrWhiteSpace(this.parameters.SubjectId))
        {
            timeSeriesQuery += $" AND SubjectID = '{this.parameters.SubjectId}'";
        }

        if (!string.IsNullOrWhiteSpace(this.parameters.IdentifierSet?.TaskId))
        {
            timeSeriesQuery += $" AND TaskID = '{this.parameters.IdentifierSet?.TaskId}'";
        }

        return timeSeriesQuery;
    }
}
