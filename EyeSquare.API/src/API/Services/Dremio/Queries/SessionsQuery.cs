using Realeyes.PreView.API.EyeSquare.Services.Dremio.QueryParameters;
using Realeyes.PreView.Infrastructure.Services.Dremio.Extension;

namespace Realeyes.PreView.API.EyeSquare.Services.Dremio.Queries
{
    public class SessionsQuery : IDremioQuery
    {
        private readonly SessionsQueryParameters parameters;

        public SessionsQuery(SessionsQueryParameters parameters)
        {
            this.parameters = parameters;
        }

        public string Build()
        {
            var listOfTestIds = QueryParameterHelper.GetListOfIds(this.parameters.TestIds);
            string sessionQuery = @$"SELECT s.id as Id FROM StudyDatabaseReadonly.StudyDatabase.dbo.Participant p JOIN StudyDatabaseReadonly.StudyDatabase.dbo.Session s ON s.ParticipantID = p.ID WHERE s.TestID IN ({listOfTestIds})";
                        

            var listOfSubjectIds = QueryParameterHelper.GetListOfIds(this.parameters.SubjectIds);
            if (!string.IsNullOrWhiteSpace(listOfSubjectIds))
            {
                sessionQuery += $" AND p.IdentityProviderKey in ({listOfSubjectIds})";
            }

            return sessionQuery;
        }
    }
}
