using Realeyes.PreView.API.EyeSquare.Services.Dremio.QueryParameters;
using Realeyes.PreView.Infrastructure.Services.Dremio.Extension;

namespace Realeyes.PreView.API.EyeSquare.Services.Dremio.Queries;

public class MediaMetricsBySessionQuery : IDremioQuery
{
    private readonly IdentifierSetParameters parameters;

    public MediaMetricsBySessionQuery(IdentifierSetParameters parameters)
    {
        this.parameters = parameters;
    }

    public string Build()
    {
        string sessionsQuery = @$"SELECT * FROM virtual.data.preview.api.preview_api_stats_counts_media_duration_metrics WHERE EyeSquareProjectID = '{this.parameters.ProjectId}'";

        if (!string.IsNullOrWhiteSpace(this.parameters.SubjectGroupId))
        {
            sessionsQuery += $" AND EyeSquareSubjectGroupID = '{this.parameters.SubjectGroupId}'";
        }

        if (!string.IsNullOrWhiteSpace(this.parameters.TaskId))
        {
            sessionsQuery += $" AND TaskID = '{this.parameters.TaskId}'";
        }

        return sessionsQuery;
    }
}
