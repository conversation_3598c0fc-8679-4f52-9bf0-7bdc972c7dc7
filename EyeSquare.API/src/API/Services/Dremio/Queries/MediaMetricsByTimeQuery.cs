using Realeyes.PreView.API.EyeSquare.Model.Requests;
using Realeyes.PreView.API.EyeSquare.Services.Dremio.QueryParameters;
using Realeyes.PreView.Infrastructure.Services.Dremio.Extension;

namespace Realeyes.PreView.API.EyeSquare.Services.Dremio.Queries;

public class MediaMetricsByTimeQuery : IDremioQuery
{
    private readonly IdentifierSetParameters parameters;

    public MediaMetricsByTimeQuery(IdentifierSetParameters parameters)
    {
        this.parameters = parameters;
    }

    public string Build()
    {
        string timeSeriesQuery = @$"SELECT * FROM virtual.data.preview.api.preview_api_time_series_media_time_metrics WHERE EyeSquareProjectID = '{this.parameters.ProjectId}'";

        if (!string.IsNullOrWhiteSpace(this.parameters.SubjectGroupId))
        {
            timeSeriesQuery += $" AND EyeSquareSubjectGroupID = '{this.parameters.SubjectGroupId}'";
        }

        if (!string.IsNullOrWhiteSpace(this.parameters.TaskId))
        {
            timeSeriesQuery += $" AND TaskID = '{this.parameters.TaskId}'";
        }

        timeSeriesQuery += @"ORDER BY EyeSquareProjectID, EyeSquareSubjectGroupID, TaskID, ElementID, ""second""";

        return timeSeriesQuery;
    }
}
