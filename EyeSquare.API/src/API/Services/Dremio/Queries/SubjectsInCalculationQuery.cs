using Realeyes.PreView.API.EyeSquare.Services.Dremio.QueryParameters;
using Realeyes.PreView.Infrastructure.Services.Dremio.Extension;

namespace Realeyes.PreView.API.EyeSquare.Services.Dremio.Queries;

public class SubjectsInCalculationQuery : IDremioQuery
{
    private readonly SubjectsInCalculationQueryParameters parameters;

    public SubjectsInCalculationQuery(SubjectsInCalculationQueryParameters parameters)
    {
        this.parameters = parameters;
    }

    public string Build()
    {
        string subjectsQuery = @$"SELECT DISTINCT SubjectID as SubjectId FROM virtual.data.preview.api.preview_api_counts_session_duration_metrics WHERE EyeSquareProjectID = '{this.parameters.IdentifierSet.ProjectId}'";

        if (!string.IsNullOrWhiteSpace(this.parameters.IdentifierSet.SubjectGroupId))
        {
            subjectsQuery += $" AND EyeSquareSubjectGroupID = '{this.parameters.IdentifierSet.SubjectGroupId}'";
        }

        if (!string.IsNullOrWhiteSpace(this.parameters.IdentifierSet.TaskId))
        {
            subjectsQuery += $" AND TaskID = '{this.parameters.IdentifierSet.TaskId}'";
        }

        var listOfIds = QueryParameterHelper.GetListOfIds(this.parameters.SubjectIds);
        if (!string.IsNullOrWhiteSpace(listOfIds))
        {
            subjectsQuery += $" AND SubjectID in ({listOfIds})";
        }

        return subjectsQuery;
    }
}
