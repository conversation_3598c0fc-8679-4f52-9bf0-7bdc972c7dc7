namespace Realeyes.PreView.API.EyeSquare.Services.Dremio.QueryParameters;

public class QueryParameterHelper
{
    public static string GetListOfIds<T>(IEnumerable<T> identifiers)
    {
        var listOfIds = identifiers?.Aggregate(
            string.Empty,
            (list, id) => id == null ? list : list += $"'{id}', ",
            list => list.LastIndexOf(", ") == list.Length - 2 ? list[..^2] : list);

        return listOfIds ?? string.Empty;
    }
}
