using Realeyes.PreView.API.EyeSquare.Services.CustomAttributes;

namespace Realeyes.PreView.API.EyeSquare.Services.DremioQueries.DataTransferObjects;

public class SubjectMetricsBySession : MetricsBySession
{
    [ExcludeFromAggregation]
    public string? SubjectID { get; set; }
    public int? exposures { get; set; }
    [ExcludeFromAggregation]
    public double? attentive_seconds { get; set; }
    [PropertyNameInAggregation("attentive_seconds")]
    public double? eyeson_seconds { get; set; }
    public double? happiness_seconds { get; set; }
    public double? confusion_seconds { get; set; }
    public double? contempt_seconds { get; set; }
    public double? disgust_seconds { get; set; }
    public double? surprise_seconds { get; set; }
    public double? negativity_seconds { get; set; }
    public double? reactions_seconds { get; set; }
    [PropertyNameInAggregation("attentive_seconds_from_duration")]
    public double? eyeson_seconds_from_duration { get; set; }
    [PropertyNameInAggregation("attentive_seconds_all_exposures")]
    public double? eyeson_seconds_all_exposures { get; set; }
    [ExcludeFromAggregation]
    public double? attentive_seconds_all_exposures { get; set; }
    [ExcludeFromAggregation]
    public double? attentive_seconds_from_duration { get; set; }
    public int? visible_seconds { get; set; }
    public double? happiness_seconds_all_exposures { get; set; }
    public double? confusion_seconds_all_exposures { get; set; }
    public double? contempt_seconds_all_exposures { get; set; }
    public double? disgust_seconds_all_exposures { get; set; }
    public double? surprise_seconds_all_exposures { get; set; }
    public double? negativity_seconds_all_exposures { get; set; }
    public double? reactions_seconds_all_exposures { get; set; }
    public int? visible_seconds_all_exposures { get; set; }
    public int? viewthrough { get; set; }
    [PropertyNameInAggregation("attentive_viewthrough")]
    public int? eyeson_viewthrough { get; set; }
    public double? inattentive_visible_seconds { get; set; }
    public double? inattentive_visible_seconds_all_exposures { get; set; }
}














