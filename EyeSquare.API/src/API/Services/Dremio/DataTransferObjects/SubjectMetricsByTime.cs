using Realeyes.PreView.API.EyeSquare.Services.CustomAttributes;

namespace Realeyes.PreView.API.EyeSquare.Services.DremioQueries.DataTransferObjects;

public class SubjectMetricsByTime : MetricsByTime
{
    [ExcludeFromAggregation]
    public string? SubjectID { get; set; }
    [ExcludeFromAggregation]
    public double? attentive { get; set; }
    [PropertyNameInAggregation("attentive")]
    public double? eyeson { get; set; }
    public double? happiness { get; set; }
    public double? confusion { get; set; }
    public double? contempt { get; set; }
    public double? disgust { get; set; }
    public double? surprise { get; set; }
    public double? negativity { get; set; }
    public double? reactions { get; set; }
    public int? exposure { get; set; }
    public int? visible { get; set; }
}
