using Realeyes.PreView.API.EyeSquare.Services.CustomAttributes;

namespace Realeyes.PreView.API.EyeSquare.Services.DremioQueries.DataTransferObjects
{
    public class MetricsByTime
    {
        [ExcludeFromAggregation]
        public int? AccountID { get; set; }
        [ExcludeFromAggregation]
        public string EyeSquareProjectID { get; set; } = string.Empty;
        [ExcludeFromAggregation]
        public string? EyeSquareSubjectGroupID { get; set; }
        [ExcludeFromAggregation]
        public string? TaskID { get; set; }
        [ExcludeFromAggregation]
        public string? ElementID { get; set; }
        [ExcludeFromAggregation]
        public int? second { get; set; }
    }
}
