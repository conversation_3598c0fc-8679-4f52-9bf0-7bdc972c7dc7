using Realeyes.PreView.API.EyeSquare.Services.CustomAttributes;

namespace Realeyes.PreView.API.EyeSquare.Services.DremioQueries.DataTransferObjects;

public class MediaMetricsBySession : MetricsBySession
{
    public double? visible_viewer_share_avg { get; set; }
    public double? visible_viewer_share_std { get; set; }
    public double? visible_viewer_share_max { get; set; }
    public double? happiness_viewer_share_avg { get; set; }
    public double? happiness_viewer_share_std { get; set; }
    public double? happiness_viewer_share_max { get; set; }
    [PropertyNameInAggregation("attentive_viewer_share_avg")]
    public double? eyeson_viewer_share_avg { get; set; }
    [PropertyNameInAggregation("attentive_viewer_share_std")]
    public double? eyeson_viewer_share_std { get; set; }
    [PropertyNameInAggregation("attentive_viewer_share_max")]
    public double? eyeson_viewer_share_max { get; set; }
    public double? confusion_viewer_share_avg { get; set; }
    public double? confusion_viewer_share_std { get; set; }
    public double? confusion_viewer_share_max { get; set; }
    public double? contempt_viewer_share_avg { get; set; }
    public double? contempt_viewer_share_std { get; set; }
    public double? contempt_viewer_share_max { get; set; }
    public double? disgust_viewer_share_avg { get; set; }
    public double? disgust_viewer_share_std { get; set; }
    public double? disgust_viewer_share_max { get; set; }
    public double? surprise_viewer_share_avg { get; set; }
    public double? surprise_viewer_share_std { get; set; }
    public double? surprise_viewer_share_max { get; set; }
    public double? negativity_viewer_share_avg { get; set; }
    public double? negativity_viewer_share_std { get; set; }
    public double? negativity_viewer_share_max { get; set; }
    public double? neutral_attention_viewer_share { get; set; }
    public double? neutral_attention_viewer_share_max { get; set; }
    public double? neutral_attention_viewer_share_std { get; set; }
    public double? neutral_eyeson_viewer_share_avg { get; set; }
    public double? neutral_eyeson_viewer_share_max { get; set; }
    public double? neutral_eyeson_viewer_share_std { get; set; }
    public double? reactions_viewer_share_avg { get; set; }
    public double? reactions_viewer_share_std { get; set; }
    public double? reactions_viewer_share_max { get; set; }
    public double? inattentive_visible_viewer_share_avg { get; set; }
    public double? inattentive_visible_viewer_share_std { get; set; }
    public double? inattentive_visible_viewer_share_max { get; set; }
    public double? not_visible_viewer_share_avg { get; set; }
    public double? not_visible_viewer_share_std { get; set; }
    public double? not_visible_viewer_share_max { get; set; }
    [PropertyNameInAggregation("attentive_seconds_avg")]
    public double? eyeson_seconds_avg { get; set; }
    [PropertyNameInAggregation("attentive_seconds_all_exposures_avg")]
    public double? eyeson_seconds_all_exposures_avg { get; set; }
    [PropertyNameInAggregation("attentive_seconds_from_duration_avg")]
    public double? eyeson_seconds_from_duration_avg { get; set; }
    [PropertyNameInAggregation("attentive_seconds_viewthrough_avg")]
    public double? eyeson_seconds_viewthrough_avg { get; set; }
    public double? happiness_seconds_avg { get; set; }
    public double? happiness_seconds_all_exposures_avg { get; set; }
    public double? confusion_seconds_avg { get; set; }
    public double? confusion_seconds_all_exposures_avg { get; set; }
    public double? contempt_seconds_avg { get; set; }
    public double? contempt_seconds_all_exposures_avg { get; set; }
    public double? disgust_seconds_avg { get; set; }
    public double? disgust_seconds_all_exposures_avg { get; set; }
    public double? surprise_seconds_avg { get; set; }
    public double? surprise_seconds_all_exposures_avg { get; set; }
    public double? negativity_seconds_avg { get; set; }
    public double? negativity_seconds_all_exposures_avg { get; set; }
    public double? percentage_with_confusion_seconds { get; set; }
    public double? percentage_with_contempt_seconds { get; set; }
    public double? percentage_with_disgust_seconds { get; set; }
    public double? percentage_with_eyeson_seconds { get; set; }
    public double? percentage_with_happiness_seconds { get; set; }
    public double? percentage_with_inattentive_visible_seconds { get; set; }
    public double? percentage_with_negativity_seconds { get; set; }
    public double? percentage_with_reactions_seconds { get; set; }
    public double? percentage_with_surprise_seconds { get; set; }
    public double? reactions_seconds_all_exposures_avg { get; set; }
    public double? reactions_seconds_avg { get; set; }
    public double? inattentive_visible_seconds_all_exposures_avg { get; set; }
    public double? inattentive_visible_seconds_avg { get; set; }
    public double? viewthrough_avg { get; set; }
    public int? exposure { get; set; }
    public int? exposures { get; set; }
    public int? second { get; set; }
    public double? visible_seconds_all_exposures_avg { get; set; }
    public double? visible_seconds_avg { get; set; }
    public int? views { get; set; }
}
