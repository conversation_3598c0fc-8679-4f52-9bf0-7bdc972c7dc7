using Realeyes.PreView.API.EyeSquare.Services.CustomAttributes;

namespace Realeyes.PreView.API.EyeSquare.Services.DremioQueries.DataTransferObjects;

public class MediaMetricsByTime : MetricsByTime
{
    public double? visible_viewer_share { get; set; }
    [ExcludeFromAggregation]
    public double? attentive_viewer_share { get; set; }
    public double? happiness_viewer_share { get; set; }
    [PropertyNameInAggregation("attentive_viewer_share")]
    public double? eyeson_viewer_share { get; set; }
    public double? confusion_viewer_share { get; set; }
    public double? contempt_viewer_share { get; set; }
    public double? disgust_viewer_share { get; set; }
    public double? surprise_viewer_share { get; set; }
    public double? reactions_viewer_share { get; set; }
    public double? inattentive_visible_viewer_share { get; set; }
    public double? not_visible_viewer_share { get; set; }
    public double? negativity_viewer_share { get; set; }
    public int? exposure { get; set; }
    public int? views { get; set; }
}
