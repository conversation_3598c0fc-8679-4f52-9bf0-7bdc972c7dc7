using Microsoft.Extensions.Options;
using Realeyes.PreView.API.EyeSquare.Model;
using Realeyes.PreView.API.EyeSquare.Services.FcpApi;
using Realeyes.PreView.API.EyeSquare.Services.FcpApi.DataTransferObjects;

namespace Realeyes.PreView.API.EyeSquare.Services;

public class FeatureCalculator : IFeatureCalculator
{
    private readonly IFeatureCalculationPlatform featureCalculationPlatform;
    private readonly FcpApiSettings fcpApiSettings;

    public FeatureCalculator(IFeatureCalculationPlatform featureApiService, IOptions<FcpApiSettings> options)
    {
        this.featureCalculationPlatform = featureApiService;
        this.fcpApiSettings = options.Value;
    }

    public async Task<Creatives> CalculateMediaClassifiersCreatives(SessionInfo sessionInfo)
    {
        FeatureCalculationRequest featureRequest = this.CreateFeatureCalculationRequest(sessionInfo);
        Creatives creatives = await CalculateMediaClassifiers(featureRequest).ConfigureAwait(false);

        return creatives;
    }

    private FeatureCalculationRequest CreateFeatureCalculationRequest(SessionInfo sessionInfo)
    {
        return new FeatureCalculationRequest
        {
            Dimensions = new List<CalculationRequestDimension>()
            {
                new CalculationRequestDimension
                {
                    SourceMedia = sessionInfo.IndentifierSets
                                             .SelectMany(i => i.Medias.Where(x => x.SourceMediaId != null && x.SourceMediaId.HasValue).Select(x => x.SourceMediaId!.Value))
                                             .Distinct()
                                             .ToList(),
                    Algorithm = new List<string>() { this.fcpApiSettings.Algorithm }
                }
            },
            Filters = new List<Filter>()
            {
                    new Filter()
                    {
                        SessionId = sessionInfo.SessionIds.ToList()
                    }
            },
            SkipCacheResults = 1,
        };
    }

    private async Task<Creatives> CalculateMediaClassifiers(FeatureCalculationRequest featureRequest)
    {
        
        var statsFeatureTask = this.CreateFeatureCalculationTask<IEnumerable<StatsMediaClassifier>>("stats-media_duration-classifiers-creative", featureRequest);
        var meanCountsFeatureTask = this.CreateFeatureCalculationTask<IEnumerable<MeanCountsMediaClassifier>>("mean_counts-media_duration-classifiers-creative", featureRequest);
        var timeSeriesMediaFeatureTask = this.CreateFeatureCalculationTask<IEnumerable<MeanMediaTimeClassifier>>("mean-media_time-classifiers-creative", featureRequest);

        await Task.WhenAll(statsFeatureTask, meanCountsFeatureTask, timeSeriesMediaFeatureTask).ConfigureAwait(false);

        var creatives = new Creatives
        (
            Stats: await statsFeatureTask.ConfigureAwait(false) ?? new List<StatsMediaClassifier>(),
            MeanCounts: await meanCountsFeatureTask.ConfigureAwait(false) ?? new List<MeanCountsMediaClassifier>(),
            MeanMediaTime: await timeSeriesMediaFeatureTask.ConfigureAwait(false) ?? new List<MeanMediaTimeClassifier>()
        );

        return creatives;
    }

    private Task<T?> CreateFeatureCalculationTask<T>(string featureName, FeatureCalculationRequest request) 
    {
        var feature = new FeatureCalculation { Name = featureName, Request = request };
        return this.featureCalculationPlatform.Calculate<T>(feature);
    }
}