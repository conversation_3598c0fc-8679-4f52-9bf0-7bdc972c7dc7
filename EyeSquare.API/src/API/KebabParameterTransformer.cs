using System.Text.RegularExpressions;

namespace Realeyes.PreView.API.EyeSquare
{
    public class KebabParameterTransformer : IOutboundParameterTransformer
    {
        private const string wordEnding = "([a-z])([A-Z])";
        private const string hyphen = "$1-$2";

        public string? TransformOutbound(object? value)
        {
            var stringToConvert = value?.ToString();
            var convertedValue = stringToConvert != null ? Regex.Replace(stringToConvert, wordEnding, hyphen).ToLower() : null;
            return convertedValue;
        }
    }
}
