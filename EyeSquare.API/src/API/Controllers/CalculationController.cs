using Asp.Versioning;
using Microsoft.AspNetCore.Mvc;
using Realeyes.PreView.API.EyeSquare.Model;
using Realeyes.PreView.API.EyeSquare.Model.Requests;
using Realeyes.PreView.API.EyeSquare.Model.Responses;
using Realeyes.PreView.API.EyeSquare.Services.Calculation;
using System.ComponentModel.DataAnnotations;

namespace Realeyes.PreView.API.EyeSquare.Controllers;

[ApiController]
[ApiVersion("1.0")]
[Route("eye-square/v{version:apiVersion}/projects/{projectId}/[controller]")]
public class CalculationController : ControllerBase
{
    private readonly IMeasurementCalculator measurementCalculator;

    public CalculationController(IMeasurementCalculator measurementCalculator)
    {
        this.measurementCalculator = measurementCalculator;
    }

    [HttpPost]
    [Route("media-measurements")]
    [Consumes("application/json")]
    [ProducesResponseType(typeof(MediaMeasurementCalculationResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> CalculateMediaMeasurements(
        [FromRoute]
        [RegularExpression(ValidationConstants.InputFormatValidator, ErrorMessage = ValidationConstants.InputFormatErrorMessage)]
        string projectId,
        [FromBody]
        MediaMeasurementCalculationRequest? request)
    {
        if (this.measurementCalculator == null)
        {
            return new JsonResult(new MediaMeasurementCalculationResponse());
        }

        var details = new MediaMeasurementCalculationDetails(
            projectId,
            request?.SubjectGroupId ?? string.Empty,
            request?.TaskId ?? string.Empty,
            request?.SubjectIds ?? Enumerable.Empty<string>(),
            request?.Duration);

        var response = await this.measurementCalculator.CalculateMediaMeasurements(details);

        return new JsonResult(response);
    }
}