using Asp.Versioning;
using Microsoft.AspNetCore.Mvc;
using Realeyes.PreView.API.EyeSquare.Model;
using Realeyes.PreView.API.EyeSquare.Model.Requests;
using Realeyes.PreView.API.EyeSquare.Model.Responses;
using Realeyes.PreView.API.EyeSquare.Services;
using System.ComponentModel.DataAnnotations;

namespace Realeyes.PreView.API.EyeSquare.Controllers;

[ApiController]
[ApiVersion("1.0")]
[Route("eye-square/v{version:apiVersion}/projects/{projectId}/[controller]")]
public class ReportController : ControllerBase
{
    private readonly IMeasurementCollector measurementCollector;

    public ReportController(IMeasurementCollector measurementCollector)
    {
        this.measurementCollector = measurementCollector;
    }

    [Obsolete]
    [HttpGet]
    [Route("media-measurements")]
    [ProducesResponseType(typeof(MediaMeasurementResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> GetMediaMeasurements([FromQuery][Required] MediaMeasurementRequest mediaMeasurementRequest)
    {
        IEnumerable<MediaMeasurement> response = await this.measurementCollector.CollectMediaMeasurements(mediaMeasurementRequest);
        var result = new MediaMeasurementResponse { Measurements = response };
        return new JsonResult(result);
    }

    [HttpGet]
    [Route("subject-measurements")]    
    [ProducesResponseType(typeof(SubjectMeasurementResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> GetSubjectMeasurements([FromQuery][Required] SubjectMeasurementRequest subjectMeasurementRequest)
    {
        IEnumerable <SubjectMeasurement> response = await this.measurementCollector.CollectSubjectMeasurements(subjectMeasurementRequest);
        var result = new SubjectMeasurementResponse { Measurements = response };
        return new JsonResult(result);
    }
}