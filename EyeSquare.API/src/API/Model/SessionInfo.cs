namespace Realeyes.PreView.API.EyeSquare.Model;

public class SessionInfo
{
    public IEnumerable<Identifiers> IndentifierSets { get; init; } = new List<Identifiers>();
    public IEnumerable<int> SessionIds { get; init; } = new List<int>(); 
    public IEnumerable<string> ExpectedSubjectIds { get; init; } = new List<string>();
}

public class Media
{
    public long? SourceMediaId { get; set; }
    public string? ElementId { get; set; }
}

public class Identifiers
{
    public int? TestId { get; set; }
    public string? ProjectId { get; set; }
    public string? SubjectGroupId { get; set; }
    public string? TaskId { get; set; }
    public IEnumerable<Media> Medias { get; set; } = new List<Media>();
}
