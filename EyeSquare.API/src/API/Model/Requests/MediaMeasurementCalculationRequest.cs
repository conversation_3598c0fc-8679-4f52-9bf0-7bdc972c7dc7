using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;

namespace Realeyes.PreView.API.EyeSquare.Model.Requests;

public class MediaMeasurementCalculationRequest
{
    [RegularExpression(ValidationConstants.InputFormatValidator, ErrorMessage = ValidationConstants.InputFormatErrorMessage)]
    public string SubjectGroupId { get; set; } = string.Empty;

    [RegularExpression(ValidationConstants.InputFormatValidator, ErrorMessage = ValidationConstants.InputFormatErrorMessage)]
    public string TaskId { get; set; } = string.Empty;

    [EnumerableRegularExpression(ValidationConstants.InputFormatValidator, ErrorMessage = ValidationConstants.InputFormatErrorMessage)]
    public IEnumerable<string> SubjectIds { get; set; } = new List<string>();

    [Range(0, long.MaxValue)]
    public long? Duration { get; set; }
}
