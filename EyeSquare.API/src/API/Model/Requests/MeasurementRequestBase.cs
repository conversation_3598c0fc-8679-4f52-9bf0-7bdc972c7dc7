using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;

namespace Realeyes.PreView.API.EyeSquare.Model.Requests;

public class MeasurementRequestBase
{
    [Required]
    [FromRoute]
    [RegularExpression(ValidationConstants.InputFormatValidator, ErrorMessage = ValidationConstants.InputFormatErrorMessage)]
    public string ProjectId { get; set; } = null!;

    [RegularExpression(ValidationConstants.InputFormatValidator, ErrorMessage = ValidationConstants.InputFormatErrorMessage)]
    public string? SubjectGroupId { get; set; }

    [RegularExpression(ValidationConstants.InputFormatValidator, ErrorMessage = ValidationConstants.InputFormatErrorMessage)]
    public string? TaskId { get; set; }
}
