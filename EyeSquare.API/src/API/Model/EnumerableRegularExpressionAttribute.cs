using System.ComponentModel.DataAnnotations;
namespace Realeyes.PreView.API.EyeSquare.Model
{
    public class EnumerableRegularExpressionAttribute : RegularExpressionAttribute
    {
        public EnumerableRegularExpressionAttribute(string pattern) 
            : base(pattern)
        {
        }

        public override bool IsValid(object? value)
        {
            return value is IEnumerable<string> items && items.All(item => base.IsValid(item));
        }
    }
}
