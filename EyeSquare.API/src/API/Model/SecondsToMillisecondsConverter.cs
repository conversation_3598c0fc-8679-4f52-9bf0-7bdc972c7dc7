using System.Text.Json;
using System.Text.Json.Serialization;

namespace Realeyes.PreView.API.EyeSquare.Model;

public class SecondsToMillisecondsConverter : JsonConverter<IEnumerable<int?>>
{
    public override IEnumerable<int?> Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        if (reader.TokenType == JsonTokenType.Null)
        {
            return Enumerable.Empty<int?>();
        }

        List<int?> values = new List<int?>();
        reader.Read();

        while (reader.TokenType != JsonTokenType.EndArray)
        {
            if (reader.TokenType == JsonTokenType.Null)
            {
                values.Add(null);
            }
            else
            {
                int valueInMilliseconds = reader.GetInt32();
                values.Add(valueInMilliseconds / 1000);
            }
            reader.Read();
        }
        
        return values;
    }

    public override void Write(Utf8JsonWriter writer, IEnumerable<int?> values, JsonSerializerOptions options)
    {
        writer.WriteStartArray();

        foreach (var value in values)
        {
            if (!value.HasValue)
            {
                writer.WriteNullValue();
            }
            else
            {
                writer.WriteNumberValue(value.Value * 1000);
            }
        }

        writer.WriteEndArray();
    }
}