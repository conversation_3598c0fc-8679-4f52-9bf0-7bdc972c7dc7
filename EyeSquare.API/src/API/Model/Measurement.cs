using System.Text.Json.Serialization;

namespace Realeyes.PreView.API.EyeSquare.Model;

public class Measurement
{    
    [JsonPropertyName("projectId")]
    public string? ProjectId { get; set; }

    [JsonPropertyName("subjectGroupId")]
    public string? SubjectGroupId { get; set; }

    [JsonPropertyName("taskId")]
    public string? TaskId { get; set; }

    [JsonPropertyName("elements")]
    public IEnumerable<MediaElement> Elements { get; set; } = Enumerable.Empty<MediaElement>();
}
