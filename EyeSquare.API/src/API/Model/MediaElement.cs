using System.Text.Json.Serialization;

namespace Realeyes.PreView.API.EyeSquare.Model;

public class MediaElement
{
    [JsonPropertyName("id")]
    public string? Id { get; set; }

    [JsonPropertyName("metrics")]
    public IEnumerable<Metric> Metrics { get; set; } = new List<Metric>();

    [JsonPropertyName("timeSeries")]
    public IEnumerable<TimeSeries> TimeSeries { get; set; } = new List<TimeSeries>();
}
