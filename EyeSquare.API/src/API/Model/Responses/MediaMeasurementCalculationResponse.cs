using System.Text.Json.Serialization;

namespace Realeyes.PreView.API.EyeSquare.Model.Responses;

public class MediaMeasurementCalculationResponse
{
    [JsonPropertyName("measurements")]
    public IEnumerable<MediaMeasurement> Measurements { get; set; } =  new List<MediaMeasurement>();

    [JsonPropertyName("subjectIdsUsedInCalculation")]
    public IEnumerable<string> SubjectIdsUsedInCalculation { get; set; } = new List<string>();
}
