using System.Text.Json.Serialization;

namespace Realeyes.PreView.API.EyeSquare.Model;

public class TimeSeries
{
    [JsonPropertyName("name")]
    public string? Name { get; set; }

    [JsonPropertyName("timeSpans")]
    [JsonConverter(typeof(SecondsToMillisecondsConverter))]
    public IEnumerable<int?> TimeSpans { get; set; } = new List<int?>();
    
    [JsonPropertyName("values")]
    public IEnumerable<object?> Values { get; set; } = new List<object?>();
}
