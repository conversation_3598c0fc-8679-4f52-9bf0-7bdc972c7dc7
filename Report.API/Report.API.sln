
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.11.35327.3
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "solutionItems", "solutionItems", "{C63479FF-4A80-4020-8C11-A2D183FE8D5B}"
	ProjectSection(SolutionItems) = preProject
		.gitignore = .gitignore
		README.md = README.md
		..\.github\workflows\report-api-ci-cd.yml = ..\.github\workflows\report-api-ci-cd.yml
		serverless.yaml = serverless.yaml
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "API", "src\API\API.csproj", "{AAECDDFF-3066-4EBC-A6AA-ADB3550F4617}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DataLayer", "..\PreView.Common\src\DataLayer\DataLayer.csproj", "{5462B70A-B192-4E1E-A63D-4F2B618CE643}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BusinessLayer", "..\PreView.Common\src\BusinessLayer\BusinessLayer.csproj", "{85211129-08A4-4CE4-A72F-76286B169843}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Dremio", "..\PreViewPortal.API\src\Dremio\Dremio.csproj", "{CCFDFE38-CAA4-47BD-8923-455545DD1080}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "EntityFramework", "..\PreView.Common\src\DataProviders\EntityFramework\EntityFramework.csproj", "{C08F54E2-3774-4B86-A87E-D9D60132915F}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Postman Tests", "Postman Tests", "{0EFDC61E-22DD-4DCE-9B4B-56E91A18BDC9}"
	ProjectSection(SolutionItems) = preProject
		tests\postman\Reporting API - LIVE.postman_collection.json = tests\postman\Reporting API - LIVE.postman_collection.json
		tests\postman\Reporting API - STAGE.postman_collection.json = tests\postman\Reporting API - STAGE.postman_collection.json
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{AAECDDFF-3066-4EBC-A6AA-ADB3550F4617}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AAECDDFF-3066-4EBC-A6AA-ADB3550F4617}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AAECDDFF-3066-4EBC-A6AA-ADB3550F4617}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AAECDDFF-3066-4EBC-A6AA-ADB3550F4617}.Release|Any CPU.Build.0 = Release|Any CPU
		{5462B70A-B192-4E1E-A63D-4F2B618CE643}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5462B70A-B192-4E1E-A63D-4F2B618CE643}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5462B70A-B192-4E1E-A63D-4F2B618CE643}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5462B70A-B192-4E1E-A63D-4F2B618CE643}.Release|Any CPU.Build.0 = Release|Any CPU
		{85211129-08A4-4CE4-A72F-76286B169843}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{85211129-08A4-4CE4-A72F-76286B169843}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{85211129-08A4-4CE4-A72F-76286B169843}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{85211129-08A4-4CE4-A72F-76286B169843}.Release|Any CPU.Build.0 = Release|Any CPU
		{CCFDFE38-CAA4-47BD-8923-455545DD1080}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CCFDFE38-CAA4-47BD-8923-455545DD1080}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CCFDFE38-CAA4-47BD-8923-455545DD1080}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CCFDFE38-CAA4-47BD-8923-455545DD1080}.Release|Any CPU.Build.0 = Release|Any CPU
		{C08F54E2-3774-4B86-A87E-D9D60132915F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C08F54E2-3774-4B86-A87E-D9D60132915F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C08F54E2-3774-4B86-A87E-D9D60132915F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C08F54E2-3774-4B86-A87E-D9D60132915F}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{0EFDC61E-22DD-4DCE-9B4B-56E91A18BDC9} = {C63479FF-4A80-4020-8C11-A2D183FE8D5B}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {02C01F20-153B-40E2-8F9F-34BF609A950A}
	EndGlobalSection
EndGlobal
