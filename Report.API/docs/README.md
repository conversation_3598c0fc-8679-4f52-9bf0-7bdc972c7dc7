# Api documentation

This folder contains api doc reStructuredText/Markdown files(`source` folder)
that are compiled into html using [Sphinx](https://www.sphinx-doc.org/en/master/)

## Build

Setup your environment

You need `python` and `pip` (distributed with python like `npm` with `node`)

```bash
pip install sphinx==7.3.7
pip install myst-parser==3.0.1
pip install sphinxawesome-theme==5.2.0
pip install sphinxcontrib-video==0.2.1
```

Steps to fully regenerate the API documentation (use prompt from `PreViewPortal\Report.API\` folder):

```bash

rm -rf ./Docs/build/*
rm -rf ./Docs/doctrees/*
rm -rf ./Docs/html/*

sphinx-build -M html ./docs/source ./docs --write-all -D environment=stage

Git add the changes of the docs folder all files
```
OR
```bash
./rebuild_docs.sh stage # in the folder of the Report.API
```
OR (windows powershell)
```powershell
./rebuild_docs.cmd stage # in the folder of the Report.API
```


To check locally the changes in the documentation, open the file `docs/html/index.html` in a browser.

## conf.py file
`source/conf.py` is a python file to configure sphinx environment and custom translate logic.
see `initialize_substitutions` function how `-D environment=live` sphinx-build parameter is handled

## Deployment

Before deploying the document, make sure you use the correct Swagger url in the References.md. If you want to deploy the
live document, url need to be changed to point to the live Swagger document.

At the moment doc are deployed into api gateway and served by api static files middleware.
It makes it possible to test documentation when running api locally.
For future usage we should deploy api docs to s3 bucket(TBD)


```
aws s3 sync ./docs/html/ s3://realeyes-docs/preview-api/${stage}/docs/report-api --delete
```

