==================================================
Welcome to the Preview Reporting API Documentation
==================================================

The Preview Reporting API provides powerful tools and endpoints to access, analyze, and manage reporting data efficiently. This documentation is designed to help developers and technical teams seamlessly integrate with the API, understand its capabilities, and leverage its full potential.

Here’s what you’ll find:

- **Getting Started**: Learn how to authenticate, set up your environment, and begin making API requests.
- **Concepts**: Detailed documentation about the Reporting API.
- **API References**: OpenAPI Specification wtih available endpoints, parameters, and responses.
- **Changelog**: Stay up to date with the latest updates, improvements, and changes to the API.
- **Support**: Need help? Reach out to our support team at `Support <https://support.realeyesit.com/hc/en-gb/requests/new>`_.

Dive in to discover how the Preview Reporting API can streamline your data workflows and empower your applications.

.. toctree::
   :hidden:

   Getting Started <./Gettingstarted.md>
   Concepts <./Concepts.rst>
   API References <./References.md>
   Changelog <./Changelog.md>
   Support <https://support.realeyesit.com/hc/en-gb/requests/new>
