@import url("https://use.fontawesome.com/releases/v5.14.0/css/all.css"); /* Add font awesome icons */

.scroll-smooth {
    scroll-behavior: smooth;
}

#flex-searchbox {
    padding-top: 30px;
}

@media (min-width: 768px) {
    .md\:h-24 {
        height: 6rem;
    }
}

/*body {*/
/*    background-color: hsl(var(--background));*/
/*}*/

body {
    background-color: var(--background);

}

.bg-background {
    background-color: var(--background);
}

.text-foreground {
    color: var(--foreground);

}

@media (min-width: 768px) {
    .md\:h-\[calc\(100vh-3\.5rem\)\] {
        height: calc(90vh - 3.5rem);
    }
}

@media (min-width: 768px) {
    .md\:w-full {
        width: 108%;
    }
}

@media (min-width: 1024px) {
    .lg\:w-64 {
        width: 100%;
    }
}




#left-sidebar a {
    align-items: end;
    border-color: transparent;
    border-radius: calc(var(--radius) - 2px);
    border-width: 1px;
    display: flex;
    padding: .375rem .5rem;
    width: 100%;
    font-size: 13px;
    font-weight: 500;
    text-transform: uppercase;
    text-align: right;
}

#right-sidebar ul li a {
    color: hsl(var(--muted-foreground));
    display: inline-block;
    text-decoration-line: none;
    transition-duration: .15s;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(.4,0,.2,1);
    font-size: 13px;
    font-weight: 500;
    text-align: right;
}

#left-sidebar a.current {
    background-color: var(--accent);
    /*border-color: var(--border);*/
    border-width: 1px;
    color: var(--accent-foreground);
    font-weight: 500;
}

#left-sidebar a:hover {
    text-decoration-line: none;
    color: #27EABF;
}

/*a:hover {*/
/*    text-decoration-line: none;*/
/*    color: #27EABF !important;*/
/*}*/

#right-sidebar ul li a:hover {
    color: #27EABF;
}

#content a:hover {
    color: #27EABF;
}

#content {
    font-size: 16px;
    font-weight: 400;
    line-height: 1.5em;
    font-family: "Roboto", Sans-serif;
}

.contents, .toctree-wrapper {
    margin-top: 24px;
    font-size: 16px;
    font-weight: 400;
    line-height: 1.5rem;
    font-family: "Roboto", Sans-serif;
}

#content ul:not(.search)>li {
    margin-top: 0px;
}

#metricslist {
    list-style-type: none !important;
    line-height: 1.5em;
}

#content section>p {
    margin-top: 1.5rem;
    font-size: 16px;
    font-weight: 400;
    line-height: 1.5em;
    font-family: "Roboto", Sans-serif;
}

#content h1 {
    font-size: 34px;
    font-weight: 600;
    line-height: 43px;
    margin-bottom: .5rem;
    font-family: "Roboto", Sans-serif;
}

#footer a:hover {
    color: #27EABF;
}

reference .internal {
    text-align: right;
}




.swagger-button {
    font-family: "Roboto", Sans-serif;
    font-size: 18px;
    font-weight: 600 !important;
    text-transform: uppercase;
    line-height: 16px;
    fill: #273235;
    color: #273235;
    border-style: solid;
    border-width: 1px 1px 1px 1px;
    border-color: #273235;
    border-radius: calc(var(--radius) - 2px);
    padding: 16px 48px 16px 48px;
    box-shadow: none;
    text-decoration-line: none !important;
    background-color: var(--accent);
}

.swagger-button:hover {
    background-color: hsl(var(--accent-foreground));
    color: var(--background);
    /*color: #273235;*/
    /*border-color: #27EABF;*/
}

#left-sidebar a.expandable {
        justify-content: left;
}

nav.table li.toctree-l1:first-child a.reference.internal::before {
    content: '\f015'; /* Font Awesome home icon */
    font-family: "Font Awesome 5 Free"; /* This is the correct font-family */
    font-weight: 900; /* Font Awesome solid style */
    margin-right: 8px; /* Adjust spacing as needed */
}

nav.table li.toctree-l1:nth-child(2) a.reference.internal::before {
    content: '\f03a'; /* Font Awesome list icon */
    font-family: "Font Awesome 5 Free"; /* This is the correct font-family */
    font-weight: 900; /* Font Awesome solid style */
    margin-right: 8px; /* Adjust spacing as needed */
}

nav.table li.toctree-l1:nth-child(2) li.toctree-l2 a.reference.internal::before {
    content: '\f105'; /* Font Awesome arrow icon */
    font-family: "Font Awesome 5 Free"; /* This is the correct font-family */
    font-weight: 900; /* Font Awesome solid style */
    margin-right: 8px; /* Adjust spacing as needed */
}

nav.table li.toctree-l1:nth-child(3) a.reference.internal::before {
    content: '\f56c'; /* Font Awesome contract icon */
    font-family: "Font Awesome 5 Free"; /* This is the correct font-family */
    font-weight: 900; /* Font Awesome solid style */
    margin-right: 8px; /* Adjust spacing as needed */
}

nav.table li.toctree-l1:nth-child(4) a.reference.internal::before {
    content: '\f017'; /* Font Awesome clock icon */
    font-family: "Font Awesome 5 Free"; /* This is the correct font-family */
    font-weight: 900; /* Font Awesome solid style */
    margin-right: 8px; /* Adjust spacing as needed */
}

nav.table li.toctree-l1:nth-child(5) a.reference.external::before {
    content: '\f0e0'; /* Font Awesome envelope icon */
    font-family: "Font Awesome 5 Free"; /* This is the correct font-family */
    font-weight: 900; /* Font Awesome solid style */
    margin-right: 8px; /* Adjust spacing as needed */
}


:root {
    --background: #F2F2F2;
    --foreground: #273235;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 47.4% 11.2%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 47.4% 11.2%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --accent: #D8D8D8;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 100% 50%;
    --destructive-foreground: 210 40% 98%;
    --ring: 215 20.2% 65.1%;
    --radius: 0.5rem;
}
.dark {
    --background: #273235;
    --foreground: #F2F2F2;
    --muted: 223 47% 11%;
    --muted-foreground: 215.4 16.3% 56.9%;
    --accent: #5F6368;
    --accent-foreground: 210 40% 98%;
    --popover: 224 71% 4%;
    --popover-foreground: 215 20.2% 65.1%;
    --border: 216 34% 17%;
    --input: 216 34% 17%;
    --card: 224 71% 4%;
    --card-foreground: 213 31% 91%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 1.2%;
    --secondary: 222.2 47.4% 11.2%;
    --secondary-foreground: 210 40% 98%;
    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;
    --ring: 216 34% 17%;
    --radius: 0.5rem;
}
