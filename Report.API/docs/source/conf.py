# Configuration file for the Sphinx documentation builder.
#
# For the full list of built-in configuration values, see the documentation:
# https://www.sphinx-doc.org/en/master/usage/configuration.html

# -- Project information -----------------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/configuration.html#project-information

from sphinxawesome_theme.postprocess import Icons
from sphinx.writers.html import HTMLTranslator
from docutils import nodes
from docutils.nodes import Element
from datetime import datetime


project = 'Preview Reporting API'
author = 'RealEyes'
release = '1.0.0'
version = '1.0.0'
domain = 'preview-api.realeyesit.com'

# -- General configuration ---------------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/configuration.html#general-configuration

extensions = ['myst_parser', 'sphinxcontrib.video']

myst_enable_extensions = [
    "amsmath",
    "attrs_inline",
    "colon_fence",
    "deflist",
    "dollarmath",
    "fieldlist",
    "html_admonition",
    "html_image",
    #"linkify",
    "replacements",
    "smartquotes",
    "strikethrough",
    "substitution",
    "tasklist",
]

html_context = {'version': 'Sphinx 7.3.7', 'css_timestamp': str(int(datetime.now().timestamp()))}

templates_path = ['_templates']
exclude_patterns = []

myst_substitutions = {    
    "environment": '',
}

# -- Options for HTML output -------------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/configuration.html#options-for-html-output

html_theme = "sphinxawesome_theme"
html_static_path = ['_static']
html_favicon = 'favicon.ico'
html_title = 'Preview Reporting API'
html_short_title = 'Preview Reporting API'


html_theme_options = {
    "logo_light": "images/realeyes_logo_solid_black_white-fill.svg",
    "logo_dark": "images/realeyes_logo_solid_white_black_fill.svg"
}

source_suffix = {
    '.rst': 'restructuredtext',
    '.txt': 'markdown',
    '.md': 'markdown',
    '.MD': 'markdown',
}

html_sidebars = {
  "**": ["sidebar_main_nav_links.html", "sidebar_toc.html"]
}

html_permalinks_icon = Icons.permalinks_icon


class PatchedHTMLTranslator(HTMLTranslator):

    def visit_reference(self, node: Element) -> None:
        atts = {'class': 'reference'}
        if node.get('internal') or 'refuri' not in node:
            atts['class'] += ' internal'
        else:
            atts['class'] += ' external'
            # ---------------------------------------------------------
            # Customize behavior (open in new tab, secure linking site)
            atts['target'] = '_blank'
            atts['rel'] = 'noopener noreferrer'
            # ---------------------------------------------------------
        if 'refuri' in node:
            atts['href'] = node['refuri'] or '#'
            if self.settings.cloak_email_addresses and atts['href'].startswith('mailto:'):
                atts['href'] = self.cloak_mailto(atts['href'])
                self.in_mailto = True
        else:
            assert 'refid' in node, \
                'References must have "refuri" or "refid" attribute.'
            atts['href'] = '#' + node['refid']
        if not isinstance(node.parent, nodes.TextElement):
            assert len(node) == 1 and isinstance(node[0], nodes.image)
            atts['class'] += ' image-reference'
        if 'reftitle' in node:
            atts['title'] = node['reftitle']
        if 'target' in node:
            atts['target'] = node['target']
        self.body.append(self.starttag(node, 'a', '', **atts))

        if node.get('secnumber'):
            self.body.append(('%s' + self.secnumber_suffix) %
                             '.'.join(map(str, node['secnumber'])))

base_api_urls = {
    "live": "https://preview-api.realeyesit.com",
    "stage": "https://stage-preview-api.realeyesit.com"
}

def initialize_substitutions(_, config):
    environment = config['environment']

    myst_substitutions['environment'] = environment
    myst_substitutions['baseUrl'] = base_api_urls[environment]

def setup(app):
    app.add_config_value('environment', '', 'env')
    app.connect('config-inited', initialize_substitutions)

    app.set_translator('html', PatchedHTMLTranslator)

