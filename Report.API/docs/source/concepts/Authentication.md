# Authentication

The Report API is secured and protected by OAuth2 authentication.
To access the API you will need to obtain a JWT access token using [OAuth 2.0 Client Credentials Grant](https://oauth.net/2/grant-types/client-credentials/).

To get the token you will need to call the https://preview-api.realeyesit.com/token endpoint with your client credentials and proper grant type:
```
curl -X POST "{BASE_URL}/token" \
-H "Content-Type: application/x-www-form-urlencoded" \
-d "grant_type=client_credentials&client_id=xxxxxxxxxx&client_secret=xxxxxxxxxx"
```

The access token in the response is valid for 1 hour, and the key should be passed with every request as part of the `Authorization` request header:
`Authorization: Bearer <token>`