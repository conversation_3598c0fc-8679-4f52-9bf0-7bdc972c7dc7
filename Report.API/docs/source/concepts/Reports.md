# Reports

A Report contains analytics data, including:
- Aggregated metric data.
- Aggregated norms data.
- Time series data for all media within the AdSet.

AdSets serve as logical groupings of SourceMedias within an ordered project. Each AdSet is uniquely identified by an AdSetKey, which customers can use to request reports. These keys can be obtained either:
- From the Report API `/v1/reports/search` endpoint.
- From the Project API response after project creation.
- From the Operations Team for manually created AdSets.

A MediaKey is a unique identifier for media uploaded by customers. It can be used optionally to filter reports within an AdSet, but by default, reports include data for all media associated with the AdSet. If MediaKeys are not user-defined, they can also be acquired from the Operations Team.