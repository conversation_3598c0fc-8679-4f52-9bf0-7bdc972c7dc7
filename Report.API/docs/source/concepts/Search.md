# Search

The API allows users to search for reports based on specified filters, which are combined using an AND logical operator. Filters support the following behaviors:
- String filters: Enable pattern matching.
- Integer and date filters: Check for equality.

Search results are paginated and grouped by AdSets, including all associated media (even if they don’t match the applied filters). Additionally, users can retrieve a specific report using its AdSetKey via the `/v1/reports` endpoint.