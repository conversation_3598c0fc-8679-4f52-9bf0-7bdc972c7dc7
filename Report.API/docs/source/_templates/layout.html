{# layout.html #}
{# Import the theme's layout. #}
{% extends '!layout.html' %}


{# Add custom CSS to the theme. #}
{% set css_files = css_files + ['_static/custom.css?time=' + css_timestamp] %}

{# Add custom fonts to the theme #}
{% block extrahead %}
<link href="https://fonts.googleapis.com/css2?family=Roboto+Serif:ital,opsz,wght@0,8..144,100..900;1,8..144,100..900&display=swap"
      rel="stylesheet">
<link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.13.0/css/all.css">
{% endblock %}

{% block header %}
{%- block mobile_menu %}
{%- if sidebars|length >0 -%}
<button
        class="inline-flex items-center justify-center h-10 px-0 py-2 mr-2 text-base font-medium transition-colors rounded-md hover:text-accent-foreground hover:bg-transparent md:hidden"
        type="button" @click="showSidebar = true">
    <svg xmlns="http://www.w3.org/2000/svg" height="24" width="24" viewBox="0 96 960 960" aria-hidden="true"
         fill="currentColor">
        <path
                d="M152.587 825.087q-19.152 0-32.326-13.174t-13.174-32.326q0-19.152 13.174-32.326t32.326-13.174h440q19.152 0 32.326 13.174t13.174 32.326q0 19.152-13.174 32.326t-32.326 13.174h-440Zm0-203.587q-19.152 0-32.326-13.174T107.087 576q0-19.152 13.174-32.326t32.326-13.174h320q19.152 0 32.326 13.174T518.087 576q0 19.152-13.174 32.326T472.587 621.5h-320Zm0-203.587q-19.152 0-32.326-13.174t-13.174-32.326q0-19.152 13.174-32.326t32.326-13.174h440q19.152 0 32.326 13.174t13.174 32.326q0 19.152-13.174 32.326t-32.326 13.174h-440ZM708.913 576l112.174 112.174q12.674 12.674 12.674 31.826t-12.674 31.826Q808.413 764.5 789.261 764.5t-31.826-12.674l-144-144Q600 594.391 600 576t13.435-31.826l144-144q12.674-12.674 31.826-12.674t31.826 12.674q12.674 12.674 12.674 31.826t-12.674 31.826L708.913 576Z"/>
    </svg>
    <span class="sr-only">Toggle navigation menu</span>
</button>
{%- endif -%}
{%- endblock mobile_menu %}
{% endblock %}


{% block toc %}
{#- Template for the on-page TOC -#}
<aside id="right-sidebar" class="hidden text-sm xl:block">
    <div class="sticky top-16 -mt-10 max-h-[calc(100vh-5rem)] overflow-y-auto pt-6 space-y-2">
        {%- block toc_before %}{%- endblock -%}
        <p class="font-medium">On this page</p>
        {{ toc }}
        {%- block toc_after %}{%- endblock -%}
    </div>
</aside>
{% endblock %}


{% block sidebar %}
{#- Template for the sidebar. -#}

{%- set only_main_nav = sidebars == ["sidebar_main_nav_links.html"] -%}

{%- if not only_main_nav and sidebars|length > 0 -%}
<aside id="left-sidebar"
       class="fixed inset-y-0 left-0 md:top-50 z-50 md:z-30 bg-background md:bg-transparent transition-all duration-100 -translate-x-full md:translate-x-0 ml-0 p-6 md:p-0 md:-ml-2 md:h-[calc(100vh-3.5rem)] w-5/6 md:w-full shrink-0 overflow-y-auto border-r border-border md:sticky"
       :aria-hidden="!showSidebar" :class="{ 'translate-x-0': showSidebar }">
    {%- else %}
    <aside id="left-sidebar"
           class="fixed inset-y-0 left-0 md:hidden z-50 bg-background transition-all duration-100 -translate-x-full ml-0 p-6 w-5/6 shrink-0 overflow-y-auto border-r border-border"
           :aria-hidden="!showSidebar" :class="{ 'translate-x-0': showSidebar }">
        {%- endif %}

        <div class="relative overflow-hidden md:overflow-auto my-4 md:my-0 h-[calc(100vh-8rem)] md:h-auto">
            <div class="overflow-y-auto h-full w-full relative pr-6">

                <div class="flex items-center justify-between flex-1 space-x-2 sm:space-x-4 md:justify-end">
                    <a href="{{ pathto(master_doc) }}" class="flex items-center mr-6">
                        {%- if logo_url %}
                        <img height="100" width="30" class="mr-2 dark:invert" src="{{ logo_url }}" alt="Logo"/>
                        {%- endif -%}
                        {%- if theme_logo_dark and not logo_url %}
                        <img width="64" height="30" class="mr-2 hidden dark:block"
                             src="{{ pathto('_static/' + theme_logo_dark, 1) }}" alt="Logo"/>
                        {%- endif -%}
                        {%- if theme_logo_light and not logo_url %}
                        <img width="64" height="30" class="mr-2 dark:hidden"
                             src="{{ pathto('_static/' + theme_logo_light, 1) }}" alt="Logo"/>
                        {%- endif -%}

                    </a>
                    <button @click="darkMode = darkMode === 'light' ? 'dark' : 'light'"
                            class="relative inline-flex items-center justify-center px-0 text-sm font-medium transition-colors rounded-md hover:bg-accent hover:text-accent-foreground h-9 w-9"
                            type="button"
                            aria-label="Color theme switcher">
                        <svg xmlns="http://www.w3.org/2000/svg" height="24" width="24" viewBox="0 96 960 960"
                             fill="currentColor"
                             class="absolute transition-all scale-100 rotate-0 dark:-rotate-90 dark:scale-0">
                            <path
                                    d="M480 685q45.456 0 77.228-31.772Q589 621.456 589 576q0-45.456-31.772-77.228Q525.456 467 480 467q-45.456 0-77.228 31.772Q371 530.544 371 576q0 45.456 31.772 77.228Q434.544 685 480 685Zm0 91q-83 0-141.5-58.5T280 576q0-83 58.5-141.5T480 376q83 0 141.5 58.5T680 576q0 83-58.5 141.5T480 776ZM80 621.5q-19.152 0-32.326-13.174T34.5 576q0-19.152 13.174-32.326T80 530.5h80q19.152 0 32.326 13.174T205.5 576q0 19.152-13.174 32.326T160 621.5H80Zm720 0q-19.152 0-32.326-13.174T754.5 576q0-19.152 13.174-32.326T800 530.5h80q19.152 0 32.326 13.174T925.5 576q0 19.152-13.174 32.326T880 621.5h-80Zm-320-320q-19.152 0-32.326-13.174T434.5 256v-80q0-19.152 13.174-32.326T480 130.5q19.152 0 32.326 13.174T525.5 176v80q0 19.152-13.174 32.326T480 301.5Zm0 720q-19.152 0-32.326-13.17Q434.5 995.152 434.5 976v-80q0-19.152 13.174-32.326T480 850.5q19.152 0 32.326 13.174T525.5 896v80q0 19.152-13.174 32.33-13.174 13.17-32.326 13.17ZM222.174 382.065l-43-42Q165.5 327.391 166 308.239t13.174-33.065q13.435-13.674 32.587-13.674t32.065 13.674l42.239 43q12.674 13.435 12.555 31.706-.12 18.272-12.555 31.946-12.674 13.674-31.445 13.413-18.772-.261-32.446-13.174Zm494 494.761-42.239-43q-12.674-13.435-12.674-32.087t12.674-31.565Q686.609 756.5 705.38 757q18.772.5 32.446 13.174l43 41.761Q794.5 824.609 794 843.761t-13.174 33.065Q767.391 890.5 748.239 890.5t-32.065-13.674Zm-42-494.761Q660.5 369.391 661 350.62q.5-18.772 13.174-32.446l41.761-43Q728.609 261.5 747.761 262t33.065 13.174q13.674 13.435 13.674 32.587t-13.674 32.065l-43 42.239q-13.435 12.674-31.706 12.555-18.272-.12-31.946-12.555Zm-495 494.761Q165.5 863.391 165.5 844.239t13.674-32.065l43-42.239q13.435-12.674 32.087-12.674t31.565 12.674Q299.5 782.609 299 801.38q-.5 18.772-13.174 32.446l-41.761 43Q231.391 890.5 212.239 890t-33.065-13.174ZM480 576Z"/>
                        </svg>
                        <svg xmlns="http://www.w3.org/2000/svg" height="24" width="24" viewBox="0 96 960 960"
                             fill="currentColor"
                             class="absolute transition-all scale-0 rotate-90 dark:rotate-0 dark:scale-100">
                            <path
                                    d="M480 936q-151 0-255.5-104.5T120 576q0-138 90-239.5T440 218q25-3 39 18t-1 44q-17 26-25.5 55t-8.5 61q0 90 63 153t153 63q31 0 61.5-9t54.5-25q21-14 43-1.5t19 39.5q-14 138-117.5 229T480 936Zm0-80q88 0 158-48.5T740 681q-20 5-40 8t-40 3q-123 0-209.5-86.5T364 396q0-20 3-40t8-40q-78 32-126.5 102T200 576q0 116 82 198t198 82Zm-10-270Z"/>
                        </svg>
                    </button>
                </div>

                {%- if docsearch or hasdoc('search') %}
                <div class="flex items-center justify-between flex-1 space-x-2 sm:space-x-4 md:justify-end"
                     id="flex-searchbox">
                    {%- include "searchbox.html" %}
                </div>
                {%- endif %}


                {%- for section in sidebars %}
                {%- include section %}
                {%- endfor %}
            </div>
        </div>
        <button type="button" @click="showSidebar = false"
                class="absolute md:hidden right-4 top-4 rounded-sm opacity-70 transition-opacity hover:opacity-100">
            <svg xmlns="http://www.w3.org/2000/svg" height="24" width="24" viewBox="0 96 960 960" fill="currentColor"
                 stroke="none" class="h-4 w-4">
                <path
                        d="M480 632 284 828q-11 11-28 11t-28-11q-11-11-11-28t11-28l196-196-196-196q-11-11-11-28t11-28q11-11 28-11t28 11l196 196 196-196q11-11 28-11t28 11q11 11 11 28t-11 28L536 576l196 196q11 11 11 28t-11 28q-11 11-28 11t-28-11L480 632Z"/>
            </svg>
        </button>
    </aside>

    {% endblock %}

    {% block footer %}
    <div class="container flex flex-col items-center justify-between gap-4 md:h-24 md:flex-row" id="footer">
        <div class="flex flex-col items-center gap-4 px-8 md:flex-row md:gap-2 md:px-0">
            <p class="text-sm leading-loose text-center text-muted-foreground md:text-left">© <span id="year"></span>,
                RealEyes Built with
                <a class="font-medium underline underline-offset-4" href="https://www.sphinx-doc.org"
                   rel="noreferrer">{{ version }}</a></p>
        </div>
        <div>
            {%- if theme_logo_dark and not logo_url %}
            <iframe src="https://realeyesit-pv.betteruptime.com/badge?theme=dark"  class="mr-2 hidden dark:block" width="250" height="30"
                    frameborder="0" scrolling="no" style="color-scheme: normal"></iframe>
            {%- endif -%}
            {%- if theme_logo_light and not logo_url %}
            <iframe src="https://realeyesit-pv.betteruptime.com/badge?theme=light" class="mr-2 dark:hidden" width="250" height="30"
                    frameborder="0" scrolling="no" style="color-scheme: normal"></iframe>
            {%- endif -%}
        </div>
        <div class="flex flex-col items-center gap-4 px-8 md:flex-row md:gap-2 md:px-0">
            <p class="text-sm leading-loose text-center text-muted-foreground md:text-right">
                <a class="font-medium underline underline-offset-4" href="https://www.realeyesit.com/terms-of-service/"
                   rel="noopener noreferrer" target="_blank">Terms of Services</a> and <a
                    class="font-medium underline underline-offset-4" href="https://www.realeyesit.com/privacy-policy/"
                    rel="noopener noreferrer" target="_blank">Privacy Policy</a></p>
        </div>
    </div>
    <script>
        document.querySelector("#year").textContent = new Date().getFullYear();
    </script>
    {% endblock %}

