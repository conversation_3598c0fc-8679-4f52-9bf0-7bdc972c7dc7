# Getting Started 

Welcome to the documentation for the Report API for Preivew. 

PreView is Realeyes’ advanced creative testing platform designed to assess the effectiveness of video ads. It provides pre-market evaluations of ad performance using AI-driven attention and reaction analysis, combined with survey insights and benchmarks, to predict brand and sales outcomes.

The Report API is to part of the [Preview - Creative Testing](https://www.realeyesit.com/preview/) solutions.
Our partners can integrate this Report API into their automated processes in order to search and query ad performance reports.

For more detailes, check [Concepts](Concepts) section!

## API Test URL
You can interact with the API and test its endpoints using the following URL:
-  {{ '[{0}/v1/reports]({0}/v1/reports)'.format(baseUrl)}}

## Authentication

The Report API is secured and protected by OAuth2 authentication.
To access the API you will need to obtain a JWT access token using [OAuth 2.0 Client Credentials Grant](https://oauth.net/2/grant-types/client-credentials/).

### Authentication example

**Endpoint**

`POST /token`

**Use Case**

Get an access token that should be passed with every request as part of the `Authorization` request header.

 **Request Example**
```
curl -X POST "{BASE_URL}/token" \
-H "Content-Type: application/x-www-form-urlencoded" \
-d "grant_type=client_credentials&client_id=xxxxxxxxxx&client_secret=xxxxxxxxxx"
```

**Request Fields**
| Field	        | Type   | Required | Description                                                                        |
| ------------- | ------ | -------- | ---------------------------------------------------------------------------------- |
| grant_type    | string | Yes      | The grant type for token issuance. For this flow, it must be "client_credentials". |
| client_id     | string | Yes      | The client ID provided during application registration.                            |
| client_secret | string | Yes      | The client secret provided during application registration.                        |

**Response Example (200 Success)**
```
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
  "expires_in": 3600,
  "token_type": "Bearer"
}
```

**Response Fields**
| Field        | Type    | Description                                                                  |
| ------------ | ------- | ---------------------------------------------------------------------------- |
| access_token | string  | The token to include in the Authorization header for authenticated requests. |
| expires_in   | integer | The time (in seconds) until the token expires.                               |
| token_type   | string  | The type of the issued token. For this API, it will always be "Bearer".      |

**Common Errors**
| Status Code | Description            |
| ----------- | -----------------------|
| 400         | Bad request.           |
| 500         | Internal server error. |

## Endpoints Overview
1. **Search Reports:** Use the `PUT /v1/reports/search` endpoint to search for reports using filters and retrieve a paginated list of results.
2. **Retrieve Report by AdSetKey:** Use the `GET /v1/reports` endpoint to fetch details of a specific report using its unique identifier - `AdSetKey`.

## Example Scenarios

### 1. Searching for Reports

**Endpoint**

`PUT /v1/reports/search`

**Use Case:**

Search for reports by filtering based on properties such as `adSetKey`, `adSetName`, `sourceMediaKey`, `duration`, and more.

**Request Example**
```
curl -X PUT "{BASE_URL}/v1/reports/search" \
-H "Content-Type: application/json" \
-H "Authorization: Bearer <your_access_token>" \
-d '{
  "adSetKey": "adset123",
  "adSetName": "Campaign A",
  "sourceMediaKey": "media123",
  "duration": 60,
  "creationDate": "2025-01-01",
  "page": 1,
  "pageSize": 10
}'
```

**Request Body**
| Field          | Type    | Required | Description                               |
| -------------- | ------- | -------- | ----------------------------------------- |
| adSetKey       | string  | No       | Filters reports by a unique AdSet key.    |
| adSetName      | string  | No       | Filters reports by AdSet name             |
| sourceMediaKey | string  | No       | Filters reports by source media key.      |
| duration       | integer | No       | Filters reports by duration in seconds.   |
| creationDate   | string  | No       | Filters reports created after this date.  |
| page           | integer | No       | Specifies the page number for pagination. |
| pageSize       | integer | No       | Specifies the number of results per page. |

**Response Example (200 Success)**
```
{
  "numberOfReports": 2,
  "currentPage": 0,
  "currentPageSize": 20,
  "reportIdentifiers": [
    {
      "adSetKey": "adset123",
      "adSetName": "Campaign A",
      "creationDate": "2025-01-01T12:00:00Z",
      "medias": [
        {
          "sourceMediaKey": "media123",
          "sourceMediaName": "Video A",
          "duration": 120
        }
      ]
    },
    {
      "adSetKey": "adset124",
      "adSetName": "Campaign B",
      "creationDate": "2025-01-02T14:00:00Z",
      "medias": []
    }
  ]
}

```
> Notes:
> - `AdSetKey`**:** This parameter can be used to retrieve detailed report information through `/v1/reports` endpoint.
> - `medias` **field in the response:** Media-related fields in the request (e.g., `sourceMediaKey`, `duration`) are used solely for filtering the reports. However, the response includes all associated media for each report, even those that do not match the provided filters.
> - **Pagination:** It uses zero-based indexing. Default values are: 
>   - `"currentPagge": 0` 
>   -  `"currentPageSize": 20`
> - **Empty Request Behavior:** If the request body is empty, the API will list all reports available to the client associated with the provided clientId (extracted from the JWT access token).

**Common Errors**
| Status Code | Description                                        |
| ----------- | -------------------------------------------------- |
| 400         | Invalid request body.                              |
| 401         | Unauthorized. Check your authentication.           |
| 403         | Forbidden. You don’t have access to this resource. |
| 500         | Internal server error.                             |

### 2. Retrieving a Specific Report by AdSetKey

**Endpoint**

`GET /v1/reports`

**Use Case**

Fetch the details of a report using its unique `AdSetKey`.
- Where to find `AdSetKey`: It is included in the response of the /v1/reports/search endpoint.

**Request Example**
```
curl -X GET "{BASE_URL}/v1/reports?AdSetKey=adset123" \
-H "Content-Type: application/json"
-H "Authorization: Bearer <your_access_token>" \
```

**Query Parameters**
| Parameter | Type   | Required | Description                                       |
| --------- | ------ | -------- | ------------------------------------------------- |
| AdSetKey  | string | Yes      | The unique key of the AdSet you want to retrieve. |

**Response Example (200 Success)**
```
{
  "data": [
    {
      "adSetKey": "adset123",
      "elements": [
        {
          "mediaKey": "media123",
          "metrics": [
            {
              "name": "HappyAvg",
              "value": 0.0128
            },
            {
              "name": "HappyPeak",
              "value": 0.0222
            },
            {
              "name": "duration",
              "value": 10
            }
            ...
          ],
          "timeSeries": [
            {
              "name": "AllReactions",
              "timeSpans": [0, 1000, 2000, 3000, 4000, 5000, 6000, 7000, 8000, 9000],
              "values": [0.1429, 0.1328, 0.145, 0.1151, 0.1463, 0.14, 0.1531, 0.1139, 0.1508, 0.1023]
            },
            ...
          ]
        }
      ]
    }
  ]
}
```

**Common Errors**
| Status Code | Description                                        |
| ----------- | -------------------------------------------------- |
| 400         | Invalid request body.                              |
| 401         | Unauthorized. Check your authentication.           |
| 403         | Forbidden. You don’t have access to this resource. |
| 500         | Internal server error.                             |

## Postman Collection
For your convenience, a Postman collection has been exported that contain preconfigured API requests. You can import this collection into your [Postman application](https://www.postman.com/) to easily test the API endpoints.
- {{ '[Collection to import](/assets/ReportingAPI-{0}.postman_collection.json \"download\") '.format(environment) }}

### Importing Postman Collection
1. Open Postman.
2. Click the "Import" button in the top-left corner.
3. Choose the exported collection.
4. The collection will be imported into your Postman workspace.