
namespace Realeyes.PreView.API.Report.Model.Requests
{
    public class SearchRequest
    {
        public string? AdSetKey { get; set; }
        public string? AdSetName { get; set; }
        public string? SourceMediaName { get; set; }
        public string? SourceMediaKey { get; set; }
        public int? Duration { get; set; }
        public DateTimeOffset? CreationDate { get; set; }
        public int Page { get; set; } = 0;
        public int PageSize { get; set; } = 20;
    }
}
