using System.Text.Json.Serialization;

namespace Realeyes.PreView.API.Report.Model
{
    public class MediaElement
    {
        [JsonPropertyName("mediaKey")]
        public string? MediaKey { get; set; }

        [JsonPropertyName("metrics")]
        public IEnumerable<Metric> Metrics { get; set; } = new List<Metric>();

        [JsonPropertyName("norms")]
        [JsonIgnore]
        public IEnumerable<Metric> Norms { get; set; } = new List<Metric>();

        [JsonPropertyName("timeSeries")]
        public IEnumerable<TimeSeries> TimeSeries { get; set; } = new List<TimeSeries>();


        [JsonPropertyName("timeSeriesNorms")]
        [JsonIgnore]
        public IEnumerable<TimeSeries> TimeSeriesNorms { get; set; } = new List<TimeSeries>();
    }
}
