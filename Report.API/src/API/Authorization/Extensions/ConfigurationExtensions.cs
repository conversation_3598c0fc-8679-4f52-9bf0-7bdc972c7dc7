using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.IdentityModel.JsonWebTokens;

namespace Realeyes.PreView.API.Report.Authorization.Extensions;

public static class ConfigurationExtensions
{
    public static void AddJwtBearerAuthentication(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
                .AddJwtBearer(scheme =>
                {
                    RealeyesJwtAuthenticationSettings authenticationSettings = GetAuthenticationSettingss(configuration);

                    scheme.Authority = authenticationSettings.ValidIssuer;
                    scheme.TokenValidationParameters = new()
                    {
                        ValidIssuer = authenticationSettings.ValidIssuer,
                        ValidateIssuerSigningKey = true,
                        ValidateLifetime = true,
                        RequireExpirationTime = true,
                        RequireSignedTokens = true,
                        ValidateAudience = false,
                        ValidateIssuer = true,
                    };

                    scheme.Events = new JwtBearerEvents()
                    {
                        OnTokenValidated = context =>
                        {
                            if (context?.SecurityToken is not JsonWebToken jwtToken) { return Task.CompletedTask; }

                            jwtToken?.Claims?.MoveScopeToTargetPrincipal(context?.Principal);

                            return Task.CompletedTask;
                        }
                    };
                });

    }

    public static void AddAccountAuthorization(this IServiceCollection services)
    {
        services.AddAuthorization(options =>
        {
            options.AddPolicy("AccountPolicy", policy => policy.Requirements.Add(new AccountRequirement()));
        });

        services.AddScoped<IAccountAuthorizer, AccountAuthorizer>();
        services.AddScoped<IAuthorizationHandler, AccountAuthorizationHandler>();
    }

    private static RealeyesJwtAuthenticationSettings GetAuthenticationSettingss(IConfiguration configuration)
    {
        var jwtConfiguration = configuration.GetSection(RealeyesJwtAuthenticationSettings.SectionName) ??
                               throw new Exception($"Could not find section in the configuration called [{RealeyesJwtAuthenticationSettings.SectionName}].");

        var jwtSettings = new RealeyesJwtAuthenticationSettings();
        jwtConfiguration.Bind(jwtSettings);

        return jwtSettings;
    }
}
