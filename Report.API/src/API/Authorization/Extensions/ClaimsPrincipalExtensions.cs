using System.Security.Claims;

namespace Realeyes.PreView.API.Report.Authorization.Extensions;

public static class ClaimsPrincipalExtensions
{
    private const string AccountIdsClaimName = "accountIds";

    public static IEnumerable<int> GetAccountIds(this IEnumerable<Claim> claims)
    {
        IEnumerable<int> accountIds = claims?.Where(cliam => cliam?.Type == AccountIdsClaimName)
                                            ?.SelectMany(claim => claim.Value.Split(","))
                                            ?.Select(idAsString => int.TryParse(idAsString, out int id) ? id : -1)
                                            ?.Where(id => id != -1)
                                            ?? Enumerable.Empty<int>();
        return accountIds;
    }

    public static void MoveScopeToTargetPrincipal(this IEnumerable<Claim> claims, ClaimsPrincipal? targetPrincipal)
    {
        var accountIds = claims?.Where(c => c.Type == "scope")
                               ?.SelectMany(c => c.Value.Split(' '))
                               ?.Select(s => s.Split(':').LastOrDefault())
                               ?.Where(accountId => !string.IsNullOrEmpty(accountId)) ?? Enumerable.Empty<string>();

        if (targetPrincipal?.Identity is not ClaimsIdentity claimsIdentity) { return; }

        claimsIdentity.AddClaim(new Claim(AccountIdsClaimName, string.Join(",", accountIds)));
    }
}
