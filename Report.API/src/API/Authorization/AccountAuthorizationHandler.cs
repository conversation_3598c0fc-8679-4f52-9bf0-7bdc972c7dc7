using BusinessLayer.AccessControlList;
using Microsoft.AspNetCore.Authorization;
using Realeyes.PreView.API.Report.Authorization.Extensions;

namespace Realeyes.PreView.API.Report.Authorization;

public class AccountAuthorizationHandler : AuthorizationHandler<AccountRequirement, ResourceToAccountAssignment>
{
    protected override Task HandleRequirementAsync(AuthorizationHandlerContext context, AccountRequirement requirement, ResourceToAccountAssignment assignment)
    {        
        if (assignment.ResourceAccountIds.Intersect(assignment.PrincipalAccountIds).Any())
        {
            context?.Succeed(requirement);
        }

        return Task.CompletedTask;
    }
}
