using BusinessLayer.AccessControlList;
using Microsoft.AspNetCore.Authorization;
using Polly;
using Realeyes.PreView.API.Report.Authorization.Extensions;
using Realeyes.PreView.API.Report.Model.Requests;
using System.Security.Claims;

namespace Realeyes.PreView.API.Report.Authorization;

public class AccountAuthorizer : IAccountAuthorizer
{
    private const string AccountPolicyName = "AccountPolicy";

    private readonly IAuthorizationService _authorizationService;
    private readonly IAccessControlListService _accessControlListService;

    public AccountAuthorizer(IAuthorizationService authorizationService, IAccessControlListService accessControlListService)
    {
        _authorizationService = authorizationService;
        _accessControlListService = accessControlListService;
    }

    public async Task<AuthorizationResult> AuthorizeAdSetAsync(string adSetKey, ClaimsPrincipal user)
    {
        var accountIds = await _accessControlListService.GetAccountIdsForAdSetAsync(adSetKey).ConfigureAwait(false);
        var assignment = new ResourceToAccountAssignment 
        {
            PrincipalAccountIds = GetAccountIds(user),
            ResourceAccountIds = accountIds
        };
        return await AuthorizeResourceAssignmentAsync(user, assignment);
    }

    public async Task<IEnumerable<string>> GetAdSetKeysForPrincipal(ClaimsPrincipal user)
    {
        var accountIds = GetAccountIds(user);        
        if (!accountIds.Any()) { return await Task.FromResult(Enumerable.Empty<string>()); }

        var adSetKeys = await _accessControlListService.GetAdSetKeysByAccountsAsync(accountIds).ConfigureAwait(false);
        return adSetKeys;
    }

    private IEnumerable<int> GetAccountIds(ClaimsPrincipal user)
    {
        return user?.Claims?.GetAccountIds() ?? Enumerable.Empty<int>();
    }

    private async Task<AuthorizationResult> AuthorizeResourceAssignmentAsync(ClaimsPrincipal user, ResourceToAccountAssignment assignment)
    {
        if (assignment == null || !assignment.ResourceAccountIds.Any())
        {
            return AuthorizationResult.Failed();
        }

        var authorizationResult = await _authorizationService.AuthorizeAsync(user, assignment, AccountPolicyName);
        return authorizationResult;
    }
}
