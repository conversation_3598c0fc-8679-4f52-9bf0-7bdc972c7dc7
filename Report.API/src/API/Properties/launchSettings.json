{"profiles": {"Mock Lambda Test Tool": {"commandName": "Executable", "executablePath": "%USERPROFILE%\\.dotnet\\tools\\dotnet-lambda-test-tool-8.0.exe", "commandLineArgs": "--port 5050", "workingDirectory": ".\\bin\\$(Configuration)\\net8.0"}, "Stage": {"commandName": "Project", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "STARTUP__SHOULDRESOLVESECRETS": "true"}, "applicationUrl": "https://localhost:50116;http://localhost:50117"}, "Development": {"commandName": "Project", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "STARTUP__SHOULDRESOLVESECRETS": "true"}, "applicationUrl": "https://localhost:50116;http://localhost:50117"}, "Production": {"commandName": "Project", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Production", "STARTUP__SHOULDRESOLVESECRETS": "true"}, "applicationUrl": "https://localhost:50116;http://localhost:50117"}}}