using Realeyes.PreView.API.Report.Model.Requests;
using Realeyes.PreView.API.Report.Model;
using BusinessLayer.Repository;
using BusinessLayer.CustomAttributes;
using BusinessLayer.Model.Request.Report;
using System.Reflection;
using BusinessLayer.Model.Response;

namespace Realeyes.PreView.API.Report.Services;

public class MeasurementCollector : IMeasurementCollector
{
    private readonly IForcedExposureMetricsRepository _forcedExposureMetricsRepository;

    public MeasurementCollector(IForcedExposureMetricsRepository forcedExposureMetricsRepository)
    {
        _forcedExposureMetricsRepository = forcedExposureMetricsRepository;
    }

    public async Task<IEnumerable<MediaMeasurement>> CollectMediaMeasurements(ReportRequest reportRequest)
    {
        var curvesTask = _forcedExposureMetricsRepository.GetCurvesData(new GetForcedExposureCurveRequest { AdSetExternalKey = reportRequest.AdSetKey });
        var gridTask = _forcedExposureMetricsRepository.GetGridData(new GetForcedExposureGridRequest { AdSetExternalKey = reportRequest.AdSetKey });

        await Task.WhenAll(curvesTask, gridTask).ConfigureAwait(false);

        IEnumerable<ForcedExposureGrid> grid = gridTask.Result;
        IEnumerable<ForcedExposureCurves> curves = curvesTask.Result;

        var measurements = grid.GroupJoin(curves,
                       grid => new { grid.AdSetExternalKey, grid.SourceMediaExternalKey },
                       curves => new { curves.AdSetExternalKey, curves.SourceMediaExternalKey },
                       (grid, curves) =>
                       {
                           var measurement = new MediaMeasurement
                           {
                               AdSetKey = grid.AdSetExternalKey,
                               Elements = GetElements(grid, curves)
                           };

                           return measurement;
                       });
                       

        return measurements.GroupBy(element => element.AdSetKey)
                           .Select(group => new MediaMeasurement
                            {
                               AdSetKey = group.Key,
                               Elements = group.SelectMany(a => a.Elements)
                                               .OrderBy(x => x.MediaKey)
                            })
                           .OrderBy(measurement => measurement.AdSetKey);
    }

    private IEnumerable<MediaElement> GetElements(ForcedExposureGrid grid, IEnumerable<ForcedExposureCurves> curves)
    {
        var (metrics, norms) = GetMetrics(grid);
        var (timeSeries, timeSeriesNorms) = GetTimeSeries(curves);

        yield return new MediaElement
        {
            MediaKey = grid.SourceMediaExternalKey,
            Metrics = metrics.OrderBy(metric => metric.Name),
            Norms = norms.OrderBy(metric => metric.Name),
            TimeSeries = timeSeries.OrderBy(entry => entry.Name),
            TimeSeriesNorms = timeSeriesNorms.OrderBy(entry => entry.Name)
        };
    }

    private (IEnumerable<Metric> metrics, IEnumerable<Metric> norms) GetMetrics(ForcedExposureGrid model)
    {
        List<Metric> norms = new();
        List<Metric> metrics = new();

        IEnumerable<PropertyInfo> propertyInfos = ExcludeFromPropertiesAttribute.GetPublicPropertiesWithoutAttribute<ForcedExposureGrid>();
        foreach (PropertyInfo propertyInfo in propertyInfos)
        {
            var name = propertyInfo.GetName();
            var value = propertyInfo.GetValue(model);

            var targetList = IsNormMetric(name) ? norms : metrics;
            targetList.Add(new Metric { Name = name, Value = value });
        }

        return (metrics, norms);
    }

    private (IEnumerable<TimeSeries> timeSeries, IEnumerable<TimeSeries> timeSeriesNorms) GetTimeSeries(IEnumerable<ForcedExposureCurves> curves)
    {
        List<TimeSeries> listOftimeSeries = new();
        List<TimeSeries> listOfTimeSeriesNorms = new();

        var orderedCurves = curves.OrderBy(curve => curve.Second);

        IEnumerable<PropertyInfo> propertyInfos = ExcludeFromPropertiesAttribute.GetPublicPropertiesWithoutAttribute<ForcedExposureCurves>();
        foreach (PropertyInfo propertyInfo in propertyInfos)
        {            
            var name = propertyInfo.GetName();
            var timeSpans = orderedCurves.Select(curve => curve.Second);
            var values = orderedCurves.Select(curve => propertyInfo.GetValue(curve));

            var targetCollection = IsNormSeries(name) ? listOfTimeSeriesNorms : listOftimeSeries;
            targetCollection.Add(new TimeSeries { Name = name, TimeSpans = timeSpans, Values = values });            
        }

        return (listOftimeSeries, listOfTimeSeriesNorms);
    }

    private bool IsNormMetric(string name)
    {
        return name.EndsWith("Median");
    }

    private bool IsNormSeries(string name)
    {
        return name.EndsWith("Norm");
    }
}
