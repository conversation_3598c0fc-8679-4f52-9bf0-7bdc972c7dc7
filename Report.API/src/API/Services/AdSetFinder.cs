using BusinessLayer.Model.Request.Report;
using BusinessLayer.Model.Response;
using DataLayer.Repository;
using Realeyes.PreView.API.Report.Authorization.Extensions;
using Realeyes.PreView.API.Report.Model;
using Realeyes.PreView.API.Report.Model.Requests;
using Realeyes.PreView.API.Report.Model.Responses;
using System.Security.Claims;

namespace Realeyes.PreView.API.Report.Services;

public class AdSetFinder : IAdSetFinder
{
    private const string AllSegment = "all";
    private readonly IAdSetRepository _adSetRepository;

    public AdSetFinder(IAdSetRepository adSetRepository)
    {
        _adSetRepository = adSetRepository;
    }

    public async Task<ReportsResponse> Search(SearchRequest request, ClaimsPrincipal user)
    {
        var accountIds = user?.Claims?.GetAccountIds().ToList();
        if (!accountIds?.Any() ?? true) { return new ReportsResponse(); }

        var adSetSearchRequest = new AdSetSearchRequest
        {
            AdSetKey = request.AdSetKey,
            AdSetName = request.AdSetName,
            SourceMediaKey = request.SourceMediaKey,
            SourceMediaName = request.SourceMediaName,
            Duration = request.Duration,
            CreationDate = request.CreationDate,
            SegmentKey = AllSegment,
            AccountIds = accountIds,
        };

        IEnumerable<AdSetSearchResult> searchResult = await _adSetRepository.SearchAsync(adSetSearchRequest).ConfigureAwait(false);
        var response = new ReportsResponse
        {
            CurrentPage = request.Page,
            CurrentPageSize = request.PageSize,
            NumberOfReports = searchResult.Count()
        };

        if (response.NumberOfReports == 0) { return response; }

        var pagedItems = searchResult.Skip(request.Page * request.PageSize)
                                     .Take(request.PageSize);

        response.ReportIdentifiers = await FetchDetailsFor(pagedItems).ConfigureAwait(false);
        return response;
    }

    private async Task<IEnumerable<ReportIdentifier>> FetchDetailsFor(IEnumerable<AdSetSearchResult> results)
    {
        var detailsRequest = new AdSetSearchDetailsRequest
        {
            AdSetKeys = results.Select(item => item.AdSetExternalKey),
            SegmentKey = AllSegment,
        };

        IEnumerable<AdSetSearchDetailsResult> details = await _adSetRepository.GetSearchDetailsAsync(detailsRequest).ConfigureAwait(false);

        IEnumerable<ReportIdentifier> reports = details
            .GroupBy(item => new
            {
                item.AdSetExternalKey,
                item.AdSet,
                item.CreationDate
            })
            .Select(group => new ReportIdentifier
            {
                AdSetKey = group.Key.AdSetExternalKey,
                AdSetName = group.Key.AdSet,
                CreationDate = group.Key.CreationDate,
                Medias = group.Select(item =>
                new ReportMedia
                {
                    SourceMediaName = item.SourceMedia,
                    SourceMediaKey = item.SourceMediaExternalKey,
                    Duration = item.Duration
                })
            });

        return reports;
    }
}
