{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Dremio": {"BaseUrl": "${ssm:/live/administration/dremio/preview/baseUrl}", "Username": "${ssm:/live/administration/dremio/preview/username}", "Password": "${ssm:/live/administration/dremio/preview/password}", "ConcurentAPIRequests": 20, "NumberOfResultsPerAPIRequest": 500, "TimeoutOfAPIRequests": 30}, "RealeyesJwtAuthentication": {"ValidIssuer": "${ssm:/preview/iam/eu-west-1/live/backend/issuer}"}, "ConnectionStrings": {"StudyDatabase": "${ssm:/live/databases/common/connectionString}"}}