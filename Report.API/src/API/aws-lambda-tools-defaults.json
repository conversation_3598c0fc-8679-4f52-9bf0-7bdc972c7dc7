{"Information": ["This file provides default values for the deployment wizard inside Visual Studio and the AWS Lambda commands added to the .NET Core CLI.", "To learn more about the Lambda commands with the .NET Core CLI execute the following command at the command line in the project root directory.", "dotnet lambda help", "All the command line options for the Lambda command can be specified in this file."], "profile": "default", "region": "eu-west-1", "configuration": "Release", "function-runtime": "dotnet8", "function-memory-size": 2048, "function-timeout": 30, "funtion-handler": "Realeyes.PreView.API.Report", "framework": "net8.0", "function-name": "preview-report-api-eu-west-1-stage-lambda", "function-description": "", "package-type": "Zip", "function-handler": "Realeyes.PreView.API.Report", "function-role": "arn:aws:iam::249265253269:role/stage-preview-report-api-role", "function-architecture": "x86_64", "function-subnets": "subnet-1dae5e79,subnet-5312240a", "function-security-groups": "sg-8b383cef", "tracing-mode": "PassThrough", "image-tag": ""}