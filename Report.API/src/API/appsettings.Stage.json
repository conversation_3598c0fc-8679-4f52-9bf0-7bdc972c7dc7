{"Logging": {"LogLevel": {"Default": "Error", "Microsoft.AspNetCore": "Error"}}, "Dremio": {"BaseUrl": "${ssm:/live/administration/dremio/preview/baseUrl}", "Username": "${ssm:/live/administration/dremio/preview/username}", "Password": "${ssm:/live/administration/dremio/preview/password}", "ConcurentAPIRequests": 20, "NumberOfResultsPerAPIRequest": 500, "TimeoutOfAPIRequests": 30}, "RealeyesJwtAuthentication": {"ValidIssuer": "${ssm:/preview/iam/eu-west-1/stage/backend/issuer}"}, "ConnectionStrings": {"StudyDatabase": "${ssm:/stage/databases/common/connectionString}"}}