using Realeyesit.Extensions.Configuration;
using Realeyes.PreView.API.Report.Filters;
using Microsoft.AspNetCore.Mvc.Filters;
using Asp.Versioning;
using Microsoft.AspNetCore.Mvc.ApplicationModels;
using Realeyes.PreView.API.Report;
using Microsoft.AspNetCore.ResponseCompression;
using System.IO.Compression;
using Realeyes.PreView.API.Report.Authorization.Extensions;
using BusinessLayer.Configuration;
using DataLayer.Configuration;
using BusinessLayer.Repository;
using EntityFramework;
using Realeyes.PreView.API.Report.Services;

var builder = WebApplication.CreateBuilder(args);

//Configuration
builder.Configuration
    .SetBasePath(builder.Environment.ContentRootPath)
    .AddJsonFile("appsettings.json")
    .AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json")
    .AddEnvironmentVariables()
    .Build();

IConfiguration configuration = builder.Configuration;
var shouldResolveSecrets = configuration.GetValue<bool>("Startup:ShouldResolveSecrets");
if (shouldResolveSecrets)
{
    configuration = configuration.ResolveSecrets(new ConfigurationSecretResolverSettings { ShouldThrow = true });
}

// Compression
builder.Services.AddResponseCompression(options =>
{
    options.EnableForHttps = true;
    options.Providers.Add<BrotliCompressionProvider>();
    options.Providers.Add<GzipCompressionProvider>();
});

builder.Services.Configure<BrotliCompressionProviderOptions>(options =>
{
    options.Level = CompressionLevel.Fastest;
});

builder.Services.Configure<GzipCompressionProviderOptions>(options =>
{
    options.Level = CompressionLevel.SmallestSize;
});

//Versioning and Documentation
var V1 = new ApiVersion(1);
builder.Services.AddSwaggerGen(options =>
{
    options.SwaggerDoc($"v{V1}", new()
    {
        Title = "Report API",
        Version = V1.ToString(),
    });
    
    options.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, "report-api-docs.xml"));
});

builder.Services.AddApiVersioning(options =>
{
    options.DefaultApiVersion = V1;
    options.ApiVersionReader = new UrlSegmentApiVersionReader();
})
.AddApiExplorer(options =>
{
    options.GroupNameFormat = "'v'VVV";
    options.SubstituteApiVersionInUrl = true;
});

//Other Services
builder.Services.AddControllers(options =>
{
    options.AllowEmptyInputInBodyModelBinding = true;
    options.Conventions.Add(new RouteTokenTransformerConvention(new KebabParameterTransformer()));
    options.Filters.Add<GlobalExceptionFilter>();
});
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddAWSLambdaHosting(LambdaEventSource.RestApi);

builder.Services.AddScoped<IActionFilter, ValidationFilter>();
builder.Services.AddScoped<IMeasurementCollector, MeasurementCollector>();
builder.Services.AddScoped<IAdSetFinder, AdSetFinder>();

builder.Services.AddJwtBearerAuthentication(configuration);
builder.Services.AddAccountAuthorization();

builder.Services.AddBusinessConfiguration();
builder.Services.AddDataConfiguration(configuration);

// TODO: Refactor Infrastructure.Persistance in PreviewPortal.API to eliminate this
builder.Services.AddScoped<IAdSetToAccountRepository, AdSetToAccountRepository>();
builder.Services.AddDbContext(configuration);

//Build & Run Application
var app = builder.Build();

var versionSet = app.NewApiVersionSet()
    .HasApiVersion(V1)
    .ReportApiVersions()
    .Build();

if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(options =>
    {
        var descriptions = app.DescribeApiVersions();
        foreach (var description in descriptions)
        {
            options.SwaggerEndpoint($"/swagger/{description.GroupName}/swagger.json", $"{builder.Environment.ApplicationName} {description.GroupName}");
        }        
    });
}

app.UseResponseCompression();
app.UseHttpsRedirection();

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers().WithApiVersionSet(versionSet);
app.Run();
