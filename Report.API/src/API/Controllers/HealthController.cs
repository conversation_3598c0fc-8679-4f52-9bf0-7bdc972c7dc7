using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Realeyes.PreView.API.Report.Controllers;

[ApiController]
[ApiVersion("1.0")]
[Route("v{version:apiVersion}")]
[Authorize]
public class HealthController : ControllerBase
{
    [HttpGet]
    [Route("reports/health")]
    [AllowAnonymous]
    public IActionResult HealthCheck()
    {
        return Ok(new { status = "Healthy" });
    }
}