using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Realeyes.PreView.API.Report.Authorization;
using Realeyes.PreView.API.Report.Model;
using Realeyes.PreView.API.Report.Model.Requests;
using Realeyes.PreView.API.Report.Model.Responses;
using Realeyes.PreView.API.Report.Services;
using System.ComponentModel.DataAnnotations;

namespace Realeyes.PreView.API.Report.Controllers
{
    [ApiController]
    [ApiVersion("1.0")]    
    [Route("v{version:apiVersion}")]
    [Authorize]
    public class ReportController : ControllerBase
    {
        private readonly IAccountAuthorizer _accountAuthorizer;
        private readonly IMeasurementCollector _measurementCollector;
        private readonly IAdSetFinder _adSetFinder;

        public ReportController(IAccountAuthorizer accountAuthorizer, 
                                IMeasurementCollector measurementCollector,
                                IAdSetFinder adSetFinder)
        {
            _accountAuthorizer = accountAuthorizer;
            _measurementCollector = measurementCollector;
            _adSetFinder = adSetFinder;
        }

        /// <summary>
        /// Searches for reports based on the specified parameters.
        /// </summary>
        /// <remarks>
        /// Filters are combined using an AND logical operator.
        /// String-typed filters perform pattern matching (case-sensitive), while integer and date-typed filters check for equality.
        /// A list of reports grouped by AdSets, including all source media (not limited to those matching the filters).
        /// </remarks>
        /// <param name="searchRequest">The search criteria containing the filters to apply.</param>        
        [HttpPut]
        [Route("reports/search")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(ReportsResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]        
        public async Task<IActionResult> GetReports([FromBody][Required] SearchRequest searchRequest)
        {
            ReportsResponse result = await _adSetFinder.Search(searchRequest, this.User);
            return new JsonResult(result);
        }
        
        [HttpGet]
        [Route("reports")]
        [ProducesResponseType(typeof(ReportResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetReport([FromQuery][Required] ReportRequest reportRequest)
        {
            var hasAccess = await _accountAuthorizer.AuthorizeAdSetAsync(reportRequest.AdSetKey, this.User).ConfigureAwait(false);
            if (!hasAccess.Succeeded)
            {
                return new ForbidResult();
            }

            IEnumerable<MediaMeasurement> measurements = await _measurementCollector.CollectMediaMeasurements(reportRequest).ConfigureAwait(false);
            return new JsonResult(new ReportResponse { Data = measurements });
        }
    }
}