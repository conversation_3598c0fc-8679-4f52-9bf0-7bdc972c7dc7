<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateRuntimeConfigurationFiles>true</GenerateRuntimeConfigurationFiles>
    <AWSProjectType>Lambda</AWSProjectType>
    <!-- This property makes the build directory similar to a publish directory and helps the AWS .NET Lambda Mock Test Tool find project dependencies. -->
    <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
    <!-- Generate ready to run images during publishing to improvement cold starts. -->
    <PublishReadyToRun>true</PublishReadyToRun>
    <RootNamespace>Realeyes.PreView.API.Report</RootNamespace>
    <AssemblyName>Realeyes.PreView.API.Report</AssemblyName>
	 <DocumentationFile>bin\Debug\net8.0\report-api-docs.xml</DocumentationFile>
  </PropertyGroup>
  <ItemGroup>
    <None Remove="README.md" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Amazon.Lambda.AspNetCoreServer.Hosting" Version="1.6.0" />
    <PackageReference Include="Asp.Versioning.Mvc.ApiExplorer" Version="8.1.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.13" />
    <PackageReference Include="Realeyesit.Extensions.Configuration" Version="3.9.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="7.2.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\PreView.Common\src\BusinessLayer\BusinessLayer.csproj" />
    <ProjectReference Include="..\..\..\PreView.Common\src\DataLayer\DataLayer.csproj" />
    <ProjectReference Include="..\..\..\PreView.Common\src\DataProviders\EntityFramework\EntityFramework.csproj" />
  </ItemGroup>
</Project>