# Report API
Welcome to the documentation for the Report API for Preivew.

## API Test URL
You can interact with the API and test its endpoints using on of the following base URLs:
- Stage: https://stage-preview-api.realeyesit.com/v1/reports
- Live https://preview-api.realeyesit.com/v1/reports

## Client Credentials for OAuth2 Client Credentials Workflow
To access protected endpoints using the Client Credentials OAuth2 workflow, you need to obtain an access token. Follow these steps:

1. Make a POST request to the */token* endpoint to obtain an access token.
2. Include the required parameters such as 
	1. Stage: 
		- Token endpoint for stage: https://stage-preview-api.realeyesit.com/token
		- Client Id and secret can be found [here](https://eu-west-1.console.aws.amazon.com/cognito/v2/idp/user-pools/eu-west-1_ztobZOI9z/applications/app-clients/63uhvhv4rpmm7kdslp2lor8hmr)
	2. Live
		- Token endpoint for stage: https://preview-api.realeyesit.com/token
		- Client Id and secret can be found [here](https://eu-west-1.console.aws.amazon.com/cognito/v2/idp/user-pools/eu-west-1_42XLC1T6H/applications/app-clients/7rnnp8vmgdn73vof6gna4cb8dk)
3. The response will contain an access token that you can use to authenticate requests.

> Use `Live` UserPool unless the StudyDB and Dremio services are properly configured on stage.
> The main reason behind this is that the Report API uses Access Control List based on Account and Resource assignments that persisted in StudyDB.
> Thus Dremio queries also can use these assignments.

## Postman Collection
For your convenience, a Postman collection have been exported that contain preconfigured API requests. You can import this collection into your [Postman application](https://www.postman.com/) to easily test the API endpoints.
- Stage: ./tests/postman/Reporting API - STAGE.postman_collection.json
- Live: ./tests/postman/Reporting API - LIVE.postman_collection.json

### Importing Postman Collection
1. Open Postman.
2. Click the "Import" button in the top-left corner.
3. Choose the exported collection.
4. The collection will be imported into your Postman workspace.