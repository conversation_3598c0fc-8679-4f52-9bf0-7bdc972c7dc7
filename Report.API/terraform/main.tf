terraform {
  backend "s3" {
    bucket = "preview-terraform-workspaces"
    key = "report-api"
	  region = "eu-west-1"
  }
}

provider "aws" {
  region = var.region
  # This is needed only for local deployment
  # ignore_tags {
  #   keys = ["AutoTag_CreateTime", "AutoTag_Creator"]
  # }
  default_tags {
    tags = {
      Costgroup   = var.cost_group
      product     = "preview"
      stage       = var.environment
    }
  }
}

locals {
  api_gateway_url = "${aws_api_gateway_rest_api.api.id}.execute-api.${var.region}.amazonaws.com"
  service_prefix_with_enviroment = "${var.environment}-${var.service_prefix}"
}


data "aws_subnet" "subnet_1" {
  id = "subnet-5312240a"  
}

data "aws_subnet" "subnet_2" {
  id = "subnet-1dae5e79" 
}

data "aws_security_group" "sec_group" {
  id = "sg-8b383cef" 
}

resource "aws_iam_role" "lambda_role" {
  name = "${local.service_prefix_with_enviroment}-lambda-execution-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action    = "sts:AssumeRole"
        Effect    = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "lambda_execution_policy" {
  role       = aws_iam_role.lambda_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

resource "aws_iam_role_policy_attachment" "AWSLambdaVPCAccessExecutionRole" {
    role       = aws_iam_role.lambda_role.name
    policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole"
}

resource "aws_iam_role_policy" "lambda_ssm_policy" {
  name   = "${aws_iam_role.lambda_role.name}-ssm-policy"
  role   =  aws_iam_role.lambda_role.name

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ssm:GetParametersByPath",
          "ssm:GetParameters",
          "ssm:GetParameter"
        ]
        Resource = [
          "arn:aws:ssm:eu-west-1:249265253269:parameter/live/administration/dremio/preview/*",
          "arn:aws:ssm:eu-west-1:249265253269:parameter/live/databases/common/connectionString",
          "arn:aws:ssm:eu-west-1:249265253269:parameter/preview/iam/eu-west-1/live/backend/issuer"
        ]
      }
    ]
  })
}

resource "aws_lambda_function" "my_lambda" {
  function_name = "${local.service_prefix_with_enviroment}-lambda"
  role          = aws_iam_role.lambda_role.arn
  handler       = "Realeyes.PreView.API.Report"
  runtime       = "dotnet8"
  memory_size      = 512
  timeout          = 30

  filename        = "../src/API/publish/report-api.zip"
  source_code_hash = filebase64sha256("../src/API/publish/report-api.zip")

  environment {
    variables = {
      ASPNETCORE_ENVIRONMENT                 = var.dotnet_enviroment
      Dremio__BaseUrl                        = "$${ssm:/live/administration/dremio/preview/baseUrl}"
      Dremio__Username                       = "$${ssm:/live/administration/dremio/preview/username}"
      Dremio__Password                       = "$${ssm:/live/administration/dremio/preview/password}"
      RealeyesJwtAuthentication__ValidIssuer = "$${ssm:/preview/iam/eu-west-1/live/backend/issuer}"
      ConnectionStrings__StudyDatabase       = "$${ssm:/live/databases/common/connectionString}"
    }
  }

  vpc_config {
    subnet_ids          = [data.aws_subnet.subnet_1.id, data.aws_subnet.subnet_2.id]
    security_group_ids  = [data.aws_security_group.sec_group.id]
  }
}

resource "aws_cloudwatch_log_group" "lambda_log_group" {
  name = "/aws/lambda/${aws_lambda_function.my_lambda.function_name}"
  retention_in_days = 30 
}

resource "aws_api_gateway_rest_api" "api" {
  name        =  "${local.service_prefix_with_enviroment}-gateway"
  description = "API Gateway for .NET Lambda"
}

resource "aws_api_gateway_resource" "proxy_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "{proxy+}"  
}

resource "aws_api_gateway_method" "proxy_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.proxy_resource.id
  http_method   = "ANY"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "lambda_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.proxy_resource.id
  http_method             = aws_api_gateway_method.proxy_method.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = aws_lambda_function.my_lambda.invoke_arn
}

resource "aws_lambda_permission" "allow_api_gateway" {
  statement_id  = "AllowApiGatewayInvoke"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.my_lambda.function_name
  principal     = "apigateway.amazonaws.com"
}

resource "aws_api_gateway_deployment" "api_deployment" {
  depends_on = [aws_api_gateway_integration.lambda_integration]
  rest_api_id = aws_api_gateway_rest_api.api.id
}

resource "aws_api_gateway_stage" "api_stage" {
  rest_api_id  = aws_api_gateway_rest_api.api.id
  deployment_id = aws_api_gateway_deployment.api_deployment.id
  stage_name   =  var.environment
  description  = "API Gateway Stage"
}

resource "aws_ssm_parameter" "ssm_gateway_url" {
  name  = "/preview/api/report/${var.environment}/gatewayUrl"
  type  = "String"
  value = local.api_gateway_url
}

output "api_url" {
  value = local.api_gateway_url
}