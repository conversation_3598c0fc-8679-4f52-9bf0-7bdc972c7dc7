{"info": {"_postman_id": "01ab4063-e257-4a1d-9806-add29034027f", "name": "Reporting API - LIVE", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "6785393"}, "item": [{"name": "token", "event": [{"listen": "test", "script": {"exec": ["var jsonData = pm.response.json();\r", "pm.collectionVariables.set(\"esToken\", jsonData.access_token);\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "basic", "basic": [{"key": "password", "value": "", "type": "string"}, {"key": "username", "value": "7rnnp8vmgdn73vof6gna4cb8dk", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "grant_type", "value": "client_credentials", "type": "text"}]}, "url": {"raw": "https://preview-api.realeyesit.com/token", "protocol": "https", "host": ["preview-api", "realeyesit", "com"], "path": ["token"]}}, "response": []}, {"name": "preview-api.realeyesit.com/v1/reports", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{esToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "https://lntkf5bing.execute-api.eu-west-1.amazonaws.com/stage/v1/reports?AdSetKey=069PX1VSJS8MZ4PFE27348B264", "protocol": "https", "host": ["lntkf5bing", "execute-api", "eu-west-1", "amazonaws", "com"], "path": ["stage", "v1", "reports"], "query": [{"key": "AdSetKey", "value": "069PX1VSJS8MZ4PFE27348B264"}]}}, "response": []}], "auth": {"type": "bearer"}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}], "variable": [{"key": "esToken", "value": "", "type": "string"}]}