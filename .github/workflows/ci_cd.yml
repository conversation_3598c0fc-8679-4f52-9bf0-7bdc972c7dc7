name: "CI/CD"
on:
  push:
    branches:
      - "**"
    paths-ignore: 
      - '.github/workflows/eye-square-api-ci-cd.yml'
      - 'EyeSquare.API/**'
      - '.github/workflows/preview-portal-export-ci-cd.yml'
      - 'PreViewPortal.Export/**'
      - '.github/workflows/report-api-ci-cd.yml'
      - 'Report.API/**'
  workflow_dispatch:

env:
  AWS_ACCESS_KEY_ID: ${{secrets.REALEYES_AWS_ACCESSKEY_ID}}
  AWS_SECRET_ACCESS_KEY: ${{secrets.REALEYES_AWS_SECRET_ACCESSKEY}}
  AWS_DEFAULT_REGION: ${{secrets.REALEYES_AWS_DEFAULT_REGION}}
  AWS_REGION: ${{secrets.REALEYES_AWS_DEFAULT_REGION}}
  SLACK_ACCESS_TOKEN: ${{secrets.SLACK_BOT_TOKEN}}
  GITHUB_USER_NAME: ${{secrets.REALEYES_GITHUB_USER}}
  GITHUB_PASSWORD: ${{secrets.REALEYES_GITHUB_TOKEN}}

jobs:
  build:
    name: Build
    timeout-minutes: 15
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Enable corepack for Yarn
        run: corepack enable

      - name: Setup dotnet sdk
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: 8.x

      - name: Setup NuGet source
        run: dotnet nuget add source https://nuget.pkg.github.com/Realeyes/index.json -n GitHubRealEyes -u ${{secrets.REALEYES_GITHUB_USER}} -p ${{secrets.REALEYES_GITHUB_TOKEN}} --store-password-in-clear-text
        
      - name: Cache ~/.nuget/packages
        uses: actions/cache@v4
        with:
          path: ~/.nuget/packages
          key: ${{ runner.os }}-build-nuget-packages-${{ hashFiles('**/global.json', '**/*.csproj') }}
          
      - name: Cache node_modules
        uses: actions/cache@v4
        with:
          path: "**/node_modules"
          key: ${{ runner.os }}-build-node-modules-${{ hashFiles('**/yarn.lock') }}

      - name: Run Application.Tests (build and run)
        run: dotnet test --logger trx
        working-directory: ./PreViewPortal.API/tests/Application.Tests

      - name: Application.Tests results
        if: success() || failure()
        uses: dorny/test-reporter@v1
        with:
          name: Application.Tests results
          path: ./PreViewPortal.API/tests/Application.Tests/TestResults/*.trx
          reporter: dotnet-trx

      - name: Setup lambda tools
        run: dotnet tool install --global Amazon.Lambda.Tools

      - name: Restore NuGet packages
        run: dotnet restore ./PreViewPortal.API/src/WebAPI

      - name: Create Package of WebAPI
        run: dotnet lambda package -c Release -o "./PreViewPortal.API/src/WebAPI/publish/lambda-package.zip" -pl "./PreViewPortal.API/src/WebAPI"

      - name: Install the dependencies of the UI
        run: yarn install
        working-directory: ./PreViewPortal.UI

      - name: Build the UI
        run: yarn run build
        working-directory: ./PreViewPortal.UI

      - name: Create UI config files with client id
        run: |
          yarn run update-config stage
          yarn run update-config beta
          yarn run update-config live
        working-directory: ./PreViewPortal.UI

      - uses: actions/upload-artifact@v4
        with:
          name: artifacts
          path: |
            ./PreViewPortal.API/src/WebAPI/publish/
            ./terraform
            ./PreViewPortal.UI/build
            ./PreViewPortal.UI/completed-config.*
            ./bot.sh
          include-hidden-files: true

  deploy-to-stage:
    uses: ./.github/workflows/preview-portal-reuseable-deploy.yml
    needs: [build]
    with:
      github_environment: Stage
      environment: stage
      url: https://stage-preview.realeyesit.com
    secrets: inherit

  deploy-to-beta:
    uses: ./.github/workflows/preview-portal-reuseable-deploy.yml
    needs: [build]
    with:
      github_environment: Beta
      environment: beta
      url: https://beta-preview.realeyesit.com
    secrets: inherit

  deploy-to-live:
    uses: ./.github/workflows/preview-portal-reuseable-deploy.yml
    if: github.event.ref == 'refs/heads/master'
    needs: [build]
    with:
      github_environment: Live
      environment: live
      url: https://preview.realeyesit.com
    secrets: inherit