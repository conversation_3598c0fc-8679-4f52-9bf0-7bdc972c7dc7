name: "Eye Square CI/CD"
on:
  push:
    branches:
      - "**"
    paths: 
      - '.github/workflows/eye-square-api-ci-cd.yml'
      - 'EyeSquare.API/**'
  workflow_dispatch:

env:
  AWS_ACCESS_KEY_ID: ${{secrets.REALEYES_AWS_ACCESSKEY_ID}}
  AWS_SECRET_ACCESS_KEY: ${{secrets.REALEYES_AWS_SECRET_ACCESSKEY}}
  AWS_DEFAULT_REGION: ${{secrets.REALEYES_AWS_DEFAULT_REGION}}
  AWS_REGION: ${{secrets.REALEYES_AWS_DEFAULT_REGION}}
  SLACK_ACCESS_TOKEN: ${{secrets.SLACK_BOT_TOKEN}}
  GITHUB_USER_NAME: ${{secrets.REALEYES_GITHUB_USER}}
  GITHUB_PASSWORD: ${{secrets.REALEYES_GITHUB_TOKEN}}

jobs:
  build:
    name: Build
    timeout-minutes: 10
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Setup dotnet sdk
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: 8.x

      - name: Setup lambda tools
        run: dotnet tool install --global Amazon.Lambda.Tools

      - name: Setup NuGet source
        run: dotnet nuget add source https://nuget.pkg.github.com/Realeyes/index.json -n GitHubRealEyes -u ${{secrets.REALEYES_GITHUB_USER}} -p ${{secrets.REALEYES_GITHUB_TOKEN}} --store-password-in-clear-text

      - name: Create Package
        run: dotnet lambda package -c Release -o "./EyeSquare.API/src/API/publish/eyesquare-api.zip" -pl "./EyeSquare.API/src/API"

      - uses: actions/upload-artifact@v4
        with:
          name: artifacts
          path: |
            ./EyeSquare.API/src/API/publish/
            ./EyeSquare.API/terraform
          retention-days: 10

  deploy-to-stage:
    name: Deploy to Stage
    runs-on: ubuntu-latest
    needs: [build]
    environment:
      name: Stage
      url: https://stage-preview-api.realeyesit.com
    steps:
      - uses: actions/download-artifact@v4
        with:
          name: artifacts
          path: artifacts

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v1

      - name: Terraform Init
        run: terraform init
        working-directory: artifacts/terraform
                
      - name: select or create workspace
        run: terraform workspace select -or-create stage
        working-directory: artifacts/terraform

      - name: Terraform Apply
        run: terraform apply -var-file=stage.tfvars -auto-approve
        working-directory: artifacts/terraform

  deploy-to-live:
    name: Deploy to Live
    runs-on: ubuntu-latest
    if: github.event.ref == 'refs/heads/master'
    needs: [build]
    environment:
      name: live
      url: https://preview.api.realeyesit.com
    steps:
      - uses: actions/download-artifact@v4
        with:
          name: artifacts
          path: artifacts

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v1

      - name: Terraform Init
        run: terraform init
        working-directory: artifacts/terraform
                
      - name: select or create workspace
        run: terraform workspace select -or-create live
        working-directory: artifacts/terraform

      - name: Terraform Apply
        run: terraform apply -var-file=live.tfvars -auto-approve
        working-directory: artifacts/terraform

