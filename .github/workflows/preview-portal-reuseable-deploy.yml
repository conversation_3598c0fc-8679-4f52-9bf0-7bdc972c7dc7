name: Reusable PreviewPortal Deployment
on:
  workflow_call:
    inputs:
      github_environment:
        required: true
        type: string
      environment:
        required: true
        type: string
      url:
        required: true
        type: string

env:
  AWS_ACCESS_KEY_ID: ${{secrets.REALEYES_AWS_ACCESSKEY_ID}}
  AWS_SECRET_ACCESS_KEY: ${{secrets.REALEYES_AWS_SECRET_ACCESSKEY}}
  AWS_DEFAULT_REGION: ${{secrets.REALEYES_AWS_DEFAULT_REGION}}
  AWS_REGION: ${{secrets.REALEYES_AWS_DEFAULT_REGION}}
  SLACK_ACCESS_TOKEN: ${{secrets.SLACK_BOT_TOKEN}}
  GITHUB_USER_NAME: ${{secrets.REALEYES_GITHUB_USER}}
  GITHUB_PASSWORD: ${{secrets.REALEYES_GITHUB_TOKEN}}

jobs:
  deploy:
    name: Deploy to ${{ inputs.github_environment }}
    runs-on: ubuntu-latest
    environment:
        name: ${{ inputs.github_environment }}
        url: ${{ inputs.url }}
    steps:
        - uses: actions/download-artifact@v4
          with:
            name: artifacts
            path: artifacts

        - name: Copy configuration file of the UI
          run: cp completed-config.${{ inputs.environment }}.js ./build/config.js
          working-directory: ./artifacts/PreViewPortal.UI

        - name: Setup Terraform
          uses: hashicorp/setup-terraform@v1

        - name: Terraform Init
          run: terraform init
          working-directory: artifacts/terraform
                    
        - name: select or create workspace
          run: terraform workspace select -or-create ${{ inputs.environment }}
          working-directory: artifacts/terraform

        - name: Terraform Apply
          run: terraform apply -var-file=${{ inputs.environment }}.tfvars -auto-approve
          working-directory: artifacts/terraform

        - name: Send notification of deployment to Slack channel
          if: ${{ always() }}
          run: chmod +x ./artifacts/bot.sh && ./artifacts/bot.sh
            ${{ secrets.SLACK_BOT_TOKEN }}
            C0PFUG1GF
            after
            ${{ job.status }}
            ${{ github.repository }}
            ${{ github.sha }}
            ${{ github.ref }}
            ${{ github.run_id }}
            ${{ github.actor }}
            ${{ inputs.environment }}

        - name: Invalidate cache
          run: curl --header "RE-API-SECRET:${{secrets.RE_API_SECRET}}" ${{ inputs.url }}/api/cache/flush