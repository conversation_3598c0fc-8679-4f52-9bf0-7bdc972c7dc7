![master workflow](https://github.com/Realeyes/PreViewPortal/actions/workflows/ci_cd.yml/badge.svg?branch=master)

## Setup
___
### Requirements

1. [.NET SDK 6.0 and 3.1.](https://dotnet.microsoft.com/en-us/download/visual-studio-sdks)
2. [Yarn](https://yarnpkg.com/)

___
### How to setup local.realeyesit.com domain

#### Edit hosts file

1. Go under "C:\Windows\System32\drivers\etc" and open hosts file with admin priviliges to edit.
2. Create the following entry: 127.0.0.1 local.realeyesit.com

#### Import pfx file

1. Go under .cert and double click local.realyesit.com.pfx
2. Follow the wizard and provide the password, which can be found in the launchSettings.json
3. Import the cert under __"Trusted Root Certification Authorities"__

___
### How to setup Github NuGet source

Run the following command, where `<USERNAME>` is your Github user name and the `<TOKEN>` is your Github personal access token.

```batch
dotnet nuget add source https://nuget.pkg.github.com/Realeyes/index.json -n GitHubRealEyes -u <USERNAME> -p <TOKEN> --store-password-in-clear-text
```

To generate a personal access token, click [here](https://github.com/settings/tokens/new) and read the documentation [here](https://help.github.com/en/github/authenticating-to-github/creating-a-personal-access-token-for-the-command-line).
The token needs `repo` and `read:packages`, `write:packages` rights.

__After the setup, restart Visual Studio, to let the package manager console pick up the new source!__

___
### Environments

We currently have Study DB and Dremio Services available on Staging (stage) and Production (live) environments. Local/Development environment is  configured to use components of Staging environment. UI and Web API components can be built and executed with these settings locally.

#### Most relevant configuration files:
- UI: 	
	- Project infrastructure: package.json
	- [Vite](https://vitejs.dev/) configuration: vite.config.js
	- General settings for backend services: ./PrevWievPortal.UI/config.{environemnt}.js	
	- [MUI](https://mui.com/) license: .env

- Web API: 
	- General settings for backend services: appsettings.{environment}.json
	- Application debug properties: launchSettings.json

#### Run local development with different backend services

1. Backend: select the required profile: Development, Staging or Production
2. Frontend: run yarn start:{environment} (dev,stage,live)

> Notes:
- If running frontend you might have `digital envelope routines::unsupported` error on nodejs >=v17, set `NODE_OPTION` env variable to `--openssl-legacy-provider` to resolve
- Realeyes VPN is required to reach StudyDB and Dremio on local machine.
___
#### Yarn setup
We use Yarn frontend package manager being listed in package.json of PreViewPortal.UI folder. To able to run the new version of yarn, you have to install and enable corepack(by this command) on CLI.

___
### Testing Api using Postman 

To be able to test `local.realeyesit.com:port` api locally you can use Postman with cookie sync for `realeyesit.com` domain enabled: https://learning.postman.com/docs/sending-requests/capturing-request-data/syncing-cookies/ to reuse authentication done in browser. Or Authentication header can be set manually.
You also have to set `Re-supervised-account` header for most endpoints.
___
### Authentication

In the current solution, the resource and the authentication servers are separated. So, Cognito was introduced. It can handle the Identity Management related responsibilities (authentication, authorization) and issuing tokens. An API Gateway is used to avoid direct communication with AWS resources and requesting/revoking tokens are also managed in this component. A migration lambda function synchronizes users between StudyDB and Cognito. Other lambda functions also used to extend the default Cognito (OAuth 2.0) workflows. More details can be found on [Confluence](https://realeyesit.atlassian.net/wiki/spaces/CT/pages/***********/Identity+Management+POC+for+PreView+App+and+Delivery+Site)

#### Configurations for local development
- UI: 
  - ./PrevWievPortal.UI/config.dev.js:
```
	...
	CLIENT_ID: 'CLIENT_ID_PLACEHOLDER', => Placeholder for the application client Id in Cognito User Pool (preview-app-dev => Preview App SPA)
	...
    AUTH_SERVICE_URL: 'https://preview-iam-euwest1-dev-auth.realeyesit.com', => Domain of Cognito User Pool (preview-app-dev)
    CALLBACK_URL: 'https://preview-portal-lambdas-eu-west-1-dev.realeyesit.com/token', => Endpoint of API Gateway (preview-iam-euwest1-dev-orchestratorAPI) - request token
    LOGOUT_URL: 'https://preview-portal-lambdas-eu-west-1-dev.realeyesit.com/logout' => Endpoint of API Gateway (preview-iam-euwest1-dev-orchestratorAPI) - revoke token
```
Web API:
	- appsettings.Development.json
```
		"RealeyesJwtAuthentication": {
			"ValidIssuer": "${ssm:/preview/iam/eu-west-1/dev/backend/issuer}", => SSM Parameter for dev environment
			"ValidAudience": "${ssm:/preview/iam/eu-west-1/dev/backend/audience}"  => SSM Parameter for dev environment
		}, ...
```

#### Practical steps
When the Login button is clicked, you are going to be redirected to Hosted UI of the configured Cognito endpoint depending on the environment. If you can't log in and get redirected to any error page, make sure you have a valid user in the configured Cognito User Pool. Users can be migrated from Study DB. (Check availablity properties in the DB like StudyDb.UserProfile.IsVerified, etc.)
___
### Deployment

To deploy from your local machine, run the following commands:

1. Create Package of WebAPI by running this in the root folder
	`dotnet lambda package -c Release -o "./PreViewPortal.API/src/WebAPI/publish/lambda-package.zip" -pl "./PreViewPortal.API/src/WebAPI"`
2. Build UI by running this in the PreviewPortal.UI folder
	`yarn run build`
3. Create UI config file with client id by running this in the PreviewPortal.UI folder
    `yarn run update-config <ENVIRONMENT>`
4. Copy the file created above into build folder of the PreviewPortal.UI with name config.js
5. Select terraform workspace
	`terraform workspace select <ENVIRONMENT>`
6. Deploy resources by running this in the terraform folder
	`terraform apply -var-file=<ENVIRONMENT>.tfvars`

Where `<ENVIRONMENT>` can be:
  - stage
  - beta
  - live

> NOTE: Development (dev) environment is only for local testing.