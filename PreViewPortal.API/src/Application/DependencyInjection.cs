using System.Linq;
using System.Reflection;
using Application.Common.Attributes;
using Application.Common.Behaviors;
using Application.Common.Caching;
using Application.Common.Security;
using Application.CreativeViewer.Queries;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.DependencyInjection;

namespace Application
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddApplication(this IServiceCollection services)
        {
            services.RegisterDefaultCacheKeyGenerators(Assembly.GetExecutingAssembly());
            services.RegisterCacheKeyGeneratorOverrides();
            services.RegisterPropertyAuthorizations(Assembly.GetExecutingAssembly());
            services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly());
            services.AddAutoMapper(Assembly.GetExecutingAssembly());
            services.AddMediatR(configuration =>
            {
                configuration.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly());
                configuration.AddBehavior(typeof(IPipelineBehavior<,>), typeof(UnhandledExceptionBehavior<,>));
                configuration.AddBehavior(typeof(IPipelineBehavior<,>), typeof(TaskCanceledExceptionBehavior<,>));
                configuration.AddBehavior(typeof(IPipelineBehavior<,>), typeof(AuthorizationBehaviour<,>));
                configuration.AddBehavior(typeof(IPipelineBehavior<,>), typeof(AuthorizationByBehavior<,>));
                configuration.AddBehavior(typeof(IPipelineBehavior<,>), typeof(ValidationBehavior<,>));
                configuration.AddBehavior(typeof(IPipelineBehavior<,>), typeof(CachingBehavior<,>));
            });

            return services;
        }

        private static void RegisterPropertyAuthorizations(this IServiceCollection services, Assembly assembly)
        {
            var authorizers = assembly.ExportedTypes
                .Where(t => t.GetInterfaces().Any(i => i == typeof(IAuthorizeByProperty)))
                .ToList();

            foreach (var authorizer in authorizers)
            {
                services.AddTransient(typeof(IAuthorizeByProperty), authorizer);
            }
        }

        private static void RegisterDefaultCacheKeyGenerators(this IServiceCollection services, Assembly assembly)
        {
            var queriesWithCacheAttribute = assembly.ExportedTypes
                .Where(t => t.GetCustomAttribute<CacheQueryAttribute>() != null)
                .ToList();

            foreach (var queryWithCacheAttribute in queriesWithCacheAttribute)
            {
                var interfaceType = typeof(ICacheKeyGenerator<>).MakeGenericType(queryWithCacheAttribute);
                var implementationType = typeof(DefaultCacheKeyGenerator<>).MakeGenericType(queryWithCacheAttribute);
                services.AddSingleton(interfaceType, implementationType);
            }
        }

        private static void RegisterCacheKeyGeneratorOverrides(this IServiceCollection services)
        {
            services
                .AddSingleton<ICacheKeyGenerator<GetCreativeViewerInContextQuery>,
                    CreativeViewerInContextCacheKeyGenerator>();
            services
                .AddSingleton<ICacheKeyGenerator<GetCreativeViewerForcedExposureQuery>,
                    CreativeViewerForcedExposureCacheKeyGenerator>();
        }
    }
}