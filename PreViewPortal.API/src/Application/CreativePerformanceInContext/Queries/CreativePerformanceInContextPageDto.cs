using System.Collections.Generic;
using BusinessLayer.Model.Filter;
using BusinessLayer.Model.Response;

namespace Application.CreativePerformanceInContext.Queries
{
    public class CreativePerformanceInContextPageDto : GridDto<InContextCreative>
    {
        public IEnumerable<string> BrandOptions { get; set; }
        public IEnumerable<string> CreationDateOptions { get; set; }
        public IEnumerable<string> CountryOptions { get; set; }
        public IEnumerable<string> DeviceOptions { get; set; }
        public IEnumerable<string> RegionOptions { get; set; }
        public IEnumerable<string> AdformatTextOptions { get; set; }
        public IEnumerable<int> DurationOptions { get; set; }
        public IEnumerable<int> ViewsOptions { get; set; }
        public IEnumerable<string> TopCategoryOptions { get; set; }
        public IEnumerable<string> MidCategoryOptions { get; set; }
        public IEnumerable<string> SubCategoryOptions { get; set; }
        public IEnumerable<float> QualityScoreOptions { get; set; }
        public IEnumerable<float> AttentiveSecondsOptions { get; set; }
        public IEnumerable<int> AttentiveSecondsRankOptions { get; set; }
        public IEnumerable<float> AttentiveSecondsMedianOptions { get; set; }
        public IEnumerable<float> AttentiveSecondsDiffOptions { get; set; }
        public IEnumerable<float> AttentiveSecondsIFOptions { get; set; }
        public IEnumerable<int> AttentiveSecondsIFRankOptions { get; set; }
        public IEnumerable<float> AttentiveSecondsIFMedianOptions { get; set; }
        public IEnumerable<float> AttentiveSecondsIFDiffOptions { get; set; }
        public IEnumerable<float> BrandRecognitionOptions { get; set; }
        public IEnumerable<int> BrandRecognitionRankOptions { get; set; }
        public IEnumerable<float> BrandRecognitionMedianOptions { get; set; }
        public IEnumerable<float> BrandRecognitionDiffOptions { get; set; }
        public IEnumerable<float> AttentiveSecondsVTROptions { get; set; }
        public IEnumerable<int> AttentiveSecondsVTRRankOptions { get; set; }
        public IEnumerable<float> AttentiveSecondsVTRMedianOptions { get; set; }
        public IEnumerable<float> AttentiveSecondsVTRDiffOptions { get; set; }
        public IEnumerable<float> AttentiveSecondsVTRIFOptions { get; set; }
        public IEnumerable<int> AttentiveSecondsVTRIFRankOptions { get; set; }
        public IEnumerable<float> AttentiveSecondsVTRIFMedianOptions { get; set; }
        public IEnumerable<float> AttentiveSecondsVTRIFDiffOptions { get; set; }
        public IEnumerable<float> ReactionsOptions { get; set; }
        public IEnumerable<int> ReactionsRankOptions { get; set; }
        public IEnumerable<float> ReactionsMedianOptions { get; set; }
        public IEnumerable<float> ReactionsDiffOptions { get; set; }
        public IEnumerable<float> NegativityAvgICOptions { get; set; }
        public IEnumerable<int> NegativityAvgICRankOptions { get; set; }
        public IEnumerable<float> NegativityAvgICMedianOptions { get; set; }
        public IEnumerable<float> NegativityAvgICDiffOptions { get; set; }
        public IEnumerable<float> NegativityAvgIFOptions { get; set; }
        public IEnumerable<int> NegativityAvgIFRankOptions { get; set; }
        public IEnumerable<float> NegativityAvgIFMedianOptions { get; set; }
        public IEnumerable<float> NegativityAvgIFDiffOptions { get; set; }
        public IEnumerable<float> AttentionAvgICOptions { get; set; }
        public IEnumerable<int> AttentionAvgICRankOptions { get; set; }
        public IEnumerable<float> AttentionAvgICMedianOptions { get; set; }
        public IEnumerable<float> AttentionAvgICDiffOptions { get; set; }
        public IEnumerable<float> AttentionAvgIFOptions { get; set; }
        public IEnumerable<int> AttentionAvgIFRankOptions { get; set; }
        public IEnumerable<float> AttentionAvgIFMedianOptions { get; set; }
        public IEnumerable<float> AttentionAvgIFDiffOptions { get; set; }
        public IEnumerable<float> ReactionsICOptions { get; set; }
        public IEnumerable<int> ReactionsICRankOptions { get; set; }
        public IEnumerable<float> ReactionsICMedianOptions { get; set; }
        public IEnumerable<float> ReactionsICDiffOptions { get; set; }
        public IEnumerable<float> AdLikeabilityOptions { get; set; }
        public IEnumerable<int> AdLikeabilityRankOptions { get; set; }
        public IEnumerable<float> AdLikeabilityMedianOptions { get; set; }
        public IEnumerable<float> AdLikeabilityDiffOptions { get; set; }
        public IEnumerable<float> HappyPeakOptions { get; set; }
        public IEnumerable<int> HappyPeakRankOptions { get; set; }
        public IEnumerable<float> HappyPeakMedianOptions { get; set; }
        public IEnumerable<float> HappyPeakDiffOptions { get; set; }
        public IEnumerable<float> HappyPeakICOptions { get; set; }
        public IEnumerable<int> HappyPeakICRankOptions { get; set; }
        public IEnumerable<float> HappyPeakICMedianOptions { get; set; }
        public IEnumerable<float> HappyPeakICDiffOptions { get; set; }
        public IEnumerable<float> VTROptions { get; set; }
        public IEnumerable<int> VTRRankOptions { get; set; }
        public IEnumerable<float> VTRMedianOptions { get; set; }
        public IEnumerable<float> VTRDiffOptions { get; set; }
        public IEnumerable<float> VTRIFOptions { get; set; }
        public IEnumerable<int> VTRIFRankOptions { get; set; }
        public IEnumerable<float> VTRIFMedianOptions { get; set; }
        public IEnumerable<float> VTRIFDiffOptions { get; set; }
        public IEnumerable<float> PlaybackSecondsOptions { get; set; }
        public IEnumerable<int> PlaybackSecondsRankOptions { get; set; }
        public IEnumerable<float> PlaybackSecondsMedianOptions { get; set; }
        public IEnumerable<float> PlaybackSecondsDiffOptions { get; set; }
        public IEnumerable<float> PlaybackSecondsIFOptions { get; set; }
        public IEnumerable<int> PlaybackSecondsIFRankOptions { get; set; }
        public IEnumerable<float> PlaybackSecondsIFMedianOptions { get; set; }
        public IEnumerable<float> PlaybackSecondsIFDiffOptions { get; set; }
        public IEnumerable<float> AdRecognitionOptions { get; set; }
        public IEnumerable<int> AdRecognitionRankOptions { get; set; }
        public IEnumerable<float> AdRecognitionMedianOptions { get; set; }
        public IEnumerable<float> AdRecognitionDiffOptions { get; set; }
        public IEnumerable<float> SurprisePeakOptions { get; set; }
        public IEnumerable<int> SurprisePeakRankOptions { get; set; }
        public IEnumerable<float> SurprisePeakMedianOptions { get; set; }
        public IEnumerable<float> SurprisePeakDiffOptions { get; set; }
        public IEnumerable<float> SurprisePeakICOptions { get; set; }
        public IEnumerable<int> SurprisePeakICRankOptions { get; set; }
        public IEnumerable<float> SurprisePeakICMedianOptions { get; set; }
        public IEnumerable<float> SurprisePeakICDiffOptions { get; set; }
        public IEnumerable<float> ConfusionPeakOptions { get; set; }
        public IEnumerable<int> ConfusionPeakRankOptions { get; set; }
        public IEnumerable<float> ConfusionPeakMedianOptions { get; set; }
        public IEnumerable<float> ConfusionPeakDiffOptions { get; set; }
        public IEnumerable<float> ConfusionPeakICOptions { get; set; }
        public IEnumerable<int> ConfusionPeakICRankOptions { get; set; }
        public IEnumerable<float> ConfusionPeakICMedianOptions { get; set; }
        public IEnumerable<float> ConfusionPeakICDiffOptions { get; set; }
        public IEnumerable<float> ContemptPeakOptions { get; set; }
        public IEnumerable<int> ContemptPeakRankOptions { get; set; }
        public IEnumerable<float> ContemptPeakMedianOptions { get; set; }
        public IEnumerable<float> ContemptPeakDiffOptions { get; set; }
        public IEnumerable<float> ContemptPeakICOptions { get; set; }
        public IEnumerable<int> ContemptPeakICRankOptions { get; set; }
        public IEnumerable<float> ContemptPeakICMedianOptions { get; set; }
        public IEnumerable<float> ContemptPeakICDiffOptions { get; set; }
        public IEnumerable<float> DisgustPeakOptions { get; set; }
        public IEnumerable<int> DisgustPeakRankOptions { get; set; }
        public IEnumerable<float> DisgustPeakMedianOptions { get; set; }
        public IEnumerable<float> DisgustPeakDiffOptions { get; set; }
        public IEnumerable<float> DisgustPeakICOptions { get; set; }
        public IEnumerable<int> DisgustPeakICRankOptions { get; set; }
        public IEnumerable<float> DisgustPeakICMedianOptions { get; set; }
        public IEnumerable<float> DisgustPeakICDiffOptions { get; set; }
        public IEnumerable<float> BrandTrustOption { get; set; }
        public IEnumerable<int> BrandTrustRankOption { get; set; }
        public IEnumerable<float> BrandTrustMedianOption { get; set; }
        public IEnumerable<float> BrandTrustDiffOption { get; set; }
        public IEnumerable<float> PersuasionOptions { get; set; }
        public IEnumerable<int> PersuasionRankOptions { get; set; }
        public IEnumerable<float> PersuasionMedianOptions { get; set; }
        public IEnumerable<float> PersuasionDiffOptions { get; set; }
        public IEnumerable<float> DistractionAvgICOptions { get; set; }
        public IEnumerable<int> DistractionAvgICRankOptions { get; set; }
        public IEnumerable<float> DistractionAvgICMedianOptions { get; set; }
        public IEnumerable<float> DistractionAvgICDiffOptions { get; set; }
        public IEnumerable<float> DistractionAvgIFOptions { get; set; }
        public IEnumerable<int> DistractionAvgIFRankOptions { get; set; }
        public IEnumerable<float> DistractionAvgIFMedianOptions { get; set; }
        public IEnumerable<float> DistractionAvgIFDiffOptions { get; set; }

        public int BrandCount { get; set; }
        public int CountryCount { get; set; }
        public int AdformatCount { get; set; }
    }
}