using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Application.Common.Attributes;
using Application.Common.Interfaces;
using Application.Common.Models;
using Application.Common.Models.Settings;
using Application.Common.Security;
using BusinessLayer.Collector;
using BusinessLayer.Model.Request;
using BusinessLayer.Model.Response;
using MediatR;
using Microsoft.Extensions.Options;

namespace Application.CreativePerformanceInContext.Queries
{
    [Authorize(Policy = Policies.HasGuestAccess)]
    [CacheQuery(CacheDuration.ThirtyMinutes)]
    public class GetCreativePerformanceInContextQuery : IRequest<List<InContextCreative>>
    {
        [AuthorizeBy(AuthorizeByPropertyType.Account)]
        public int AccountId { get; set; }
        public List<MediaModelRequest> Media { get; set; }
        public bool IsAdmin { get; set; }
        public List<string> SegmentKeys { get; set; }
        public GetCreativePerformanceInContextQuery Clone() => (GetCreativePerformanceInContextQuery)MemberwiseClone();
    }

    public class GetCreativePerformanceInContextQueryHandler : IRequestHandler<GetCreativePerformanceInContextQuery, List<InContextCreative>>
    {
        private readonly IInContextCollector _inContextCollector;
        private readonly IMediaService _mediaService;
        private readonly IBrandService _brandService;
        private readonly QualitySettings _qualitySettings;

        public GetCreativePerformanceInContextQueryHandler(
            IInContextCollector inContextCollector,
            IMediaService mediaService,
            IBrandService brandService,
            IOptions<QualitySettings> qualitySettings)
        {
            _inContextCollector = inContextCollector;
            _mediaService = mediaService;
            _brandService = brandService;
            _qualitySettings = qualitySettings.Value;
        }

        public async Task<List<InContextCreative>> Handle(GetCreativePerformanceInContextQuery request, CancellationToken cancellationToken)
        {
            var getCreativesRequest = new GetCreativesRequest
            {
                AccountId = request.AccountId,
                Media = request.Media,
                SegmentKeys = request.SegmentKeys,
                IsAdmin = request.IsAdmin,
                MinViewThresholdForScores = _qualitySettings.MinViewThresholdForScores
            };

            var creatives = (await _inContextCollector.GetCreativesAsync(getCreativesRequest)).ToList();

            var brandLogoFileNames = creatives.Select(d => d.BrandLogoFileName);

            var brandLogoUrls = _brandService.GetLogoSignedUrls(brandLogoFileNames);
            foreach (var creativePerformanceDto in creatives)
            {
                if (!string.IsNullOrEmpty(creativePerformanceDto.BrandLogoFileName) && brandLogoUrls.TryGetValue(creativePerformanceDto.BrandLogoFileName, out var brandLogoUrl))
                {
                    creativePerformanceDto.BrandLogoUrl = brandLogoUrl;
                }

                creativePerformanceDto.ThumbnailUrl = _mediaService.GetThumbnailUrl(creativePerformanceDto.SourceMediaThumbnailFileName);
            }

            return creatives;
        }
    }
}
