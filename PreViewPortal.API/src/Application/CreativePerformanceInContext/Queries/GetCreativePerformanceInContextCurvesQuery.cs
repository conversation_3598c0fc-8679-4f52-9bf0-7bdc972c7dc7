using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Application.Common.Attributes;
using Application.Common.Models;
using Application.Common.Security;
using BusinessLayer.Collector;
using BusinessLayer.Model.Request;
using MediatR;

namespace Application.CreativePerformanceInContext.Queries
{
    [Authorize(Policy = Policies.HasGuestAccess)]
    [CacheQuery(CacheDuration.ThirtyMinutes)]
    public class GetCreativePerformanceInContextCurvesQuery : IRequest<List<BusinessLayer.Model.Response.InContextCurve>>
    {
        public List<MediaModelRequest> Media { get; set; } = new();
        public List<string> SegmentKeys { get; set; }
    }

    public class GetCreativePerformanceInContextCurvesQueryHandler : IRequestHandler<GetCreativePerformanceInContextCurvesQuery, List<BusinessLayer.Model.Response.InContextCurve>>
    {
        private readonly IInContextCollector _inContextCollector;


        public GetCreativePerformanceInContextCurvesQueryHandler(IInContextCollector inContextCollector)
        {
            _inContextCollector = inContextCollector;
        }

        public async Task<List<BusinessLayer.Model.Response.InContextCurve>> Handle(GetCreativePerformanceInContextCurvesQuery request, CancellationToken cancellationToken)
        {
            var getCurvesRequest = new GetCurvesRequest
            {
                Media = request.Media,
                SegmentKeys = request.SegmentKeys
            };

            var curves = await _inContextCollector.GetCurvesAsync(getCurvesRequest);

            return curves.ToList();
        }
    }
}
