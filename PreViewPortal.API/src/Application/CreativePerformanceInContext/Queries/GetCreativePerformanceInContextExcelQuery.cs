using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Application.Common.Interfaces;
using Application.Common.Security;
using BusinessLayer.Model;
using BusinessLayer.Model.Filter;
using BusinessLayer.Model.Request;
using BusinessLayer.Model.Response;
using BusinessLayer.Service;
using MediatR;
using ExportQuery = Application.Common.Queries.ExportQuery;

namespace Application.CreativePerformanceInContext.Queries
{
    [Authorize(Policy = Policies.HasGuestAccess)]
    public class GetCreativePerformanceInContextExcelQuery : ExportQuery, IRequest<ExportResult>
    {
        [AuthorizeBy(AuthorizeByPropertyType.Account)]
        public int AccountId { get; set; }
    }

    public class
        GetCreativePerformanceInContextExcelQueryHandler : IRequestHandler<GetCreativePerformanceInContextExcelQuery,
        ExportResult>
    {
        private readonly ICurrentUserService _currentUserService;
        private readonly IExportService _exportService;

        public GetCreativePerformanceInContextExcelQueryHandler(IMediator mediator,
            ICurrentUserService currentUserService, IExportService exportService)
        {
            _currentUserService = currentUserService;
            _exportService = exportService;
        }

        public async Task<ExportResult> Handle(GetCreativePerformanceInContextExcelQuery request,
            CancellationToken cancellationToken)
        {
            var exportFolderPath = GenerateExportFolderPath(request.AccountId);
            var exportConfiguration = CreateExportConfiguration(request, exportFolderPath);

            return await _exportService.StartExport(exportConfiguration);
        }

        private string GenerateExportFolderPath(int accountId)
        {
            var guid = Guid.NewGuid().ToString();
            return $"{accountId}/export/{guid}";
        }

        private StartExportRequest CreateExportConfiguration(GetCreativePerformanceInContextExcelQuery request,
            string exportFolderPath)
        {
            return new StartExportRequest
            {
                ProductType = ProductType.InContext,
                AccountId = request.AccountId,
                IsAdmin = _currentUserService.IsAdmin,
                ExportFileName = request.ExportFileName,
                SegmentKeys = request.SegmentKeys,
                ExportFolderPath = exportFolderPath,
                Media = request.Media,
                Filters = request.Filters.Select(item => new GridFilterItem
                {
                    ColumnField = item.ColumnField,
                    Value = item.Value,
                    OperatorValue = item.OperatorValue,
                    OrQueryGroupId = item.OrQueryGroupId
                }).ToList(),
                VisibleColumnsOrder = request.VisibleColumnsOrder,
                Sorting = request.Sorting.Select(item => new GridSortingItem
                {
                    Field = item.Field,
                    Direction = item.Direction,
                    FirstOrderText = item.FirstOrderText
                }).ToList(),
                CurveFilter = request.SelectedCurveTypes
            };
        }
    }
}