using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Application.Common.Attributes;
using Application.Common.Models;
using Application.Common.Queries;
using Application.Common.Security;
using BusinessLayer.Extensions;
using BusinessLayer.Model.Response;
using MediatR;

namespace Application.CreativePerformanceInContext.Queries
{
    [CacheQuery(CacheDuration.ThirtyMinutes)]
    public class GetCreativePerformanceInContextGridQuery : GridQuery, IRequest<CreativePerformanceInContextPageDto>
    {
        [AuthorizeBy(AuthorizeByPropertyType.Account)]
        public int AccountId { get; set; }
        public string SegmentKey { get; set; }
        public bool IsAdmin { get; set; }
    }

    public class GetCreativePerformanceInContextGridQueryHandler : IRequestHandler<
        GetCreativePerformanceInContextGridQuery, CreativePerformanceInContextPageDto>
    {
        private readonly IMediator _mediator;

        public GetCreativePerformanceInContextGridQueryHandler(IMediator mediator)
        {
            _mediator = mediator;
        }

        public async Task<CreativePerformanceInContextPageDto> Handle(GetCreativePerformanceInContextGridQuery request,
            CancellationToken cancellationToken)
        {
            var creativePerformances = await _mediator.Send(new GetCreativePerformanceInContextQuery
            {
                AccountId = request.AccountId,
                IsAdmin = request.IsAdmin,
                SegmentKeys = new() { request.SegmentKey },
            }, cancellationToken);

            CreativePerformanceInContextPageDto result = SetOptions(creativePerformances);
            var filteredCompetitives = creativePerformances.ApplyFilter(request);

            result.RowCount = filteredCompetitives.Count;
            result.Rows = filteredCompetitives.GetPage(request);
            result.BrandCount = filteredCompetitives.Select(c => c.BrandID).Distinct().Count(brandId => brandId != null);
            result.CountryCount = filteredCompetitives.Select(c => c.Country_code).Distinct().Count(countryCode => countryCode != null);
            result.AdformatCount = filteredCompetitives.Select(c => c.Adformat).Distinct().Count(Adformat => Adformat != null);

            return result;
        }

        private CreativePerformanceInContextPageDto SetOptions(List<InContextCreative> items)
        {
            if (items.Count == 0)
            {
                return new CreativePerformanceInContextPageDto();
            }

            return new CreativePerformanceInContextPageDto
            {
                BrandOptions = items.CreateCustomFilterOptionList(i => i.Brand),
                CreationDateOptions = items.CreateCustomFilterOptionDateString(b => b.CreationDate),
                CountryOptions = items.CreateCustomFilterOptionList(i => i.Country),
                DeviceOptions = items.CreateCustomFilterOptionList(i => i.Device),
                RegionOptions = items.CreateCustomFilterOptionList(i => i.GeographicRegion),
                AdformatTextOptions = items.CreateCustomFilterOptionList(i => i.AdformatText),
                DurationOptions = items.CreateCustomFilterOptionRange(b => b.Duration),
                ViewsOptions = items.CreateCustomFilterOptionRange(b => b.Views),
                TopCategoryOptions = items.CreateCustomFilterOptionList(b => b.TopCategory),
                MidCategoryOptions = items.CreateCustomFilterOptionList(b => b.MidCategory),
                SubCategoryOptions = items.CreateCustomFilterOptionList(b => b.SubCategory),
                QualityScoreOptions = items.CreateCustomFilterOptionRange(b => b.QualityScore),
                AttentiveSecondsOptions = items.CreateCustomFilterOptionRange(b => b.AttentiveSeconds),
                AttentiveSecondsMedianOptions = items.CreateCustomFilterOptionRange(b => b.AttentiveSecondsMedian),
                AttentiveSecondsRankOptions = items.CreateCustomFilterOptionRange(b => b.AttentiveSecondsRank),
                AttentiveSecondsDiffOptions = items.CreateCustomFilterOptionRange(b => b.AttentiveSecondsDiff),
                AttentiveSecondsIFOptions = items.CreateCustomFilterOptionRange(b => b.AttentiveSecondsIF),
                AttentiveSecondsIFMedianOptions = items.CreateCustomFilterOptionRange(b => b.AttentiveSecondsIFMedian),
                AttentiveSecondsIFRankOptions = items.CreateCustomFilterOptionRange(b => b.AttentiveSecondsIFRank),
                AttentiveSecondsIFDiffOptions = items.CreateCustomFilterOptionRange(b => b.AttentiveSecondsIFDiff),
                BrandRecognitionOptions = items.CreateCustomFilterOptionRange(b => b.BrandRecognition),
                BrandRecognitionMedianOptions = items.CreateCustomFilterOptionRange(b => b.BrandRecognitionMedian),
                BrandRecognitionRankOptions = items.CreateCustomFilterOptionRange(b => b.BrandRecognitionRank),
                BrandRecognitionDiffOptions = items.CreateCustomFilterOptionRange(b => b.BrandRecognitionDiff),
                AttentiveSecondsVTROptions = items.CreateCustomFilterOptionRange(b => b.AttentiveSecondsVTR),
                AttentiveSecondsVTRMedianOptions =
                    items.CreateCustomFilterOptionRange(b => b.AttentiveSecondsVTRMedian),
                AttentiveSecondsVTRRankOptions = items.CreateCustomFilterOptionRange(b => b.AttentiveSecondsVTRRank),
                AttentiveSecondsVTRDiffOptions = items.CreateCustomFilterOptionRange(b => b.AttentiveSecondsVTRDiff),
                AttentiveSecondsVTRIFOptions = items.CreateCustomFilterOptionRange(b => b.AttentiveSecondsVTRIF),
                AttentiveSecondsVTRIFMedianOptions =
                    items.CreateCustomFilterOptionRange(b => b.AttentiveSecondsVTRIFMedian),
                AttentiveSecondsVTRIFRankOptions =
                    items.CreateCustomFilterOptionRange(b => b.AttentiveSecondsVTRIFRank),
                AttentiveSecondsVTRIFDiffOptions =
                    items.CreateCustomFilterOptionRange(b => b.AttentiveSecondsVTRIFDiff),
                ReactionsOptions = items.CreateCustomFilterOptionRange(b => b.Reactions),
                ReactionsMedianOptions = items.CreateCustomFilterOptionRange(b => b.ReactionsMedian),
                ReactionsRankOptions = items.CreateCustomFilterOptionRange(b => b.ReactionsRank),
                ReactionsDiffOptions = items.CreateCustomFilterOptionRange(b => b.ReactionsDiff),
                ReactionsICOptions = items.CreateCustomFilterOptionRange(b => b.ReactionsIC),
                ReactionsICMedianOptions = items.CreateCustomFilterOptionRange(b => b.ReactionsICMedian),
                ReactionsICRankOptions = items.CreateCustomFilterOptionRange(b => b.ReactionsICRank),
                ReactionsICDiffOptions = items.CreateCustomFilterOptionRange(b => b.ReactionsICDiff),
                NegativityAvgICOptions = items.CreateCustomFilterOptionRange(b => b.NegativityAvgIC),
                NegativityAvgICMedianOptions = items.CreateCustomFilterOptionRange(b => b.NegativityAvgICMedian),
                NegativityAvgICRankOptions = items.CreateCustomFilterOptionRange(b => b.NegativityAvgICRank),
                NegativityAvgICDiffOptions = items.CreateCustomFilterOptionRange(b => b.NegativityAvgICDiff),
                NegativityAvgIFOptions = items.CreateCustomFilterOptionRange(b => b.NegativityAvgIF),
                NegativityAvgIFMedianOptions = items.CreateCustomFilterOptionRange(b => b.NegativityAvgIFMedian),
                NegativityAvgIFRankOptions = items.CreateCustomFilterOptionRange(b => b.NegativityAvgIFRank),
                NegativityAvgIFDiffOptions = items.CreateCustomFilterOptionRange(b => b.NegativityAvgIFDiff),
                AttentionAvgICOptions = items.CreateCustomFilterOptionRange(b => b.AttentionAvgIC),
                AttentionAvgICMedianOptions = items.CreateCustomFilterOptionRange(b => b.AttentionAvgICMedian),
                AttentionAvgICRankOptions = items.CreateCustomFilterOptionRange(b => b.AttentionAvgICRank),
                AttentionAvgICDiffOptions = items.CreateCustomFilterOptionRange(b => b.AttentionAvgICDiff),
                AttentionAvgIFOptions = items.CreateCustomFilterOptionRange(b => b.AttentionAvgIF),
                AttentionAvgIFMedianOptions = items.CreateCustomFilterOptionRange(b => b.AttentionAvgIFMedian),
                AttentionAvgIFRankOptions = items.CreateCustomFilterOptionRange(b => b.AttentionAvgIFRank),
                AttentionAvgIFDiffOptions = items.CreateCustomFilterOptionRange(b => b.AttentionAvgIFDiff),
                AdLikeabilityOptions = items.CreateCustomFilterOptionRange(b => b.AdLikeability),
                AdLikeabilityMedianOptions = items.CreateCustomFilterOptionRange(b => b.AdLikeabilityMedian),
                AdLikeabilityRankOptions = items.CreateCustomFilterOptionRange(b => b.AdLikeabilityRank),
                AdLikeabilityDiffOptions = items.CreateCustomFilterOptionRange(b => b.AdLikeabilityDiff),
                HappyPeakOptions = items.CreateCustomFilterOptionRange(b => b.HappyPeak),
                HappyPeakMedianOptions = items.CreateCustomFilterOptionRange(b => b.HappyPeakMedian),
                HappyPeakRankOptions = items.CreateCustomFilterOptionRange(b => b.HappyPeakRank),
                HappyPeakDiffOptions = items.CreateCustomFilterOptionRange(b => b.HappyPeakDiff),
                HappyPeakICOptions = items.CreateCustomFilterOptionRange(b => b.HappyPeakIC),
                HappyPeakICMedianOptions = items.CreateCustomFilterOptionRange(b => b.HappyPeakICMedian),
                HappyPeakICRankOptions = items.CreateCustomFilterOptionRange(b => b.HappyPeakICRank),
                HappyPeakICDiffOptions = items.CreateCustomFilterOptionRange(b => b.HappyPeakICDiff),
                VTROptions = items.CreateCustomFilterOptionRange(b => b.VTR),
                VTRRankOptions = items.CreateCustomFilterOptionRange(b => b.VTRRank),
                VTRMedianOptions = items.CreateCustomFilterOptionRange(b => b.VTRMedian),
                VTRDiffOptions = items.CreateCustomFilterOptionRange(b => b.VTRDiff),
                VTRIFOptions = items.CreateCustomFilterOptionRange(b => b.VTRIF),
                VTRIFRankOptions = items.CreateCustomFilterOptionRange(b => b.VTRIFRank),
                VTRIFMedianOptions = items.CreateCustomFilterOptionRange(b => b.VTRIFMedian),
                VTRIFDiffOptions = items.CreateCustomFilterOptionRange(b => b.VTRIFDiff),
                PlaybackSecondsOptions = items.CreateCustomFilterOptionRange(b => b.PlaybackSeconds),
                PlaybackSecondsRankOptions = items.CreateCustomFilterOptionRange(b => b.PlaybackSecondsRank),
                PlaybackSecondsMedianOptions = items.CreateCustomFilterOptionRange(b => b.PlaybackSecondsMedian),
                PlaybackSecondsDiffOptions = items.CreateCustomFilterOptionRange(b => b.PlaybackSecondsDiff),
                PlaybackSecondsIFOptions = items.CreateCustomFilterOptionRange(b => b.PlaybackSecondsIF),
                PlaybackSecondsIFRankOptions = items.CreateCustomFilterOptionRange(b => b.PlaybackSecondsIFRank),
                PlaybackSecondsIFMedianOptions = items.CreateCustomFilterOptionRange(b => b.PlaybackSecondsIFMedian),
                PlaybackSecondsIFDiffOptions = items.CreateCustomFilterOptionRange(b => b.PlaybackSecondsIFDiff),
                AdRecognitionOptions = items.CreateCustomFilterOptionRange(b => b.AdRecognition),
                AdRecognitionRankOptions = items.CreateCustomFilterOptionRange(b => b.AdRecognitionRank),
                AdRecognitionMedianOptions = items.CreateCustomFilterOptionRange(b => b.AdRecognitionMedian),
                AdRecognitionDiffOptions = items.CreateCustomFilterOptionRange(b => b.AdRecognitionDiff),
                SurprisePeakOptions = items.CreateCustomFilterOptionRange(b => b.SurprisePeak),
                SurprisePeakRankOptions = items.CreateCustomFilterOptionRange(b => b.SurprisePeakRank),
                SurprisePeakMedianOptions = items.CreateCustomFilterOptionRange(b => b.SurprisePeakMedian),
                SurprisePeakDiffOptions = items.CreateCustomFilterOptionRange(b => b.SurprisePeakDiff),
                SurprisePeakICOptions = items.CreateCustomFilterOptionRange(b => b.SurprisePeakIC),
                SurprisePeakICRankOptions = items.CreateCustomFilterOptionRange(b => b.SurprisePeakICRank),
                SurprisePeakICMedianOptions = items.CreateCustomFilterOptionRange(b => b.SurprisePeakICMedian),
                SurprisePeakICDiffOptions = items.CreateCustomFilterOptionRange(b => b.SurprisePeakICDiff),
                ConfusionPeakOptions = items.CreateCustomFilterOptionRange(b => b.ConfusionPeak),
                ConfusionPeakRankOptions = items.CreateCustomFilterOptionRange(b => b.ConfusionPeakRank),
                ConfusionPeakMedianOptions = items.CreateCustomFilterOptionRange(b => b.ConfusionPeakMedian),
                ConfusionPeakDiffOptions = items.CreateCustomFilterOptionRange(b => b.ConfusionPeakDiff),
                ConfusionPeakICOptions = items.CreateCustomFilterOptionRange(b => b.ConfusionPeakIC),
                ConfusionPeakICRankOptions = items.CreateCustomFilterOptionRange(b => b.ConfusionPeakICRank),
                ConfusionPeakICMedianOptions = items.CreateCustomFilterOptionRange(b => b.ConfusionPeakICMedian),
                ConfusionPeakICDiffOptions = items.CreateCustomFilterOptionRange(b => b.ConfusionPeakICDiff),
                ContemptPeakOptions = items.CreateCustomFilterOptionRange(b => b.ContemptPeak),
                ContemptPeakRankOptions = items.CreateCustomFilterOptionRange(b => b.ContemptPeakRank),
                ContemptPeakMedianOptions = items.CreateCustomFilterOptionRange(b => b.ContemptPeakMedian),
                ContemptPeakDiffOptions = items.CreateCustomFilterOptionRange(b => b.ContemptPeakDiff),
                ContemptPeakICOptions = items.CreateCustomFilterOptionRange(b => b.ContemptPeakIC),
                ContemptPeakICRankOptions = items.CreateCustomFilterOptionRange(b => b.ContemptPeakICRank),
                ContemptPeakICMedianOptions = items.CreateCustomFilterOptionRange(b => b.ContemptPeakICMedian),
                ContemptPeakICDiffOptions = items.CreateCustomFilterOptionRange(b => b.ContemptPeakICDiff),
                DisgustPeakOptions = items.CreateCustomFilterOptionRange(b => b.DisgustPeak),
                DisgustPeakRankOptions = items.CreateCustomFilterOptionRange(b => b.DisgustPeakRank),
                DisgustPeakMedianOptions = items.CreateCustomFilterOptionRange(b => b.DisgustPeakMedian),
                DisgustPeakDiffOptions = items.CreateCustomFilterOptionRange(b => b.DisgustPeakDiff),
                DisgustPeakICOptions = items.CreateCustomFilterOptionRange(b => b.DisgustPeakIC),
                DisgustPeakICRankOptions = items.CreateCustomFilterOptionRange(b => b.DisgustPeakICRank),
                DisgustPeakICMedianOptions = items.CreateCustomFilterOptionRange(b => b.DisgustPeakICMedian),
                DisgustPeakICDiffOptions = items.CreateCustomFilterOptionRange(b => b.DisgustPeakICDiff),
                BrandTrustOption = items.CreateCustomFilterOptionRange(b => b.BrandTrust),
                BrandTrustRankOption = items.CreateCustomFilterOptionRange(b => b.BrandTrustRank),
                BrandTrustMedianOption = items.CreateCustomFilterOptionRange(b => b.BrandTrustMedian),
                BrandTrustDiffOption = items.CreateCustomFilterOptionRange(b => b.BrandTrustDiff),
                PersuasionOptions = items.CreateCustomFilterOptionRange(b => b.Persuasion),
                PersuasionRankOptions = items.CreateCustomFilterOptionRange(b => b.PersuasionRank),
                PersuasionMedianOptions = items.CreateCustomFilterOptionRange(b => b.PersuasionMedian),
                PersuasionDiffOptions = items.CreateCustomFilterOptionRange(b => b.PersuasionDiff),
                DistractionAvgICOptions = items.CreateCustomFilterOptionRange(b => b.DistractionAvgIC),
                DistractionAvgICRankOptions = items.CreateCustomFilterOptionRange(b => b.DistractionAvgICRank),
                DistractionAvgICMedianOptions = items.CreateCustomFilterOptionRange(b => b.DistractionAvgICMedian),
                DistractionAvgICDiffOptions = items.CreateCustomFilterOptionRange(b => b.DistractionAvgICDiff),
                DistractionAvgIFOptions = items.CreateCustomFilterOptionRange(b => b.DistractionAvgIF),
                DistractionAvgIFRankOptions = items.CreateCustomFilterOptionRange(b => b.DistractionAvgIFRank),
                DistractionAvgIFMedianOptions = items.CreateCustomFilterOptionRange(b => b.DistractionAvgIFMedian),
                DistractionAvgIFDiffOptions = items.CreateCustomFilterOptionRange(b => b.DistractionAvgIFDiff)
            };
        }
    }
}