using System.Collections.Generic;
using BusinessLayer.Model.Filter;
using BusinessLayer.Model.Response;

namespace Application.CreativePerformanceForcedExposure
{
    public class CreativePerformanceForcedExposurePageDto : GridDto<ForcedExposureCreative>
    {
        public IEnumerable<string> BrandOptions { get; set; }
        public IEnumerable<string> CreationDateOptions { get; set; }
        public IEnumerable<string> CountryOptions { get; set; }
        public IEnumerable<string> DeviceOptions { get; set; }
        public IEnumerable<string> RegionOptions { get; set; }
        public IEnumerable<string> AdformatTextOptions { get; set; }
        public IEnumerable<int> DurationOptions { get; set; }
        public IEnumerable<int> ViewsOptions { get; set; }
        public IEnumerable<string> TopCategoryOptions { get; set; }
        public IEnumerable<string> MidCategoryOptions { get; set; }
        public IEnumerable<string> SubCategoryOptions { get; set; }
        public IEnumerable<float> QualityScoreOptions { get; set; }
        public IEnumerable<float> AttentiveSecondsOptions { get; set; }
        public IEnumerable<int> AttentiveSecondsRankOptions { get; set; }
        public IEnumerable<float> AttentiveSecondsMedianOptions { get; set; }
        public IEnumerable<float> AttentiveSecondsDiffOptions { get; set; }
        public IEnumerable<float> AttentionAvgOptions { get; set; }
        public IEnumerable<int> AttentionAvgRankOptions { get; set; }
        public IEnumerable<float> AttentionAvgMedianOptions { get; set; }
        public IEnumerable<float> AttentionAvgDiffOptions { get; set; }
        public IEnumerable<float> AttentiveSecondsVTROptions { get; set; }
        public IEnumerable<int> AttentiveSecondsVTRRankOptions { get; set; }
        public IEnumerable<float> AttentiveSecondsVTRMedianOptions { get; set; }
        public IEnumerable<float> AttentiveSecondsVTRDiffOptions { get; set; }
        public IEnumerable<float> ReactionsOptions { get; set; }
        public IEnumerable<int> ReactionsRankOptions { get; set; }
        public IEnumerable<float> ReactionsMedianOptions { get; set; }
        public IEnumerable<float> ReactionsDiffOptions { get; set; }
        public IEnumerable<float> NegativityAvgOptions { get; set; }
        public IEnumerable<int> NegativityAvgRankOptions { get; set; }
        public IEnumerable<float> NegativityAvgMedianOptions { get; set; }
        public IEnumerable<float> NegativityAvgDiffOptions { get; set; }
        public IEnumerable<float> HappyPeakOptions { get; set; }
        public IEnumerable<int> HappyPeakRankOptions { get; set; }
        public IEnumerable<float> HappyPeakMedianOptions { get; set; }
        public IEnumerable<float> HappyPeakDiffOptions { get; set; }
        public IEnumerable<float> VTROptions { get; set; }
        public IEnumerable<int> VTRRankOptions { get; set; }
        public IEnumerable<float> VTRMedianOptions { get; set; }
        public IEnumerable<float> VTRDiffOptions { get; set; }
        public IEnumerable<float> PlaybackSecondsOptions { get; set; }
        public IEnumerable<int> PlaybackSecondsRankOptions { get; set; }
        public IEnumerable<float> PlaybackSecondsMedianOptions { get; set; }
        public IEnumerable<float> PlaybackSecondsDiffOptions { get; set; }
        public IEnumerable<float> SurprisePeakOptions { get; set; }
        public IEnumerable<int> SurprisePeakRankOptions { get; set; }
        public IEnumerable<float> SurprisePeakMedianOptions { get; set; }
        public IEnumerable<float> SurprisePeakDiffOptions { get; set; }
        public IEnumerable<float> ConfusionPeakOptions { get; set; }
        public IEnumerable<int> ConfusionPeakRankOptions { get; set; }
        public IEnumerable<float> ConfusionPeakMedianOptions { get; set; }
        public IEnumerable<float> ConfusionPeakDiffOptions { get; set; }
        public IEnumerable<float> ContemptPeakOptions { get; set; }
        public IEnumerable<int> ContemptPeakRankOptions { get; set; }
        public IEnumerable<float> ContemptPeakMedianOptions { get; set; }
        public IEnumerable<float> ContemptPeakDiffOptions { get; set; }
        public IEnumerable<float> DisgustPeakOptions { get; set; }
        public IEnumerable<int> DisgustPeakRankOptions { get; set; }
        public IEnumerable<float> DisgustPeakMedianOptions { get; set; }
        public IEnumerable<float> DisgustPeakDiffOptions { get; set; }
        public IEnumerable<float> DistractionAvgOptions { get; set; }
        public IEnumerable<int> DistractionAvgRankOptions { get; set; }
        public IEnumerable<float> DistractionAvgMedianOptions { get; set; }
        public IEnumerable<float> DistractionAvgDiffOptions { get; set; }

        public int BrandCount { get; set; }
        public int CountryCount { get; set; }
    }
}