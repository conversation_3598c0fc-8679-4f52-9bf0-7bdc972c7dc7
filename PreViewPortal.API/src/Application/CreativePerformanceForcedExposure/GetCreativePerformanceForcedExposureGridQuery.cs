using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Application.Common.Attributes;
using Application.Common.Models;
using Application.Common.Queries;
using Application.Common.Security;
using BusinessLayer.Extensions;
using BusinessLayer.Model.Response;
using MediatR;

namespace Application.CreativePerformanceForcedExposure
{
    [CacheQuery(CacheDuration.ThirtyMinutes)]
    public class GetCreativePerformanceForcedExposureGridQuery : GridQuery,
        IRequest<CreativePerformanceForcedExposurePageDto>
    {
        [AuthorizeBy(AuthorizeByPropertyType.Account)]
        public int AccountId { get; set; }
        public string SegmentKey { get; set; }
        public bool IsAdmin { get; set; }
    }

    public class GetCreativePerformanceForcedExposureGridQueryHandler : IRequestHandler<
        GetCreativePerformanceForcedExposureGridQuery, CreativePerformanceForcedExposurePageDto>
    {
        private readonly IMediator _mediator;

        public GetCreativePerformanceForcedExposureGridQueryHandler(IMediator mediator)
        {
            _mediator = mediator;
        }

        public async Task<CreativePerformanceForcedExposurePageDto> Handle(
            GetCreativePerformanceForcedExposureGridQuery request, CancellationToken cancellationToken)
        {
            var creativePerformances = await _mediator.Send(new GetCreativePerformanceForcedExposureQuery
            {
                AccountId = request.AccountId,
                IsAdmin = request.IsAdmin,
                SegmentKeys = new() { request.SegmentKey },
            }, cancellationToken);

            var result = SetOptions(creativePerformances);
            var filteredCompetitives = creativePerformances.ApplyFilter(request);

            result.RowCount = filteredCompetitives.Count;
            result.Rows = filteredCompetitives.GetPage(request);
            result.BrandCount = filteredCompetitives.Select(c => c.BrandID).Distinct().Count(brandId => brandId != null);
            result.CountryCount = filteredCompetitives.Select(c => c.Country_code).Distinct().Count(countryCode => countryCode != null);

            return result;
        }

        private CreativePerformanceForcedExposurePageDto SetOptions(List<ForcedExposureCreative> items)
        {
            if (items.Count == 0)
            {
                return new CreativePerformanceForcedExposurePageDto();
            }

            return new CreativePerformanceForcedExposurePageDto
            {
                BrandOptions = items.CreateCustomFilterOptionList(i => i.Brand),
                CreationDateOptions = items.CreateCustomFilterOptionDateString(b => b.CreationDate),
                CountryOptions = items.CreateCustomFilterOptionList(i => i.Country),
                DeviceOptions = items.CreateCustomFilterOptionList(i => i.Device),
                RegionOptions = items.CreateCustomFilterOptionList(i => i.GeographicRegion),
                AdformatTextOptions = items.CreateCustomFilterOptionList(i => i.AdformatText),
                DurationOptions = items.CreateCustomFilterOptionRange(b => b.Duration),
                ViewsOptions = items.CreateCustomFilterOptionRange(b => b.Views),
                TopCategoryOptions = items.CreateCustomFilterOptionList(b => b.TopCategory),
                MidCategoryOptions = items.CreateCustomFilterOptionList(b => b.MidCategory),
                SubCategoryOptions = items.CreateCustomFilterOptionList(b => b.SubCategory),
                QualityScoreOptions = items.CreateCustomFilterOptionRange(b => b.QualityScore),
                AttentiveSecondsOptions = items.CreateCustomFilterOptionRange(b => b.AttentiveSeconds),
                AttentiveSecondsMedianOptions = items.CreateCustomFilterOptionRange(b => b.AttentiveSecondsMedian),
                AttentiveSecondsRankOptions = items.CreateCustomFilterOptionRange(b => b.AttentiveSecondsRank),
                AttentiveSecondsDiffOptions = items.CreateCustomFilterOptionRange(b => b.AttentiveSecondsDiff),
                AttentionAvgOptions = items.CreateCustomFilterOptionRange(b => b.AttentionAvg),
                AttentionAvgMedianOptions = items.CreateCustomFilterOptionRange(b => b.AttentionAvgMedian),
                AttentionAvgRankOptions = items.CreateCustomFilterOptionRange(b => b.AttentionAvgRank),
                AttentionAvgDiffOptions = items.CreateCustomFilterOptionRange(b => b.AttentionAvgDiff),
                AttentiveSecondsVTROptions = items.CreateCustomFilterOptionRange(b => b.AttentiveSecondsVTR),
                AttentiveSecondsVTRMedianOptions = items.CreateCustomFilterOptionRange(b => b.AttentiveSecondsVTRMedian),
                AttentiveSecondsVTRRankOptions = items.CreateCustomFilterOptionRange(b => b.AttentiveSecondsVTRRank),
                AttentiveSecondsVTRDiffOptions = items.CreateCustomFilterOptionRange(b => b.AttentiveSecondsVTRDiff),
                ReactionsOptions = items.CreateCustomFilterOptionRange(b => b.Reactions),
                ReactionsMedianOptions = items.CreateCustomFilterOptionRange(b => b.ReactionsMedian),
                ReactionsRankOptions = items.CreateCustomFilterOptionRange(b => b.ReactionsRank),
                ReactionsDiffOptions = items.CreateCustomFilterOptionRange(b => b.ReactionsDiff),
                NegativityAvgOptions = items.CreateCustomFilterOptionRange(b => b.NegativityAvg),
                NegativityAvgMedianOptions = items.CreateCustomFilterOptionRange(b => b.NegativityAvgMedian),
                NegativityAvgRankOptions = items.CreateCustomFilterOptionRange(b => b.NegativityAvgRank),
                NegativityAvgDiffOptions = items.CreateCustomFilterOptionRange(b => b.NegativityAvgDiff),
                HappyPeakOptions = items.CreateCustomFilterOptionRange(b => b.HappyPeak),
                HappyPeakMedianOptions = items.CreateCustomFilterOptionRange(b => b.HappyPeakMedian),
                HappyPeakRankOptions = items.CreateCustomFilterOptionRange(b => b.HappyPeakRank),
                HappyPeakDiffOptions = items.CreateCustomFilterOptionRange(b => b.HappyPeakDiff),
                VTROptions = items.CreateCustomFilterOptionRange(b => b.VTR),
                VTRRankOptions = items.CreateCustomFilterOptionRange(b => b.VTRRank),
                VTRMedianOptions = items.CreateCustomFilterOptionRange(b => b.VTRMedian),
                VTRDiffOptions = items.CreateCustomFilterOptionRange(b => b.VTRDiff),
                PlaybackSecondsOptions = items.CreateCustomFilterOptionRange(b => b.PlaybackSeconds),
                PlaybackSecondsRankOptions = items.CreateCustomFilterOptionRange(b => b.PlaybackSecondsRank),
                PlaybackSecondsMedianOptions = items.CreateCustomFilterOptionRange(b => b.PlaybackSecondsMedian),
                PlaybackSecondsDiffOptions = items.CreateCustomFilterOptionRange(b => b.PlaybackSecondsDiff),
                SurprisePeakOptions = items.CreateCustomFilterOptionRange(b => b.SurprisePeak),
                SurprisePeakRankOptions = items.CreateCustomFilterOptionRange(b => b.SurprisePeakRank),
                SurprisePeakMedianOptions = items.CreateCustomFilterOptionRange(b => b.SurprisePeakMedian),
                SurprisePeakDiffOptions = items.CreateCustomFilterOptionRange(b => b.SurprisePeakDiff),
                ConfusionPeakOptions = items.CreateCustomFilterOptionRange(b => b.ConfusionPeak),
                ConfusionPeakRankOptions = items.CreateCustomFilterOptionRange(b => b.ConfusionPeakRank),
                ConfusionPeakMedianOptions = items.CreateCustomFilterOptionRange(b => b.ConfusionPeakMedian),
                ConfusionPeakDiffOptions = items.CreateCustomFilterOptionRange(b => b.ConfusionPeakDiff),
                ContemptPeakOptions = items.CreateCustomFilterOptionRange(b => b.ContemptPeak),
                ContemptPeakRankOptions = items.CreateCustomFilterOptionRange(b => b.ContemptPeakRank),
                ContemptPeakMedianOptions = items.CreateCustomFilterOptionRange(b => b.ContemptPeakMedian),
                ContemptPeakDiffOptions = items.CreateCustomFilterOptionRange(b => b.ContemptPeakDiff),
                DisgustPeakOptions = items.CreateCustomFilterOptionRange(b => b.DisgustPeak),
                DisgustPeakRankOptions = items.CreateCustomFilterOptionRange(b => b.DisgustPeakRank),
                DisgustPeakMedianOptions = items.CreateCustomFilterOptionRange(b => b.DisgustPeakMedian),
                DisgustPeakDiffOptions = items.CreateCustomFilterOptionRange(b => b.DisgustPeakDiff),
                DistractionAvgOptions = items.CreateCustomFilterOptionRange(b => b.DistractionAvg),
                DistractionAvgRankOptions = items.CreateCustomFilterOptionRange(b => b.DistractionAvgRank),
                DistractionAvgMedianOptions = items.CreateCustomFilterOptionRange(b => b.DistractionAvgMedian),
                DistractionAvgDiffOptions = items.CreateCustomFilterOptionRange(b => b.DistractionAvgDiff)
            };
        }
    }
}