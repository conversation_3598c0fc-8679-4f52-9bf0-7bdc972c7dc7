using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Application.Common.Interfaces;
using Application.Common.Security;
using BusinessLayer.Model;
using BusinessLayer.Model.Filter;
using BusinessLayer.Model.Request;
using BusinessLayer.Model.Response;
using BusinessLayer.Service;
using MediatR;
using ExportQuery = Application.Common.Queries.ExportQuery;

namespace Application.CreativePerformanceForcedExposure
{
    [Authorize(Policy = Policies.HasGuestAccess)]
    public class GetCreativePerformanceForcedExposureExcelQuery : ExportQuery, IRequest<ExportResult>
    {
        [AuthorizeBy(AuthorizeByPropertyType.Account)]
        public int AccountId { get; set; }
    }

    public class
        GetCreativePerformanceForcedExposureExcelQueryHandler : IRequestHandler<
        GetCreativePerformanceForcedExposureExcelQuery, ExportResult>
    {
        private readonly ICurrentUserService _currentUserService;
        private readonly IExportService _exportService;

        public GetCreativePerformanceForcedExposureExcelQueryHandler(IMediator mediator,
            ICurrentUserService currentUserService, IExportService exportService)
        {
            _currentUserService = currentUserService;
            _exportService = exportService;
        }

        public async Task<ExportResult> Handle(GetCreativePerformanceForcedExposureExcelQuery request,
            CancellationToken cancellationToken)
        {
            var exportFolderPath = GenerateExportFolderPath(request.AccountId);
            var exportConfiguration = CreateExportConfiguration(request, exportFolderPath);

            return await _exportService.StartExport(exportConfiguration);
        }

        private string GenerateExportFolderPath(int accountId)
        {
            var guid = Guid.NewGuid().ToString();
            return $"{accountId}/export/{guid}";
        }

        private StartExportRequest CreateExportConfiguration(GetCreativePerformanceForcedExposureExcelQuery request,
            string exportFolderPath)
        {
            return new StartExportRequest
            {
                ExportFolderPath = exportFolderPath,
                ProductType = ProductType.NewForcedExposure,
                AccountId = request.AccountId,
                IsAdmin = _currentUserService.IsAdmin,
                SegmentKeys = request.SegmentKeys,
                Media = request.Media,
                ExportFileName = request.ExportFileName,
                Filters = request.Filters.Select(item => new GridFilterItem
                {
                    ColumnField = item.ColumnField,
                    Value = item.Value,
                    OperatorValue = item.OperatorValue,
                    OrQueryGroupId = item.OrQueryGroupId
                }).ToList(),
                VisibleColumnsOrder = request.VisibleColumnsOrder,
                Sorting = request.Sorting.Select(item => new GridSortingItem
                {
                    Field = item.Field,
                    Direction = item.Direction,
                    FirstOrderText = item.FirstOrderText
                }).ToList(),
                CurveFilter = request.SelectedCurveTypes
            };
        }
    }
}