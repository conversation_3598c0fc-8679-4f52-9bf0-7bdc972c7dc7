using Application.Common.Attributes;
using Application.Common.Models;
using Application.Common.Security;
using MediatR;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using BusinessLayer.Model.Request;
using System.Linq;
using BusinessLayer.Collector;

namespace Application.CreativePerformanceForcedExposure
{
    [Authorize(Policy = Policies.HasGuestAccess)]
    [CacheQuery(CacheDuration.ThirtyMinutes)]
    public class GetCreativePerformanceForcedExposureCurvesQuery : IRequest<List<BusinessLayer.Model.Response.ForcedExposureCurve>>
    {
        public List<MediaModelRequest> Media { get; set; } = new();
        public List<string> SegmentKeys { get; set; }
    }

    public class GetCreativePerformanceForcedExposureCurvesQueryHandler : IRequestHandler<GetCreativePerformanceForcedExposureCurvesQuery, List<BusinessLayer.Model.Response.ForcedExposureCurve>>
    {
        private readonly IForcedExposureCollector _forcedExposureCollector;

        public GetCreativePerformanceForcedExposureCurvesQueryHandler(IForcedExposureCollector forcedExposureCollector)
        {
            _forcedExposureCollector = forcedExposureCollector;
        }

        public async Task<List<BusinessLayer.Model.Response.ForcedExposureCurve>> Handle(GetCreativePerformanceForcedExposureCurvesQuery request, CancellationToken cancellationToken)
        {
            var getCurvesRequest = new GetCurvesRequest
            {
                Media = request.Media,
                SegmentKeys = request.SegmentKeys
            };

            var curves = await _forcedExposureCollector.GetCurvesAsync(getCurvesRequest);

            return curves.ToList();
        }
    }

}
