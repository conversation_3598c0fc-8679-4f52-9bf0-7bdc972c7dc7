using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Application.Common.Attributes;
using Application.Common.Extensions;
using Application.Common.Interfaces;
using Application.Common.Models;
using Application.Common.Models.Settings;
using Application.Common.Security;
using Application.CreativePerformanceInContext.Queries;
using Application.MediaSegment.Queries;
using BusinessLayer.Constant;
using BusinessLayer.Model.Request;
using BusinessLayer.Model.Response;
using MediatR;
using Microsoft.Extensions.Options;

namespace Application.CreativeViewer.Queries
{
    [Authorize(Policy = Policies.HasGuestAccess)]
    [CacheQuery(CacheDuration.ThirtyMinutes)]
    public class GetCreativeViewerInContextQuery : IRequest<CreativeViewerVm>
    {
        public List<MediaModelRequest> Media { get; set; }

        [AuthorizeBy(AuthorizeByPropertyType.Account)]
        public int AccountId { get; set; }
    }

    public class
        GetCreativeViewerInContextQueryHandler : IRequestHandler<GetCreativeViewerInContextQuery, CreativeViewerVm>
    {
        private readonly IMediaService _mediaService;
        private readonly ISender _mediator;
        private readonly QualitySettings _qualitySettings;
        private readonly ICurrentUserService _currentUserService;


        public GetCreativeViewerInContextQueryHandler(
            IMediaService mediaService,
            ISender mediator,
            IOptions<QualitySettings> qualitySettings,
            ICurrentUserService currentUserService)
        {
            _mediaService = mediaService;
            _mediator = mediator;
            _qualitySettings = qualitySettings.Value;
            _currentUserService = currentUserService;
        }

        public async Task<CreativeViewerVm> Handle(GetCreativeViewerInContextQuery request,
            CancellationToken cancellationToken)
        {
            var creativePerformanceDataTask =
                _mediator.Send(
                    new GetCreativePerformanceInContextQuery
                    {
                        AccountId = request.AccountId,
                        Media = request.Media,
                        IsAdmin = _currentUserService.IsAdmin
                    },
                    cancellationToken);

            var curveDataTask =
                _mediator.Send(
                    new GetCreativePerformanceInContextCurvesQuery
                    { Media = request.Media }, cancellationToken);

            var mediaSegmentsTask = _mediator.Send(new GetMediaSegmentKeyQuery
            {
                AccountId = request.AccountId,
                Media = request.Media
            }, cancellationToken);

            await Task.WhenAll(creativePerformanceDataTask, curveDataTask, mediaSegmentsTask).ConfigureAwait(false);
            var creativeData = await creativePerformanceDataTask.ConfigureAwait(false);
            var curveData = await curveDataTask.ConfigureAwait(false);
            var segmentsData = await mediaSegmentsTask.ConfigureAwait(false);

            var creativePerformanceInContextDtos = creativeData
                .OrderBy(cp => request.Media
                    .FindIndex(m =>
                        m.SourceMediaID == cp.SourceMediaID && m.TestID == cp.TestID && m.TaskID == cp.TaskID && m.OrderAdSetID == cp.OrderAdSetID))
                .ToList();

            var segmentKeys = segmentsData.SegmentKeyLabels.Select(s => s.SegmentKey);

            var curveDtos = MapCurveToDto(curveData, creativePerformanceInContextDtos, segmentKeys,
                _qualitySettings.MinViewThresholdForScores);

            var creativeViewerScores = creativePerformanceInContextDtos
                .Select(cp =>
                    new CreativeViewerSurveyScore
                    {
                        Id = $"{cp.SourceMediaID}-{cp.TestID}-{cp.TaskID}-{cp.OrderAdSetID}",
                        SegmentKey = cp.SegmentKey,
                        Views = cp.Views,
                        BrandRecognition = cp.BrandRecognition,
                        BrandRecognitionMedian = cp.BrandRecognitionMedian,
                        BrandRecognitionDiff = cp.BrandRecognitionDiff,
                        AdLikeability = cp.AdLikeability,
                        AdLikeabilityMedian = cp.AdLikeabilityMedian,
                        AdLikeabilityDiff = cp.AdLikeabilityDiff,
                        AdRecognition = cp.AdRecognition,
                        AdRecognitionMedian = cp.AdRecognitionMedian,
                        AdRecognitionDiff = cp.AdRecognitionDiff,
                        BrandTrust = cp.BrandTrust,
                        BrandTrustMedian = cp.BrandTrustMedian,
                        BrandTrustDiff = cp.BrandTrustDiff,
                        Persuasion = cp.Persuasion,
                        PersuasionMedian = cp.PersuasionMedian,
                        PersuasionDiff = cp.PersuasionDiff,
                    }
                )
                .ToList();

            var creativeViewerNorms = creativePerformanceInContextDtos
                .Select(cp =>
                    new CreativeViewerNorm
                    {
                        VideoId = $"{cp.SourceMediaID}-{cp.TestID}-{cp.TaskID}-{cp.OrderAdSetID}",
                        SegmentKey = cp.SegmentKey,

                        Views = cp.Views,

                        NormFallbackIC = cp.NormFallbackIC,
                        NormFallbackIF = cp.NormFallbackIF,
                        NormFallbackSurveyIC = cp.NormFallbackSurveyIC,
                        NormFallbackSurveyIF = cp.NormFallbackSurveyIF,
                        NormSegmentKeyIC = cp.NormSegmentKeyIC,
                        NormSegmentKeyIF = cp.NormSegmentKeyIF,

                        ReactionsNormDeviceIF = cp.ReactionsNormDeviceIF,
                        ReactionsNormDeviceIC = cp.ReactionsNormDeviceIC,
                        SurveyNormDeviceIF = cp.SurveyNormDeviceIF,
                        SurveyNormDeviceIC = cp.SurveyNormDeviceIC,
                        AttentionNormDeviceIF = cp.AttentionNormDeviceIF,
                        AttentionNormDeviceIC = cp.AttentionNormDeviceIC,

                        ReactionsNormFormatIF = cp.ReactionsNormFormatIF,
                        ReactionsNormFormatIC = cp.ReactionsNormFormatIC,
                        SurveyNormFormatIF = cp.SurveyNormFormatIF,
                        SurveyNormFormatIC = cp.SurveyNormFormatIC,
                        AttentionNormFormatIF = cp.AttentionNormFormatIF,
                        AttentionNormFormatIC = cp.AttentionNormFormatIC,

                        ReactionsNormDurationIF = cp.ReactionsNormDurationIF,
                        ReactionsNormDurationIC = cp.ReactionsNormDurationIC,
                        SurveyNormDurationIF = cp.SurveyNormDurationIF,
                        SurveyNormDurationIC = cp.SurveyNormDurationIC,
                        AttentionNormDurationIF = cp.AttentionNormDurationIF,
                        AttentionNormDurationIC = cp.AttentionNormDurationIC,

                        ReactionsNormAdFormatIF = cp.ReactionsNormAdFormatIF,
                        ReactionsNormAdFormatIC = cp.ReactionsNormAdFormatIC,
                        SurveyNormAdFormatIF = cp.SurveyNormAdFormatIF,
                        SurveyNormAdFormatIC = cp.SurveyNormAdFormatIC,
                        AttentionNormAdFormatIF = cp.AttentionNormAdFormatIF,
                        AttentionNormAdFormatIC = cp.AttentionNormAdFormatIC,

                        ReactionsNormAdFormatNameIF = cp.ReactionsNormAdFormatNameIF,
                        ReactionsNormAdFormatNameIC = cp.ReactionsNormAdFormatNameIC,
                        SurveyNormAdFormatNameIF = cp.SurveyNormAdFormatNameIF,
                        SurveyNormAdFormatNameIC = cp.SurveyNormAdFormatNameIC,
                        AttentionNormAdFormatNameIF = cp.AttentionNormAdFormatNameIF,
                        AttentionNormAdFormatNameIC = cp.AttentionNormAdFormatNameIC,

                        ReactionsNormEnvironmentCategoryIF = cp.ReactionsNormEnvironmentCategoryIF,
                        ReactionsNormEnvironmentCategoryIC = cp.ReactionsNormEnvironmentCategoryIC,
                        SurveyNormEnvironmentCategoryIF = cp.SurveyNormEnvironmentCategoryIF,
                        SurveyNormEnvironmentCategoryIC = cp.SurveyNormEnvironmentCategoryIC,
                        AttentionNormEnvironmentCategoryIF = cp.AttentionNormEnvironmentCategoryIF,
                        AttentionNormEnvironmentCategoryIC = cp.AttentionNormEnvironmentCategoryIC,

                        ReactionsNormRegionIF = cp.ReactionsNormRegionIF,
                        ReactionsNormRegionIC = cp.ReactionsNormRegionIC,
                        SurveyNormRegionIF = cp.SurveyNormRegionIF,
                        SurveyNormRegionIC = cp.SurveyNormRegionIC,
                        AttentionNormRegionIF = cp.AttentionNormRegionIF,
                        AttentionNormRegionIC = cp.AttentionNormRegionIC,

                        ReactionsNormSampleSizeIF = cp.ReactionsNormSampleSizeIF,
                        ReactionsNormSampleSizeIC = cp.ReactionsNormSampleSizeIC,
                        SurveyNormSampleSizeIF = cp.SurveyNormSampleSizeIF,
                        SurveyNormSampleSizeIC = cp.SurveyNormSampleSizeIC,
                        AttentionNormSampleSizeIF = cp.AttentionNormSampleSizeIF,
                        AttentionNormSampleSizeIC = cp.AttentionNormSampleSizeIC,

                        ReactionsNormRefreshDateIF = cp.ReactionsNormRefreshDateIF,
                        ReactionsNormRefreshDateIC = cp.ReactionsNormRefreshDateIC,
                        SurveyNormRefreshDateIF = cp.SurveyNormRefreshDateIF,
                        SurveyNormRefreshDateIC = cp.SurveyNormRefreshDateIC,
                        AttentionNormRefreshDateIF = cp.AttentionNormRefreshDateIF,
                        AttentionNormRefreshDateIC = cp.AttentionNormRefreshDateIC,

                        ReactionsNormCustomNormFilters = cp.ReactionsNormCustomNormFilters,
                        AttentionNormCustomNormFilters = cp.AttentionNormCustomNormFilters,
                        SurveyNormCustomNormFilters = cp.SurveyNormCustomNormFilters,
                        ReactionsNormCustomNormFiltersIC = cp.ReactionsNormCustomNormFiltersIC,
                        AttentionNormCustomNormFiltersIC = cp.AttentionNormCustomNormFiltersIC,
                        SurveyNormCustomNormFiltersIC = cp.SurveyNormCustomNormFiltersIC,
                        ReactionsNormCustomNormFiltersIF = cp.ReactionsNormCustomNormFiltersIF,
                        AttentionNormCustomNormFiltersIF = cp.AttentionNormCustomNormFiltersIF,
                        SurveyNormCustomNormFiltersIF = cp.SurveyNormCustomNormFiltersIF
                    }
                )
                .ToList();

            var creativeViewerVideoDtos = creativePerformanceInContextDtos
                .Where(d => d.SegmentKey == SegmentKeyConstant.AllSegmentKey)
                .Select(cp =>
                    new CreativeViewerVideoDto
                    {
                        SourceMediaID = cp?.SourceMediaID ?? default,
                        TestID = cp?.TestID ?? default,
                        InFocusTestID = cp?.InFocusTestID ?? default,
                        SurveyKeyAlias = cp?.SurveyKeyAlias,
                        TaskID = cp?.TaskID,
                        OrderExternalKey = cp?.OrderExternalKey,
                        OrderAdSetID = cp?.OrderAdSetID ?? default,
                        SourceMedia = cp?.SourceMedia,
                        Duration = cp?.Duration ?? default,
                        Brand = cp?.Brand,
                        BrandLogoUrl = cp?.BrandLogoUrl,
                        ThumbnailUrl = cp?.ThumbnailUrl,
                        SourceMediaUrl = _mediaService.GetMediaSignedUrl(cp.SourceMediaConvertedFileName),
                        ThumbstripUrl = _mediaService.GetMediaSignedUrl(cp.SourceMediaThumbstripFileName),
                        Platform = cp?.Platform,
                        AdformatText = cp?.AdformatText,
                        AdformatTextIF = cp?.AdformatTextIF,
                        Country = cp?.Country,
                        Country_code = cp?.Country_code
                    })
                .ToList();

            return new CreativeViewerVm
            {
                VideoData = creativeViewerVideoDtos,
                CurveData = curveDtos,
                SegmentData = segmentsData,
                SurveyScoreData = creativeViewerScores,
                NormData = creativeViewerNorms
            };
        }

        private static List<CreativeViewerCurvesDto> MapCurveToDto(IEnumerable<InContextCurve> curves,
            IEnumerable<InContextCreative> creatives, IEnumerable<string> segmentKeys, int minThreshold)
        {
            var mediaCurves = new List<CreativeViewerCurvesDto>();

            foreach (var videoCurves in curves.GroupBy(c => new { c.SourceMediaID, c.TestID, c.TaskID }))
            {
                var mediaCurve = new CreativeViewerCurvesDto
                {
                    SourceMediaId = videoCurves.Key.SourceMediaID,
                    TestId = videoCurves.Key.TestID,
                    TaskId = videoCurves.Key.TaskID
                };

                foreach (var segmentKey in segmentKeys)
                {
                    var creative = creatives.FirstOrDefault(cp =>
                        cp.SourceMediaID == videoCurves.Key.SourceMediaID && cp.TestID == videoCurves.Key.TestID &&
                        cp.TaskID == videoCurves.Key.TaskID && cp.SegmentKey == segmentKey);

                    if (mediaCurve.OrderAdSetID is null)
                    {
                        mediaCurve.OrderAdSetID = creative?.OrderAdSetID;
                    }

                    var views = creative?.Views;

                    if (creative is null || views < minThreshold)
                        continue;

                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Attention, segmentKey, false,
                        ExposureGroup.InContext, c => c.Attention.Value, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Attention, segmentKey, true,
                        ExposureGroup.InContext, c => c.AttentionNorm, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Attention, segmentKey, false,
                        ExposureGroup.Focused, c => c.AttentionIF, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Attention, segmentKey, true,
                        ExposureGroup.Focused, c => c.AttentionIFNorm, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.AllReactions, segmentKey, false,
                        ExposureGroup.Focused, c => c.AllReactions, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.AllReactions, segmentKey, true,
                        ExposureGroup.Focused, c => c.AllReactionsNorm, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.AllReactions, segmentKey, false,
                        ExposureGroup.InContext, c => c.AllReactionsIC, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.AllReactions, segmentKey, true,
                        ExposureGroup.InContext, c => c.AllReactionsICNorm, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Negativity, segmentKey, false,
                        ExposureGroup.Focused, c => c.Negativity, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Negativity, segmentKey, true,
                        ExposureGroup.Focused, c => c.NegativityNorm, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Negativity, segmentKey, false,
                        ExposureGroup.InContext, c => c.NegativityIC, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Negativity, segmentKey, true,
                        ExposureGroup.InContext, c => c.NegativityICNorm, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Happiness, segmentKey, false,
                        ExposureGroup.Focused, c => c.Happiness, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Happiness, segmentKey, true,
                        ExposureGroup.Focused, c => c.HappinessNorm, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Happiness, segmentKey, false,
                        ExposureGroup.InContext, c => c.HappinessIC, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Happiness, segmentKey, true,
                        ExposureGroup.InContext, c => c.HappinessICNorm, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Contempt, segmentKey, false,
                        ExposureGroup.Focused, c => c.Contempt, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Contempt, segmentKey, true,
                        ExposureGroup.Focused, c => c.ContemptNorm, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Contempt, segmentKey, false,
                        ExposureGroup.InContext, c => c.ContemptIC, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Contempt, segmentKey, true,
                        ExposureGroup.InContext, c => c.ContemptICNorm, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Surprise, segmentKey, false,
                        ExposureGroup.Focused, c => c.Surprise, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Surprise, segmentKey, true,
                        ExposureGroup.Focused, c => c.SurpriseNorm, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Surprise, segmentKey, false,
                        ExposureGroup.InContext, c => c.SurpriseIC, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Surprise, segmentKey, true,
                        ExposureGroup.InContext, c => c.SurpriseICNorm, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Confusion, segmentKey, false,
                        ExposureGroup.Focused, c => c.Confusion, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Confusion, segmentKey, true,
                        ExposureGroup.Focused, c => c.ConfusionNorm, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Confusion, segmentKey, false,
                        ExposureGroup.InContext, c => c.ConfusionIC, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Confusion, segmentKey, true,
                        ExposureGroup.InContext, c => c.ConfusionICNorm, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Disgust, segmentKey, false,
                        ExposureGroup.Focused, c => c.Disgust, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Disgust, segmentKey, true,
                        ExposureGroup.Focused, c => c.DisgustNorm, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Disgust, segmentKey, false,
                        ExposureGroup.InContext, c => c.DisgustIC, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Disgust, segmentKey, true,
                        ExposureGroup.InContext, c => c.DisgustICNorm, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Playback, segmentKey, false,
                        ExposureGroup.Focused, c => c.PlaybackIF, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Playback, segmentKey, true,
                        ExposureGroup.Focused, c => c.PlaybackIFNorm, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Playback, segmentKey, false,
                        ExposureGroup.InContext, c => c.PlaybackIC, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Playback, segmentKey, true,
                        ExposureGroup.InContext, c => c.PlaybackICNorm, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Distraction, segmentKey, false,
                        ExposureGroup.InContext, c => c.DistractionIC, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Distraction, segmentKey, true,
                        ExposureGroup.InContext, c => c.DistractionICNorm, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Distraction, segmentKey, false,
                        ExposureGroup.Focused, c => c.DistractionIF, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Distraction, segmentKey, true,
                        ExposureGroup.Focused, c => c.DistractionIFNorm, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.NeutralAttention, segmentKey, false,
                        ExposureGroup.InContext, c => c.NeutralAttention, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.NeutralAttention, segmentKey, true,
                        ExposureGroup.InContext, c => c.NeutralAttentionNorm, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.NeutralAttention, segmentKey, false,
                        ExposureGroup.Focused, c => c.NeutralAttentionIF, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.NeutralAttention, segmentKey, true,
                        ExposureGroup.Focused, c => c.NeutralAttentionIFNorm, views));
                }

                // UI requirement: If there is any second of data missing(null) remove whole line
                mediaCurve.Curves = mediaCurve.Curves.Where(c => c.Values.Any() && c.Values.All(p => p.Pct != null))
                    .ToList();

                mediaCurves.Add(mediaCurve);
            }

            return mediaCurves;
        }
    }
}