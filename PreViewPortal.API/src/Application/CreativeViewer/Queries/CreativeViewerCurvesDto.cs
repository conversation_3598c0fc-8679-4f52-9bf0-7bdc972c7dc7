using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Application.CreativeViewer.Queries;

public class CreativeViewerCurvesDto
{
    public string Id => $"{SourceMediaId}-{TestId}{(TaskId is null ? "" : '-' + TaskId)}-{OrderAdSetID}";
    public int SourceMediaId { get; set; }
    public int TestId { get; set; }
    public int? OrderAdSetID { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string TaskId { get; set; }

    public List<CreativeViewerCurveDto> Curves { get; set; } = new();
}