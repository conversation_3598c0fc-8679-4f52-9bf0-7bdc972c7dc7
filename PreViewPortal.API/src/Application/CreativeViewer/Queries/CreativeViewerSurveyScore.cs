namespace Application.CreativeViewer.Queries;

public class CreativeViewerSurveyScore
{
    public string Id { get; set; }
    public string SegmentKey { get; set; }
    public int? Views { get; set; }
    public float? BrandRecognition { get; set; }
    public float? BrandRecognitionMedian { get; set; }
    public float? BrandRecognitionDiff { get; set; }
    public float? AdRecognition { get; set; }
    public float? AdRecognitionMedian { get; set; }
    public float? AdRecognitionDiff { get; set; }
    public float? BrandTrust { get; set; }
    public float? BrandTrustMedian { get; set; }
    public float? BrandTrustDiff { get; set; }
    public float? AdLikeability { get; set; }
    public float? AdLikeabilityMedian { get; set; }
    public float? AdLikeabilityDiff { get; set; }
    public float? Persuasion { get; set; }
    public float? PersuasionMedian { get; set; }
    public float? PersuasionDiff { get; set; }
}