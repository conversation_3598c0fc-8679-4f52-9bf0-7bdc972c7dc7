using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Application.Common.Attributes;
using Application.Common.Extensions;
using Application.Common.Interfaces;
using Application.Common.Models;
using Application.Common.Models.Settings;
using Application.Common.Security;
using Application.CreativePerformanceForcedExposure;
using Application.MediaSegment.Queries;
using BusinessLayer.Constant;
using BusinessLayer.Model.Request;
using BusinessLayer.Model.Response;
using MediatR;
using Microsoft.Extensions.Options;

namespace Application.CreativeViewer.Queries
{
    [Authorize(Policy = Policies.HasGuestAccess)]
    [CacheQuery(CacheDuration.ThirtyMinutes)]
    public class GetCreativeViewerForcedExposureQuery : IRequest<CreativeViewerVm>
    {
        public List<MediaModelRequest> Media { get; set; }
        
        [AuthorizeBy(AuthorizeByPropertyType.Account)]
        public int AccountId { get; set; }
        public string ShareKey { get; set; }
    }

    public class
        GetCreativeViewerForcedExposureQueryHandler : IRequestHandler<GetCreativeViewerForcedExposureQuery,
        CreativeViewerVm>
    {
        private readonly IMediaService _mediaService;
        private readonly ISender _mediator;
        private readonly QualitySettings _qualitySettings;
        private readonly ICurrentUserService _currentUserService;

        public GetCreativeViewerForcedExposureQueryHandler(
            IMediaService mediaService,
            ISender mediator,
            IOptions<QualitySettings> qualitySettings,
            ICurrentUserService currentUserService)
        {
            _mediaService = mediaService;
            _mediator = mediator;
            _qualitySettings = qualitySettings.Value;
            _currentUserService = currentUserService;
        }

        public async Task<CreativeViewerVm> Handle(GetCreativeViewerForcedExposureQuery request,
            CancellationToken cancellationToken)
        {
            var creativePerformanceDataTask = _mediator.Send(new GetCreativePerformanceForcedExposureQuery
            {
                AccountId = request.AccountId,
                Media = request.Media,
                IsAdmin = _currentUserService.IsAdmin
            }, cancellationToken);

            var curveDataTask = _mediator.Send(new GetCreativePerformanceForcedExposureCurvesQuery
            {
                Media = request.Media
            }, cancellationToken);

            var mediaSegmentsTask = _mediator.Send(new GetMediaSegmentKeyQuery
            {
                AccountId = request.AccountId,
                Media = request.Media,
            }, cancellationToken);

            await Task.WhenAll(creativePerformanceDataTask, curveDataTask, mediaSegmentsTask).ConfigureAwait(false);
            var creativeData = await creativePerformanceDataTask.ConfigureAwait(false);
            var curveData = await curveDataTask.ConfigureAwait(false);
            var segmentsData = await mediaSegmentsTask.ConfigureAwait(false);

            var creativePerformanceForcedExposureDtos = creativeData
                .OrderBy(cp => request.Media
                    .FindIndex(m => m.SourceMediaID == cp.SourceMediaID && m.TestID == cp.TestID && m.OrderAdSetID == cp.OrderAdSetID))
                .ToList();

            var segmentKeys = segmentsData.SegmentKeyLabels.Select(s => s.SegmentKey);

            var curveDtos = MapCurveToDto(curveData, creativePerformanceForcedExposureDtos, segmentKeys,
                _qualitySettings.MinViewThresholdForScores);

            var creativeViewerNorms = creativePerformanceForcedExposureDtos
                .Select(cp =>
                    new CreativeViewerNorm
                    {
                        VideoId = $"{cp.SourceMediaID}-{cp.TestID}-{cp.OrderAdSetID}",
                        SegmentKey = cp.SegmentKey,

                        Views = cp.Views,

                        ReactionsNormDevice = cp.ReactionsNormDevice,
                        SurveyNormDevice = cp.SurveyNormDevice,
                        AttentionNormDevice = cp.AttentionNormDevice,

                        ReactionsNormFormat = cp.ReactionsNormFormat,
                        SurveyNormFormat = cp.SurveyNormFormat,
                        AttentionNormFormat = cp.AttentionNormFormat,

                        ReactionsNormDuration = cp.ReactionsNormDuration,
                        SurveyNormDuration = cp.SurveyNormDuration,
                        AttentionNormDuration = cp.AttentionNormDuration,

                        ReactionsNormAdFormat = cp.ReactionsNormAdFormat,
                        SurveyNormAdFormat = cp.SurveyNormAdFormat,
                        AttentionNormAdFormat = cp.AttentionNormAdFormat,

                        ReactionsNormAdFormatName = cp.ReactionsNormAdFormatName,
                        SurveyNormAdFormatName = cp.SurveyNormAdFormatName,
                        AttentionNormAdFormatName = cp.AttentionNormAdFormatName,

                        ReactionsNormEnvironmentCategory = cp.ReactionsNormEnvironmentCategory,
                        SurveyNormEnvironmentCategory = cp.SurveyNormEnvironmentCategory,
                        AttentionNormEnvironmentCategory = cp.AttentionNormEnvironmentCategory,

                        ReactionsNormRegion = cp.ReactionsNormRegion,
                        SurveyNormRegion = cp.SurveyNormRegion,
                        AttentionNormRegion = cp.AttentionNormRegion,

                        ReactionsNormSampleSize = cp.ReactionsNormSampleSize,
                        SurveyNormSampleSize = cp.SurveyNormSampleSize,
                        AttentionNormSampleSize = cp.AttentionNormSampleSize,

                        ReactionsNormRefreshDate = cp.ReactionsNormRefreshDate,
                        SurveyNormRefreshDate = cp.SurveyNormRefreshDate,
                        AttentionNormRefreshDate = cp.AttentionNormRefreshDate,

                        ReactionsNormCustomNormFilters = cp.ReactionsNormCustomNormFilters,
                        AttentionNormCustomNormFilters = cp.AttentionNormCustomNormFilters,
                        SurveyNormCustomNormFilters = cp.SurveyNormCustomNormFilters,
                    }
                )
                .ToList();

            var creativeViewerVideoDtos = creativePerformanceForcedExposureDtos
                .Where(d => d.SegmentKey == SegmentKeyConstant.AllSegmentKey)
                .Select(cp =>
                    new CreativeViewerVideoDto
                    {
                        SourceMediaID = cp?.SourceMediaID ?? default,
                        TestID = cp?.TestID ?? default,
                        OrderExternalKey = cp?.OrderExternalKey,
                        OrderAdSetID = cp?.OrderAdSetID ?? default,
                        SourceMedia = cp?.SourceMedia,
                        SurveyKeyAlias = cp?.SurveyKeyAlias,
                        Duration = cp?.Duration ?? default,
                        Brand = cp?.Brand,
                        BrandLogoUrl = cp?.BrandLogoUrl,
                        ThumbnailUrl = cp?.ThumbnailUrl,
                        SourceMediaUrl = _mediaService.GetMediaSignedUrl(cp.SourceMediaConvertedFileName),
                        ThumbstripUrl = _mediaService.GetMediaSignedUrl(cp.SourceMediaThumbstripFileName),
                        Platform = cp?.Platform,
                        AdformatText = cp?.AdformatText,
                        AdformatTextIF = cp?.AdformatTextIF,
                        Country = cp?.Country,
                        Country_code = cp?.Country_code
                    })
                .ToList();

            return new CreativeViewerVm
            {
                VideoData = creativeViewerVideoDtos,
                CurveData = curveDtos,
                SegmentData = segmentsData,
                NormData = creativeViewerNorms
            };
        }

        private static List<CreativeViewerCurvesDto> MapCurveToDto(IEnumerable<ForcedExposureCurve> curves,
            IEnumerable<ForcedExposureCreative> creatives, IEnumerable<string> segmentKeys,
            int minThreshold)
        {
            var mediaCurves = new List<CreativeViewerCurvesDto>();

            foreach (var videoCurves in curves.GroupBy(c => new { c.SourceMediaID, c.TestID }))
            {
                var mediaCurve = new CreativeViewerCurvesDto
                {
                    SourceMediaId = videoCurves.Key.SourceMediaID,
                    TestId = videoCurves.Key.TestID
                };

                foreach (var segmentKey in segmentKeys)
                {
                    var creative = creatives.FirstOrDefault(cp =>
                        cp.SourceMediaID == videoCurves.Key.SourceMediaID && cp.TestID == videoCurves.Key.TestID &&
                        cp.SegmentKey == segmentKey);

                    if (mediaCurve.OrderAdSetID is null)
                    {
                        mediaCurve.OrderAdSetID = creative?.OrderAdSetID;
                    }

                    var views = creative?.Views;

                    if (creative is null || views < minThreshold)
                        continue;

                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Attention, segmentKey, false,
                        c => c.Attention, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Attention, segmentKey, true,
                        c => c.AttentionNorm, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.AllReactions, segmentKey, false,
                        c => c.AllReactions, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.AllReactions, segmentKey, true,
                        c => c.AllReactionsNorm, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Negativity, segmentKey, false,
                        c => c.Negativity, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Negativity, segmentKey, true,
                        c => c.NegativityNorm, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Happiness, segmentKey, false,
                        c => c.Happiness, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Happiness, segmentKey, true,
                        c => c.HappinessNorm, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Contempt, segmentKey, false,
                        c => c.Contempt, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Contempt, segmentKey, true,
                        c => c.ContemptNorm, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Surprise, segmentKey, false,
                        c => c.Surprise, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Surprise, segmentKey, true,
                        c => c.SurpriseNorm, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Confusion, segmentKey, false,
                        c => c.Confusion, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Confusion, segmentKey, true,
                        c => c.ConfusionNorm, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Disgust, segmentKey, false,
                        c => c.Disgust, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Disgust, segmentKey, true,
                        c => c.DisgustNorm, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Playback, segmentKey, false,
                        c => c.Playback, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Playback, segmentKey, true,
                        c => c.PlaybackNorm, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Distraction, segmentKey, false,
                        c => c.Distraction, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.Distraction, segmentKey, true,
                        c => c.DistractionNorm, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.NeutralAttention, segmentKey, false,
                        c => c.NeutralAttention, views));
                    mediaCurve.Curves.Add(videoCurves.ToDto(CreativeViewerCurveType.NeutralAttention, segmentKey, true,
                        c => c.NeutralAttentionNorm, views));
                }

                // UI requirement: If there is any second of data missing(null) remove whole line
                mediaCurve.Curves = mediaCurve.Curves.Where(c => c.Values.Any() && c.Values.All(p => p.Pct != null))
                    .ToList();

                mediaCurves.Add(mediaCurve);
            }

            return mediaCurves;
        }
    }
}