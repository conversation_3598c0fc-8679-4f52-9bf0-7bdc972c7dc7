using System;
using System.Text.Json.Serialization;

namespace Application.CreativeViewer.Queries
{
    public class CreativeViewerVideoDto
    {
        public string Id => $"{SourceMediaID}-{TestID}{(TaskID is null ? "" : '-' + TaskID)}-{OrderAdSetID}";
        public int SourceMediaID { get; set; }
        public int TestID { get; set; }
        public string SurveyKeyAlias { get; set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public int? InFocusTestID { get; set; }
        public string OrderExternalKey { get; set; }
        public int OrderAdSetID { get; set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string TaskID { get; set; }
        public string SourceMedia { get; set; }
        public string SourceMediaConvertedFileName { get; set; }
        public int Duration { get; set; }
        public string Brand { get; set; }
        public string BrandLogoUrl { get; set; }
        public string ThumbnailUrl { get; set; }
        public string ThumbstripUrl { get; set; }
        public string SourceMediaUrl { get; set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string Platform { get; set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string AdformatText { get; set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string AdformatTextIF { get; set; }
        public string Country { get; set; }
        public string Country_code { get; set; }
    }
}
