using BusinessLayer.Model.Response;

namespace Application.CreativeViewer.Queries;

public class CreativeViewerNorm
{
    public string VideoId { get; set; }
    public string SegmentKey { get; set; }
    
    public int? Views { get; set; }
    
    public int? NormFallbackIC { get; set; }
    public int? NormFallbackIF { get; set; }
    public int? NormFallbackSurveyIC { get; set; }
    public int? NormFallbackSurveyIF { get; set; }
    public string NormSegmentKeyIC { get; set; }
    public string NormSegmentKeyIF { get; set; }
    
    public string ReactionsNormDeviceIF { get; set; }
    public string ReactionsNormDeviceIC { get; set; }
    public string SurveyNormDeviceIF { get; set; }
    public string SurveyNormDeviceIC { get; set; }
    public string AttentionNormDeviceIF { get; set; }
    public string AttentionNormDeviceIC { get; set; }
    public string ReactionsNormDevice { get; set; }
    public string SurveyNormDevice { get; set; }
    public string AttentionNormDevice { get; set; }

    public string ReactionsNormFormatIF { get; set; }
    public string ReactionsNormFormatIC { get; set; }
    public string SurveyNormFormatIF { get; set; }
    public string SurveyNormFormatIC { get; set; }
    public string AttentionNormFormatIF { get; set; }
    public string AttentionNormFormatIC { get; set; }
    public string AttentionNormFormat { get; set; }
    public string ReactionsNormFormat { get; set; }
    public string SurveyNormFormat { get; set; }

    public int? ReactionsNormDurationIF { get; set; }
    public int? ReactionsNormDurationIC { get; set; }
    public int? SurveyNormDurationIF { get; set; }
    public int? SurveyNormDurationIC { get; set; }
    public int? AttentionNormDurationIF { get; set; }
    public int? AttentionNormDurationIC { get; set; }
    public int? ReactionsNormDuration { get; set; }
    public int? SurveyNormDuration { get; set; }
    public int? AttentionNormDuration { get; set; }

    public string ReactionsNormAdFormatIF { get; set; }
    public string ReactionsNormAdFormatIC { get; set; }
    public string SurveyNormAdFormatIF { get; set; }
    public string SurveyNormAdFormatIC { get; set; }
    public string AttentionNormAdFormatIF { get; set; }
    public string AttentionNormAdFormatIC { get; set; }
    public string ReactionsNormAdFormat { get; set; }
    public string SurveyNormAdFormat { get; set; }
    public string AttentionNormAdFormat { get; set; }

    public string ReactionsNormEnvironmentCategoryIF { get; set; }
    public string ReactionsNormEnvironmentCategoryIC { get; set; }
    public string SurveyNormEnvironmentCategoryIF { get; set; }
    public string SurveyNormEnvironmentCategoryIC { get; set; }
    public string AttentionNormEnvironmentCategoryIF { get; set; }
    public string AttentionNormEnvironmentCategoryIC { get; set; }
    public string ReactionsNormEnvironmentCategory { get; set; }
    public string SurveyNormEnvironmentCategory { get; set; }
    public string AttentionNormEnvironmentCategory { get; set; }

    public string ReactionsNormRegionIF { get; set; }
    public string ReactionsNormRegionIC { get; set; }
    public string SurveyNormRegionIF { get; set; }
    public string SurveyNormRegionIC { get; set; }
    public string AttentionNormRegionIF { get; set; }
    public string AttentionNormRegionIC { get; set; }
    public string ReactionsNormRegion { get; set; }
    public string SurveyNormRegion { get; set; }
    public string AttentionNormRegion { get; set; }

    public int? ReactionsNormSampleSizeIF { get; set; }
    public int? ReactionsNormSampleSizeIC { get; set; }
    public int? SurveyNormSampleSizeIF { get; set; }
    public int? SurveyNormSampleSizeIC { get; set; }
    public int? AttentionNormSampleSizeIF { get; set; }
    public int? AttentionNormSampleSizeIC { get; set; }
    public int? ReactionsNormSampleSize { get; set; }
    public int? SurveyNormSampleSize { get; set; }
    public int? AttentionNormSampleSize { get; set; }

    public string ReactionsNormRefreshDateIF { get; set; }
    public string ReactionsNormRefreshDateIC { get; set; }
    public string SurveyNormRefreshDateIF { get; set; }
    public string SurveyNormRefreshDateIC { get; set; }
    public string AttentionNormRefreshDateIF { get; set; }
    public string AttentionNormRefreshDateIC { get; set; }
    public string ReactionsNormRefreshDate { get; set; }
    public string SurveyNormRefreshDate { get; set; }
    public string AttentionNormRefreshDate { get; set; }

    public string ReactionsNormAdFormatNameIF { get; set; }
    public string ReactionsNormAdFormatNameIC { get; set; }
    public string SurveyNormAdFormatNameIF { get; set; }
    public string SurveyNormAdFormatNameIC { get; set; }
    public string AttentionNormAdFormatNameIF { get; set; }
    public string AttentionNormAdFormatNameIC { get; set; }
    public string ReactionsNormAdFormatName { get; set; }
    public string SurveyNormAdFormatName { get; set; }
    public string AttentionNormAdFormatName { get; set; }

    public NormFilters? ReactionsNormCustomNormFilters { get; set; }
    public NormFilters? AttentionNormCustomNormFilters { get; set; }
    public NormFilters? SurveyNormCustomNormFilters { get; set; }
    public NormFilters? ReactionsNormCustomNormFiltersIC { get; set; }
    public NormFilters? AttentionNormCustomNormFiltersIC { get; set; }
    public NormFilters? SurveyNormCustomNormFiltersIC { get; set; }
    public NormFilters? ReactionsNormCustomNormFiltersIF { get; set; }
    public NormFilters? AttentionNormCustomNormFiltersIF { get; set; }
    public NormFilters? SurveyNormCustomNormFiltersIF { get; set; }
}