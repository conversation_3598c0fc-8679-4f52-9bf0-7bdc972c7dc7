using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Application.CreativeViewer.Queries;

public class CreativeViewerCurveDto
{
    public CreativeViewerCurveType Type { get; set; }
    public string SegmentKey { get; set; }
    public bool IsNorm { get; set; }
    public string ExposureGroup { get; set; }
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public int? Views { get; set; }
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]

    public List<CreativeViewerCurveSecondDto> Values { get; set; } = new();
}