using System.Collections.Generic;
using System.Text.Json.Serialization;
using Application.MediaSegment.Queries;
using BusinessLayer.Model;
using BusinessLayer.Model.Request;

namespace Application.CreativeViewer.Queries
{
    public class CreativeViewerVm
    {
        public List<CreativeViewerVideoDto> VideoData { get; set; } = new();       
        public List<CreativeViewerCurvesDto> CurveData { get; set; } = new();
        public List<MediaModelRequest> Media { get; set; } = new();
        public ProductType? SelectedProduct { get; set; }
        public string ShareKey { get; set; }
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public SegmentKeysInfo SegmentData { get; set; }
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public List<CreativeViewerSurveyScore> SurveyScoreData { get; set; }
        public List<CreativeViewerNorm> NormData { get; set; }
    }
}