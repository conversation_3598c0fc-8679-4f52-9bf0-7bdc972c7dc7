using System.Collections.Generic;
using Application.Account.Queries;

namespace Application.User.Queries
{
    public class CurrentUserInfoDto
    {
        public int Id { get; set; }
        public string Email { get; set; }
        public int? Account { get; set; }
        public List<int> Accounts { get; set; } = new List<int>();
        public bool IsAdmin { get; set; }
        public bool HasAccess { get; set; }
    }
}