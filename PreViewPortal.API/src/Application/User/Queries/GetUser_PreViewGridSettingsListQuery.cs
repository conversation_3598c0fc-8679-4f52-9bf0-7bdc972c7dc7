using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Application.Common.Attributes;
using Application.Common.Interfaces;
using Application.Common.Models;
using Application.Common.Security;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.User.Queries
{
    [Authorize(Policy = Policies.HasAccess)]
    [CacheQuery(CacheDuration.OneDay)]
    public class GetUser_PreViewGridSettingsListQuery : IRequest<List<User_PreViewGridSettingsDto>>
    {
        [AuthorizeBy(AuthorizeByPropertyType.User)]
        public int UserId { get; set; }
    }

    public class GetUser_PreViewGridSettingsQueryHandler : IRequestHandler<GetUser_PreViewGridSettingsListQuery, List<User_PreViewGridSettingsDto>>
    {
        private readonly IApplicationDbContext _context;
        private readonly IMapper _mapper;

        public GetUser_PreViewGridSettingsQueryHandler(IApplicationDbContext context, IMapper mapper)
        {
            _context = context;
            _mapper = mapper;
        }

        public async Task<List<User_PreViewGridSettingsDto>> Handle(GetUser_PreViewGridSettingsListQuery request, CancellationToken cancellationToken)
        {
            var dtos = await _context.User_PreViewGridSettings
                      .Where(x => x.UserId == request.UserId)
                      .ProjectTo<User_PreViewGridSettingsDto>(_mapper.ConfigurationProvider)
                      .ToListAsync(cancellationToken);

            return dtos;
        }
    }
}



