using System.Threading;
using System.Threading.Tasks;
using Application.Common.Interfaces;
using MediatR;

namespace Application.User.Queries
{
    public class GetCurrentUserQuery : IRequest<CurrentUserInfoDto>
    {
    }

    public class GetCurrentUserQueryHandler : IRequestHandler<GetCurrentUserQuery, CurrentUserInfoDto>
    {
        private readonly ICurrentUserService _currentUserService;

        public GetCurrentUserQueryHandler(ICurrentUserService currentUserService)
        {
            _currentUserService = currentUserService;
        }

        public Task<CurrentUserInfoDto> Handle(GetCurrentUserQuery request, CancellationToken token)
        {
            return Task.FromResult(new CurrentUserInfoDto
            {
                Id = _currentUserService.Id,
                Email = _currentUserService.Email,
                Account = _currentUserService.Account,
                Accounts = _currentUserService.Accounts,
                IsAdmin = _currentUserService.IsAdmin,
                HasAccess = _currentUserService.IsAdmin || _currentUserService.HasAccess
            });

        }
    }
}
