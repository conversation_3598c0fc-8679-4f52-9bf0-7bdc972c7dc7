using System.Threading;
using System.Threading.Tasks;
using Application.Common.Exceptions;
using Application.Common.Interfaces;
using Application.Common.Security;
using Domain.Enums;
using Domain.Events;
using MediatR;
using Microsoft.EntityFrameworkCore;


namespace Application.User.Commands
{
    [Authorize(Policy = Policies.HasAccess)]
    public class DeleteUser_PreViewGridSettingsCommand : IRequest
    {
        [AuthorizeBy(AuthorizeByPropertyType.User)]
        public int UserId { get; set; }
        public PreviewGrid GridId { get; set; }
    }

    public class DeleteUser_PreViewGridSettingsCommandHandler : IRequestHandler<DeleteUser_PreViewGridSettingsCommand>
    {
        private readonly IApplicationDbContext _context;

        public DeleteUser_PreViewGridSettingsCommandHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task Handle(DeleteUser_PreViewGridSettingsCommand request, CancellationToken cancellationToken)
        {
            var entity = await _context.User_PreViewGridSettings.SingleOrDefaultAsync(x => x.UserId == request.UserId && x.GridId == request.GridId, cancellationToken);
            if (entity == null)
            {
                throw new NotFoundException(nameof(Domain.Entitites.User_PreViewGridSettings), new { request.UserId, request.GridId });
            }

            _context.User_PreViewGridSettings.Remove(entity);

            entity.DomainEvents.Add(new User_PreViewGridSettingsDeleteEvent(request.UserId));

            await _context.SaveChangesAsync(cancellationToken);
        }
    }
}
