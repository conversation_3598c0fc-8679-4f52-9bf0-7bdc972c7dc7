using System;
using System.Threading;
using System.Threading.Tasks;
using Application.Common.Interfaces;
using Application.Common.Security;
using Domain.Enums;
using Domain.Events;
using MediatR;
using Microsoft.EntityFrameworkCore;


namespace Application.User.Commands
{
    [Authorize(Policy = Policies.HasAccess)]
    public class CreateOrUpdateUser_PreViewGridSettingsCommand : IRequest
    {
        [AuthorizeBy(AuthorizeByPropertyType.User)]
        public int UserId { get; set; }
        public PreviewGrid GridId { get; set; }
        public string GridSettings { get; set; }
    }

    public class CreateOrUpdateUser_PreViewGridSettingsCommandHandler : IRequestHandler<CreateOrUpdateUser_PreViewGridSettingsCommand>
    {
        private readonly IApplicationDbContext _context;

        public CreateOrUpdateUser_PreViewGridSettingsCommandHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task Handle(CreateOrUpdateUser_PreViewGridSettingsCommand request, CancellationToken cancellationToken)
        {
            var entity = await _context.User_PreViewGridSettings.SingleOrDefaultAsync(x => x.UserId == request.UserId && x.GridId == request.GridId, cancellationToken);
            if (entity != null)
            {
                entity.GridSettings = request.GridSettings;
                entity.ModifiedAt = DateTimeOffset.UtcNow;
            }
            else
            {
                entity = new Domain.Entitites.User_PreViewGridSettings
                {
                    UserId = request.UserId,
                    GridId = request.GridId,
                    GridSettings = request.GridSettings,
                    ModifiedAt = DateTimeOffset.UtcNow
                };

                _context.User_PreViewGridSettings.Add(entity);
            }

            entity.DomainEvents.Add(new User_PreViewGridSettingsCreateOrUpdateEvent(request.UserId));

            await _context.SaveChangesAsync(cancellationToken);
        }
    }
}
