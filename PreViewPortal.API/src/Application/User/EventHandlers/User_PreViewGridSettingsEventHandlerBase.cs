using System.Threading;
using System.Threading.Tasks;
using Application.Common.Behaviors;
using Application.Common.Interfaces;
using Application.User.Queries;

namespace Application.User.EventHandlers
{
    public abstract class User_PreViewGridSettingsEventHandlerBase
    {
        private readonly ICacheService _cacheService;
        private readonly ICacheKeyGenerator<GetUser_PreViewGridSettingsListQuery> _getUser_PreViewGridSettingsListQueryCacheKeyGenerator;

        protected User_PreViewGridSettingsEventHandlerBase(
            ICacheService cacheService,
            ICacheKeyGenerator<GetUser_PreViewGridSettingsListQuery> getUser_PreViewGridSettingsListQueryCacheKeyGenerator
        )
        {
            _cacheService = cacheService;
            _getUser_PreViewGridSettingsListQueryCacheKeyGenerator = getUser_PreViewGridSettingsListQueryCacheKeyGenerator;
        }

        protected async Task InvalidateQueries(int userId, CancellationToken cancellationToken = default)
        {
            var cacheKey = _getUser_PreViewGridSettingsListQueryCacheKeyGenerator.GetCacheKey(new GetUser_PreViewGridSettingsListQuery
            {
                UserId = userId
            });

            await _cacheService.RemoveAsync(cacheKey, cancellationToken);
        }
    }
}
