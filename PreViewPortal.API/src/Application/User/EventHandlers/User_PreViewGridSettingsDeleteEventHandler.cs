using System.Threading;
using System.Threading.Tasks;
using Application.Common.Behaviors;
using Application.Common.Interfaces;
using Application.Common.Models;
using Application.User.Queries;
using Domain.Events;
using MediatR;

namespace Application.User.EventHandlers
{
    public class User_PreViewGridSettingsDeleteEventHandler : User_PreViewGridSettingsEventHandlerBase, INotificationHandler<DomainEventNotification<User_PreViewGridSettingsDeleteEvent>>
    {
        public User_PreViewGridSettingsDeleteEventHandler(ICacheService cacheService, ICacheKeyGenerator<GetUser_PreViewGridSettingsListQuery> getUser_PreViewGridSettingsCacheKeyGenerator) : base(cacheService, getUser_PreViewGridSettingsCacheKeyGenerator)
        {
        }

        public async Task Handle(DomainEventNotification<User_PreViewGridSettingsDeleteEvent> notification, CancellationToken cancellationToken)
        {
            await InvalidateQueries(notification.DomainEvent.UserId, cancellationToken);
        }
    }
}
