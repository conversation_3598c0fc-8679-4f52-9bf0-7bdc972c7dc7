using System.Threading;
using System.Threading.Tasks;
using Application.Common.Interfaces;
using Application.Common.Security;
using MediatR;

namespace Application.Caching.Commands
{
    [Authorize(Roles = RealeyesJwtRoles.Administrator, AllowSecret = true)]
    public class FlushCacheCommand : IRequest
    {
    }

    public class FlushCacheCommandHandler : IRequestHandler<FlushCacheCommand>
    {
        private readonly ICacheService _cacheService;

        public FlushCacheCommandHandler(ICacheService cacheService)
        {
            _cacheService = cacheService;
        }

        public async Task Handle(FlushCacheCommand request, CancellationToken cancellationToken)
        {
            await _cacheService.FlushAsync(cancellationToken);
        }
    }
}
