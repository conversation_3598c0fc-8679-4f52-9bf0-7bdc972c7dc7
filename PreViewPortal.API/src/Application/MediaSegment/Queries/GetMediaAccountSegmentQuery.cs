using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Application.Common.Attributes;
using Application.Common.Models;
using Application.Common.Security;
using BusinessLayer.Model;
using BusinessLayer.Model.Request;
using BusinessLayer.Model.Response;
using BusinessLayer.Repository;
using MediatR;

namespace Application.MediaSegment.Queries
{
    [Authorize(Policy = Policies.HasGuestAccess)]
    [CacheQuery(CacheDuration.OneHour)]
    public class GetMediaAccountSegmentQuery : IRequest<List<SegmentKeyLabel>>
    {
        public ProductType ProductType { get; set; }

        [AuthorizeBy(AuthorizeByPropertyType.Account)]
        public int AccountId { get; set; }
        public List<string> SegmentKeys { get; set; }
    }

    public class GetMediaAccountSegmentQueryHandler : IRequestHandler<GetMediaAccountSegmentQuery, List<SegmentKeyLabel>>
    {
        private readonly ISegmentRepository _segmentRepository;
        public GetMediaAccountSegmentQueryHandler(ISegmentRepository segmentRepository)
        {
            _segmentRepository = segmentRepository;
        }

        public async Task<List<SegmentKeyLabel>> Handle(GetMediaAccountSegmentQuery request, CancellationToken cancellationToken)
        {
            return await _segmentRepository.GetSegmentKeys(new GetSegmentKeysRequest
            {
                AccountId = request.AccountId,
                ProductType = request.ProductType,
                SegmentKeys = request.SegmentKeys
            });
        }
    }
}
