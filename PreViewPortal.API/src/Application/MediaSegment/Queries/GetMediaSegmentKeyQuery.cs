using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Application.Common.Attributes;
using Application.Common.Models;
using Application.Common.Security;
using BusinessLayer.Model.Request;
using BusinessLayer.Model.Response;
using BusinessLayer.Repository;
using MediatR;

namespace Application.MediaSegment.Queries
{
    [Authorize(Policy = Policies.HasGuestAccess)]
    [CacheQuery(CacheDuration.OneHour)]
    public class GetMediaSegmentKeyQuery : IRequest<SegmentKeysInfo>
    {
        public List<MediaModelRequest> Media { get; set; } = new();

        [AuthorizeBy(AuthorizeByPropertyType.Account)]
        public int AccountId { get; set; }
    }

    public class GetMediaSegmentKeyQueryHandler : IRequestHandler<GetMediaSegmentKeyQuery, SegmentKeysInfo>
    {
        private readonly IMediaSegmentRepository _mediaSegmentRepository;
        public GetMediaSegmentKeyQueryHandler(IMediaSegmentRepository mediaSegmentRepository)
        {
            _mediaSegmentRepository = mediaSegmentRepository;
        }

        public async Task<SegmentKeysInfo> Handle(GetMediaSegmentKeyQuery request, CancellationToken cancellationToken)
        {

            var mediaSegmentKeys = await _mediaSegmentRepository.GetMediaSegmentKeys(new GetMediaSegmentKeysRequest { Media = request.Media });

            var segmentKeys = mediaSegmentKeys
                .GroupBy(m => m.SegmentKey)
                .Select(m => m.FirstOrDefault())
                .ToList();


            var segmentKeysLabels = segmentKeys
                .Select(m => new SegmentKeyLabel { SegmentKey = m.SegmentKey, Question = m.Question, Answer = m.Answer })
                .ToList();

            var segmentGroups = mediaSegmentKeys
                .Where(s => s.Question != null)
                .GroupBy(m => new { m.Question, m.QuestionOrder })
                .OrderBy(g => g.Key.QuestionOrder ?? int.MaxValue)
                .ThenBy(g => g.Key.Question)
                .Select(g => new SegmentGroup
                {
                    Question = g.Key.Question,
                    Options = g
                                .OrderBy(m => m.AnswerOrder ?? int.MaxValue)
                                .ThenBy(m => m.Answer)
                                .Select(m => m.SegmentKey)
                                .ToList()
                })
                                .ToList();

            return new SegmentKeysInfo
            {
                SegmentKeyLabels = segmentKeysLabels,
                SegmentGroups = segmentGroups
            };
        }
    }
}
