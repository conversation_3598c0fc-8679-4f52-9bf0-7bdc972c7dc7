using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Application.Common.Attributes;
using Application.Common.Models;
using Application.Common.Security;
using BusinessLayer.Model.Request;
using BusinessLayer.Model.Response;
using BusinessLayer.Repository;
using MediatR;

namespace Application.Account.Queries
{
    [Authorize(Policy = Policies.HasAccess)]
    [CacheQuery(CacheDuration.ThirtyMinutes)]
    public class GetAccountProductModelsQuery : IRequest<List<AccountProduct>>
    {
        public bool IsAdmin { get; set; }

        [AuthorizeBy(AuthorizeByPropertyType.Account)]
        public List<int> AccountIds { get; set; } = new();
    }

    public class GetAccountProductModelsQueryHandler : IRequestHandler<GetAccountProductModelsQuery, List<AccountProduct>>
    {
        private readonly IAccountProductRepository _accountProductRepository;

        public GetAccountProductModelsQueryHandler(IAccountProductRepository accountProductRepository)
        {
            _accountProductRepository = accountProductRepository;
        }

        public async Task<List<AccountProduct>> Handle(GetAccountProductModelsQuery request, CancellationToken cancellationToken)
        {

            var accountProducts = await _accountProductRepository.GetAccountProducts(new GetAccountProductsRequest { IsAdmin = request.IsAdmin, AccountIds = request.AccountIds });

            return accountProducts.ToList();

        }
    }
}
