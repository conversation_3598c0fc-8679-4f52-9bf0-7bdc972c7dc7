using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Application.Common.Interfaces;
using Application.Common.Security;
using BusinessLayer.Model.Request;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Share.Queries
{
    [Authorize(Policy = Policies.HasAccess)]
    public class GetShareKeyQuery : IRequest<ShareKeyVm>
    {
        [AuthorizeBy(AuthorizeByPropertyType.Account)]
        public int AccountId { get; set; }

        public List<MediaModelRequest> Media { get; set; }
    }

    public class GetShareKeyQueryHandler : IRequestHandler<GetShareKeyQuery, ShareKeyVm>
    {
        private readonly IApplicationDbContext _context;

        public GetShareKeyQueryHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<ShareKeyVm> Handle(GetShareKeyQuery request, CancellationToken cancellationToken)
        {
            var uniqueKey = GenerateUniqueShareKey(request.Media);

            var shareKey = await _context.ShareKey
                .FirstOrDefaultAsync(sk => sk.HashKey == uniqueKey, cancellationToken);

            return new ShareKeyVm
            {
                ShareKey = shareKey?.IsEnabled == true ? shareKey.HashKey : ""
            };
        }

        private string GenerateUniqueShareKey(IEnumerable<MediaModelRequest> mediaItems, bool useBase64 = false)
        {
            // Order by IDs to ensure consistent hashing
            var orderedItems = mediaItems.OrderBy(m => m.TestID)
                .ThenBy(m => m.SourceMediaID)
                .ThenBy(m => m.TaskID)
                .ToList();

            var combinedIds = string.Join("", orderedItems.Select(m => $"{m.TestID}{m.SourceMediaID}{m.TaskID}"));

            using var sha256 = SHA256.Create();
            var inputBytes = Encoding.UTF8.GetBytes(combinedIds);
            var hashBytes = sha256.ComputeHash(inputBytes);

            if (useBase64)
            {
                return Convert.ToBase64String(hashBytes).Substring(0, 64);
            }

            var hexHash = BitConverter.ToString(hashBytes).Replace("-", "").ToLower();
            return hexHash.Substring(0, Math.Min(64, hexHash.Length)); // Shorten if necessary
        }
    }
}