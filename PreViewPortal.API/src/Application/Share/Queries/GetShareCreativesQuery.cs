using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Application.Common.Exceptions;
using Application.Common.Interfaces;
using Application.Common.Security;
using BusinessLayer.Model.Request;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Share.Queries
{
    [Authorize(Policy = Policies.HasGuestAccess)]
    public class GetShareCreativesQuery : IRequest<ShareCreativesVm>
    {
        public string ShareKey { get; set; }
    }

    public class GetShareCreativesQueryHandler : IRequestHandler<GetShareCreativesQuery, ShareCreativesVm>
    {
        private readonly IApplicationDbContext _context;

        public GetShareCreativesQueryHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<ShareCreativesVm> Handle(GetShareCreativesQuery request, CancellationToken cancellationToken)
        {
            var shareKey = await _context.ShareKey.Where(sk => sk.HashKey == request.ShareKey)
                .Include(sk => sk.ShareItems)
                .SingleAsync(cancellationToken);

            if (!shareKey.IsEnabled)
            {
                throw new ForbiddenAccessException();
            }

            var mediaList = shareKey.ShareItems.Any()
                ? shareKey.ShareItems.Select(si => new MediaModelRequest
                {
                    SourceMediaID = si.SourceMediaID,
                    TestID = si.TestID,
                    TaskID = si.TaskID,
                    OrderAdSetID = si.AdSetID ?? default
                }).ToList()
                : new List<MediaModelRequest>();

            return new ShareCreativesVm
            {
                AccountId = shareKey.AccountID,
                ProductType = shareKey.ProductType,
                SelectedCreatives = mediaList,
                ShareKey = shareKey.HashKey
            };
        }
    }
}