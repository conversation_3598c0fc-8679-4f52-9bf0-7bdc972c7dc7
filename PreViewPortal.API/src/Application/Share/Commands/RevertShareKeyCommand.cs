using System;
using System.Threading;
using System.Threading.Tasks;
using Application.Common.Interfaces;
using Application.Common.Security;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Share.Commands
{
    [Authorize(Policy = Policies.HasAccess)]
    public class RevertShareKeyCommand : IRequest<string>
    {
        [AuthorizeBy(AuthorizeByPropertyType.Account)]
        public int AccountId { get; set; }

        public string ShareKey { get; set; }
    }

    public class RevertShareKeyCommandHandler : IRequestHandler<RevertShareKeyCommand, string>
    {
        private readonly IApplicationDbContext _context;

        public RevertShareKeyCommandHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<string> Handle(RevertShareKeyCommand request, CancellationToken cancellationToken)
        {
            var shareKey = await _context.ShareKey
                .FirstOrDefaultAsync(sk => sk.HashKey == request.ShareKey, cancellationToken);

            shareKey.IsEnabled = false;
            shareKey.UpdateDate = DateTime.UtcNow;

            _context.ShareKey.Update(shareKey);

            await _context.SaveChangesAsync(cancellationToken);
            return request.ShareKey;
        }
    }
}