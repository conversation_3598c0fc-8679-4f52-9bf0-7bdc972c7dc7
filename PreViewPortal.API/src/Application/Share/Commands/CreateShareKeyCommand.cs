using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Application.Common.Interfaces;
using Application.Common.Security;
using Application.Share.Queries;
using BusinessLayer.Model;
using BusinessLayer.Model.Request;
using Domain.Entitites;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Share.Commands
{
    [Authorize(Policy = Policies.HasAccess)]
    public class CreateShareKeyCommand : IRequest<ShareKeyVm>
    {
        public ProductType ProductType { get; set; }
        public List<MediaModelRequest> Media { get; set; }

        [AuthorizeBy(AuthorizeByPropertyType.Account)]
        public int AccountId { get; set; }
    }

    public class CreateShareKeyCommandHandler : IRequestHandler<CreateShareKeyCommand, ShareKeyVm>
    {
        private readonly IApplicationDbContext _context;
        private readonly IShareService _shareService;

        public CreateShareKeyCommandHandler(IShareService shareService, IApplicationDbContext context)
        {
            _shareService = shareService;
            _context = context;
        }

        public async Task<ShareKeyVm> Handle(CreateShareKeyCommand request, CancellationToken cancellationToken)
        {
            var uniqueKey = _shareService.GenerateUniqueShareKey(request.Media);

            var shareKey = await _context.ShareKey
                .FirstOrDefaultAsync(sk => sk.HashKey == uniqueKey, cancellationToken);

            if (shareKey != null)
            {
                // Enable the key if it was previously disabled
                if (shareKey.IsEnabled)
                {
                    return new ShareKeyVm
                    {
                        ShareKey = uniqueKey
                    };
                }

                shareKey.IsEnabled = true;
                shareKey.UpdateDate = DateTime.UtcNow;
                _context.ShareKey.Update(shareKey);
                await _context.SaveChangesAsync(cancellationToken);

                return new ShareKeyVm
                {
                    ShareKey = uniqueKey
                };
            }

            var entity = new ShareKey
            {
                HashKey = uniqueKey,
                IsEnabled = true,
                ProductType = request.ProductType,
                AccountID = request.AccountId,
                CreateDate = DateTime.UtcNow,
                ShareItems = request.Media.Select(media => new ShareItem
                {
                    TestID = media.TestID,
                    SourceMediaID = media.SourceMediaID,
                    TaskID = media.TaskID,
                    AdSetID = media.OrderAdSetID
                }).ToList()
            };
            _context.ShareKey.Add(entity);

            await _context.SaveChangesAsync(cancellationToken);
            return new ShareKeyVm
            {
                ShareKey = uniqueKey
            };
        }
    }
}