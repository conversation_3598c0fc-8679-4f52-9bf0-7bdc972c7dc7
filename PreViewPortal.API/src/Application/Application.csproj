<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
	<PackageReference Include="AutoMapper" Version="13.0.0" />
    <PackageReference Include="ClosedXML" Version="0.102.2" />
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.5.2" />
    <PackageReference Include="MediatR" Version="12.4.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authorization" Version="8.0.12" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.12" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\PreView.Common\src\BusinessLayer\BusinessLayer.csproj" />
    <ProjectReference Include="..\..\..\PreView.Common\src\DataLayer\DataLayer.csproj" />
    <ProjectReference Include="..\Domain\Domain.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Common\Mappings\" />
  </ItemGroup>

</Project>
