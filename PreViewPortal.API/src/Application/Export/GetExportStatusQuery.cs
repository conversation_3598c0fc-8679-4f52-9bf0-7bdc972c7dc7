using System.Threading;
using System.Threading.Tasks;
using Application.Common.Queries;
using Application.Common.Security;
using BusinessLayer.Model.Request;
using BusinessLayer.Model.Response;
using BusinessLayer.Service;
using MediatR;

namespace Application.Export;

[Authorize(Policy = Policies.HasGuestAccess)]
public class GetExportStatusQuery : ExportQuery, IRequest<ExportResult>
{
    public string ExportPath { get; set; }
    public string ExportBucket { get; set; }
}

public class GetExportStatusQueryHandler : IRequestHandler<GetExportStatusQuery, ExportResult>
{
    private readonly IExportService _exportService;

    public GetExportStatusQueryHandler(IExportService exportService)
    {
        _exportService = exportService;
    }

    public Task<ExportResult> Handle(GetExportStatusQuery request,
        CancellationToken cancellationToken)
    {
        return _exportService.GetExportResult(new GetExportResultRequest
        {
            ExportBucket = request.ExportBucket,
            ExportPath = request.ExportPath
        });
    }
}