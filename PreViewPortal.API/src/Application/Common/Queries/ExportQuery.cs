using System.Collections.Generic;
using BusinessLayer.Interfaces;
using BusinessLayer.Model.Filter;
using BusinessLayer.Model.Response;
using Application.CreativeViewer.Queries;

namespace Application.Common.Queries
{
    public class SelectedCurveType
    {
        public CreativeViewerCurveType Type { get; set; }
        public string ExposureGroup { get; set; }
    }

    public class ExportQuery : IExportQuery
    {
        public List<GridFilterItem> Filters { get; set; } = new();
        public List<string> VisibleColumnsOrder { get; set; } = new();
        public List<GridSortingItem> Sorting { get; set; } = new();
        public List<Media> Media { get; set; } = new();
        public List<string> SegmentKeys { get; set; } = new();
        public string ExportFileName { get; set; }
        public List<CurveFilterItem> SelectedCurveTypes { get; set; } = new();
    }
}