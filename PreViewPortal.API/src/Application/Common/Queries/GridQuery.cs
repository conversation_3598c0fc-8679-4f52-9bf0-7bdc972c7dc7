using System.Collections.Generic;
using BusinessLayer.Interfaces;
using BusinessLayer.Model.Filter;

namespace Application.Common.Queries
{
    public class GridQuery : IGridQuery
    {
        public int PageSize { get; set; }
        public int CurrentPage { get; set; }
        public List<GridSortingItem> Sorting { get; set; } = new();
        public List<GridFilterItem> Filters { get; set; } = new();
    }
}