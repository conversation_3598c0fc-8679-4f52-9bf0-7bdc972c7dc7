using System;
using System.Collections;
using System.Globalization;
using System.Linq;
using System.Text;
using Application.Common.Behaviors;
using MediatR;

namespace Application.Common.Caching
{
    public class DefaultCacheKeyGenerator<TRequest> : ICacheKeyGenerator<TRequest> where TRequest : IBaseRequest
    {
        public virtual string GetCacheKey(TRequest request)
        {
            var type = typeof(TRequest);
            var key = new StringBuilder($"{type.Name}|");

            foreach (var property in type.GetProperties())
            {
                key.Append($"{property.Name}");
                key.Append("(");

                var value = property.GetValue(request);
                Append(key, value);

                key.Append(")");
            }

            return key.ToString();
        }

        private static void Append(StringBuilder key, object value)
        {
            switch (value)
            {
                case null:
                    key.Append("NULL");
                    break;
                case IConvertible c:
                    key.Append(c.ToString(CultureInfo.InvariantCulture));
                    break;
                case IFormattable f:
                    key.Append(f.ToString(null, CultureInfo.InvariantCulture));
                    break;
                case IEnumerable e:
                    foreach (var element in e)
                    {
                        Append(key, element);

                        key.Append(",");
                    }
                    break;
                default:
                    var propertyInfo = value.GetType().GetProperties();
                    var i = 0;
                    foreach (var info in propertyInfo.Where(info => info.CanRead).OrderBy(info => info.Name))
                    {
                        if (i > 0)
                            key.Append(",");
                        key.Append(info.Name).Append(":");
                        key.Append(info.GetValue(value, null));
                        i++;
                    }
                    break;
            }
        }
    }
}