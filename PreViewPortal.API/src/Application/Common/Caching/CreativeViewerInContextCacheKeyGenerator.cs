using System.Linq;
using Application.CreativeViewer.Queries;

namespace Application.Common.Caching
{
    public class CreativeViewerInContextCacheKeyGenerator : DefaultCacheKeyGenerator<GetCreativeViewerInContextQuery>
    {
        public override string GetCacheKey(GetCreativeViewerInContextQuery request)
        {
            request.Media = request.Media.OrderBy(m => m.SourceMediaID)
                                        .ThenBy(m => m.TestID)
                                        .ThenBy(m => m.TaskID)
                                        .ThenBy(m => m.OrderAdSetID)
                                        .ToList();

            return base.GetCacheKey(request);
        }
    }
}