using Application.CreativePerformanceForcedExposure;
using Application.CreativeViewer.Queries;
using System.Linq;

namespace Application.Common.Caching
{
    public class CreativeViewerForcedExposureCacheKeyGenerator : DefaultCacheKeyGenerator<GetCreativeViewerForcedExposureQuery>
    {
        public override string GetCacheKey(GetCreativeViewerForcedExposureQuery request)
        {
            request.Media = request.Media.OrderBy(m => m.SourceMediaID).ThenBy(m => m.TestID).ThenBy(m => m.OrderAdSetID).ToList();

            return base.GetCacheKey(request);
        }
    }
}
