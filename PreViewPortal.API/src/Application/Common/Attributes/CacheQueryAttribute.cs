using System;

namespace Application.Common.Attributes
{
    [AttributeUsage(AttributeTargets.Class)]
    public class CacheQueryAttribute : Attribute
    {
        public TimeSpan CacheDuration = TimeSpan.FromMinutes(1);

        public CacheQueryAttribute()
        {
        }

        public CacheQueryAttribute(int cacheDurationMs)
        {
            CacheDuration = TimeSpan.FromMilliseconds(cacheDurationMs);
        }
    }
}
