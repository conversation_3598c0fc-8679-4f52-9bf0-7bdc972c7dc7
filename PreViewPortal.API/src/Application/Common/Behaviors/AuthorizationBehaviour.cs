using System;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using Application.Common.Exceptions;
using Application.Common.Interfaces;
using Application.Common.Models.Settings;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Options;
using AuthorizeAttribute = Application.Common.Security.AuthorizeAttribute;

namespace Application.Common.Behaviors
{
    public class AuthorizationBehaviour<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse> where TRequest : class, IBaseRequest
    {
        private readonly ICurrentUserService _currentUserService;
        private readonly IAuthorizationService _authorizationService;
        private readonly ApiAuthorizationSettings _settings;

        public AuthorizationBehaviour(ICurrentUserService currentUserService, IAuthorizationService authorizationService, IOptions<ApiAuthorizationSettings> options)
        {
            _currentUserService = currentUserService;
            _authorizationService = authorizationService;
            _settings = options.Value;
        }

        public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
        {
            if (!_currentUserService.IsValidationNeeded)
            {
                return await next(); 
            }
            
            var authorizeAttributes = request.GetType().GetCustomAttributes<AuthorizeAttribute>().ToList();
            if (authorizeAttributes.Any())
            {
                
                // Role-based authorization
                var authorizeAttributesWithRoles = authorizeAttributes.Where(a => !string.IsNullOrWhiteSpace(a.Roles) && !ExcludeByApiSecret(a)).ToList();
                if (authorizeAttributesWithRoles.Any())
                {
                    var isAuthorized = false;

                    foreach (var roles in authorizeAttributesWithRoles.Select(a => a.Roles.Split(',')))
                    {
                        foreach (var role in roles)
                        {
                            var isInRole = _currentUserService.Roles.Contains(role);
                            if (isInRole)
                            {
                                isAuthorized = true;
                                break;
                            }
                        }
                    }

                    // Must be a member of at least one role in roles
                    if (!isAuthorized)
                    {
                        throw new ForbiddenAccessException();
                    }
                }

                // Policy-based authorization
                var authorizeAttributesWithPolicies = authorizeAttributes.Where(a => !string.IsNullOrWhiteSpace(a.Policy) && !ExcludeByApiSecret(a)).ToList();
                if (authorizeAttributesWithPolicies.Any())
                {
                    foreach (var policy in authorizeAttributesWithPolicies.Select(a => a.Policy))
                    {
                        var authorizationResult = await _authorizationService.AuthorizeAsync(_currentUserService.User, policy);
                        if (!authorizationResult.Succeeded)
                        {
                            throw new ForbiddenAccessException();
                        }
                    }
                }
            }

            // User is authorized / authorization not required
            return await next();
        }

        private bool ExcludeByApiSecret(AuthorizeAttribute attribute)
        {
            return attribute.AllowSecret && _currentUserService.InternalApiSecret == _settings.InternalApiSecret;
        }
    }
}