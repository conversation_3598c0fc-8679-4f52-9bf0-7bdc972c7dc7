using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using Application.Common.Exceptions;
using Application.Common.Interfaces;
using Application.Common.Security;
using MediatR;

namespace Application.Common.Behaviors
{
    public class AuthorizationByBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse> where TRequest : class, IRequest<TResponse>
    {
        private readonly IEnumerable<IAuthorizeByProperty> _propertyAuthorizers;
        private readonly ICurrentUserService _currentUserService;

        public AuthorizationByBehavior(IEnumerable<IAuthorizeByProperty> propertyAuthorizers, ICurrentUserService currentUserService)
        {
            _propertyAuthorizers = propertyAuthorizers;
            _currentUserService = currentUserService;
        }

        public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
        {
            if (!_currentUserService.IsValidationNeeded)
            {
                return await next(); 
            }
            
            var propertyAuthorizeInfos = request
                .GetType()
                .GetProperties()
                .Where(pi => pi.GetCustomAttribute<AuthorizeByAttribute>() != null)
                .Select(pi => (pi.GetCustomAttribute<AuthorizeByAttribute>()!.PropertyType, pi.GetValue(request, null)))
                .ToList();

            if (propertyAuthorizeInfos.Any())
            {
                var isAuthorized = false;

                foreach (var (type, value) in propertyAuthorizeInfos)
                {
                    var propertyAuthorizer = _propertyAuthorizers.SingleOrDefault(a => a.Type == type);
                    if (propertyAuthorizer is null)
                    {
                        throw new NotImplementedException($"Authorization by property is not implemented for type: {type}");
                    }

                    isAuthorized = await propertyAuthorizer.Authorize(value, cancellationToken);
                }

                if (!isAuthorized)
                {
                    throw new ForbiddenAccessException();
                }
            }

            return await next();
        }
    }
}