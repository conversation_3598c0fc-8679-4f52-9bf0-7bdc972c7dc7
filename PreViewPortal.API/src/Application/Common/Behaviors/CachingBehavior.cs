using System;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using Application.Common.Attributes;
using Application.Common.Interfaces;
using MediatR;
using Microsoft.Extensions.DependencyInjection;

namespace Application.Common.Behaviors
{
    public class CachingBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse> where TRequest : class, IRequest<TResponse>
    {
        private readonly ICacheService _cacheService;
        private readonly IServiceProvider _serviceProvider;

        public CachingBehavior(ICacheService cacheService, IServiceProvider serviceProvider)
        {
            _cacheService = cacheService;
            _serviceProvider = serviceProvider;
        }

        public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
        {
            var cacheAttribute = typeof(TRequest).GetCustomAttribute<CacheQueryAttribute>();
            if (cacheAttribute != null)
            {
                return await HandleQueryCaching(cacheAttribute, request, cancellationToken, next);
            }

            return await next();
        }

        private async Task<TResponse> HandleQueryCaching(CacheQueryAttribute attribute, TRequest request, CancellationToken cancellationToken, RequestHandlerDelegate<TResponse> next)
        {
            var cacheKeyGenerator = _serviceProvider.GetService<ICacheKeyGenerator<TRequest>>();
            if (cacheKeyGenerator == null)
            {
                throw new Exception($"Could not find {nameof(ICacheKeyGenerator<TRequest>)} registered.");
            }

            var cacheKey = cacheKeyGenerator.GetCacheKey(request);
            var cachedResponse = await _cacheService.GetAsync<TResponse>(cacheKey, cancellationToken);
            if (cachedResponse != null)
            {
                return cachedResponse;
            }

            var response = await next();

            await _cacheService.SetAsync(cacheKey, response, attribute.CacheDuration, cancellationToken);

            return response;
        }
    }

}