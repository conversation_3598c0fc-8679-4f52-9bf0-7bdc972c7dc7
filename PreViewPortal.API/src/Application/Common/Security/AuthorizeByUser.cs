using System;
using System.Threading;
using System.Threading.Tasks;
using Application.Common.Interfaces;

namespace Application.Common.Security
{
    public class AuthorizeByUser : IAuthorizeByProperty
    {
        private readonly ICurrentUserService _currentUserService;

        public AuthorizeByPropertyType Type => AuthorizeByPropertyType.User;

        public AuthorizeByUser(ICurrentUserService currentUserService)
        {
            _currentUserService = currentUserService;
        }

        public Task<bool> Authorize(object propertyValue, CancellationToken cancellationToken = default)
        {
            if (_currentUserService.IsAdmin)
            {
                return Task.FromResult(true);
            }

            var userId = (int)propertyValue;

            return Task.FromResult(_currentUserService.Id == userId);
        }
    }
}