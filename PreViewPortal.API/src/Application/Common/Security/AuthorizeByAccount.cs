using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Application.Common.Interfaces;

namespace Application.Common.Security
{
    public class AuthorizeByAccount : IAuthorizeByProperty
    {
        private readonly ICurrentUserService _currentUserService;

        public AuthorizeByPropertyType Type => AuthorizeByPropertyType.Account;

        public AuthorizeByAccount(ICurrentUserService currentUserService)
        {
            _currentUserService = currentUserService;
        }

        public Task<bool> Authorize(object propertyValue, CancellationToken cancellationToken = default)
        {
            if (_currentUserService.IsAdmin)
            {
                return Task.FromResult(true);
            }

            var accountIds = new List<int>();

            switch (propertyValue)
            {
                case int id:
                    accountIds.Add(id);
                    break;
                case IEnumerable<int> ids:
                    accountIds.AddRange(ids);
                    break;
                default:
                    throw new InvalidCastException();
            }

            accountIds = accountIds.Where(id => id != default).ToList();

            if (!accountIds.Any())
            {
                return Task.FromResult(false);
            }

            var canAccessAccounts = accountIds.All(a => _currentUserService.Accounts.Contains(a));

            return Task.FromResult(canAccessAccounts);
        }
    }
}