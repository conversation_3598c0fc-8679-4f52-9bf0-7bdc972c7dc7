using System.Security.Claims;
using System.Threading.Tasks;
using Application.Common.Interfaces;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;

namespace Application.Common.Security
{
    public class GuestMiddleware
    {
        private readonly RequestDelegate _next;

        public GuestMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            var shareService = context.RequestServices.GetRequiredService<IShareService>();
            
            if (context.Request.Headers.TryGetValue("X-Share-Key", out var guestKey)  && await shareService.IsValidShareKey(guestKey))
            {
                var guestPrincipal = CreateGuestPrincipal();

                context.User = guestPrincipal;
            }

            await _next(context);
        }

        private ClaimsPrincipal CreateGuestPrincipal()
        {
            var claims = new[]
            {
                new Claim(ClaimTypes.Role, RealeyesJwtRoles.Guest)
            };

            var identity = new ClaimsIdentity(claims, RealeyesJwtRoles.Guest);
            return new ClaimsPrincipal(identity);
        }
    }

    public static class GuestMiddlewareExtensions
    {
        public static IApplicationBuilder UseGuestMiddleware(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<GuestMiddleware>();
        }
    }
}