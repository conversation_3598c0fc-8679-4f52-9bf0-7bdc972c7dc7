using Application.CreativeViewer.Queries;
using BusinessLayer.Model.Response;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Application.Common.Extensions
{
    public static class ForcedExposureCurveExtensions
    {
        public static CreativeViewerCurveDto ToDto(this IEnumerable<ForcedExposureCurve> curves, CreativeViewerCurveType type, string segmentKey, bool isNorm, Func<ForcedExposureCurve, float?> pctSelector, int? views)
        {
            var secondValues = curves
                .Where(curve => curve.SegmentKey == segmentKey)
                .Select(c => new CreativeViewerCurveSecondDto
                {
                    //Note: second starts with 0 on ic curves vds
                    Second = c.Second + 1,
                    Pct = pctSelector(c)
                })
                .OrderBy(c => c.Second)
                .ToList();

            var curveDto = new CreativeViewerCurveDto
            {
                Type = type,
                SegmentKey = segmentKey,
                IsNorm = isNorm,
                ExposureGroup = ExposureGroup.Focused,
                Values = secondValues,
                Views = views
            };

            return curveDto;
        }
    }
}
