using System;
using System.Collections.Generic;
using System.Linq;
using Application.CreativeViewer.Queries;
using BusinessLayer.Model.Response;


namespace Application.Common.Extensions;

public static class InContextCurveExtensions
{
    public static CreativeViewerCurveDto ToDto(this IEnumerable<InContextCurve> curves, CreativeViewerCurveType type, string segmentKey, bool isNorm, string exposureGroup, Func<InContextCurve, float?> pctSelector, int? views)
    {
        var secondValues = curves
            .Where(curve => curve.SegmentKey == segmentKey)
            .Select(c => new CreativeViewerCurveSecondDto
            {
                //Note: second starts with 0 on ic curves vds
                Second = c.Second + 1,
                Pct = pctSelector(c)
            })
            .OrderBy(c => c.Second)
            .ToList();

        var curveDto = new CreativeViewerCurveDto
        {
            Type = type,
            SegmentKey = segmentKey,
            IsNorm = isNorm,
            ExposureGroup = exposureGroup,
            Values = secondValues,
            Views = views
        };

        return curveDto;
    }
}