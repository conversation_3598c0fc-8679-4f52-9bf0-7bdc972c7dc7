using System.Collections.Generic;
using System.Security.Claims;

namespace Application.Common.Interfaces
{
    public interface ICurrentUserService
    {
        public int Id { get; }
        public string Email { get; }
        public string Name { get; }
        public int? Account { get; }
        public List<int> Accounts { get; }
        public List<string> Roles { get; }
        public bool IsAdmin { get; }
        public bool IsValidationNeeded { get; }
        public bool HasAccess { get; }
        public ClaimsPrincipal User { get; }

        /// <summary>
        /// Internal (for RE services inside) api secret key retrieved from request or null
        /// </summary>
        public string InternalApiSecret { get; }
    }
}
