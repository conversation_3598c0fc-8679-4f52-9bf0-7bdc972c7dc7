using System.Threading;
using System.Threading.Tasks;
using Domain.Entitites;
using Microsoft.EntityFrameworkCore;

namespace Application.Common.Interfaces
{
    public interface IApplicationDbContext
    {
        DbSet<SourceMedia> SourceMedia { get; set; }
        DbSet<MediaStorage> MediaStorage { get; set; }
        DbSet<BrandLogo> BrandLogo { get; set; }
        DbSet<Domain.Entitites.Account> Account { get; set; }
        DbSet<UserProfile> UserProfile { get; set; }
        DbSet<UserSystemRole> UserSystemRole { get; set; }
        DbSet<User_PreViewGridSettings> User_PreViewGridSettings { get; set; }
        DbSet<AdSet> AdSet { get; set; }
        DbSet<Domain.Entitites.ShareKey> ShareKey { get; set; }
        DbSet<Domain.Entitites.ShareItem> ShareItem { get; set; }
        Task<int> SaveChangesAsync(CancellationToken cancellationToken);
    }
}
