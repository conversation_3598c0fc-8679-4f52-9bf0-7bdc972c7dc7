using Application.Common.Interfaces;
using Infrastructure.Authentication;
using Infrastructure.Authorization;
using Infrastructure.Persistence;
using Infrastructure.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Realeyes.PreView.Infrastructure.Services.Dremio;
using StackExchange.Redis;

namespace Infrastructure
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddTransient<IBrandService, BrandService>();
            services.AddTransient<IMediaService, MediaService>();
            services.AddTransient<IDremioService, DremioService>();
            services.AddTransient<ICacheService, DistributedCacheService>();
           
            services.AddHttpClient();

            var redisConnectionString = configuration.GetConnectionString("Redis");
            if (!string.IsNullOrEmpty(redisConnectionString))
            {
                var configurationOptions = ConfigurationOptions.Parse(redisConnectionString);
                configurationOptions.AllowAdmin = true;

                services.AddStackExchangeRedisCache(option => option.ConfigurationOptions = configurationOptions);

                var multiplexer = ConnectionMultiplexer.Connect(configurationOptions);
                services.AddSingleton<IConnectionMultiplexer>(multiplexer);
            }
            else
            {
                services.AddDistributedMemoryCache();
            }

            services.AddDbContext<StudyDbContext>(option => option.UseSqlServer(configuration.GetConnectionString("StudyDatabase")));

            services.AddScoped<IApplicationDbContext>(provider => provider.GetRequiredService<StudyDbContext>());
            services.AddScoped<IDomainEventService, DomainEventService>();

            services.AddRealeyesJwtAuthentication(configuration);

            AuthorizationExtensions.AddAuthorization(services);

            return services;
        }
    }
}
