using System.Security.Claims;
using Application.Common.Security;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;

namespace Infrastructure.Authorization
{
    public static class AuthorizationExtensions
    {
        public static IServiceCollection AddAuthorization(this IServiceCollection services)
        {
            services.AddAuthorization(c =>
            {
                c.AddPolicy(Policies.HasAccess, p => { p.RequireAssertion(HasAdminOrAccessPermissions); });
                c.AddPolicy(Policies.HasGuestAccess, p =>
                {
                    p.RequireAssertion(context =>
                        HasAdminOrAccessPermissions(context) || IsGuest(context)
                    );
                });
            });

            return services;
        }

        private static bool HasAdminOrAccessPermissions(AuthorizationHandlerContext context)
        {
            var isAdmin = context.User.HasClaim(ClaimTypes.Role, RealeyesJwtRoles.Administrator);
            var hasAccess = context.User.HasClaim(RealeyesJwtClaimTypes.Permission, RealeyesJwtPermissions.PreViewPortal);

            return isAdmin || hasAccess;
        }

        private static bool IsGuest(AuthorizationHandlerContext context)
        {
            return context.User.HasClaim(ClaimTypes.Role, RealeyesJwtRoles.Guest);
        }
    }
}