using System;
using System.IO;
using Amazon.CloudFront;
using Application.Common.Interfaces;
using Application.Common.Models.Settings;
using Microsoft.Extensions.Options;

namespace Infrastructure.Services
{
    public class MediaService : IMediaService
    {
        private readonly ThumbnailBucketSettings _thumbnailSettings;
        private readonly SourceMediaDataBucketSettings  _sourceMediaSettings;

        private static readonly TimeSpan DefaultExpiration = TimeSpan.FromHours(5);

        public MediaService(IOptions<ThumbnailBucketSettings> optionsOne, IOptions<SourceMediaDataBucketSettings> optionsTwo)
        {
            _thumbnailSettings = optionsOne.Value;
            _sourceMediaSettings = optionsTwo.Value;
        }

        public string GetThumbnailUrl(string thumbnailFileName)
        {
            var cdn = _thumbnailSettings.CloudFrontDomainName;
            const int width = 153;
            const int height = 94;
            const string theme = "realeyes";

            return $"{cdn.Trim('/')}/?s3Key={thumbnailFileName ?? string.Empty}&width={width}&height={height}&theme={theme}";
        }

        public string GetMediaSignedUrl(string sourceMediaConvertedFileName, TimeSpan? expiration = null)
        {
            using var privateKeyReader = new StringReader(_sourceMediaSettings.PrivateKey);

            return AmazonCloudFrontUrlSigner.GetCannedSignedURL(
                AmazonCloudFrontUrlSigner.Protocol.https,
                _sourceMediaSettings.CloudFrontDomainName,
                privateKeyReader,
                sourceMediaConvertedFileName,
                _sourceMediaSettings.KeyPairId,
                DateTime.UtcNow.Add(expiration ?? DefaultExpiration)
            );
        }
    }
}
