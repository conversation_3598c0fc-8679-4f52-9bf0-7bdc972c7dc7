using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Amazon.CloudFront;
using Application.Common.Interfaces;
using Application.Common.Models.Settings;
using Microsoft.Extensions.Options;

namespace Infrastructure.Services
{
    public class BrandService : IBrandService
    {
        private static readonly TimeSpan DefaultExpiration = TimeSpan.FromHours(5);

        private readonly SourceMediaDataBucketSettings _settings;

        public BrandService(IOptions<SourceMediaDataBucketSettings> options)
        {
            _settings = options.Value;
        }

        public string GetLogoSignedUrl(string brandLogoFileName, TimeSpan? expiration = null)
        {
            using var privateKeyReader = new StringReader(_settings.PrivateKey);

            return AmazonCloudFrontUrlSigner.GetCannedSignedURL(
                AmazonCloudFrontUrlSigner.Protocol.https,
                _settings.CloudFrontDomainName,
                privateKeyReader,
                brandLogoFileName,
                _settings.KeyPairId,
                DateTime.UtcNow.Add(expiration ?? DefaultExpiration)
            );
        }

        public Dictionary<string, string> GetLogoSignedUrls(IEnumerable<string> brandLogoFileNames, TimeSpan? expiration = null)
        {
            return brandLogoFileNames
                .Distinct()
                .Where(fn => !string.IsNullOrEmpty(fn))
                .ToDictionary(fn => fn, fn => GetLogoSignedUrl(fn, expiration ?? DefaultExpiration));
        }
    }
}
