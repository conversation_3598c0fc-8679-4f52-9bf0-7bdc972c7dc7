using System;
using System.Threading.Tasks;
using Application.Common.Interfaces;
using Application.Common.Models;
using Domain.Common;
using MediatR;

namespace Infrastructure.Services
{
    public class DomainEventService : IDomainEventService
    {
        private readonly IPublisher _publisher;

        public DomainEventService(IPublisher publisher)
        {
            _publisher = publisher;
        }

        public async Task Publish(DomainEvent domainEvent)
        {
            var notification = (INotification)Activator.CreateInstance(typeof(DomainEventNotification<>).MakeGenericType(domainEvent.GetType()), domainEvent);
            await _publisher.Publish(notification);
        }
    }
}
