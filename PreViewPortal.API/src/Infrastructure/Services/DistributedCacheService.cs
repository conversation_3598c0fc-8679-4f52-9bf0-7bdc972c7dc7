using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Application.Common.Interfaces;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;

namespace Infrastructure.Services
{
    public class DistributedCacheService : ICacheService
    {
        private readonly IDistributedCache _distributedCache;
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<DistributedCacheService> _logger;

        public DistributedCacheService(IDistributedCache distributedCache, IServiceProvider serviceProvider, ILogger<DistributedCacheService> logger)
        {
            _distributedCache = distributedCache;
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        public async Task<T> GetAsync<T>(string key, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(key))
            {
                return default;
            }

            _logger.LogDebug("Retrieving object with key [{CacheKey}] from cache.", key);

            var cachedResponse = await _distributedCache.GetStringAsync(key, cancellationToken);
            if (cachedResponse != null)
            {
                _logger.LogDebug("Successfully retrieved object with key [{CacheKey}] from cache", key);

                return JsonSerializer.Deserialize<T>(cachedResponse);
            }

            return default;
        }

        public async Task SetAsync<T>(string key, T value, TimeSpan expiration, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(key))
            {
                return;
            }

            var responseToCache = JsonSerializer.SerializeToUtf8Bytes(value);

            _logger.LogDebug("Setting object with key [{CacheKey}] in cache.", key);

            await _distributedCache.SetAsync(key, responseToCache, new DistributedCacheEntryOptions { AbsoluteExpirationRelativeToNow = expiration }, cancellationToken);
        }

        public async Task RemoveAsync(string key, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(key))
            {
                return;
            }

            _logger.LogDebug("Removing object with key [{CacheKey}] from cache.", key);

            await _distributedCache.RemoveAsync(key, cancellationToken);
        }

        public async Task FlushAsync(CancellationToken cancellationToken = default)
        {
            var redisConnection = _serviceProvider.GetService<IConnectionMultiplexer>();
            if (redisConnection is null)
            {
                throw new Exception("Redis is not available(when running locally flush is not supported).");
            }

            var endpoints = redisConnection.GetEndPoints();
            foreach (var endpoint in endpoints)
            {
                var server = redisConnection.GetServer(endpoint);
                await server.FlushAllDatabasesAsync();
            }
        }
    }
}
