using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using Application.Common.Interfaces;
using Domain.Common;
using Domain.Entitites;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Persistence
{
    public static class StudyDbContextQuery
    {
        public static string AccountWithPreviewFlag = @"
SELECT a.*, ISNULL(pv.Enabled, 0) IsPreviewEnabled FROM Account a
OUTER APPLY (
    SELECT TOP 1 1 as Enabled FROM DisplaySetting d WHERE a.id = d.AccountId AND d.DisplaySettingTypeID = 11 AND d.Value = 'true'
) pv
";
    }
}
