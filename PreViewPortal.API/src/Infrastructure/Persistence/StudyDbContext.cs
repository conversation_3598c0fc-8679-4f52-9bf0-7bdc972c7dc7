using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using Application.Common.Interfaces;
using Domain.Common;
using Domain.Entitites;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Persistence
{
    public class StudyDbContext : DbContext, IApplicationDbContext
    {
        private readonly IDomainEventService _domainEventService;

        public StudyDbContext(DbContextOptions<StudyDbContext> options, IDomainEventService domainEventService) : base(options)
        {
            _domainEventService = domainEventService;
        }

        public DbSet<SourceMedia> SourceMedia { get; set; }
        public DbSet<MediaStorage> MediaStorage { get; set; }
        public DbSet<BrandLogo> BrandLogo { get; set; }
        public DbSet<Account> Account { get; set; }
        public DbSet<UserProfile> UserProfile { get; set; }
        public DbSet<UserSystemRole> UserSystemRole { get; set; }
        public DbSet<User_PreViewGridSettings> User_PreViewGridSettings { get; set; }
        public DbSet<AdSet> AdSet { get; set; }
        public DbSet<ShareKey> ShareKey { get; set; }
        public DbSet<ShareItem> ShareItem { get; set; }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = new CancellationToken())
        {
            var events = ChangeTracker.Entries<IHasDomainEvent>()
                .SelectMany(ee => ee.Entity.DomainEvents)
                .Where(de => !de.IsPublished)
                .ToList();

            var result = await base.SaveChangesAsync(cancellationToken);

            await DispatchEvents(events);

            return result;
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());

            base.OnModelCreating(modelBuilder);
        }

        private async Task DispatchEvents(IEnumerable<DomainEvent> events)
        {
            foreach (var @event in events)
            {
                @event.IsPublished = true;
                await _domainEventService.Publish(@event);
            }
        }
    }
}
