using Domain.Entitites;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Persistence.Configurations
{
    public class BrandLogoConfiguration : IEntityTypeConfiguration<BrandLogo>
    {
        public void Configure(EntityTypeBuilder<BrandLogo> builder)
        {
            // Primary Key
            builder.HasKey(t => t.ID);

            // Table & Column Mappings
            builder.ToTable("BrandLogo");
            builder.Property(t => t.ID).HasColumnName("ID");
            builder.Property(t => t.BrandId).HasColumnName("BrandId");
            builder.Property(t => t.CountryID).HasColumnName("CountryID");
            builder.Property(t => t.InterfaceLocaleID).HasColumnName("InterfaceLocaleID");
            builder.Property(t => t.MediaStorageId).HasColumnName("MediaStorageId");

            // Relationships
            builder.HasOne(t => t.MediaStorage)
                .WithOne(d => d.BrandLogo)
                .HasForeignKey<BrandLogo>(t => t.MediaStorageId);
        }
    }
}
