using Domain.Entitites;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Persistence.Configurations
{
    public class AdSetConfiguration : IEntityTypeConfiguration<AdSet>
    {
        public void Configure(EntityTypeBuilder<AdSet> builder)
        {
            // Primary Key
            builder.HasKey(t => t.ID);

            // Properties
            builder.Property(t => t.Name).IsRequired().HasMaxLength(255);
            builder.Property(t => t.ExternalKey).IsRequired().HasMaxLength(36);
            builder.Property(t => t.AdSetTypeID).IsRequired();
            builder.Property(t => t.CreateTime).IsRequired();

            // Table & Column Mappings
            builder.ToTable("AdSet");

            builder.Property(t => t.ID).HasColumnName("ID");
            builder.Property(t => t.Name).HasColumnName("Name");
            builder.Property(t => t.ExternalKey).HasColumnName("ExternalKey");
            builder.Property(t => t.AdSetTypeID).HasColumnName("AdSetTypeID");
            builder.Property(t => t.CreateTime).HasColumnName("CreateTime");
            builder.Property(t => t.ModifiedTime).HasColumnName("ModifiedTime");
        }
    }
}
