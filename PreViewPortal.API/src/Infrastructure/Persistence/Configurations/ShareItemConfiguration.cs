using Domain.Entitites;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Persistence.Configurations
{
    public class ShareItemConfiguration : IEntityTypeConfiguration<ShareItem>
    {
        public void Configure(EntityTypeBuilder<ShareItem> builder)
        {
            builder.HasKey(si => si.ID);

            builder.ToTable("Share_Item");

            builder.HasKey(si => si.ID);
            builder.Property(si => si.ShareID).HasColumnName("ShareID");
            builder.Property(si => si.TestID).HasColumnName("TestID");
            builder.Property(si => si.SourceMediaID).HasColumnName("SourceMediaID");
            builder.Property(si => si.TaskID).HasColumnName("TaskID");
            builder.Property(si => si.AdSetID).HasColumnName("AdSetID");

            // Relationships
            builder.HasOne(t => t.ShareKey)
                .WithMany(t => t.ShareItems)
                .HasForeignKey(d => d.ShareID)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(t => t.AdSet)
                .WithMany(d => d.ShareItems)
                .HasForeignKey(t => t.AdSetID);
        }
    }
}