using Domain.Entitites;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Persistence.Configurations
{
    public class User_PreViewGridSettingsConfiguration : IEntityTypeConfiguration<User_PreViewGridSettings>
    {
        public void Configure(EntityTypeBuilder<User_PreViewGridSettings> builder)
        {
            builder.HasKey(gs => new { gs.UserId, gs.GridId });

            builder.ToTable("User_PreViewGridSettings");

            builder.Property(gs => gs.UserId).HasColumnName("UserId");
            builder.Property(gs => gs.GridId).HasColumnName("GridId");
            builder.Property(gs => gs.GridSettings).HasColumnName("GridSettings");
            builder.Property(gs => gs.ModifiedAt).HasColumnName("ModifiedAt");

            builder
                .HasOne(gs => gs.UserProfile)
                .WithMany()
                .HasForeignKey(gs => gs.UserId);

            builder.Ignore(gs => gs.DomainEvents);
        }
    }
}
