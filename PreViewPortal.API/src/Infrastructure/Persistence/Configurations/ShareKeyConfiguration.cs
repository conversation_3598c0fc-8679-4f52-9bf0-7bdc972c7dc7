using Domain.Entitites;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Persistence.Configurations
{
    public class ShareKeyConfiguration : IEntityTypeConfiguration<ShareKey>
    {
        public void Configure(EntityTypeBuilder<ShareKey> builder)
        {
            builder.HasKey(sk => sk.ID);

            builder.ToTable("Share_Key");

            builder.Property(sk => sk.ID).HasColumnName("ID");
            builder.Property(sk => sk.HashKey).HasColumnName("HashKey");
            builder.Property(sk => sk.IsEnabled).HasColumnName("IsEnabled");
            builder.Property(sk => sk.CreateDate).HasColumnName("CreateDate");
            builder.Property(sk => sk.UpdateDate).HasColumnName("UpdateDate");
            builder.Property(sk => sk.ProductType).HasColumnName("ProductType");
            builder.Property(sk => sk.AccountID).HasColumnName("AccountID");
        }
    }
}