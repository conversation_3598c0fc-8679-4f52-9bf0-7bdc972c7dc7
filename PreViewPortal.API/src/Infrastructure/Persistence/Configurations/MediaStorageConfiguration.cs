using Domain.Entitites;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Persistence.Configurations
{
    public class MediaStorageConfiguration : IEntityTypeConfiguration<MediaStorage>
    {
        public void Configure(EntityTypeBuilder<MediaStorage> builder)
        {
            // Primary Key
            builder.HasKey(t => t.ID);

            // Properties
            builder.Property(t => t.FileName)
                .HasMaxLength(255);

            builder.Property(t => t.ConvertedFileName)
                .HasMaxLength(255);

            builder.Property(t => t.ThumbnailFileName)
                .HasMaxLength(255);

            builder.Property(t => t.ContentType)
                .HasMaxLength(255);

            builder.Property(t => t.ThumbstripFileName)
                .HasMaxLength(255);

            // Table & Column Mappings
            builder.ToTable("MediaStorage");
            builder.Property(t => t.ID).HasColumnName("ID");
            builder.Property(t => t.GUID).HasColumnName("GUID");
            builder.Property(t => t.FileName).HasColumnName("FileName");
            builder.Property(t => t.ConvertedFileName).HasColumnName("ConvertedFileName");
            builder.Property(t => t.ThumbnailFileName).HasColumnName("ThumbnailFileName");
            builder.Property(t => t.ContentType).HasColumnName("ContentType");
            builder.Property(t => t.ThumbstripFileName).HasColumnName("ThumbstripFileName");
            builder.Property(t => t.Duration).HasColumnName("Duration");
            builder.Property(t => t.Height).HasColumnName("Height");
            builder.Property(t => t.Width).HasColumnName("Width");

        }
    }
}
