using Domain.Entitites;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Persistence.Configurations
{
    public class UserAccountConfiguration : IEntityTypeConfiguration<UserAccount>
    {
        public void Configure(EntityTypeBuilder<UserAccount> builder)
        {
            builder.HasKey(ua => new { ua.UserID, ua.AccountID });

            builder.ToTable("User_Account");

            builder.Property(ua => ua.UserID).HasColumnName("UserID");
            builder.Property(ua => ua.AccountID).HasColumnName("AccountID");
            builder.Property(ua => ua.RoleId).HasColumnName("RoleId");

            builder
                .HasOne(ua => ua.Account)
                .WithMany(a => a.UserAccounts)
                .HasForeignKey(ua => ua.AccountID);

            builder
                .HasOne(ua => ua.UserProfile)
                .WithMany(up => up.UserAccounts)
                .HasForeignKey(ua => ua.UserID);
        }
    }
}
