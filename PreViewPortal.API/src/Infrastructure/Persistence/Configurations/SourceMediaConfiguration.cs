using Domain.Entitites;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Persistence.Configurations
{
    public class SourceMediaConfiguration : IEntityTypeConfiguration<SourceMedia>
    {
        public void Configure(EntityTypeBuilder<SourceMedia> builder)
        {
            // Primary Key
            builder.HasKey(t => t.ID);

            // Properties
            builder.Property(t => t.Name)
                .IsRequired()
                .HasMaxLength(255);

            builder.Property(t => t.MediaURI)
                .HasMaxLength(255);

            // Table & Column Mappings
            builder.ToTable("SourceMedia");
            builder.Property(t => t.ID).HasColumnName("ID");
            builder.Property(t => t.Name).HasColumnName("Name");
            builder.Property(t => t.MediaURI).HasColumnName("MediaURI");
            builder.Property(t => t.OriginalWidth).HasColumnName("OriginalWidth");
            builder.Property(t => t.OriginalHeight).HasColumnName("OriginalHeight");
            builder.Property(t => t.SourceMediaTypeID).HasColumnName("SourceMediaTypeID");
            builder.Property(t => t.MediaStorageID).HasColumnName("MediaStorageID");
            builder.Property(t => t.Duration).HasColumnName("Duration");
            builder.Property(t => t.AccountID).HasColumnName("AccountID");
            builder.Property(t => t.BrandId).HasColumnName("BrandID");

            // Relationships
            builder.HasOne(t => t.MediaStorage)
                .WithOne(d => d.SourceMedia)
                .HasForeignKey<SourceMedia>(t => t.MediaStorageID);
        }
    }
}
