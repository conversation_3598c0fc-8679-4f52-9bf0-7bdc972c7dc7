using Domain.Entitites;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Persistence.Configurations
{
    public class UserSystemRoleConfiguration : IEntityTypeConfiguration<UserSystemRole>
    {
        public void Configure(EntityTypeBuilder<UserSystemRole> builder)
        {
            builder.HasKey(e => new { e.UserId, e.RoleId });

            builder.ToTable("webpages_UsersInRoles");

            builder.Property(t => t.RoleId).HasColumnName("RoleId");
            builder.Property(t => t.UserId).HasColumnName("UserId");
        }
    }
}
