using Application.Common.Models.Settings;
using Application.Common.Security;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;

namespace Infrastructure.Authentication
{
    public static class AuthenticationExtensions
    {
        public static void AddRealeyesJwtAuthentication(this IServiceCollection services, IConfiguration config)
        {
            var configSection = config.GetSection(RealeyesJwtAuthenticationDefaults.ConfigSectionName);
            if (configSection is null)
            {
                throw new Exception($"Could not find section in the configuration called [{RealeyesJwtAuthenticationDefaults.ConfigSectionName}].");
            }

            var jwtConfig = new RealeyesJwtAuthenticationSettings();
            configSection.Bind(jwtConfig);
            // To force signing out of all users set 'SignOutBefore' env variable on deployed lambda function to the current time (yyyy-MM-dd mm:hh)
            var signOutBefore = config.GetValue<DateTime>("SignOutBefore");

            services
                .AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
                .AddJwtBearer(c =>
                {
                    c.Authority = jwtConfig.ValidIssuer;

                    c.TokenValidationParameters = new()
                    {
                        ValidateIssuerSigningKey = true,
                        ValidateIssuer = true,
                        ValidIssuer = jwtConfig.ValidIssuer,                        
                        ValidateAudience = true,
                        ValidAudiences = jwtConfig.ValidAudiences,
                        RequireExpirationTime = true,
                        RequireSignedTokens = true,
                        RequireAudience = true                        
                    };

                    c.SaveToken = false;

                    c.RequireHttpsMetadata = true;

                    c.Events = new JwtBearerEvents
                    {
                        OnMessageReceived = context =>
                        {
                            context.Token = context.Request.Cookies[RealeyesJwtAuthenticationDefaults.TokenCookieName];

                            return Task.CompletedTask;
                        },
                        OnChallenge = context =>
                        {   // Here we can overwrite response in case of no failed auth 
                            return Task.CompletedTask;
                        },
                        OnTokenValidated = context =>
                        {
                            if (context.SecurityToken.ValidFrom > default(DateTime) && context.SecurityToken.ValidFrom < signOutBefore)
                                context.Fail(StatusCodes.Status401Unauthorized.ToString());
                            return Task.CompletedTask;
                        }
                    };
                });
        }
    }
}