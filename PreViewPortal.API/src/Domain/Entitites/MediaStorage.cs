using System;

namespace Domain.Entitites
{
    public class MediaStorage
    {
        public int ID { get; set; }
        public Guid GUID { get; set; }
        public string FileName { get; set; }
        public string ConvertedFileName { get; set; }
        public string ThumbnailFileName { get; set; }
        public string ContentType { get; set; }
        public string ThumbstripFileName { get; set; }
        public long? Duration { get; set; }
        public int? Height { get; set; }
        public int? Width { get; set; }

        public virtual SourceMedia SourceMedia { get; set; }
        public virtual BrandLogo BrandLogo { get; set; }
    }
}
