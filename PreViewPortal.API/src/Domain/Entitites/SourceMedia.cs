namespace Domain.Entitites
{
    public class SourceMedia
    {
        public int ID { get; set; }
        public string Name { get; set; }
        public string MediaURI { get; set; }
        public int? OriginalWidth { get; set; }
        public int? OriginalHeight { get; set; }
        public int SourceMediaTypeID { get; set; }
        public int? MediaStorageID { get; set; }
        public int? Duration { get; set; }
        public int? AccountID { get; set; }
        public int? BrandId { get; set; }

        public virtual MediaStorage MediaStorage { get; set; }
    }
}
