using System.Collections.Generic;
using Domain.Common;
using Domain.Enums;
using System;

namespace Domain.Entitites
{
    public class User_PreViewGridSettings : IHasDomainEvent
    {
        public int UserId { get; set; }
        public PreviewGrid GridId { get; set; }

        public string GridSettings { get; set; }
        public DateTimeOffset ModifiedAt { get; set; }

        public virtual UserProfile UserProfile { get; set; }

        public List<DomainEvent> DomainEvents { get; set; } = new List<DomainEvent>();

    }
}
