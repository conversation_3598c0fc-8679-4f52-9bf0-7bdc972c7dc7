using System;
using System.Collections.Generic;

namespace Domain.Entitites
{
    public class AdSet
    {
        public int ID { get; set; }
        public string Name { get; set; }
        public string ExternalKey { get; set; }
        public int AdSetTypeID { get; set; }
        public DateTime CreateTime { get; set; }
        public DateTime? ModifiedTime { get; set; }

        public ICollection<ShareItem> ShareItems { get; set; }
    }
}
