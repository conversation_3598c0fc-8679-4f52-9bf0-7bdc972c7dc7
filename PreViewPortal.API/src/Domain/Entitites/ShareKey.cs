using System;
using System.Collections.Generic;
using BusinessLayer.Model;

namespace Domain.Entitites;

public class ShareKey
{
    public ShareKey()
    {
        CreateDate = DateTime.UtcNow;
    }

    public int ID { get; set; }
    public string Hash<PERSON>ey { get; set; }
    public bool IsEnabled { get; set; }
    public int AccountID { get; set; }
    public DateTime CreateDate { get; set; }
    public DateTime? UpdateDate { get; set; }
    public ProductType ProductType { get; set; }
    public List<ShareItem> ShareItems { get; set; }
}