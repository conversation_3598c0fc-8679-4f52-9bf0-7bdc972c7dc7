using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Realeyes.PreView.Infrastructure.Services.Dremio.Extension;

public class DremioQueryExecutor : IQueryExecutor
{
    private readonly IDremioDataProvider dremioDataProvider;

    public DremioQueryExecutor(IDremioDataProvider dremioDataProvider)
    {
        this.dremioDataProvider = dremioDataProvider;
    }

    public Task<IEnumerable<T>> Execute<T>(IDremioQuery query)
    {
        string queryString = query.Build();

        if (string.IsNullOrWhiteSpace(queryString)) 
        { 
            return Task.FromResult(Enumerable.Empty<T>()); 
        }

        return dremioDataProvider.GetRows<T>(queryString);
    }
}