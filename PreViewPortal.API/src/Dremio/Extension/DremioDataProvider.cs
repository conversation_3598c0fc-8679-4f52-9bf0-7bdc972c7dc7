using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Realeyes.PreView.Infrastructure.Services.Dremio.Extension;

public class DremioDataProvider : IDremioDataProvider
{
    private readonly IDremioService dremioService;

    public DremioDataProvider(IDremioService dremioService)
    {
        this.dremioService = dremioService;
    }

    public async Task<IEnumerable<TResponse>> GetRows<TResponse>(string query,
        CancellationToken cancellationToken = default)
    {
        DremioJobResultModel<TResponse> queryResult =
            await dremioService.RunQuery<TResponse>(query, cancellationToken: cancellationToken);
        return queryResult.Rows;
    }
}