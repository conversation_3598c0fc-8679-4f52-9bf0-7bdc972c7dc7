using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;


namespace Realeyes.PreView.Infrastructure.Services.Dremio.Configuration;

public static class DremioServiceCollectionExtension
{
    public static IServiceCollection AddDremioService(this IServiceCollection services)
    {
        services.AddHttpClient<IDremioService, DremioService>();
        services.AddScoped<IDremioService, DremioService>();        

        return services;
    }

    public static IServiceCollection AddDremioSettingsFrom(this IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<DremioSettings>(configuration.GetSection(DremioSettings.SectionName));
        services.PostConfigure<DremioSettings>(settings =>
        {
            if (settings.NumberOfResultsPerAPIRequest > DremioSettings.MaxResultsPerRequest)
            {
                settings.NumberOfResultsPerAPIRequest = DremioSettings.MaxResultsPerRequest;
            }
        });

        return services;
    }
}
