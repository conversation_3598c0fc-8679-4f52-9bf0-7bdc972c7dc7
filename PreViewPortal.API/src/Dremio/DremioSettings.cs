using System.ComponentModel.DataAnnotations;

namespace Realeyes.PreView.Infrastructure.Services.Dremio;

public class DremioSettings
{
    public const string SectionName = "dremio";
    public const int MaxResultsPerRequest = 500;

    public string BaseUrl { get; set; }
    public string Username { get; set; }
    public string Password { get; set; }
    public int ConcurentAPIRequests { get; set; } = 5;
    public int NumberOfResultsPerAPIRequest { get; set; } = 500;
    public int TimeoutOfAPIRequests { get; set; } = 10;
}
