using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Polly;
using Polly.Retry;

namespace Realeyes.PreView.Infrastructure.Services.Dremio;

public class DremioService : IDremioService
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ILogger<DremioService> _logger;
    private readonly DremioSettings _dremioSettings;

    private readonly AsyncRetryPolicy<HttpResponseMessage> _httpRequestPolicy;

    private static DremioLoginModel DremioLogin = new DremioLoginModel();

    private static JsonSerializerOptions SerializerOptions { get; }
    private static readonly SemaphoreSlim TokenSemaphore = new SemaphoreSlim(1, 1);

    static DremioService()
    {
        SerializerOptions = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
        SerializerOptions.Converters.Add(new DateTimeConverter());
        SerializerOptions.Converters.Add(new DateTimeOffsetConverter());
    }

    public DremioService(IHttpClientFactory httpClientFactory, IOptions<DremioSettings> dremioSettings,
        ILogger<DremioService> logger)
    {
        _logger = logger;
        _httpClientFactory = httpClientFactory;
        _dremioSettings = dremioSettings.Value;

        _httpRequestPolicy = Policy<HttpResponseMessage>
            .HandleResult(r => r.StatusCode == HttpStatusCode.Unauthorized)
            .RetryAsync(1, onRetryAsync: async (ex, i) => await RefreshToken());
    }

    private async Task RefreshToken()
    {
        try
        {
            await TokenSemaphore.WaitAsync();

            var tokenExpiration = DateTimeOffset.FromUnixTimeMilliseconds(DremioLogin.Expires).UtcDateTime;
            if (tokenExpiration > DateTime.UtcNow)
            {
                return;
            }

            var body = JsonSerializer.Serialize(new
                { userName = _dremioSettings.Username, password = _dremioSettings.Password });
            var content = new StringContent(body, Encoding.UTF8, "application/json");

            var response = await SendDremioRequest(HttpMethod.Post, "/apiv2/login", false, content);

            if (response.IsSuccessStatusCode)
            {
                DremioLogin = await response.Content.ReadFromJsonAsync<DremioLoginModel>(SerializerOptions);
                _logger.LogTrace("The dremio user is logged in and the token was succsessfully refreshed!");
            }
            else
            {
                throw new Exception(
                    "The dremio user can not be authenticated! Please check user credentials in the parameterstore!");
            }
        }
        finally
        {
            TokenSemaphore.Release(1);
        }
    }

    private async Task<HttpResponseMessage> SendDremioRequest(HttpMethod httpMethod, string requestUrl, bool withAuth,
        HttpContent content = null, CancellationToken cancellationToken = default, int? timeout = null)
    {
        using var client = _httpClientFactory.CreateClient();
        client.BaseAddress = new Uri(_dremioSettings.BaseUrl);
        if (timeout.HasValue)
            client.Timeout = TimeSpan.FromSeconds(timeout.Value);
        var request = new HttpRequestMessage(httpMethod, requestUrl);

        if (content != null)
            request.Content = content;

        if (withAuth)
            request.Headers.Add("Authorization", $"_dremio{DremioLogin.Token}");

        var httpResponse = await client.SendAsync(request, cancellationToken);
        return httpResponse;
    }

    public async Task<string> CreateJobByQuery(string query, CancellationToken cancellationToken = default)
    {
        var body = JsonSerializer.Serialize(new { sql = query });
        var content = new StringContent(body, Encoding.UTF8, "application/json");

        var response = await _httpRequestPolicy.ExecuteAsync(() =>
            SendDremioRequest(HttpMethod.Post, "/api/v3/sql", true, content, cancellationToken));
        var model = await response.Content.ReadFromJsonAsync<DremioJobIdModel>(SerializerOptions, cancellationToken);

        return model.Id;
    }

    public async Task<DremioJobStateModel> GetJobState(string jobId, CancellationToken cancellationToken = default)
    {
        var response = await _httpRequestPolicy.ExecuteAsync(() =>
            SendDremioRequest(HttpMethod.Get, $"/api/v3/job/{jobId}", true, null, cancellationToken));
        var model = await response.Content.ReadFromJsonAsync<DremioJobStateModel>(SerializerOptions, cancellationToken);
        return model;
    }

    public async Task<DremioJobResultModel<T>> GetJobResult<T>(string query, string jobId, DremioJobStateModel jobState,
        int? offset, int? limit)
    {
        var requestUrlBase = $"/api/v3/job/{jobId}/results";
        var requestUrls = new List<string>();
        if (offset.HasValue || limit.HasValue)
        {
            requestUrls.Add(requestUrlBase +
                            $"?offset={offset ?? 0}&limit={limit ?? _dremioSettings.NumberOfResultsPerAPIRequest}");
        }
        else
        {
            int currentOffset = 0;
            while (currentOffset < jobState.RowCount)
            {
                requestUrls.Add(requestUrlBase +
                                $"?offset={currentOffset}&limit={_dremioSettings.NumberOfResultsPerAPIRequest}");
                currentOffset += _dremioSettings.NumberOfResultsPerAPIRequest;
            }
        }

        var maxRequestGate =
            new SemaphoreSlim(_dremioSettings.ConcurentAPIRequests, _dremioSettings.ConcurentAPIRequests);

        var resultRequestTasks = requestUrls.Select(r => GetResultForRequestUrl<T>(query, r, maxRequestGate)).ToArray();

        await Task.WhenAll(resultRequestTasks);

        var result = new DremioJobResultModel<T>
        {
            RowCount = jobState.RowCount,
            Rows = resultRequestTasks.SelectMany(task => task.Result.Rows).ToList()
        };

        return result;
    }

    private async Task<DremioJobResultModel<T>> GetResultForRequestUrl<T>(string query, string requestUrl,
        SemaphoreSlim gate = null)
    {
        try
        {
            if (gate != null) await gate.WaitAsync();

            var response = await _httpRequestPolicy.ExecuteAsync(() =>
                SendDremioRequest(HttpMethod.Get, requestUrl, true, timeout: _dremioSettings.TimeoutOfAPIRequests));
            return await response.Content.ReadFromJsonAsync<DremioJobResultModel<T>>(SerializerOptions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex.Message);
            return new DremioJobResultModel<T>();
        }
        finally
        {
            gate?.Release();
        }
    }

    public async Task<DremioJobResultModel<T>> RunQuery<T>(string query, int? offset = null, int? limit = null,
        CancellationToken cancellationToken = default)
    {
        var jobId = await CreateJobByQuery(query, cancellationToken);

        var waitAndRetryPolicy = Policy
            .HandleResult<DremioJobStateModel>(r =>
                r.JobState != DremioJobState.Completed && r.JobState != DremioJobState.Failed)
            .WaitAndRetryForeverAsync(_ => TimeSpan.FromSeconds(1));

        //We have a 30 second timeout for API Gateway.
        var timeoutPolicy = Policy.TimeoutAsync(TimeSpan.FromSeconds(30));

        var waitAndRetryWithTimeoutPolicy = timeoutPolicy.WrapAsync(waitAndRetryPolicy);

        var policyResult =
            await waitAndRetryWithTimeoutPolicy.ExecuteAndCaptureAsync(async ct => await GetJobState(jobId, ct),
                cancellationToken);

        var jobStateModel = policyResult.Result;

        if (cancellationToken.IsCancellationRequested && jobStateModel?.JobState != DremioJobState.Failed)
        {
            await CancelQuery(jobId);

            throw new TaskCanceledException($"Dremio query cancelled. JobId: {jobId}");
        }

        if (jobStateModel?.JobState != DremioJobState.Completed)
        {
            throw new Exception($"Dremio query failed. Error: {jobStateModel?.ErrorMessage}");
        }

        var jobResult = await GetJobResult<T>(query, jobId, jobStateModel, offset, limit);

        return jobResult;
    }

    private async Task CancelQuery(string jobId)
    {
        var requestUrl = $"/api/v3/job/{jobId}/cancel";

        await SendDremioRequest(HttpMethod.Post, requestUrl, true);
    }
}