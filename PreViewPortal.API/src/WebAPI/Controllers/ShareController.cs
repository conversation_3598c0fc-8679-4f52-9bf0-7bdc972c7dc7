using System;
using System.Threading.Tasks;
using Application.Share.Commands;
using Application.Share.Queries;
using Microsoft.AspNetCore.Mvc;

namespace WebAPI.Controllers
{
    public class ShareController : ApiControllerBase
    {
        [HttpPost("createShareKey")]
        public async Task<ActionResult<ShareKeyVm>> CreateShareKey([FromBody] CreateShareKeyCommand command)
        {
            command.AccountId = CurrentUserService.Account.Value;
            var shareKeyVm = await Mediator.Send(command);
            return shareKeyVm;
        }

        [HttpPost("revertShareKey")]
        public async Task<ActionResult<string>> RevertShareKey([FromBody] string shareKey)
        {
            return await Mediator.Send(new RevertShareKeyCommand
            {
                AccountId = CurrentUserService.Account.Value,
                ShareKey = shareKey
            });
        }
    }
}