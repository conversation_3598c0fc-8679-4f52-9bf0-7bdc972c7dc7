using System.Threading;
using System.Threading.Tasks;
using Application.Caching.Commands;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace WebAPI.Controllers
{
    [AllowAnonymous]
    public class CacheController : ApiControllerBase
    {
        private readonly ILogger<CacheController> _logger;
        public CacheController(ILogger<CacheController> logger) => this._logger = logger;

        [HttpGet("flush")]
        public async Task Flush(CancellationToken cancellationToken)
        {
            _logger.LogInformation("cache Flush(invalidate) was requested");
            await Mediator.Send(new FlushCacheCommand(), cancellationToken);
        }
    }
}
