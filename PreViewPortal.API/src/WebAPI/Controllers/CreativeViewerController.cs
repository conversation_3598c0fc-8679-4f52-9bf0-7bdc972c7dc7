using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Application.CreativeViewer.Queries;
using Application.Share.Queries;
using BusinessLayer.Model;
using BusinessLayer.Model.Request;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace WebAPI.Controllers
{
    public class CreativeViewerController : ApiControllerBase
    {
        [HttpPost("incontext")]
        public async Task<ActionResult<CreativeViewerVm>> GetInContext([FromBody] List<MediaModelRequest> media,
            CancellationToken cancellationToken)
        {
            var shareKeyTask = Mediator.Send(
                new GetShareKeyQuery { AccountId = CurrentUserService.Account.Value, Media = media },
                cancellationToken);

            var creativeViewerTask = Mediator.Send(
                new GetCreativeViewerInContextQuery
                    { AccountId = CurrentUserService.Account.Value, Media = media },
                cancellationToken);

            await Task.WhenAll(shareKeyTask, creativeViewerTask).ConfigureAwait(false);

            var shareKeyVm = await shareKeyTask.ConfigureAwait(false);
            var creativeViewerVm = await creativeViewerTask.ConfigureAwait(false);

            creativeViewerVm.ShareKey = shareKeyVm.ShareKey;

            return creativeViewerVm;
        }

        [HttpPost("forcedexposure")]
        public async Task<ActionResult<CreativeViewerVm>> GetForcedExposure([FromBody] List<MediaModelRequest> media,
            CancellationToken cancellationToken)
        {
            var shareKeyTask = Mediator.Send(
                new GetShareKeyQuery { AccountId = CurrentUserService.Account.Value, Media = media },
                cancellationToken);

            var creativeViewerTask = Mediator.Send(
                new GetCreativeViewerForcedExposureQuery
                    { AccountId = CurrentUserService.Account.Value, Media = media },
                cancellationToken);

            await Task.WhenAll(shareKeyTask, creativeViewerTask).ConfigureAwait(false);

            var shareKeyVm = await shareKeyTask.ConfigureAwait(false);
            var creativeViewerVm = await creativeViewerTask.ConfigureAwait(false);

            creativeViewerVm.ShareKey = shareKeyVm.ShareKey;

            return creativeViewerVm;
        }

        [AllowAnonymous]
        [HttpGet("creativeViewerByShareKeyHash")]
        public async Task<ActionResult<CreativeViewerVm>> GetCreativeViewerByShareHash(
            [FromHeader(Name = "X-Share-Key")] string shareKey,
            CancellationToken cancellationToken)
        {
            var shareCreatives = await Mediator.Send(new GetShareCreativesQuery
            {
                ShareKey = shareKey
            }, cancellationToken);

            var creativeViewerVm = shareCreatives.ProductType switch
            {
                ProductType.InContext => await Mediator.Send(
                    new GetCreativeViewerInContextQuery
                    {
                        AccountId = shareCreatives.AccountId,
                        Media = shareCreatives.SelectedCreatives
                    }, cancellationToken),
                ProductType.NewForcedExposure => await Mediator.Send(
                    new GetCreativeViewerForcedExposureQuery
                    {
                        AccountId = shareCreatives.AccountId,
                        ShareKey = shareKey,
                        Media = shareCreatives.SelectedCreatives
                    }, cancellationToken),
                _ => throw new ArgumentOutOfRangeException()
            };

            creativeViewerVm.Media = shareCreatives.SelectedCreatives;
            creativeViewerVm.SelectedProduct = shareCreatives.ProductType;
            creativeViewerVm.ShareKey = shareCreatives.ShareKey;
            return creativeViewerVm;
        }
    }
}