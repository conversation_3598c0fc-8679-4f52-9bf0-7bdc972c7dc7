using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Application.CreativePerformanceForcedExposure;
using Application.MediaSegment.Queries;
using Application.Share.Queries;
using BusinessLayer.Model;
using BusinessLayer.Model.Response;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace WebAPI.Controllers
{
    public class CreativePerformanceForcedExposureController : ApiControllerBase
    {
        [HttpGet("grid")]
        public async Task<ActionResult<CreativePerformanceForcedExposurePageDto>> GetGrid(
            [FromQuery] GetCreativePerformanceForcedExposureGridQuery query, CancellationToken cancellationToken)
        {
            query.AccountId = CurrentUserService.Account.Value;
            query.IsAdmin = CurrentUserService.IsAdmin;
            return await Mediator.Send(query, cancellationToken);
        }

        [HttpGet("mediaSegments")]
        public async Task<ActionResult<List<SegmentKeyLabel>>> GetGrid(CancellationToken cancellationToken)
        {
            return await Mediator.Send(new GetMediaAccountSegmentQuery { AccountId = CurrentUserService.Account.Value, ProductType = ProductType.NewForcedExposure }, cancellationToken);
        }

        [AllowAnonymous]
        [HttpGet("export")]
        public async Task<ExportResult> GetExport(
            [FromHeader(Name = "X-Share-Key")] string shareKey,
            [FromQuery] GetCreativePerformanceForcedExposureExcelQuery query, CancellationToken cancellationToken)
        {
            try
            {
                if (CurrentUserService.Account == null)
                {
                    var shareCreatives = await Mediator.Send(new GetShareCreativesQuery
                    {
                        ShareKey = shareKey
                    }, cancellationToken);

                    query.AccountId = shareCreatives.AccountId;
                }
                else
                {
                    query.AccountId = CurrentUserService.Account.Value;
                }

                var result = await Mediator.Send(query, cancellationToken);

                return result;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }
    }
}