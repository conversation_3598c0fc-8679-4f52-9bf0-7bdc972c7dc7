using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Application.Account.Queries;
using Application.User.Commands;
using Application.User.Queries;
using BusinessLayer.Model.Response;
using Domain.Enums;
using Microsoft.AspNetCore.Mvc;

namespace WebAPI.Controllers
{
    public class UserController : ApiControllerBase
    {
        [HttpGet("current")]
        public async Task<ActionResult<CurrentUserInfoDto>> GetCurrentUser()
        {
            var userInfo = await Mediator.Send(new GetCurrentUserQuery());
            return userInfo;
        }

        [HttpGet("account")]
        public async Task<ActionResult<List<AccountProduct>>> GetAccounts(CancellationToken cancellationToken)
        {
            var query = new GetAccountProductModelsQuery
            {
                IsAdmin = CurrentUserService.IsAdmin,
                AccountIds = CurrentUserService.IsAdmin ? new List<int>() : CurrentUserService.Accounts,
            };

            return await Mediator.Send(query, cancellationToken);
        }

        [HttpGet("account/{accountId}")]
        public async Task<ActionResult<AccountProduct>> GetAccount(int accountId, CancellationToken cancellationToken)
        {
            var accounts = await Mediator.Send(new GetAccountProductModelsQuery { IsAdmin = CurrentUserService.IsAdmin, AccountIds = new List<int> { accountId } },
                cancellationToken);

            return accounts.Single();
        }

        [HttpGet("gridSettings")]
        public async Task<ActionResult<List<User_PreViewGridSettingsDto>>> GetGridSettings(CancellationToken cancellationToken)
        {
            return await Mediator.Send(new GetUser_PreViewGridSettingsListQuery { UserId = CurrentUserService.Id }, cancellationToken);
        }

        [HttpPut("gridSettings")]
        public async Task<ActionResult> CreateOrUpdateGridSettings([FromBody] CreateOrUpdateUser_PreViewGridSettingsCommand command)
        {
            command.UserId = CurrentUserService.Id;

            await Mediator.Send(command);

            return NoContent();
        }

        [HttpDelete("gridSettings/{gridId}")]
        public async Task<ActionResult> DeleteGridSettings(PreviewGrid gridId)
        {
            await Mediator.Send(new DeleteUser_PreViewGridSettingsCommand { UserId = CurrentUserService.Id, GridId = gridId });

            return NoContent();
        }
    }
}
