using System.Threading;
using System.Threading.Tasks;
using Application.Export;
using BusinessLayer.Model.Response;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace WebAPI.Controllers;

public class ExportController : ApiControllerBase
{
    [AllowAnonymous]
    [HttpGet("export-status")]
    public async Task<ExportResult> GetExportStatus([FromQuery] GetExportStatusQuery query,
        CancellationToken cancellationToken)
    {
        return await Mediator.Send(query, cancellationToken);
    }
}