using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Application.CreativePerformanceInContext.Queries;
using Application.MediaSegment.Queries;
using Application.Share.Queries;
using BusinessLayer.Model.Response;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using BusinessLayer.Model;

namespace WebAPI.Controllers
{
    public class CreativePerformanceInContextController : ApiControllerBase
    {
        [HttpGet("grid")]
        public async Task<ActionResult<CreativePerformanceInContextPageDto>> GetGrid(
            [FromQuery] GetCreativePerformanceInContextGridQuery query, CancellationToken cancellationToken)
        {
            query.AccountId = CurrentUserService.Account.Value;
            query.IsAdmin = CurrentUserService.IsAdmin;
            return await Mediator.Send(query, cancellationToken);
        }

        [HttpGet("mediaSegments")]
        public async Task<ActionResult<List<SegmentKeyLabel>>> GetGrid(CancellationToken cancellationToken)
        {
            return await Mediator.Send(new GetMediaAccountSegmentQuery { AccountId = CurrentUserService.Account.Value, ProductType = ProductType.InContext }, cancellationToken);
        }

        [AllowAnonymous]
        [HttpGet("export")]
        public async Task<ExportResult> GetExport(
            [FromHeader(Name = "X-Share-Key")] string shareKey,
            [FromQuery] GetCreativePerformanceInContextExcelQuery query, CancellationToken cancellationToken)
        {
            if (CurrentUserService.Account == null)
            {
                var shareCreatives = await Mediator.Send(new GetShareCreativesQuery
                {
                    ShareKey = shareKey
                }, cancellationToken);

                query.AccountId = shareCreatives.AccountId;
            }
            else
            {
                query.AccountId = CurrentUserService.Account.Value;
            }

            return await Mediator.Send(query, cancellationToken);
        }
    }
}