using System;
using System.Threading;
using System.Threading.Tasks;
using Amazon.Runtime.Internal.Util;
using Application.Caching.Commands;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace WebAPI.Controllers
{
    [AllowAnonymous]
    public class LogController : ApiControllerBase
    {

        private readonly ILogger<LogController> _logger;
        public LogController(ILogger<LogController> logger) => _logger = logger;

        [HttpGet("info")]
        public async Task Info(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Ping was called");
            await Task.CompletedTask;
        }

        [HttpGet("error")]
        public async Task Error(string message,CancellationToken cancellationToken)
        {
            _logger.LogError(new Exception(message), message);
            await Task.CompletedTask;
        }
    }
}
