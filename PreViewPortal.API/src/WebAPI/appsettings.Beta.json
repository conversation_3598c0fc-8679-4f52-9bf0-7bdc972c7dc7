{"Lambda.Logging": {"IncludeException": true}, "Logging": {"LogLevel": {"System": "Error", "Microsoft": "Error"}}, "ConnectionStrings": {"StudyDatabase": "${ssm:/live/databases/common/connectionString}"}, "ApiAuthorization": {"InternalApiSecret": "${ssm:/live/site/preview/internalApiSecret}"}, "RealeyesJwtAuthentication": {"ValidIssuer": "${ssm:/preview/iam/eu-west-1/live/backend/issuer}", "ValidAudiences": ["${ssm:/preview/iam/eu-west-1/live/backend/audience}", "${ssm:/preview/iam/eu-west-1/beta/backend/audience}"]}, "Dremio": {"BaseUrl": "${ssm:/live/administration/dremio/preview/baseUrl}", "Username": "${ssm:/live/administration/dremio/preview/username}", "Password": "${ssm:/live/administration/dremio/preview/password}", "ConcurentAPIRequests": 5, "NumberOfResultsPerAPIRequest": 500, "TimeoutOfAPIRequests": 10}, "ThumbnailBucket": {"BucketName": "delivery-thumbnails", "CloudFrontDomainName": "https://d1kwsvfr3z6fdm.cloudfront.net"}, "SourceMediaDataBucket": {"BucketName": "sourcedatabucket", "CloudFrontDomainName": "dwc110u9h6nl2.cloudfront.net"}, "Quality": {"MinViewingsThreshold": 30}, "Export": {"ExportBucket": "beta-preview-portal-export-bucket"}}