using Application.Common.Interfaces;
using Application.Common.Models.Settings;
using Application.Common.Security;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using System.IO;
using Application;
using Application.Export;
using BusinessLayer.Configuration;
using DataLayer.Configuration;
using Infrastructure;
using WebAPI.Filters;
using WebAPI.Services;
using Realeyesit.Extensions.Configuration;
using Realeyes.PreView.Infrastructure.Services.Dremio.Configuration;

var builder = WebApplication.CreateBuilder(new WebApplicationOptions
{
    Args = args, ContentRootPath = Directory.GetCurrentDirectory()
});

var env = builder.Environment;

builder.Configuration
    .SetBasePath(env.ContentRootPath)
    .AddJsonFile("appsettings.json")
    .AddJsonFile($"appsettings.{env.EnvironmentName}.json")
    .AddEnvironmentVariables()
    .Build();

// Note: we cast builder.Configuration to IConfiguration variable to be able to optionally call ResolveSecrets on it and pass it to Service below
// so WebApplication builder will use original(not resolved secrets) configuration manager inside, while our application code will use copy of its resolved configuration with secrets
IConfiguration configuration = builder.Configuration;

var shouldResolveSecrets = configuration.GetValue<bool>("Startup:ShouldResolveSecrets");
if (shouldResolveSecrets)
{
    configuration = configuration.ResolveSecrets(new ConfigurationSecretResolverSettings { ShouldThrow = true });
}

// Add services to the container.

builder.Services.AddResponseCompression(options =>
{
    options.EnableForHttps = true;
});


builder.Services.AddControllers(options =>
{
    options.Filters.Add<ApiExceptionFilterAttribute>();
});

builder.Services.AddEndpointsApiExplorer();
builder.Services.AddAWSLambdaHosting(LambdaEventSource.RestApi);
builder.Services.AddApplication();
builder.Services.AddInfrastructure(configuration);

builder.Services.AddHttpContextAccessor();
builder.Services.AddSingleton<ICurrentUserService, CurrentUserService>();
builder.Services.AddScoped<IShareService, ShareService>();

builder.Services.Configure<RealeyesJwtAuthenticationSettings>(configuration.GetSection(RealeyesJwtAuthenticationDefaults.ConfigSectionName));
builder.Services.AddDremioSettingsFrom(configuration);
builder.Services.AddBusinessConfiguration();
builder.Services.AddDataConfiguration(configuration);
builder.Services.Configure<ThumbnailBucketSettings>(configuration.GetSection("ThumbnailBucket"));
builder.Services.Configure<SourceMediaDataBucketSettings>(configuration.GetSection("SourceMediaDataBucket"));
builder.Services.Configure<ApiAuthorizationSettings>(configuration.GetSection("ApiAuthorization"));
builder.Services.Configure<QualitySettings>(configuration.GetSection("Quality"));


var app = builder.Build();

if (env.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
}

app.UseResponseCompression();

app.UseHttpsRedirection();

app.UseRouting();

app.UseAuthentication();

app.UseAuthorization();

app.UseGuestMiddleware();

app.UseEndpoints(endpoints =>
{
    endpoints.MapControllers();
});

app.Run();
