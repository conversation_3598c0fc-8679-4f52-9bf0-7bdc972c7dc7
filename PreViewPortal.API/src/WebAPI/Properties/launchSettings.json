{"profiles": {"Staging": {"commandName": "Project", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Staging", "ASPNETCORE_Kestrel__Certificates__Default__Path": "../../../.cert/local.realeyesit.com.pfx", "ASPNETCORE_Kestrel__Certificates__Default__Password": "4f16c5754f04466faffba346bc15e720", "LOGGING__LOGLEVEL__DEFAULT": "Debug", "LOGGING__LOGLEVEL__SYSTEM": "Information", "LOGGING__LOGLEVEL__MICROSOFT": "Information", "STARTUP__SHOULDRESOLVESECRETS": "true"}, "applicationUrl": "https://local.realeyesit.com:5003"}, "Production": {"commandName": "Project", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Production", "ASPNETCORE_Kestrel__Certificates__Default__Path": "../../../.cert/local.realeyesit.com.pfx", "ASPNETCORE_Kestrel__Certificates__Default__Password": "4f16c5754f04466faffba346bc15e720", "LOGGING__LOGLEVEL__DEFAULT": "Debug", "LOGGING__LOGLEVEL__SYSTEM": "Information", "LOGGING__LOGLEVEL__MICROSOFT": "Information", "STARTUP__SHOULDRESOLVESECRETS": "true"}, "applicationUrl": "https://local.realeyesit.com:5003"}, "Mock Lambda Test Tool": {"commandName": "Executable", "executablePath": "%USERPROFILE%\\.dotnet\\tools\\dotnet-lambda-test-tool-8.0.exe", "commandLineArgs": "--port 5050", "workingDirectory": ".\\bin\\$(Configuration)\\net8.0"}, "Development": {"commandName": "Project", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_Kestrel__Certificates__Default__Path": "../../../.cert/local.realeyesit.com.pfx", "ASPNETCORE_Kestrel__Certificates__Default__Password": "4f16c5754f04466faffba346bc15e720", "LOGGING__LOGLEVEL__DEFAULT": "Debug", "LOGGING__LOGLEVEL__SYSTEM": "Information", "LOGGING__LOGLEVEL__MICROSOFT": "Information", "STARTUP__SHOULDRESOLVESECRETS": "true"}, "applicationUrl": "https://local.realeyesit.com:5003"}}}