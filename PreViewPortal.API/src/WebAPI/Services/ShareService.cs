using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using Application.Common.Interfaces;
using BusinessLayer.Model.Request;
using Microsoft.EntityFrameworkCore;

namespace WebAPI.Services
{
    public class ShareService : IShareService
    {
        private readonly IApplicationDbContext _context;

        public ShareService(IApplicationDbContext context)
        {
            _context = context;
        }

        public string GenerateUniqueShareKey(IEnumerable<MediaModelRequest> mediaItems)
        {
            // Order by IDs to ensure consistent hashing
            var orderedItems = mediaItems.OrderBy(m => m.TestID)
                .ThenBy(m => m.SourceMediaID)
                .ThenBy(m => m.TaskID)
                .ToList();

            var combinedIds = string.Join("", orderedItems.Select(m => $"{m.TestID}{m.SourceMediaID}{m.TaskID}"));

            using var sha256 = SHA256.Create();
            var inputBytes = Encoding.UTF8.GetBytes(combinedIds);
            var hashBytes = sha256.ComputeHash(inputBytes);

            var hexHash = BitConverter.ToString(hashBytes).Replace("-", "").ToLower();
            return hexHash.Substring(0, Math.Min(64, hexHash.Length));
        }

        public async Task<bool> IsValidShareKey(string shareKey)
        {
            var entity = await _context.ShareKey.SingleOrDefaultAsync(sk => sk.HashKey == shareKey);

            return entity != null;
        }
    }
}