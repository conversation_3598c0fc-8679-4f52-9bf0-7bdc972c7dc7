using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using Application.Common.Interfaces;
using Application.Common.Security;
using Microsoft.AspNetCore.Http;

namespace WebAPI.Services
{
    public class CurrentUserService : ICurrentUserService
    {
        private const string SupervisedAccountHeaderKey = "RE-SUPERVISED-ACCOUNT";
        private const string ApiSecretHeaderKey = "RE-API-SECRET";

        private readonly IHttpContextAccessor _httpContextAccessor;

        public ClaimsPrincipal User => _httpContextAccessor.HttpContext.User ?? throw new UnauthorizedAccessException();

        public int Id => int.Parse(User.FindFirstValue(RealeyesJwtClaimTypes.UserIdentifier));
        public string Email => User.FindFirstValue(ClaimTypes.Email);
        public string Name => User.FindFirstValue(ClaimTypes.Name);
        public List<string> Roles => User.FindAll(ClaimTypes.Role).Select(c => c.Value).ToList();
        public int? Account
        {
            get
            {
                var onlyHasOneAccount = !IsAdmin && Accounts.Count == 1;
                if (onlyHasOneAccount)
                {
                    return Accounts.Single();
                }

                int? supervisedAccountId = null;

                if (_httpContextAccessor.HttpContext.Request.Headers.TryGetValue(SupervisedAccountHeaderKey, out var accounts) && int.TryParse(accounts.Single(), out var accountIdFromHeader))
                {
                    supervisedAccountId = accountIdFromHeader;
                }

                if (!supervisedAccountId.HasValue)
                {
                    return null;
                }

                var hasAccessToSupervisedAccount = IsAdmin || Accounts.Contains(supervisedAccountId.Value);
                if (hasAccessToSupervisedAccount)
                {
                    return supervisedAccountId.Value;
                }

                return null;
            }
        }
        public List<int> Accounts => User.FindAll(RealeyesJwtClaimTypes.Account).SelectMany(c => c.Value.Split(',')).Select(int.Parse).ToList();
        public bool IsAdmin => Roles.Contains(RealeyesJwtRoles.Administrator);
        public bool IsValidationNeeded => !IsAdmin && !IsGuest;
        public bool HasAccess => User.HasClaim(RealeyesJwtClaimTypes.Permission, RealeyesJwtPermissions.PreViewPortal);
        public string InternalApiSecret => _httpContextAccessor.HttpContext.Request.Headers.TryGetValue(ApiSecretHeaderKey, out var secrets) ? secrets.Single() : null;
        private bool IsGuest => Roles.Contains(RealeyesJwtRoles.Guest);


        public CurrentUserService(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }
    }
}