{"Lambda.Logging": {"IncludeException": true}, "Logging": {"LogLevel": {"System": "Error", "Microsoft": "Error"}}, "ConnectionStrings": {"StudyDatabase": "${ssm:/stage/databases/common/connectionString}"}, "ApiAuthorization": {"InternalApiSecret": "${ssm:/live/site/preview/internalApiSecret}"}, "RealeyesJwtAuthentication": {"ValidIssuer": "${ssm:/preview/iam/eu-west-1/dev/backend/issuer}", "ValidAudiences": ["${ssm:/preview/iam/eu-west-1/dev/backend/audience}"]}, "Dremio": {"BaseUrl": "${ssm:/stage/administration/dremio/preview/baseUrl}", "Username": "${ssm:/stage/administration/dremio/preview/username}", "Password": "${ssm:/stage/administration/dremio/preview/password}", "ConcurentAPIRequests": 5, "NumberOfResultsPerAPIRequest": 500, "TimeoutOfAPIRequests": 10}, "ThumbnailBucket": {"BucketName": "stagedelivery-thumbnails", "CloudFrontDomainName": "https://d38swynar38jv6.cloudfront.net"}, "SourceMediaDataBucket": {"BucketName": "stagesourcedatabucket", "CloudFrontDomainName": "d2ia5kfyvawmyt.cloudfront.net"}, "Quality": {"MinViewingsThreshold": 10}, "Export": {"ExportBucket": "stage-preview-portal-export-bucket"}}