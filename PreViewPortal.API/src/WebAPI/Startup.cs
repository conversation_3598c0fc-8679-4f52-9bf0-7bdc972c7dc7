using Application;
using Application.Common.Interfaces;
using Application.Common.Models.Settings;
using Application.Common.Security;
using BusinessLayer.Configuration;
using DataLayer.Configuration;
using Infrastructure;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Realeyes.PreView.Infrastructure.Services.Dremio.Configuration;
using Realeyesit.Extensions.Configuration;
using WebAPI.Filters;
using WebAPI.Services;

namespace WebAPI
{
    public class Startup
    {
        public IConfiguration Configuration;
        public IWebHostEnvironment Environment;

        public Startup(IWebHostEnvironment env)
        {
            Environment = env;

            Configuration = new ConfigurationBuilder()
                .SetBasePath(env.ContentRootPath)
                .AddJsonFile("appsettings.json")
                .AddJsonFile($"appsettings.{env.EnvironmentName}.json")
                .AddEnvironmentVariables()
                .Build();

            var shouldResolveSecrets = Configuration.GetValue<bool>("Startup:ShouldResolveSecrets");
            if (shouldResolveSecrets)
            {
                Configuration = Configuration.ResolveSecrets(new ConfigurationSecretResolverSettings
                    { ShouldThrow = true });
            }
        }

        public void ConfigureServices(IServiceCollection services)
        {
            services.AddControllers(options => { options.Filters.Add<ApiExceptionFilterAttribute>(); });

            services.AddApplication();

            services.AddInfrastructure(Configuration);

            services.AddHttpContextAccessor();
            services.AddSingleton<ICurrentUserService, CurrentUserService>();

            services.Configure<RealeyesJwtAuthenticationSettings>(
                Configuration.GetSection(RealeyesJwtAuthenticationDefaults.ConfigSectionName));
            services.AddDremioSettingsFrom(Configuration);
            services.AddBusinessConfiguration();
            services.AddDataConfiguration(Configuration);
            services.Configure<ThumbnailBucketSettings>(Configuration.GetSection("ThumbnailBucket"));
            services.Configure<SourceMediaDataBucketSettings>(Configuration.GetSection("SourceMediaDataBucket"));
            services.Configure<ApiAuthorizationSettings>(Configuration.GetSection("ApiAuthorization"));
            services.Configure<QualitySettings>(Configuration.GetSection("Quality"));
        }

        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }

            app.UseHttpsRedirection();

            app.UseRouting();

            app.UseAuthentication();

            app.UseAuthorization();

            app.UseGuestMiddleware();

            app.UseEndpoints(endpoints => { endpoints.MapControllers(); });
        }
    }
}