using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Application.Account.Queries;
using Application.Common.Behaviors;
using Application.Common.Exceptions;
using Application.Common.Interfaces;
using Application.Common.Security;
using Application.MediaSegment.Queries;
using BusinessLayer.Model.Response;
using Microsoft.AspNetCore.Authorization;
using Moq;
using NUnit.Framework;

namespace Application.Tests.Common.Behaviors
{
    public class AuthorizationByBehaviourTests
    {
        [Test]
        public async Task TestByAccount()
        {
            var currentUserServiceMock = new Mock<ICurrentUserService>();
            var authorizationServiceMock = new Mock<IAuthorizationService>();
            var behavior = new AuthorizationByBehavior<GetMediaAccountSegmentQuery, List<SegmentKeyLabel>>(
                new List<IAuthorizeByProperty>
                {
                    new AuthorizeByAccount(currentUserServiceMock.Object)
                }, currentUserServiceMock.Object);

            // Policy is not set here on request model, but roles are
            var request = new GetMediaAccountSegmentQuery
            {
                AccountId = 1
            };
            currentUserServiceMock.Setup(a => a.IsValidationNeeded).Returns(true);
            currentUserServiceMock.Setup(a => a.Accounts).Returns(new List<int> { });

            // No accounts
            Assert.ThrowsAsync<ForbiddenAccessException>((async () =>
            {
                await behavior.Handle(request, () => Task.FromResult<List<SegmentKeyLabel>>(null),
                    CancellationToken.None);
            }));

            currentUserServiceMock.Setup(a => a.Accounts).Returns(new List<int> { 2 });
            // wrong account
            Assert.ThrowsAsync<ForbiddenAccessException>((async () =>
            {
                await behavior.Handle(request, () => Task.FromResult<List<SegmentKeyLabel>>(null),
                    CancellationToken.None);
            }));

            currentUserServiceMock.Setup(a => a.Accounts).Returns(new List<int> { 1 });
            // correct account
            Assert.DoesNotThrowAsync((async () =>
            {
                await behavior.Handle(request, () => Task.FromResult<List<SegmentKeyLabel>>(null),
                    CancellationToken.None);
            }));

            currentUserServiceMock.Setup(a => a.Accounts).Returns(new List<int> { });
            currentUserServiceMock.Setup(a => a.IsAdmin).Returns(true);
            // no account but Admin
            Assert.DoesNotThrowAsync((async () =>
            {
                await behavior.Handle(request, () => Task.FromResult<List<SegmentKeyLabel>>(null),
                    CancellationToken.None);
            }));
        }


        [Test]
        public async Task TestByAccountList()
        {
            var currentUserServiceMock = new Mock<ICurrentUserService>();
            var authorizationServiceMock = new Mock<IAuthorizationService>();
            var behavior = new AuthorizationByBehavior<GetAccountProductModelsQuery, List<AccountProduct>>(
                new List<IAuthorizeByProperty>
                {
                    new AuthorizeByAccount(currentUserServiceMock.Object)
                }, currentUserServiceMock.Object);

            // Policy is not set here on request model, but roles are
            var request = new GetAccountProductModelsQuery()
            {
                AccountIds = new List<int> { 1, 2 }
            };
            currentUserServiceMock.Setup(a => a.IsValidationNeeded).Returns(true);
            currentUserServiceMock.Setup(a => a.Accounts).Returns(new List<int> { });

            // No accounts
            Assert.ThrowsAsync<ForbiddenAccessException>((async () =>
            {
                await behavior.Handle(request, () => Task.FromResult<List<AccountProduct>>(null),
                    CancellationToken.None);
            }));

            currentUserServiceMock.Setup(a => a.Accounts).Returns(new List<int> { 2 });
            // non full accounts
            Assert.ThrowsAsync<ForbiddenAccessException>((async () =>
            {
                await behavior.Handle(request, () => Task.FromResult<List<AccountProduct>>(null),
                    CancellationToken.None);
            }));

            currentUserServiceMock.Setup(a => a.Accounts).Returns(new List<int> { 1, 2 });
            // correct accounts
            Assert.DoesNotThrowAsync((async () =>
            {
                await behavior.Handle(request, () => Task.FromResult<List<AccountProduct>>(null),
                    CancellationToken.None);
            }));

            currentUserServiceMock.Setup(a => a.Accounts).Returns(new List<int> { });
            currentUserServiceMock.Setup(a => a.IsAdmin).Returns(true);
            // no account but Admin
            Assert.DoesNotThrowAsync((async () =>
            {
                await behavior.Handle(request, () => Task.FromResult<List<AccountProduct>>(null),
                    CancellationToken.None);
            }));
        }
    }
}