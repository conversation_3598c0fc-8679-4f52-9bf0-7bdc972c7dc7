using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using Application.Caching.Commands;
using Application.Common.Behaviors;
using Application.Common.Exceptions;
using Application.Common.Interfaces;
using Application.Common.Security;
using Application.CreativePerformanceInContext.Queries;
using Application.Tests.Helpers;
using BusinessLayer.Model.Response;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Moq;
using NUnit.Framework;

namespace Application.Tests.Common.Behaviors
{
    public class AuthorizationBehaviourTests
    {

        [Test]
        public async Task TestPolicy()
        {
            var currentUserServiceMock = new Mock<ICurrentUserService>();
            var authorizationServiceMock = new Mock<IAuthorizationService>();
            var behavior = new AuthorizationBehaviour<GetCreativePerformanceInContextQuery, List<InContextCreative>>(currentUserServiceMock.Object,
                authorizationServiceMock.Object, MockHelper.ApiAuthorizationSettings("secret"));


            var request = new GetCreativePerformanceInContextQuery
            {
                AccountId = 1
            };

            currentUserServiceMock.Setup(a => a.IsValidationNeeded).Returns(true);
            // Policy returns success
            authorizationServiceMock.Setup(a => a.AuthorizeAsync(It.IsAny<ClaimsPrincipal>(), null, It.IsAny<string>()))
                .Returns(Task.FromResult(AuthorizationResult.Success()));

            Assert.DoesNotThrowAsync((async () =>
            {
                await behavior.Handle(request, () => Task.FromResult<List<InContextCreative>>(null), CancellationToken.None);
            }));

            authorizationServiceMock.Verify(a => a.AuthorizeAsync(It.IsAny<ClaimsPrincipal>(), null, Policies.HasGuestAccess), Times.Once);


            // Policy returns fail
            authorizationServiceMock.Setup(a => a.AuthorizeAsync(It.IsAny<ClaimsPrincipal>(), null, It.IsAny<string>()))
                .Returns(Task.FromResult(AuthorizationResult.Failed()));

            Assert.ThrowsAsync<ForbiddenAccessException>((async () =>
            {
                await behavior.Handle(request, () => Task.FromResult<List<InContextCreative>>(null), CancellationToken.None);
            }));
        }

        [Test]
        public async Task TestApiKey()
        {
            var internalApiKey = "secret";
            var currentUserServiceMock = new Mock<ICurrentUserService>();
            var authorizationServiceMock = new Mock<IAuthorizationService>();
            var behavior = new AuthorizationBehaviour<FlushCacheCommand, Unit>(currentUserServiceMock.Object,
                authorizationServiceMock.Object, MockHelper.ApiAuthorizationSettings(internalApiKey));


            var request = new FlushCacheCommand()
            {
            };

            currentUserServiceMock.Setup(a => a.IsValidationNeeded).Returns(true);
            currentUserServiceMock.Setup(a => a.Roles).Returns(new List<string> {});
            currentUserServiceMock.Setup(a => a.InternalApiSecret).Returns(String.Empty);

            Assert.ThrowsAsync<ForbiddenAccessException>((async () =>
            {
                await behavior.Handle(request, () => Task.FromResult<Unit>(default(Unit)), CancellationToken.None);
            }));

            currentUserServiceMock.Setup(a => a.InternalApiSecret).Returns(internalApiKey);


            Assert.DoesNotThrowAsync((async () =>
            {
                await behavior.Handle(request, () => Task.FromResult<Unit>(default(Unit)), CancellationToken.None);
            }));

        }
    }
}
