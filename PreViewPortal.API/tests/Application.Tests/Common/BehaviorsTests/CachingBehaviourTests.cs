using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Application.CreativePerformanceInContext.Queries;
using Application.Tests.Helpers;
using BusinessLayer.Model.Response;
using NUnit.Framework;

namespace Application.Tests.Common.Behaviors
{
    public class CachingBehaviourTests
    {
        [Test]
        public async Task TestCaching()
        {
            var behavior = MockHelper.CreateCachingBehavior<GetCreativePerformanceInContextQuery, List<InContextCreative>>();

            var request = new GetCreativePerformanceInContextQuery { AccountId = 1 };

            int called = 0;
            var mockedResult = new List<InContextCreative>();
            Task<List<InContextCreative>> Next()
            {
                called++;
                return Task.FromResult<List<InContextCreative>>(mockedResult);
            }

            var result = await behavior.Handle(request, Next, CancellationToken.None);
            Assert.AreEqual(mockedResult, result); 
            Assert.AreEqual(1, called);

            result = await behavior.Handle(request, Next, CancellationToken.None);
            // note in case of distributed cache(redis), references would be different.
            Assert.AreEqual(mockedResult, result);
            // cache used
            Assert.AreEqual(1, called);

            // change cached key
            request.AccountId = 2;
            result = await behavior.Handle(request, Next, CancellationToken.None);
            Assert.AreEqual(mockedResult, result);
            Assert.AreEqual(2, called);
        }
    }
}
