using System;
using System.ComponentModel.Design;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading;
using Application.Common.Attributes;
using Application.Common.Behaviors;
using Application.Common.Caching;
using Application.Common.Models.Settings;
using Application.CreativeViewer.Queries;
using Infrastructure.Services;
using MediatR;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.Extensions.Options;
using Moq;

namespace Application.Tests.Helpers
{
    public static class MockHelper
    {

        public static IOptions<TClass> MockOptions<TClass>(TClass options) where TClass : class
        {
            return Options.Create<TClass>(options);
        }

        public static IOptions<ThumbnailBucketSettings> ThumbnailBucketSettings(Action<ThumbnailBucketSettings> overwrite = null)
        {
            var settings = new ThumbnailBucketSettings
            {
                BucketName = "test-bucket",
                CloudFrontDomainName = "test.realeyesit.com"
            };
            overwrite?.Invoke(settings);
            return MockOptions(settings);
        }

        public static IOptions<SourceMediaDataBucketSettings> SourceMediaDataBucketSettings(Action<SourceMediaDataBucketSettings> overwrite = null)
        {
            var settings = new SourceMediaDataBucketSettings
            {
                BucketName = "test-bucket",
                CloudFrontDomainName = "test.realeyesit.com",
                KeyPairId = "AAAAAAAAAAAAAAAAAAAA", //"APKAJBXSOGBYX6SKWDDQ",
                PrivateKey = File.ReadAllText("Helpers/PrivateKey.txt")
            };
            overwrite?.Invoke(settings);
            return MockOptions(settings);
        }

        public static IOptions<QualitySettings> QualitySettings(int minThreshold = 10)
        {
            var settings = new QualitySettings
            {
                MinViewingsThreshold = minThreshold
            };
            return MockOptions(settings);
        }

        public static IOptions<ApiAuthorizationSettings> ApiAuthorizationSettings(string internalApiSecret = "secret")
        {
            var settings = new ApiAuthorizationSettings
            {
                InternalApiSecret = internalApiSecret
            };
            return MockOptions(settings);
        }

        public static void AddMediatorHandlerToMock<TRequest, TResponse>(Mock<IMediator> mock, IRequestHandler<TRequest, TResponse> handler,
            bool addCachingBehavior = true) where TRequest : class, IRequest<TResponse>
        {
            if (addCachingBehavior)
            {
                var cache = CreateCachingBehavior<TRequest, TResponse>();
                mock.Setup(m => m.Send(It.IsAny<TRequest>(), It.IsAny<CancellationToken>())).Returns<TRequest, CancellationToken>(
                    (r, cts) =>
                    {
                        return cache.Handle(r, () => handler.Handle(r, cts), cts);
                    });
            }
            else
            {
                mock.Setup(m => m.Send(It.IsAny<TRequest>(), It.IsAny<CancellationToken>())).Returns<TRequest, CancellationToken>(handler.Handle);
            }
        }

        public static CachingBehavior<TRequest, TResponse> CreateCachingBehavior<TRequest, TResponse>() where TRequest : class, IRequest<TResponse>
        {
            var provider = new ServiceContainer();
            AddCacheKeyGenerators(provider);
            var cacheService = new DistributedCacheService(new MemoryDistributedCache(Options.Create<MemoryDistributedCacheOptions>(new MemoryDistributedCacheOptions())),
                provider, new NullLogger<DistributedCacheService>());
            var caching = new CachingBehavior<TRequest, TResponse>(cacheService, provider);
            return caching;
        }

        private static void AddCacheKeyGenerators(ServiceContainer provider)
        {
            var queriesWithCacheAttribute = typeof(ICacheKeyGenerator<>).Assembly.ExportedTypes
                .Where(t => t.GetCustomAttribute<CacheQueryAttribute>() != null)
                .ToList();

            foreach (var queryWithCacheAttribute in queriesWithCacheAttribute)
            {
                var interfaceType = typeof(ICacheKeyGenerator<>).MakeGenericType(queryWithCacheAttribute);
                var implementationType = typeof(DefaultCacheKeyGenerator<>).MakeGenericType(queryWithCacheAttribute);

                provider.AddService(interfaceType, Activator.CreateInstance(implementationType));
            }

            provider.RemoveService(typeof(ICacheKeyGenerator<GetCreativeViewerInContextQuery>));
            provider.AddService(typeof(ICacheKeyGenerator<GetCreativeViewerInContextQuery>), new CreativeViewerInContextCacheKeyGenerator());
        }
    }
}
