using System.IO;
using Newtonsoft.Json;

namespace Application.Tests.Helpers
{
    public static class TestDataReader
    {
        private static FileStream GetTestData(string filePath)
        {
            filePath = $"./{filePath}";
            if (File.Exists(filePath))
            {
                return File.Open(filePath, FileMode.Open);
            }

            return null;
        }

        public static T GetJsonTestData<T>(string filePath)
        {
            using var stream = GetTestData(filePath);
            using var streamReader = new StreamReader(stream);
            using var jsonTextReader = new JsonTextReader(streamReader);
            var serializer = new JsonSerializer();
            return serializer.Deserialize<T>(jsonTextReader);
        }

    }
}
