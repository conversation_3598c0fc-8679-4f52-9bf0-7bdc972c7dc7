using System;
using Application.Common.Interfaces;
using Application.Common.Mappings;
using AutoMapper;
using Infrastructure.Persistence;
using Infrastructure.Services;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Moq;
using NUnit.Framework;

namespace Application.Tests.Base
{
    [TestFixture]
    public class BaseTestClass
    {
        private Lazy<StudyDbContext> _dbContextLazy;
        protected IApplicationDbContext DbContext => _dbContextLazy.Value;

        /// <summary>
        /// Publisher Mock to mock Mediator send/publisher behavior
        /// </summary>
        protected Mock<IMediator> MediatorMock;

        protected IMapper Mapper;

        [SetUp]
        public void Setup()
        {
            _dbContextLazy = new Lazy<StudyDbContext>(CreateInMemoryContext);
            MediatorMock = new Mock<IMediator>();
            Mapper = new Mapper(new MapperConfiguration(cfg => { cfg.AddProfile(new MappingProfile()); }));
        }

        [TearDown]
        public void TearDown()
        {
            if (_dbContextLazy.IsValueCreated)
            {
                _dbContextLazy.Value.Dispose();
            }
        }


        protected StudyDbContext CreateInMemoryContext()
        {
            var optionBuilder = new DbContextOptionsBuilder<StudyDbContext>();
            optionBuilder.UseInMemoryDatabase($"db_{TestContext.CurrentContext.Test.FullName}");
            return new StudyDbContext(optionBuilder.Options, new DomainEventService(MediatorMock.Object));
        }
    }
}