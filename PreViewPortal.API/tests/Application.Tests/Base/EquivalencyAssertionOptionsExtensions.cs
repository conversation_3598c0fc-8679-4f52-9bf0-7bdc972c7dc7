using System;
using System.Collections.Generic;
using System.Text;
using FluentAssertions;
using FluentAssertions.Equivalency;
using FluentAssertions.Extensions;

namespace Application.Tests.Base
{
    internal static class EquivalencyAssertionOptionsExtensions
    {
        public static EquivalencyAssertionOptions<T> DateTimeCloseBy<T>(this EquivalencyAssertionOptions<T> options, TimeSpan maxDifference)
        {
            return options.Using<DateTime>(ctx => ctx.Subject.Should().BeCloseTo(ctx.Expectation, maxDifference)).WhenTypeIs<DateTime>()
                .Using<DateTimeOffset>(ctx => ctx.Subject.Should().BeCloseTo(ctx.Expectation, maxDifference)).WhenTypeIs<DateTimeOffset>();
        }
    }
}
