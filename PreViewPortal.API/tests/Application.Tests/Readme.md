# Backend unit/integration tests

## Mocking Mediatr

One option to go was to fully utilize Application service collection registration code and create real Mediatr instance with all
dependecies registered.
```
var serviceCollection = new ServiceCollection();
Application.DependencyInjection.AddApplication(serviceCollection);
// overwrite some critical dependencies with mocks
serviceCollection.Add...<SomeIsolatedService>(mock.Object)

//In tests
services.BuildServiceProvider().GetService(typeof(IMediator))
```
Note: At the moment `new ServiceCollection()` will not complie as it seems we have different versions of ServiceCollection from two nuget packages referenced.
I suspect EF core 5 to have older one.

But this was not the simpliest option to go as many dependencies has to be replaced/removed.

We use custom Mock approach. When creating test class inmherit from `BaseTestClass`. You will get:
* Application `DbContext` (in memory) for each test run (reset on each test). Populate it before test.
* `Mapper` instance with main mapping profile registered once.
* `MediatorMock` of `Mock<IMediator>` type, that is already passed to `DbContext` (for domain notifications publish events).
Also you can use it to to mock `IMediator` dependencies in your Queries/Handelers (when one handler calls other handler).
For this purpose there is already utilizy method to quickly mock(i.e. register handler) specific request handler:
`MockHelper.AddMediatorHandlerToMock<>(this.MediatorMock, your_specific_request_handler_instance, true)`. 
Last paramter `true` tells to add Caching behavior like it is done in real application for all handlers.

### MockHelper class

You can use/extend MockHelper to quickly mock specififc dependencies like `IOptions<type>`, Mediatr handlers and etc.

### Test Data

For test data you can use external json or csv(with headers for csv helper library to parse it) with `copy always` content.
There is `TestDataReader` class to read test data.

### Mocking Dremio (IPreViewDataProvider)

Mocking Dremio query services can be done on `IPreViewDataProvider` level. There are two approaches there:
* Simple response mock. Use `MockHelper.MockPreViewDataProvider<TResponse>(List<TResponse> mockedResponse | Func<..>)` 
to get mocked instance that return same output or output by executing your Func.
* Implement you own inmemory preview data provider if possible. See example of `MockedCreativePerformanceDataProvider`