
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.2.32616.157
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "WebAPI", "src\WebAPI\WebAPI.csproj", "{DF8FAB34-9CB6-4BDD-8271-D7EB835CA07E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Application", "src\Application\Application.csproj", "{7A0304D9-B60D-4294-B82C-DA4CCC98F19B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Domain", "src\Domain\Domain.csproj", "{792CEB28-996A-4CE2-B3EB-47557B458935}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Infrastructure", "src\Infrastructure\Infrastructure.csproj", "{1BF80200-B56F-4CFC-99D2-897F5B626881}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Application.Tests", "tests\Application.Tests\Application.Tests.csproj", "{4A9322F7-B40F-45C4-8DEA-37F3D44CA9ED}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Dremio", "src\Dremio\Dremio.csproj", "{E7E8A498-7823-4470-9E5F-80BF2CAB3F82}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BusinessLayer", "..\PreView.Common\src\BusinessLayer\BusinessLayer.csproj", "{47DDFD87-6C55-4355-9101-87DF1A627B98}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DataLayer", "..\PreView.Common\src\DataLayer\DataLayer.csproj", "{C05F0F3A-00DA-4126-B8F9-24778B308E59}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BusinessLayer.Tests", "..\PreView.Common\tests\BusinessLayer.Tests\BusinessLayer.Tests.csproj", "{70D7C49E-E92B-45F8-B9C1-6CD5BBFA81D5}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{DF8FAB34-9CB6-4BDD-8271-D7EB835CA07E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DF8FAB34-9CB6-4BDD-8271-D7EB835CA07E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DF8FAB34-9CB6-4BDD-8271-D7EB835CA07E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DF8FAB34-9CB6-4BDD-8271-D7EB835CA07E}.Release|Any CPU.Build.0 = Release|Any CPU
		{7A0304D9-B60D-4294-B82C-DA4CCC98F19B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7A0304D9-B60D-4294-B82C-DA4CCC98F19B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7A0304D9-B60D-4294-B82C-DA4CCC98F19B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7A0304D9-B60D-4294-B82C-DA4CCC98F19B}.Release|Any CPU.Build.0 = Release|Any CPU
		{792CEB28-996A-4CE2-B3EB-47557B458935}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{792CEB28-996A-4CE2-B3EB-47557B458935}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{792CEB28-996A-4CE2-B3EB-47557B458935}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{792CEB28-996A-4CE2-B3EB-47557B458935}.Release|Any CPU.Build.0 = Release|Any CPU
		{1BF80200-B56F-4CFC-99D2-897F5B626881}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1BF80200-B56F-4CFC-99D2-897F5B626881}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1BF80200-B56F-4CFC-99D2-897F5B626881}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1BF80200-B56F-4CFC-99D2-897F5B626881}.Release|Any CPU.Build.0 = Release|Any CPU
		{4A9322F7-B40F-45C4-8DEA-37F3D44CA9ED}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4A9322F7-B40F-45C4-8DEA-37F3D44CA9ED}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4A9322F7-B40F-45C4-8DEA-37F3D44CA9ED}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4A9322F7-B40F-45C4-8DEA-37F3D44CA9ED}.Release|Any CPU.Build.0 = Release|Any CPU
		{E7E8A498-7823-4470-9E5F-80BF2CAB3F82}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E7E8A498-7823-4470-9E5F-80BF2CAB3F82}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E7E8A498-7823-4470-9E5F-80BF2CAB3F82}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E7E8A498-7823-4470-9E5F-80BF2CAB3F82}.Release|Any CPU.Build.0 = Release|Any CPU
		{47DDFD87-6C55-4355-9101-87DF1A627B98}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{47DDFD87-6C55-4355-9101-87DF1A627B98}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{47DDFD87-6C55-4355-9101-87DF1A627B98}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{47DDFD87-6C55-4355-9101-87DF1A627B98}.Release|Any CPU.Build.0 = Release|Any CPU
		{C05F0F3A-00DA-4126-B8F9-24778B308E59}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C05F0F3A-00DA-4126-B8F9-24778B308E59}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C05F0F3A-00DA-4126-B8F9-24778B308E59}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C05F0F3A-00DA-4126-B8F9-24778B308E59}.Release|Any CPU.Build.0 = Release|Any CPU
		{70D7C49E-E92B-45F8-B9C1-6CD5BBFA81D5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{70D7C49E-E92B-45F8-B9C1-6CD5BBFA81D5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{70D7C49E-E92B-45F8-B9C1-6CD5BBFA81D5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{70D7C49E-E92B-45F8-B9C1-6CD5BBFA81D5}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {0CD449B1-FB0E-4CF5-8F77-0F2D212AD550}
	EndGlobalSection
EndGlobal
