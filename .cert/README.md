# Creating a self signed cert for local development (local.realeyesit.com)

Guide in case certs expire or something goes wrong and we need to create a new one.

## 1. Create .pfx file

In powershell, run the following commands in the same terminal:

```powershell
$cert = New-SelfSignedCertificate -certstorelocation cert:\localmachine\my -dnsname local.realeyesit.com
$pwd = ConvertTo-SecureString -String "4f16c5754f04466faffba346bc15e720" -Force -AsPlainText
$certpath = "cert:\localMachine\my\$($cert.Thumbprint)"
Export-PfxCertificate -Cert $certpath -FilePath "local.realeyesit.com.pfx" -Password $pwd
```

Backend only needs this generated .pfx file.

## 2. Create .crt and .key files

For this, you are going to need openssl installed on your machine. [You can find an installer here.](https://slproweb.com/products/Win32OpenSSL.html)

Run the following commands using openssl cli

```cmd
openssl pkcs12 -nodes -in local.realeyesit.com.pfx -nocerts -out local.realeyesit.com.key
openssl pkcs12 -in local.realeyesit.com.pfx -clcerts -nokeys -out local.realeyesit.com.crt
```

## 3. Import pfx file

For browsers to accept the cert, you need to import the pfx file. _(You are going to need the password you set for it.)_ Click import and go through the wizard, __make sure you import the cert under "Trusted Root Certification Authorities".__

## 4. Last step
Copy everything under .cert folder.