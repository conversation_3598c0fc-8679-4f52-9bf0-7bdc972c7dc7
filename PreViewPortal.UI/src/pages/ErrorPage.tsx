import RefreshIcon from '@mui/icons-material/Refresh';
import { Button, Stack, Typography, useTheme } from '@mui/material';
import { useWindowSize } from 'react-use';
import { useDocumentTitle } from '../hooks/use-document-title';
import { SplashPage } from './SplashPage';

interface Props {
    onRefresh: () => void;
}

export const ErrorPage = ({ onRefresh }: Props) => {
    useDocumentTitle('Error');

    const theme = useTheme();

    const { height: windowHeight } = useWindowSize();

    return (
        <SplashPage
            header={
                <Stack
                    py={windowHeight < 850 ? 8 : 20}
                    direction='column'
                    alignItems='center'
                    justifyContent='center'
                    bgcolor={theme.palette.error.main}
                >
                    <Typography variant='h1' color='white' fontSize='3.5rem'>
                        Oops!
                    </Typography>

                    <Typography
                        variant='h1'
                        color='white'
                        my={4}
                        fontSize='12.5rem'
                    >
                        500
                    </Typography>

                    <Typography variant='h4' color='white' fontSize='1.5rem'>
                        An unexpected error happened in the background.
                    </Typography>

                    <Stack my={5} direction='row' gap={2}>
                        <Button
                            variant='contained'
                            endIcon={<RefreshIcon />}
                            onClick={onRefresh}
                        >
                            Refresh page
                        </Button>
                    </Stack>
                </Stack>
            }
        />
    );
};
