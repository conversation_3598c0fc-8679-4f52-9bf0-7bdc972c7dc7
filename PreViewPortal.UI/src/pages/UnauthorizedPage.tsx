import { Button, Stack, Typography } from '@mui/material';
import { ThemeProvider } from '@mui/system';
import { Navigate } from 'react-router';
import { useWindowSize } from 'react-use';
import { Authentication } from '../helpers/authentication';
import { handleContactSupport } from '../helpers/handle-contact-support';
import { useDocumentTitle } from '../hooks/use-document-title';
import { useUser } from '../hooks/use-user';
import { Themes } from '../theme';
import { SplashPage } from './SplashPage';

const UnauthorizedPage = () => {
    useDocumentTitle('Unauthorized');

    const { height: windowHeight } = useWindowSize();

    const { isLoggedIn } = useUser();

    if (!isLoggedIn) {
        return <Navigate to='/' />;
    }

    return (
        <SplashPage
            header={
                <Stack
                    py={windowHeight < 900 ? 8 : 20}
                    gap={windowHeight < 800 ? 2 : 4}
                    direction='column'
                    alignItems='center'
                    justifyContent='center'
                    bgcolor='#EBBE21'
                >
                    <Typography variant='h1' color='white' fontSize={'3.5rem'}>
                        Oops
                    </Typography>
                    <Typography variant='h1' color='white' fontSize={'12.5rem'}>
                        401
                    </Typography>
                    <Typography variant='h4' color='white' fontSize='1.5rem'>
                        Authorization required
                    </Typography>

                    <Typography variant='body1' color='white' fontSize='1rem'>
                        {"You don't have permission to open this page"}
                    </Typography>

                    <Button variant='contained' onClick={handleContactSupport}>
                        Contact support
                    </Button>

                    <Typography variant='body1' color='white' fontSize='1rem'>
                        Already have permission?
                    </Typography>

                    <ThemeProvider theme={Themes.dark}>
                        <Button
                            sx={{ mb: 5 }}
                            variant='outlined'
                            onClick={Authentication.logout}
                        >
                            Back to login
                        </Button>
                    </ThemeProvider>
                </Stack>
            }
        />
    );
};

export default UnauthorizedPage;
