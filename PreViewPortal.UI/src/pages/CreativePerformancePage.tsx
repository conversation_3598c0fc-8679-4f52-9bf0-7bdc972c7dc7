import { withHandledErrorBoundary } from '../hocs/with-handled-error-boundary';
import { CreativePerformanceInContextGrid } from '@/components/CreativePerformanceInContextGrid';
import { useProduct } from '@/hooks/use-product';
import { useDocumentTitle } from '@/hooks/use-document-title';
import { ProductOptions } from '@/interfaces/product-options';
import { CreativePerformanceForcedExposureGrid } from '@/components/CreativePerformanceForcedExposureGrid';

const CreativePerformancePage = () => {
    useDocumentTitle('Creative Performance');

    const { selectedProduct } = useProduct();

    return (
        <>
            {(() => {
                switch (selectedProduct) {
                    case ProductOptions.InContext:
                        return <CreativePerformanceInContextGrid />;
                    case ProductOptions.NewForcedExposure:
                        return <CreativePerformanceForcedExposureGrid />;
                    default:
                        return null;
                }
            })()}
        </>
    );
};

export default withHandledErrorBoundary(CreativePerformancePage);
