import {
    <PERSON>comple<PERSON>,
    Box,
    Button,
    Link,
    Stack,
    TextField,
    Typography
} from '@mui/material';
import { useSnackbar } from 'notistack';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router';
import { withHandledErrorBoundary } from '../hocs/with-handled-error-boundary';
import { useDocumentTitle } from '../hooks/use-document-title';
import { useHttp } from '../hooks/use-http';
import { useSupervisedAccount } from '../hooks/use-supervised-account';
import { AccountLookupModel } from '@/interfaces/account-lookup-model';
import { useUser } from '@/hooks/use-user';

const SwitchAccountPage = () => {
    useDocumentTitle('Switch Account');

    const navigate = useNavigate();
    const { enqueueSnackbar } = useSnackbar();
    const [accounts, setAccounts] = useState<AccountLookupModel[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const { supervisedAccount, setSupervisedAccount } = useSupervisedAccount();
    const [selectedAccount, setSelectedAccount] =
        useState<AccountLookupModel | null>(supervisedAccount);
    const { http, abort, handleError } = useHttp();
    const { user, isAdmin } = useUser();

    useEffect(() => {
        http.get('user/account')
            .json<AccountLookupModel[]>()
            .then(setAccounts)
            .then(_ => setIsLoading(false))
            .catch(handleError);

        return () => abort();
    }, [http, abort, handleError]);

    const clearGridCache = async () => {
        const gridCacheStorageName = 'realeyesit-api-grid';
        if (await caches.has(gridCacheStorageName)) {
            await caches.delete(gridCacheStorageName);
        }
    };

    const handleSave = async () => {
        if (!selectedAccount) return;

        const { hasAccessToNewForcedExposure, hasREInContext } =
            selectedAccount;

        if (!hasAccessToNewForcedExposure && !hasREInContext) {
            enqueueSnackbar(
                'Reporting is not enabled for this account on the portal.',
                {
                    variant: 'error'
                }
            );

            return;
        }

        setSupervisedAccount(selectedAccount);
        await clearGridCache();

        enqueueSnackbar('You sucessfully switched account', {
            variant: 'success'
        });
        navigate('/');
    };

    const handleCancel = () => {
        setSelectedAccount(supervisedAccount);
    };

    const noSelectedOrEqualAccount =
        !selectedAccount || supervisedAccount?.id === selectedAccount.id;

    const userHasMultipleAccount = isAdmin || user.accounts?.length > 1;

    const contactEmail = '<EMAIL>';

    return (
        <Box position='relative'>
            <Typography sx={{ mb: 2 }} variant='h4'>
                {!supervisedAccount && userHasMultipleAccount
                    ? 'Select Account'
                    : 'Account'}
            </Typography>
            <Typography variant='body1' mb={4}>
                {userHasMultipleAccount
                    ? 'Please select an account to supervise and see only its related data.'
                    : 'You can access the data related to the following account:'}
            </Typography>
            <Autocomplete
                sx={{ mb: 2, width: 400 }}
                value={selectedAccount}
                options={accounts}
                getOptionLabel={o => `ID${o.id} - ${o.name}`}
                isOptionEqualToValue={(o, v) => o.id === v.id}
                onChange={(_, o) => setSelectedAccount(o)}
                renderInput={props => (
                    <TextField
                        {...props}
                        label={
                            userHasMultipleAccount
                                ? 'Search for an account'
                                : ''
                        }
                    />
                )}
                disabled={!userHasMultipleAccount}
                loading={isLoading}
            />
            {userHasMultipleAccount && (
                <Stack direction='row' gap={2}>
                    <Button
                        disabled={noSelectedOrEqualAccount}
                        variant='outlined'
                        onClick={handleCancel}
                    >
                        {'Cancel'}
                    </Button>
                    <Button
                        disabled={noSelectedOrEqualAccount}
                        variant='contained'
                        onClick={handleSave}
                    >
                        {'Save and Continue'}
                    </Button>
                </Stack>
            )}
            <Typography position='absolute' top={275}>
                {'Please contact '}
                <Link fontWeight='bolder' href={`mailto:${contactEmail}`}>
                    {contactEmail}
                </Link>
                {' to get access to company account data.'}
            </Typography>
        </Box>
    );
};

export default withHandledErrorBoundary(SwitchAccountPage);
