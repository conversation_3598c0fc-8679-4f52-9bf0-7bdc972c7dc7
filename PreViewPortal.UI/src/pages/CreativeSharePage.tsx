import {withHandledErrorBoundary} from '@/hocs/with-handled-error-boundary';
import {useLocation} from 'react-router';
import {CreativeViewerDrawer} from "@/components/creative-viewer/CreativeViewerDrawer";
import ExportStatus from "@/components/ExportStatusBar";

const CreativeSharePage = () => {
    const location = useLocation();
    const queryParams = new URLSearchParams(location.search);
    const shareKey = queryParams.get('shareKey') || '';

    return (
        <>
            <ExportStatus />
            <CreativeViewerDrawer
                shareKeyParam={shareKey}
                open={true}
                selectedCreatives={[]}
                onClose={() => {
                }}
            />
        </>
    );
};

export default withHandledErrorBoundary(CreativeSharePage);
