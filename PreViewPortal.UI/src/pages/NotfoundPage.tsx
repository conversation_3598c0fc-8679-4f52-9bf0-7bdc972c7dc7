import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { Button, Stack, Typography } from '@mui/material';
import { Link } from 'react-router';
import { useWindowSize } from 'react-use';
import { useDocumentTitle } from '../hooks/use-document-title';
import { SplashPage } from './SplashPage';

const NotfoundPage = () => {
    useDocumentTitle('Not found');

    const { height: windowHeight } = useWindowSize();

    return (
        <SplashPage
            header={
                <Stack
                    py={windowHeight < 850 ? 8 : 20}
                    direction='column'
                    alignItems='center'
                    justifyContent='center'
                    bgcolor='#E2626A'
                >
                    <Typography variant='h1' color='white' fontSize='3.5rem'>
                        Oops!
                    </Typography>
                    <Typography
                        variant='h1'
                        color='white'
                        my={4}
                        fontSize='12.5rem'
                    >
                        404
                    </Typography>
                    <Typography variant='h4' color='white' fontSize='1.5rem'>
                        Page not found
                    </Typography>

                    <Typography
                        variant='body1'
                        color='white'
                        my={4}
                        fontSize='1rem'
                    >
                        The page you are looking for may have been removed, name
                        <br />
                        changed or has been temporarily removed.
                    </Typography>

                    <Button
                        sx={{ mb: 5 }}
                        variant='contained'
                        startIcon={<ArrowBackIcon />}
                        component={Link}
                        to='/'
                    >
                        Back to home
                    </Button>
                </Stack>
            }
        />
    );
};

export default NotfoundPage;
