import { Button, <PERSON>ack, Typography, SvgIcon } from '@mui/material';
import { useWindowSize } from 'react-use';
import { useDocumentTitle } from '../hooks/use-document-title';
import { SplashPage } from './SplashPage';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import { Authentication } from '../helpers/authentication';
import ClockSvg from '../assets/icons/clock-icon.svg?react';
import { useUser } from '../hooks/use-user';
import { Navigate } from 'react-router';

const ExpiredPage = () => {
    useDocumentTitle('Session Expired');

    const { height: windowHeight } = useWindowSize();

    const { isLoggedIn } = useUser();

    if (isLoggedIn) {
        return <Navigate to='/' />;
    }

    return (
        <SplashPage
            header={
                <Stack
                    py={windowHeight < 850 ? 8 : 20}
                    direction='column'
                    alignItems='center'
                    justifyContent='center'
                    bgcolor='#2DC0A2'
                >
                    <Typography variant='h1' color='white' fontSize='3.5rem'>
                        Preview
                    </Typography>
                    <Typography
                        my={4}
                        variant='h1'
                        color='white'
                        fontSize='12.5rem'
                    >
                        <SvgIcon
                            component={ClockSvg}
                            inheritViewBox
                            fontSize='inherit'
                        />
                    </Typography>
                    <Typography variant='h4' color='white' fontSize='1.5rem'>
                        You session has expired
                    </Typography>

                    <Typography
                        variant='body1'
                        color='white'
                        my={4}
                        fontSize='1rem'
                    >
                        Please login to the page again
                    </Typography>

                    <Button
                        sx={{ px: 5, mt: 5 }}
                        endIcon={<ArrowForwardIcon />}
                        variant='contained'
                        size='large'
                        onClick={Authentication.login}
                    >
                        Login
                    </Button>
                </Stack>
            }
        />
    );
};

export default ExpiredPage;
