import {
    Box,
    Button,
    Checkbox,
    Container,
    FormControlLabel,
    FormGroup,
    Radio,
    Switch,
    Tab,
    Tabs,
    TextField,
    ThemeProvider,
    Typography
} from '@mui/material';
import { useState } from 'react';
import { Themes } from '../theme';
import { FullWidthBox } from '../components/FullWidthBox';

const DesignPage = () => {
    const [theme, setTheme] = useState(Themes.light);

    return (
        <FullWidthBox
            sx={{
                backgroundColor: theme === Themes.dark ? '#273235' : undefined
            }}
        >
            <Container
                maxWidth='xl'
                sx={{ pt: 5, pb: 5, mt: '-40px', mb: '-40px' }}
            >
                <ThemeProvider theme={theme}>
                    <Box sx={{ mb: 5 }}>
                        <FormControlLabel
                            sx={{ mb: 5 }}
                            control={
                                <Switch
                                    color='secondary'
                                    onChange={(_, checked) =>
                                        setTheme(
                                            checked ? Themes.dark : Themes.light
                                        )
                                    }
                                />
                            }
                            label='Dark?'
                        />

                        <Typography variant='h2' mb={4}>
                            Typography
                        </Typography>

                        <Typography variant='h1'>h1</Typography>
                        <Typography variant='h2'>h2</Typography>
                        <Typography variant='h3'>h3</Typography>
                        <Typography variant='h4'>h4</Typography>
                        <Typography variant='h5'>h5</Typography>
                        <Typography variant='h6'>h6</Typography>
                        <Typography variant='subtitle1'>subtitle1</Typography>
                        <Typography variant='subtitle2'>subtitle2</Typography>
                        <Typography variant='body1'>body1</Typography>
                        <Typography variant='body2'>body2</Typography>
                        <Typography variant='caption'>caption</Typography>
                        <Box>
                            <Typography variant='overline'>overline</Typography>
                        </Box>
                    </Box>
                    <Box sx={{ mb: 5 }}>
                        <Typography variant='h2' mb={4}>
                            Button
                        </Typography>
                        <Box sx={{ mb: 3 }}>
                            <Typography mb={2} variant='h5'>
                                Contained
                            </Typography>
                            <Button sx={{ mr: 2 }} variant='contained'>
                                default
                            </Button>
                            <Button variant='contained' disabled>
                                disabled
                            </Button>
                        </Box>

                        <Box sx={{ mb: 3 }}>
                            <Typography mb={2} variant='h5'>
                                Outlined
                            </Typography>
                            <Button sx={{ mr: 2 }} variant='outlined'>
                                default
                            </Button>
                            <Button variant='outlined' disabled>
                                disabled
                            </Button>
                        </Box>

                        <Box sx={{ mb: 3 }}>
                            <Typography mb={2} variant='h5'>
                                Text
                            </Typography>
                            <Button sx={{ mr: 2 }} variant='text'>
                                default
                            </Button>
                            <Button variant='text' disabled>
                                disabled
                            </Button>
                        </Box>
                    </Box>
                    <Box sx={{ mb: 5 }}>
                        <Typography variant='h2' mb={4}>
                            Checkbox
                        </Typography>

                        <FormGroup>
                            <FormControlLabel
                                control={<Checkbox />}
                                label='Inactive'
                            />
                            <FormControlLabel
                                control={<Checkbox />}
                                label='Disabled'
                                disabled
                            />
                            <FormControlLabel
                                control={<Checkbox />}
                                label='Disabled active'
                                checked
                                disabled
                            />
                            <FormControlLabel
                                control={<Checkbox />}
                                label='Active'
                                checked
                            />
                        </FormGroup>
                    </Box>
                    <Box sx={{ mb: 5 }}>
                        <Typography variant='h2' mb={4}>
                            Radio
                        </Typography>

                        <FormGroup>
                            <FormControlLabel
                                control={<Radio />}
                                label='Inactive'
                            />
                            <FormControlLabel
                                control={<Radio />}
                                label='Disabled'
                                disabled
                            />
                            <FormControlLabel
                                control={<Radio />}
                                label='Disabled active'
                                checked
                                disabled
                            />
                            <FormControlLabel
                                control={<Radio />}
                                label='Active'
                                checked
                            />
                        </FormGroup>
                    </Box>
                    <Box sx={{ mb: 5 }}>
                        <Typography variant='h2' mb={4}>
                            Switch
                        </Typography>
                        <FormControlLabel
                            control={<Switch />}
                            label='Inactive'
                        />
                        <FormControlLabel
                            control={<Switch disabled />}
                            label='Disabled'
                        />
                        <FormControlLabel
                            control={<Switch checked disabled />}
                            label='Disabled active'
                        />
                        <FormControlLabel
                            control={<Switch checked />}
                            label='Active'
                        />
                    </Box>

                    <Box sx={{ mb: 5 }}>
                        <Typography variant='h2' mb={4}>
                            Text Field
                        </Typography>

                        <TextField variant='outlined' label='Label' />

                        <Box sx={{ mt: 4 }}>
                            <TextField
                                variant='outlined'
                                label='Label'
                                focused
                            />
                        </Box>

                        <Box sx={{ mt: 4 }}>
                            <TextField variant='outlined' value='Input text' />
                        </Box>

                        <Box sx={{ mt: 4 }}>
                            <TextField
                                variant='outlined'
                                label='Disabled'
                                disabled
                            />
                        </Box>

                        <Box sx={{ mt: 4 }}>
                            <TextField
                                variant='outlined'
                                label='Error'
                                helperText='Helper text'
                                focused
                                error
                            />
                        </Box>

                        <Box sx={{ mt: 4 }}>
                            <TextField
                                variant='outlined'
                                label='Error'
                                value='Placeholder'
                                helperText='Helper text'
                                error
                            />
                        </Box>

                        <Box sx={{ mt: 4 }}>
                            <TextField
                                variant='standard'
                                label='Label'
                                value='Placeholder'
                            />
                        </Box>
                    </Box>

                    <Box sx={{ mb: 5 }}>
                        <Typography variant='h2' mb={4}>
                            Tabs
                        </Typography>

                        <Tabs value='Tab page 1'>
                            <Tab label='Tab page 1' value='Tab page 1' />
                            <Tab label='Tab page 2' value='Tab page 2' />
                            <Tab label='Tab page 3' value='Tab page 3' />
                            <Tab
                                label='Tab page 4'
                                value='Disabled Tab page 4'
                                disabled
                            />
                        </Tabs>
                    </Box>
                </ThemeProvider>
            </Container>
        </FullWidthBox>
    );
};

export default DesignPage;
