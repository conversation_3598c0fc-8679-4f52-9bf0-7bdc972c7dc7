import { Box, Stack } from '@mui/material';
import React from 'react';
import { useMeasure } from 'react-use';
import RealeyesLogo from '../components/RealeyesLogo';

interface Props {
    header: React.ReactNode;
    body?: React.ReactNode;
}

export const SplashPage = ({ header, body }: Props) => {
    const [headerRef, { height: headerHeight }] = useMeasure<HTMLElement>();
    const [bodyRef, { height: bodyHeight }] = useMeasure<HTMLElement>();
    const [maskRef, { height: maskHeight }] = useMeasure<HTMLDivElement>();

    return (
        <Box bgcolor='white' height='100vh'>
            <Box
                position='relative'
                overflow='hidden'
                maxHeight={`calc(100% - ${bodyHeight + maskHeight}px)`}
                height={headerHeight}
            >
                <Box position='absolute' top={0} left={0} right={0} zIndex={3}>
                    <Box maxWidth={1280} mx='auto'>
                        <Stack
                            bgcolor='white'
                            display='inline-flex'
                            alignItems='center'
                            fontSize={40}
                            p={1}
                        >
                            <RealeyesLogo fontSize='inherit' />
                        </Stack>
                    </Box>
                </Box>

                <Box
                    ref={headerRef}
                    top={0}
                    left={0}
                    right={0}
                    zIndex={2}
                    position='absolute'
                >
                    {header}
                </Box>

                <Stack
                    ref={maskRef}
                    sx={{ pointerEvents: 'none' }}
                    position='absolute'
                    left={0}
                    right={0}
                    bottom={0}
                    zIndex={3}
                    maxHeight={110}
                >
                    <svg
                        fill='white'
                        width='100%'
                        height='100%'
                        viewBox='0 0 1881 137'
                        preserveAspectRatio='none'
                    >
                        <path d='M939.813 124.67C-154.455 121.793 -523.511 -192.622 -525.856 -274V137H2405.48V-269.89C2405.48 -194.266 2034.08 127.958 939.813 124.67Z' />
                    </svg>
                </Stack>
            </Box>

            {body && (
                <Box ref={bodyRef} my={5}>
                    {body}
                </Box>
            )}
        </Box>
    );
};
