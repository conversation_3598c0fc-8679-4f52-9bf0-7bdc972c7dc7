import { ConfirmDialogOptions } from '../interfaces/confirm-dialog-options';

export const SHOW_CONFIRM = 'SHOW_CONFIRM';
export const HIDE_CONFIRM = 'HIDE_CONFIRM';

type ConfirmDialogActions =
    | { type: typeof SHOW_CONFIRM; options: ConfirmDialogOptions }
    | { type: typeof HIDE_CONFIRM };

type ConfirmDialogState = {
    isOpen: boolean;
    options?: ConfirmDialogOptions;
};

export const dialogInitialState: ConfirmDialogState = {
    isOpen: false
};

export const confirmDialogReducer = (
    state: ConfirmDialogState = dialogInitialState,
    action: ConfirmDialogActions
): ConfirmDialogState => {
    switch (action.type) {
        case SHOW_CONFIRM:
            return { isOpen: true, options: action.options };
        case HIDE_CONFIRM:
            return { isOpen: false };
        default:
            return state;
    }
};
