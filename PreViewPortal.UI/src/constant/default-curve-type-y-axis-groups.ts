import { SideType } from '@/interfaces/side-type';
import { CreativeViewerCurveType } from '../interfaces/creative-viewer-curve-type';

export type CurveTypeYAxisMap = { [key: string]: SideType };

export const defaultCurveTypeYAxisGroups: CurveTypeYAxisMap = {
    [`${CreativeViewerCurveType.Distraction}-focused`]: 'left',
    [`${CreativeViewerCurveType.Distraction}-inContext`]: 'left',
    [`${CreativeViewerCurveType.Happiness}-focused`]: 'right',
    [`${CreativeViewerCurveType.Happiness}-inContext`]: 'right',
    [`${CreativeViewerCurveType.Negativity}-focused`]: 'right',
    [`${CreativeViewerCurveType.Negativity}-inContext`]: 'right',
    [`${CreativeViewerCurveType.Contempt}-focused`]: 'right',
    [`${CreativeViewerCurveType.Contempt}-inContext`]: 'right',
    [`${CreativeViewerCurveType.Surprise}-focused`]: 'right',
    [`${CreativeViewerCurveType.Surprise}-inContext`]: 'right',
    [`${CreativeViewerCurveType.Confusion}-focused`]: 'right',
    [`${CreativeViewerCurveType.Confusion}-inContext`]: 'right',
    [`${CreativeViewerCurveType.Disgust}-focused`]: 'right',
    [`${CreativeViewerCurveType.Disgust}-inContext`]: 'right',
    [`${CreativeViewerCurveType.Attention}-focused`]: 'left',
    [`${CreativeViewerCurveType.Attention}-inContext`]: 'left',
    [`${CreativeViewerCurveType.NeutralAttention}-focused`]: 'left',
    [`${CreativeViewerCurveType.NeutralAttention}-inContext`]: 'left',
    [`${CreativeViewerCurveType.AllReactions}-focused`]: 'right',
    [`${CreativeViewerCurveType.AllReactions}-inContext`]: 'right',
    [`${CreativeViewerCurveType.Playback}-focused`]: 'left',
    [`${CreativeViewerCurveType.Playback}-inContext`]: 'left'
};
