declare global {
    interface Window {
        RUNTIME_CONFIG: {
            HUBSPOT: {
                IS_TRACKING_ENABLED: boolean;
                ACCOUNT_ID: number;
                USER_GUIDE_URL: string;
            };
            CREATIVE_VIEWER_MEDIA_LIMIT: number;
            MIN_SESSION_VIEW_COUNT: number;
            MIN_VIEW_THRESHOLD_FOR_SCORES: number;
            EXCEL_EXPORT_ROW_LIMIT: number;
            OAUTH: {
                CLIENT_ID: string;
                RESPONSE_TYPE: string;
                SCOPES: string;
                AUTH_SERVICE_URL: string;
                CALLBACK_URL: string;
                LOGOUT_URL: string;
            };
        };
        _hsq: any;
    }
}

export const minSessionViewCount = window.RUNTIME_CONFIG.MIN_SESSION_VIEW_COUNT;

export const minViewThresholdForScore =
    window.RUNTIME_CONFIG.MIN_VIEW_THRESHOLD_FOR_SCORES;

export const excelExportRowLimit = window.RUNTIME_CONFIG.EXCEL_EXPORT_ROW_LIMIT;

export const materialLicense = import.meta.env.VITE_REACT_APP_MUI_LICENSE!;

export const hubspot = {
    isTrackingEnabled: window.RUNTIME_CONFIG.HUBSPOT?.IS_TRACKING_ENABLED,
    accoundId: window.RUNTIME_CONFIG.HUBSPOT?.ACCOUNT_ID,
    userGuideUrl: window.RUNTIME_CONFIG.HUBSPOT?.USER_GUIDE_URL
};

export const creativeViewerMediaLimit =
    window.RUNTIME_CONFIG.CREATIVE_VIEWER_MEDIA_LIMIT;

export const oauthSettings = {
    clientId: window.RUNTIME_CONFIG.OAUTH?.CLIENT_ID,
    responseType: window.RUNTIME_CONFIG.OAUTH?.RESPONSE_TYPE,
    scopes: window.RUNTIME_CONFIG.OAUTH?.SCOPES,
    authServiceUrl: window.RUNTIME_CONFIG.OAUTH?.AUTH_SERVICE_URL,
    callbackUrl: window.RUNTIME_CONFIG.OAUTH?.CALLBACK_URL,
    logoutUrl: window.RUNTIME_CONFIG.OAUTH?.LOGOUT_URL
};
