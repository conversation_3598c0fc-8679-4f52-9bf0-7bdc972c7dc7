import { ExposureGroupType } from '@/contexts/creative-viewer-context';
import { ScoreBase } from '@/interfaces/score-base';

export interface SurveyScore {
    id: string;
    views?: number;
    segmentKey?: string;
    adRecognition?: number;
    adRecognitionMedian?: number;
    adRecognitionDiff?: number;
    brandRecognition?: number;
    brandRecognitionMedian?: number;
    brandRecognitionDiff?: number;
    adLikeability?: number;
    adLikeabilityMedian?: number;
    adLikeabilityDiff?: number;
    brandTrust?: number;
    brandTrustMedian?: number;
    brandTrustDiff?: number;
    persuasion?: number;
    persuasionMedian?: number;
    persuasionDiff?: number;
    segmentLabel?: string;
}

export interface SurveyScoreOption {
    label: string;
    score: keyof SurveyScore;
    norm: keyof SurveyScore;
    vsNorm: keyof SurveyScore;
    scoreBase?: ScoreBase;
    exposureGroup: ExposureGroupType;
}

export const defaultSurveyScore: SurveyScoreOption = {
    label: 'Brand Recognition',
    score: 'brandRecognition',
    norm: 'brandRecognitionMedian',
    vsNorm: 'brandRecognitionDiff',
    scoreBase: '%',
    exposureGroup: 'inContext'
};

export const surveyScoreOptions: SurveyScoreOption[] = [
    defaultSurveyScore,
    {
        label: 'Ad Recognition',
        score: 'adRecognition',
        norm: 'adRecognitionMedian',
        vsNorm: 'adRecognitionDiff',
        scoreBase: '%',
        exposureGroup: 'inContext'
    },
    {
        label: 'Brand Trust',
        score: 'brandTrust',
        norm: 'brandTrustMedian',
        vsNorm: 'brandTrustDiff',
        exposureGroup: 'focused'
    },
    {
        label: 'Ad Likeability',
        score: 'adLikeability',
        norm: 'adLikeabilityMedian',
        vsNorm: 'adLikeabilityDiff',
        exposureGroup: 'focused'
    },
    {
        label: 'Persuasion',
        score: 'persuasion',
        norm: 'persuasionMedian',
        vsNorm: 'persuasionDiff',
        scoreBase: '%',
        exposureGroup: 'focused'
    }
];
