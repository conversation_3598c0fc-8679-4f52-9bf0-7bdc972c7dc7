import {
    GridColDef,
    GridColumnGroupingModel,
    GridColumnVisibilityModel,
    GridSortingInitialState
} from '@mui/x-data-grid-premium';
import {
    AdformatCell,
    AdSetCell,
    BrandCell,
    CountOrValue<PERSON>ell,
    CountryCell,
    CreativeCell,
    DateCell,
    DiffCell,
    DurationCell,
    OrderCell,
    PercentageCell,
    QualityScoreCell,
    SecondBasedScoreCell
} from '../components/GridRenderCell';
import { getHeaderWithExposureLabel } from '@/helpers/get-header-with-exposure-label';
import { Typography } from '@mui/material';
import { ExposureLabel } from '@/interfaces/exposure-label';
import { CreativePerformanceForcedExposureListModel } from '@/interfaces/creative-performance-forced-exposure-list-model';
import { CellWithTooltip } from '@/components/CellWithTooltip';

export const getCreativePerformanceForcedExposureColDef = (
    isAdmin: boolean,
    showNormTooltip: boolean,
    segmentKeyLabel: string,
    hasCountry: boolean,
    hasIndustry: boolean,
    hasBrand: boolean
): GridColDef<CreativePerformanceForcedExposureListModel>[] => [
    ...(isAdmin
        ? [
              {
                  field: 'isEnabledForClient',
                  headerName: 'Reporting Status',
                  width: 150,
                  renderCell: ({ row: { isEnabledForClients } }: any) => (
                      <Typography
                          variant='body2'
                          margin='revert'
                          fontWeight='bold'
                          fontSize={14}
                          color={
                              isEnabledForClients
                                  ? 'success.main'
                                  : 'error.main'
                          }
                      >
                          {isEnabledForClients === undefined
                              ? ''
                              : isEnabledForClients
                                ? 'Enabled'
                                : 'Disabled'}
                      </Typography>
                  )
              }
          ]
        : []),
    {
        field: 'creationDate',
        headerName: 'Date Tested',
        width: 150,
        renderCell: ({ row: { creationDate } }) => (
            <DateCell date={creationDate} />
        )
    },
    {
        field: 'topCategory',
        headerName: 'Industry (top level)',
        type: 'string',
        width: 150,
        renderCell: ({ row: { topCategory, topCategoryCount } }) => (
            <CountOrValueCell value={topCategory} count={topCategoryCount} />
        )
    },
    {
        field: 'midCategory',
        headerName: 'Industry (mid level)',
        type: 'string',
        width: 150,
        renderCell: ({ row: { midCategory, midCategoryCount } }) => (
            <CountOrValueCell value={midCategory} count={midCategoryCount} />
        )
    },
    {
        field: 'subCategory',
        headerName: 'Industry (sub level)',
        type: 'string',
        width: 150,
        renderCell: ({ row: { subCategory, subCategoryCount } }) => (
            <CountOrValueCell value={subCategory} count={subCategoryCount} />
        )
    },
    {
        field: 'brand',
        headerName: 'Brand',
        type: 'string',
        headerAlign: 'center',
        align: 'center',
        renderCell: ({ row: { brand, brandLogoUrl, brandCount } }) => (
            <BrandCell
                brand={brand!}
                brandLogoUrl={brandLogoUrl}
                brandCount={brandCount}
            />
        )
    },
    {
        field: 'order',
        headerName: 'Order',
        type: 'string',
        width: 260,
        renderCell: ({ row: { order, orderExternalKey } }) => (
            <OrderCell
                name={order!}
                externalKey={orderExternalKey!}
                isAdmin={isAdmin}
            />
        )
    },
    {
        field: 'customAdSets',
        headerName: 'Ad Set',
        type: 'string',
        width: 260,
        renderCell: ({ row: { customAdSets } }) => (
            <AdSetCell value={customAdSets!} />
        )
    },
    {
        field: 'sourceMedia',
        headerName: 'Creative',
        type: 'string',
        width: 260,
        renderCell: ({
            row: {
                sourceMedia,
                thumbnailUrl,
                sourceMediaID,
                testID,
                surveyKeyAlias
            }
        }) => (
            <CreativeCell
                name={sourceMedia}
                thumbnailUrl={thumbnailUrl}
                sourceMediaID={sourceMediaID!}
                inFocusTestID={testID!}
                isAdmin={isAdmin}
                surveyKeyAlias={surveyKeyAlias!}
            />
        )
    },
    {
        field: 'duration',
        headerName: 'Duration',
        type: 'number',
        width: 90,
        headerAlign: 'center',
        align: 'center',
        renderCell: ({ row: { duration } }) => <DurationCell value={duration} />
    },
    {
        field: 'device',
        headerName: 'Device',
        type: 'string',
        width: 150,
        headerAlign: 'center',
        align: 'center',
        renderCell: ({ row: { device, deviceCount } }) => (
            <CountOrValueCell value={device} count={deviceCount} />
        )
    },
    {
        field: 'geographicRegion',
        headerName: 'Region',
        type: 'string',
        width: 150,
        renderCell: ({ row: { geographicRegion, geographicRegionCount } }) => (
            <CountOrValueCell
                value={geographicRegion}
                count={geographicRegionCount}
            />
        )
    },
    {
        field: 'country',
        headerName: 'Country',
        type: 'string',
        headerAlign: 'center',
        align: 'center',
        renderCell: ({ row: { country, country_code, countryCount } }) => (
            <CountryCell
                country={country!}
                countryCode={country_code!}
                countryCount={countryCount}
            />
        )
    },
    {
        field: 'views',
        headerName: 'Views',
        type: 'number',
        width: 80,
        headerAlign: 'center',
        align: 'center'
    },
    {
        field: 'qualityScore',
        headerName: 'Quality Score',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 110,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                field='qualityScore'
                row={row}
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
            >
                <QualityScoreCell
                    score={row.qualityScore!}
                    scoreIndex={row.qualityScore_index!}
                    fontSize={15}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'adformatTextIF',
        headerName: getHeaderWithExposureLabel('Ad Format', 'focused'),
        renderHeader: () => 'Ad Format',
        type: 'string',
        width: 285,
        align: 'center',
        renderCell: ({
            row: { adformatTextIF, platform },
            rowNode: { type }
        }) =>
            type === 'pinnedRow' ? (
                1
            ) : (
                <AdformatCell
                    adformatName={adformatTextIF!}
                    platform={platform!}
                />
            )
    },
    {
        field: 'playbackSeconds',
        headerName: getHeaderWithExposureLabel('Playback Seconds', 'focused'),
        renderHeader: () => 'Playback Seconds',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 140,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='playbackSeconds'
                diffField='playbackSecondsDiff'
                normField='playbackSecondsMedian'
                scoreName='Playback Seconds'
                scoreBase='sec'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <SecondBasedScoreCell value={row.playbackSeconds} />
            </CellWithTooltip>
        )
    },
    {
        field: 'playbackSecondsDiff',
        headerName: getHeaderWithExposureLabel(
            'Playback Seconds',
            'focused',
            true
        ),
        renderHeader: () => 'Playback Seconds vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 140,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='playbackSecondsDiff'
                diffField='playbackSecondsDiff'
                normField='playbackSecondsMedian'
                scoreField='playbackSeconds'
                scoreName='Playback Seconds'
                scoreBase='sec'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.playbackSecondsDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'playbackSecondsMedian',
        headerName: getHeaderWithExposureLabel(
            'Playback Seconds (Norm)',
            'focused'
        ),
        renderHeader: () => 'Playback Seconds (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 185,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='playbackSecondsMedian'
                diffField='playbackSecondsDiff'
                normField='playbackSecondsMedian'
                scoreField='playbackSeconds'
                scoreName='Playback Seconds'
                scoreBase='sec'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <SecondBasedScoreCell value={row.playbackSecondsMedian} />
            </CellWithTooltip>
        )
    },
    {
        field: 'playbackSecondsRank',
        headerName: getHeaderWithExposureLabel(
            'Playback Seconds (Rank)',
            'focused'
        ),
        renderHeader: () => 'Playback Seconds (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 180,
        renderCell: ({ row }) => (
            <CellWithTooltip
                field='playbackSecondsRank'
                row={row}
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
            >
                {row.playbackSecondsRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'vtr',
        headerName: getHeaderWithExposureLabel('VTR', 'focused'),
        renderHeader: () => 'VTR',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='vtr'
                diffField='vtrDiff'
                normField='vtrMedian'
                scoreName='VTR'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.vtr}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'vtrDiff',
        headerName: getHeaderWithExposureLabel('VTR', 'focused', true),
        renderHeader: () => 'VTR vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 100,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='vtrDiff'
                diffField='vtrDiff'
                normField='vtrMedian'
                scoreField='vtr'
                scoreName='VTR'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.vtrDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'vtrMedian',
        headerName: getHeaderWithExposureLabel('VTR (Norm)', 'focused'),
        renderHeader: () => 'VTR (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 160,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='vtrMedian'
                diffField='vtrDiff'
                normField='vtrMedian'
                scoreField='vtr'
                scoreName='VTR'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.vtrMedian}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'vtrRank',
        headerName: getHeaderWithExposureLabel('VTR (Rank)', 'focused'),
        renderHeader: () => 'VTR (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 160,
        renderCell: ({ row }) => (
            <CellWithTooltip
                field='vtrRank'
                row={row}
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
            >
                {row.vtrRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'attentiveSeconds',
        headerName: getHeaderWithExposureLabel('Attentive Seconds', 'focused'),
        renderHeader: () => 'Attentive Seconds',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 140,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='attentiveSeconds'
                diffField='attentiveSecondsDiff'
                normField='attentiveSecondsMedian'
                scoreName='Attentive Seconds'
                scoreBase='sec'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <SecondBasedScoreCell value={row.attentiveSeconds} />
            </CellWithTooltip>
        )
    },
    {
        field: 'attentiveSecondsDiff',
        headerName: getHeaderWithExposureLabel(
            'Attentive Seconds',
            'focused',
            true
        ),
        renderHeader: () => 'Attentive Seconds vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 140,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='attentiveSecondsDiff'
                diffField='attentiveSecondsDiff'
                normField='attentiveSecondsMedian'
                scoreField='attentiveSeconds'
                scoreName='Attentive Seconds'
                scoreBase='sec'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.attentiveSecondsDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'attentiveSecondsMedian',
        headerName: getHeaderWithExposureLabel(
            'Attentive Seconds (Norm)',
            'focused'
        ),
        renderHeader: () => 'Attentive Seconds (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 185,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='attentiveSecondsMedian'
                diffField='attentiveSecondsDiff'
                normField='attentiveSecondsMedian'
                scoreField='attentiveSeconds'
                scoreName='Attentive Seconds'
                scoreBase='sec'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <SecondBasedScoreCell value={row.attentiveSecondsMedian} />
            </CellWithTooltip>
        )
    },
    {
        field: 'attentiveSecondsRank',
        headerName: getHeaderWithExposureLabel(
            'Attentive Seconds (Rank)',
            'focused'
        ),
        renderHeader: () => 'Attentive Seconds (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 180,
        renderCell: ({ row }) => (
            <CellWithTooltip
                field='attentiveSecondsRank'
                row={row}
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
            >
                {row.attentiveSecondsRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'attentionAvg',
        headerName: getHeaderWithExposureLabel('Attention Avg.', 'focused'),
        renderHeader: () => 'Attention Avg.',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 110,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='attentionAvg'
                diffField='attentionAvgDiff'
                normField='attentionAvgMedian'
                scoreName='Attention Avg.'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.attentionAvg}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'attentionAvgDiff',
        headerName: getHeaderWithExposureLabel(
            'Attention Avg.',
            'focused',
            true
        ),
        renderHeader: () => 'Attention Avg. vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 115,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='attentionAvgDiff'
                diffField='attentionAvgDiff'
                normField='attentionAvgMedian'
                scoreField='attentionAvg'
                scoreName='Attention Avg.'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.attentionAvgDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'attentionAvgMedian',
        headerName: getHeaderWithExposureLabel(
            'Attention Avg. (Norm)',
            'focused'
        ),
        renderHeader: () => 'Attention Avg. (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 160,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='attentionAvgMedian'
                diffField='attentionAvgDiff'
                normField='attentionAvgMedian'
                scoreField='attentionAvg'
                scoreName='Attention Avg.'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.attentionAvgMedian}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'attentionAvgRank',
        headerName: getHeaderWithExposureLabel(
            'Attention Avg. (Rank)',
            'focused'
        ),
        renderHeader: () => 'Attention Avg. (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 160,
        renderCell: ({ row }) => (
            <CellWithTooltip
                field='attentionAvgRank'
                row={row}
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
            >
                {row.attentionAvgRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'attentiveSecondsVTR',
        headerName: getHeaderWithExposureLabel('Attentive VTR', 'focused'),
        renderHeader: () => 'Attentive VTR',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='attentiveSecondsVTR'
                diffField='attentiveSecondsVTRDiff'
                normField='attentiveSecondsVTRMedian'
                scoreName='Attentive VTR'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.attentiveSecondsVTR}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'attentiveSecondsVTRDiff',
        headerName: getHeaderWithExposureLabel(
            'Attentive VTR',
            'focused',
            true
        ),
        renderHeader: () => 'Attentive VTR vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='attentiveSecondsVTRDiff'
                diffField='attentiveSecondsVTRDiff'
                normField='attentiveSecondsVTRMedian'
                scoreField='attentiveSecondsVTR'
                scoreName='Attentive VTR'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.attentiveSecondsVTRDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'attentiveSecondsVTRMedian',
        headerName: getHeaderWithExposureLabel(
            'Attentive VTR (Norm)',
            'focused'
        ),
        renderHeader: () => 'Attentive VTR (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 160,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='attentiveSecondsVTRMedian'
                diffField='attentiveSecondsVTRDiff'
                normField='attentiveSecondsVTRMedian'
                scoreField='attentiveSecondsVTR'
                scoreName='Attentive VTR'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.attentiveSecondsVTRMedian}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'attentiveSecondsVTRRank',
        headerName: getHeaderWithExposureLabel(
            'Attentive VTR (Rank)',
            'focused'
        ),
        renderHeader: () => 'Attentive VTR (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 160,
        renderCell: ({ row }) => (
            <CellWithTooltip
                field='attentiveSecondsVTRRank'
                row={row}
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
            >
                {row.attentiveSecondsVTRRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'distractionAvg',
        headerName: getHeaderWithExposureLabel('Distraction Avg.', 'focused'),
        renderHeader: () => 'Distraction Avg.',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='distractionAvg'
                diffField='distractionAvgDiff'
                normField='distractionAvgMedian'
                scoreName='Distraction Avg.'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.distractionAvg}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'distractionAvgDiff',
        headerName: getHeaderWithExposureLabel(
            'Distraction Avg.',
            'focused',
            true
        ),
        renderHeader: () => 'Distraction Avg. vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 125,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='distractionAvgDiff'
                diffField='distractionAvgDiff'
                normField='distractionAvgMedian'
                scoreField='distractionAvg'
                scoreName='Distraction Avg.'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.distractionAvgDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'distractionAvgMedian',
        headerName: getHeaderWithExposureLabel(
            'Distraction Avg. (Norm)',
            'focused'
        ),
        renderHeader: () => 'Distraction Avg. (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 170,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='distractionAvgMedian'
                diffField='distractionAvgDiff'
                normField='distractionAvgMedian'
                scoreField='distractionAvg'
                scoreName='Distraction Avg.'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.distractionAvgMedian}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'distractionAvgRank',
        headerName: getHeaderWithExposureLabel(
            'Distraction Avg. (Rank)',
            'focused'
        ),
        renderHeader: () => 'Distraction Avg. (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 170,
        renderCell: ({ row }) => (
            <CellWithTooltip
                field='distractionAvgRank'
                row={row}
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
            >
                {row.distractionAvgRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'reactions',
        headerName: getHeaderWithExposureLabel('Reactions Avg.', 'focused'),
        renderHeader: () => 'Reactions Avg.',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 115,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='reactions'
                diffField='reactionsDiff'
                normField='reactionsMedian'
                scoreName='Reactions Avg.'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.reactions}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'reactionsDiff',
        headerName: getHeaderWithExposureLabel(
            'Reactions Avg.',
            'focused',
            true
        ),
        renderHeader: () => 'Reactions Avg. vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='reactionsDiff'
                diffField='reactionsDiff'
                normField='reactionsMedian'
                scoreField='reactions'
                scoreName='Reactions Avg.'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.reactionsDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'reactionsMedian',
        headerName: getHeaderWithExposureLabel(
            'Reactions Avg. (Norm)',
            'focused'
        ),
        renderHeader: () => 'Reactions Avg. (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 165,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='reactionsMedian'
                diffField='reactionsDiff'
                normField='reactionsMedian'
                scoreField='reactions'
                scoreName='Reactions Avg.'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.reactionsMedian}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'reactionsRank',
        headerName: getHeaderWithExposureLabel(
            'Reactions Avg. (Rank)',
            'focused'
        ),
        renderHeader: () => 'Reactions Avg. (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 160,
        renderCell: ({ row }) => (
            <CellWithTooltip
                field='reactionsRank'
                row={row}
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
            >
                {row.reactionsRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'negativityAvg',
        headerName: getHeaderWithExposureLabel('Negativity Avg.', 'focused'),
        renderHeader: () => 'Negativity Avg.',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 115,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='negativityAvg'
                diffField='negativityAvgDiff'
                normField='negativityAvgMedian'
                scoreName='Negativity Avg.'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.negativityAvg}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'negativityAvgDiff',
        headerName: getHeaderWithExposureLabel(
            'Negativity Avg.',
            'focused',
            true
        ),
        renderHeader: () => 'Negativity Avg. vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='negativityAvgDiff'
                diffField='negativityAvgDiff'
                normField='negativityAvgMedian'
                scoreField='negativityAvg'
                scoreName='Negativity Avg.'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.negativityAvgDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'negativityAvgMedian',
        headerName: getHeaderWithExposureLabel(
            'Negativity Avg. (Norm)',
            'focused'
        ),
        renderHeader: () => 'Negativity Avg. (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 165,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='negativityAvgMedian'
                diffField='negativityAvgDiff'
                normField='negativityAvgMedian'
                scoreField='negativityAvg'
                scoreName='Negativity Avg.'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.negativityAvgMedian}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'negativityAvgRank',
        headerName: getHeaderWithExposureLabel(
            'Negativity Avg. (Rank)',
            'focused'
        ),
        renderHeader: () => 'Negativity Avg. (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 160,
        renderCell: ({ row }) => (
            <CellWithTooltip
                field='negativityAvgRank'
                row={row}
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
            >
                {row.negativityAvgRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'happyPeak',
        headerName: getHeaderWithExposureLabel('Happy Peak', 'focused'),
        renderHeader: () => 'Happy Peak',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='happyPeak'
                diffField='happyPeakDiff'
                normField='happyPeakMedian'
                scoreName='Happy Peak'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.happyPeak}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'happyPeakDiff',
        headerName: getHeaderWithExposureLabel('Happy Peak', 'focused', true),
        renderHeader: () => 'Happy Peak vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='happyPeakDiff'
                diffField='happyPeakDiff'
                normField='happyPeakMedian'
                scoreField='happyPeak'
                scoreName='Happy Peak'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.happyPeakDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'happyPeakMedian',
        headerName: getHeaderWithExposureLabel('Happy Peak (Norm)', 'focused'),
        renderHeader: () => 'Happy Peak (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 150,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='happyPeakMedian'
                diffField='happyPeakDiff'
                normField='happyPeakMedian'
                scoreField='happyPeak'
                scoreName='Happy Peak'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.happyPeakMedian}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'happyPeakRank',
        headerName: getHeaderWithExposureLabel('Happy Peak (Rank)', 'focused'),
        renderHeader: () => 'Happy Peak (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 140,
        renderCell: ({ row }) => (
            <CellWithTooltip
                field='happyPeakRank'
                row={row}
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
            >
                {row.happyPeakRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'surprisePeak',
        headerName: getHeaderWithExposureLabel('Surprise Peak', 'focused'),
        renderHeader: () => 'Surprise Peak',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='surprisePeak'
                diffField='surprisePeakDiff'
                normField='surprisePeakMedian'
                scoreName='Surprise Peak'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.surprisePeak}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'surprisePeakDiff',
        headerName: getHeaderWithExposureLabel(
            'Surprise Peak',
            'focused',
            true
        ),
        renderHeader: () => 'Surprise Peak vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='surprisePeakDiff'
                diffField='surprisePeakDiff'
                normField='surprisePeakMedian'
                scoreField='surprisePeak'
                scoreName='Surprise Peak'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.surprisePeakDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'surprisePeakMedian',
        headerName: getHeaderWithExposureLabel(
            'Surprise Peak (Norm)',
            'focused'
        ),
        renderHeader: () => 'Surprise Peak (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 160,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='surprisePeakMedian'
                diffField='surprisePeakDiff'
                normField='surprisePeakMedian'
                scoreField='surprisePeak'
                scoreName='Surprise Peak'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.surprisePeakMedian}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'surprisePeakRank',
        headerName: getHeaderWithExposureLabel(
            'Surprise Peak (Rank)',
            'focused'
        ),
        renderHeader: () => 'Surprise Peak (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 160,
        renderCell: ({ row }) => (
            <CellWithTooltip
                field='surprisePeakRank'
                row={row}
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
            >
                {row.surprisePeakRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'confusionPeak',
        headerName: getHeaderWithExposureLabel('Confusion Peak', 'focused'),
        renderHeader: () => 'Confusion Peak',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 130,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='confusionPeak'
                diffField='confusionPeakDiff'
                normField='confusionPeakMedian'
                scoreName='Confusion Peak'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.confusionPeak}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'confusionPeakDiff',
        headerName: getHeaderWithExposureLabel(
            'Confusion Peak',
            'focused',
            true
        ),
        renderHeader: () => 'Confusion Peak vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 130,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='confusionPeakDiff'
                diffField='confusionPeakDiff'
                normField='confusionPeakMedian'
                scoreField='confusionPeak'
                scoreName='Confusion Peak'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.confusionPeakDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'confusionPeakMedian',
        headerName: getHeaderWithExposureLabel(
            'Confusion Peak (Norm)',
            'focused'
        ),
        renderHeader: () => 'Confusion Peak (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 170,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='confusionPeakMedian'
                diffField='confusionPeakDiff'
                normField='confusionPeakMedian'
                scoreField='confusionPeak'
                scoreName='Confusion Peak'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.confusionPeakMedian}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'confusionPeakRank',
        headerName: getHeaderWithExposureLabel(
            'Confusion Peak (Rank)',
            'focused'
        ),
        renderHeader: () => 'Confusion Peak (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 170,
        renderCell: ({ row }) => (
            <CellWithTooltip
                field='confusionPeakRank'
                row={row}
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
            >
                {row.confusionPeakRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'contemptPeak',
        headerName: getHeaderWithExposureLabel('Contempt Peak', 'focused'),
        renderHeader: () => 'Contempt Peak',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 130,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='contemptPeak'
                diffField='contemptPeakDiff'
                normField='contemptPeakMedian'
                scoreName='Contempt Peak'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.contemptPeak}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'contemptPeakDiff',
        headerName: getHeaderWithExposureLabel(
            'Contempt Peak',
            'focused',
            true
        ),
        renderHeader: () => 'Contempt Peak vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 130,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='contemptPeakDiff'
                diffField='contemptPeakDiff'
                normField='contemptPeakMedian'
                scoreField='contemptPeak'
                scoreName='Contempt Peak'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.contemptPeakDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'contemptPeakMedian',
        headerName: getHeaderWithExposureLabel(
            'Contempt Peak (Norm)',
            'focused'
        ),
        renderHeader: () => 'Contempt Peak (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 170,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='contemptPeakMedian'
                diffField='contemptPeakDiff'
                normField='contemptPeakMedian'
                scoreField='contemptPeak'
                scoreName='Contempt Peak'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.contemptPeakMedian}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'contemptPeakRank',
        headerName: getHeaderWithExposureLabel(
            'Contempt Peak (Rank)',
            'focused'
        ),
        renderHeader: () => 'Contempt Peak (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 170,
        renderCell: ({ row }) => (
            <CellWithTooltip
                field='contemptPeakRank'
                row={row}
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
            >
                {row.contemptPeakRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'disgustPeak',
        headerName: getHeaderWithExposureLabel('Disgust Peak', 'focused'),
        renderHeader: () => 'Disgust Peak',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='disgustPeak'
                diffField='disgustPeakDiff'
                normField='disgustPeakMedian'
                scoreName='Disgust Peak'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.disgustPeak}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'disgustPeakDiff',
        headerName: getHeaderWithExposureLabel('Disgust Peak', 'focused', true),
        renderHeader: () => 'Disgust Peak vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='disgustPeakDiff'
                diffField='disgustPeakDiff'
                normField='disgustPeakMedian'
                scoreField='disgustPeak'
                scoreName='Disgust Peak'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.disgustPeakDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'disgustPeakMedian',
        headerName: getHeaderWithExposureLabel(
            'Disgust Peak (Norm)',
            'focused'
        ),
        renderHeader: () => 'Disgust Peak (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 160,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='disgustPeakMedian'
                diffField='disgustPeakDiff'
                normField='disgustPeakMedian'
                scoreField='disgustPeak'
                scoreName='Disgust Peak'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.disgustPeakMedian}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'disgustPeakRank',
        headerName: getHeaderWithExposureLabel(
            'Disgust Peak (Rank)',
            'focused'
        ),
        renderHeader: () => 'Disgust Peak (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 150,
        renderCell: ({ row }) => (
            <CellWithTooltip
                field='disgustPeakRank'
                row={row}
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
            >
                {row.disgustPeakRank}
            </CellWithTooltip>
        )
    }
];

export const defaultCreativePerformanceForcedExposureColumnsVisibility: GridColumnVisibilityModel =
    {
        creationDate: true,
        topCategory: false,
        midCategory: false,
        subCategory: false,
        brand: true,
        order: true,
        customAdSets: false,
        sourceMedia: true,
        duration: true,
        device: false,
        geographicRegion: false,
        country: true,
        views: true,
        qualityScore: false,
        adformatTextIF: true,
        playbackSeconds: true,
        playbackSecondsDiff: true,
        playbackSecondsMedian: false,
        playbackSecondsRank: false,
        vtr: true,
        vtrDiff: true,
        vtrMedian: false,
        vtrRank: false,
        attentiveSeconds: true,
        attentiveSecondsDiff: true,
        attentiveSecondsMedian: false,
        attentiveSecondsRank: false,
        attentionAvg: true,
        attentionAvgDiff: true,
        attentionAvgMedian: false,
        attentionAvgRank: false,
        attentiveSecondsVTR: true,
        attentiveSecondsVTRDiff: true,
        attentiveSecondsVTRMedian: false,
        attentiveSecondsVTRRank: false,
        distractionAvg: true,
        distractionAvgDiff: true,
        distractionAvgMedian: false,
        distractionAvgRank: false,
        reactions: true,
        reactionsDiff: true,
        reactionsMedian: false,
        reactionsRank: false,
        negativityAvg: false,
        negativityAvgDiff: false,
        negativityAvgMedian: false,
        negativityAvgRank: false,
        happyPeak: true,
        happyPeakDiff: true,
        happyPeakMedian: false,
        happyPeakRank: false,
        surprisePeak: true,
        surprisePeakDiff: true,
        surprisePeakMedian: false,
        surprisePeakRank: false,
        confusionPeak: true,
        confusionPeakDiff: true,
        confusionPeakMedian: false,
        confusionPeakRank: false,
        contemptPeak: true,
        contemptPeakDiff: true,
        contemptPeakMedian: false,
        contemptPeakRank: false,
        disgustPeak: true,
        disgustPeakDiff: true,
        disgustPeakMedian: false,
        disgustPeakRank: false
    };

export const defaultCreativePerformanceForcedExposureSortModel: GridSortingInitialState =
    {
        sortModel: [
            { field: 'creationDate', sort: 'desc' },
            { field: 'brand', sort: 'desc' },
            { field: 'sourceMedia', sort: 'desc' }
        ]
    };

export const creativePerformanceForcedExposureColumnGroupModel: GridColumnGroupingModel =
    [
        {
            groupId: 'focusedGroup',
            headerName: ExposureLabel.Focused,
            children: [
                { field: 'adformatTextIF' },
                { field: 'playbackSeconds' },
                { field: 'playbackSecondsRank' },
                { field: 'playbackSecondsMedian' },
                { field: 'playbackSecondsDiff' },
                { field: 'vtr' },
                { field: 'vtrRank' },
                { field: 'vtrMedian' },
                { field: 'vtrDiff' },
                { field: 'attentiveSeconds' },
                { field: 'attentiveSecondsRank' },
                { field: 'attentiveSecondsMedian' },
                { field: 'attentiveSecondsDiff' },
                { field: 'attentionAvg' },
                { field: 'attentionAvgMedian' },
                { field: 'attentionAvgRank' },
                { field: 'attentionAvgDiff' },
                { field: 'attentiveSecondsVTR' },
                { field: 'attentiveSecondsVTRRank' },
                { field: 'attentiveSecondsVTRMedian' },
                { field: 'attentiveSecondsVTRDiff' },
                { field: 'distractionAvg' },
                { field: 'distractionAvgRank' },
                { field: 'distractionAvgMedian' },
                { field: 'distractionAvgDiff' },
                { field: 'reactions' },
                { field: 'reactionsRank' },
                { field: 'reactionsMedian' },
                { field: 'reactionsDiff' },
                { field: 'negativityAvg' },
                { field: 'negativityAvgRank' },
                { field: 'negativityAvgMedian' },
                { field: 'negativityAvgDiff' },
                { field: 'happyPeak' },
                { field: 'happyPeakRank' },
                { field: 'happyPeakMedian' },
                { field: 'happyPeakDiff' },
                { field: 'surprisePeak' },
                { field: 'surprisePeakRank' },
                { field: 'surprisePeakMedian' },
                { field: 'surprisePeakDiff' },
                { field: 'confusionPeak' },
                { field: 'confusionPeakRank' },
                { field: 'confusionPeakMedian' },
                { field: 'confusionPeakDiff' },
                { field: 'contemptPeak' },
                { field: 'contemptPeakRank' },
                { field: 'contemptPeakMedian' },
                { field: 'contemptPeakDiff' },
                { field: 'disgustPeak' },
                { field: 'disgustPeakRank' },
                { field: 'disgustPeakMedian' },
                { field: 'disgustPeakDiff' }
            ]
        }
    ];
