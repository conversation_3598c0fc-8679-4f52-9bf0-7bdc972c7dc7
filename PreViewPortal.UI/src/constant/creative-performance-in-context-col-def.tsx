import {
    GridColDef,
    GridColumnGroupingModel,
    GridColumnVisibilityModel,
    GridSortingInitialState
} from '@mui/x-data-grid-premium';
import {
    AdformatCell,
    OrderCell,
    BrandCell,
    CountOrValueCell,
    CountryCell,
    CreativeCell,
    DateCell,
    DiffCell,
    DurationCell,
    PercentageCell,
    QualityScoreCell,
    SecondBasedScoreCell,
    AdSetCell
} from '../components/GridRenderCell';
import { Typography } from '@mui/material';
import { getHeaderWithExposureLabel } from '@/helpers/get-header-with-exposure-label';
import { ExposureLabel } from '@/interfaces/exposure-label';
import { CellWithTooltip } from '@/components/CellWithTooltip';
import { CreativePerformanceInContextListModel } from '@/interfaces/creative-performance-in-context-list-model';

export const getCreativePerformanceInContextColDef = (
    isAdmin: boolean,
    showNormTooltip: boolean,
    segmentKeyLabel: string,
    hasCountry: boolean,
    hasIndustry: boolean,
    hasBrand: boolean
): GridColDef<CreativePerformanceInContextListModel>[] => [
    ...(isAdmin
        ? [
              {
                  field: 'isEnabledForClient',
                  headerName: 'Reporting Status',
                  width: 150,
                  renderCell: ({ row: { isEnabledForClients } }: any) => (
                      <Typography
                          variant='body2'
                          fontWeight='bold'
                          margin='revert'
                          color={
                              isEnabledForClients
                                  ? 'success.main'
                                  : 'error.main'
                          }
                      >
                          {isEnabledForClients === undefined
                              ? ''
                              : isEnabledForClients
                                ? 'Enabled'
                                : 'Disabled'}
                      </Typography>
                  )
              }
          ]
        : []),
    {
        field: 'creationDate',
        headerName: 'Date Tested',
        width: 150,
        renderCell: ({ row: { creationDate } }) => (
            <DateCell date={creationDate} />
        )
    },
    {
        field: 'topCategory',
        headerName: 'Industry (top level)',
        type: 'string',
        width: 150,
        renderCell: ({ row: { topCategory, topCategoryCount } }) => (
            <CountOrValueCell value={topCategory} count={topCategoryCount} />
        )
    },
    {
        field: 'midCategory',
        headerName: 'Industry (mid level)',
        type: 'string',
        width: 150,
        renderCell: ({ row: { midCategory, midCategoryCount } }) => (
            <CountOrValueCell value={midCategory} count={midCategoryCount} />
        )
    },
    {
        field: 'subCategory',
        headerName: 'Industry (sub level)',
        type: 'string',
        width: 150,
        renderCell: ({ row: { subCategory, subCategoryCount } }) => (
            <CountOrValueCell value={subCategory} count={subCategoryCount} />
        )
    },
    {
        field: 'brand',
        headerName: 'Brand',
        type: 'string',
        headerAlign: 'center',
        align: 'center',
        renderCell: ({ row: { brand, brandLogoUrl, brandCount } }) => (
            <BrandCell
                brand={brand!}
                brandLogoUrl={brandLogoUrl}
                brandCount={brandCount}
            />
        )
    },
    {
        field: 'order',
        headerName: 'Order',
        type: 'string',
        width: 260,
        renderCell: ({ row: { order, orderExternalKey } }) => (
            <OrderCell
                name={order!}
                externalKey={orderExternalKey!}
                isAdmin={isAdmin}
            />
        )
    },
    {
        field: 'customAdSets',
        headerName: 'Ad Set',
        type: 'string',
        width: 260,
        renderCell: ({ row: { customAdSets } }) => (
            <AdSetCell value={customAdSets!} />
        )
    },
    {
        field: 'sourceMedia',
        headerName: 'Creative',
        type: 'string',
        width: 260,
        renderCell: ({
            row: {
                sourceMedia,
                thumbnailUrl,
                sourceMediaID,
                testID,
                inFocusTestID,
                surveyKeyAlias
            }
        }) => (
            <CreativeCell
                sourceMediaID={sourceMediaID!}
                inFocusTestID={testID!}
                inContextTestID={inFocusTestID}
                name={sourceMedia}
                thumbnailUrl={thumbnailUrl}
                surveyKeyAlias={surveyKeyAlias!}
                isAdmin={isAdmin}
            />
        )
    },
    {
        field: 'duration',
        headerName: 'Duration',
        type: 'number',
        width: 90,
        headerAlign: 'center',
        align: 'center',
        renderCell: ({ row: { duration } }) => <DurationCell value={duration} />
    },
    {
        field: 'device',
        headerName: 'Device',
        type: 'string',
        width: 150,
        headerAlign: 'center',
        align: 'center',
        renderCell: ({ row: { device, deviceCount } }) => (
            <CountOrValueCell value={device} count={deviceCount} />
        )
    },
    {
        field: 'geographicRegion',
        headerName: 'Region',
        type: 'string',
        width: 150,
        renderCell: ({ row: { geographicRegion, geographicRegionCount } }) => (
            <CountOrValueCell
                value={geographicRegion}
                count={geographicRegionCount}
            />
        )
    },
    {
        field: 'country',
        headerName: 'Country',
        type: 'string',
        headerAlign: 'center',
        align: 'center',
        renderCell: ({ row: { country, country_code, countryCount } }) => (
            <CountryCell
                country={country!}
                countryCode={country_code!}
                countryCount={countryCount}
            />
        )
    },
    {
        field: 'views',
        headerName: 'Views',
        type: 'number',
        width: 80,
        headerAlign: 'center',
        align: 'center'
    },
    {
        field: 'qualityScore',
        headerName: 'Quality Score',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 115,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                field='qualityScore'
                row={row}
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
            >
                <QualityScoreCell
                    score={row.qualityScore!}
                    scoreIndex={row.qualityScore_index!}
                    fontSize={15}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'adformatText',
        headerName: getHeaderWithExposureLabel('Ad Format', 'inContext'),
        renderHeader: () => 'Ad Format',
        type: 'string',
        width: 285,
        renderCell: ({ row: { adformatText, platform, adformatCount } }) => (
            <AdformatCell
                adformatName={adformatText!}
                platform={platform!}
                adformatCount={adformatCount}
            />
        )
    },
    {
        field: 'playbackSeconds',
        headerName: getHeaderWithExposureLabel('Playback Seconds', 'inContext'),
        renderHeader: () => 'Playback Seconds',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 140,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                field='playbackSeconds'
                diffField='playbackSecondsDiff'
                normField='playbackSecondsMedian'
                scoreName='Playback Seconds'
                scoreBase='sec'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
            >
                <SecondBasedScoreCell value={row.playbackSeconds} />
            </CellWithTooltip>
        )
    },
    {
        field: 'playbackSecondsDiff',
        headerName: getHeaderWithExposureLabel(
            'Playback Seconds',
            'inContext',
            true
        ),
        renderHeader: () => 'Playback Seconds vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 140,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                field='playbackSecondsDiff'
                diffField='playbackSecondsDiff'
                normField='playbackSecondsMedian'
                scoreField='playbackSeconds'
                scoreName='Playback Seconds'
                scoreBase='sec'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
            >
                <DiffCell value={row.playbackSecondsDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'playbackSecondsMedian',
        headerName: getHeaderWithExposureLabel(
            'Playback Seconds (Norm)',
            'inContext'
        ),
        renderHeader: () => 'Playback Seconds (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 185,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                field='playbackSecondsMedian'
                diffField='playbackSecondsDiff'
                normField='playbackSecondsMedian'
                scoreField='playbackSeconds'
                scoreName='Playback Seconds'
                scoreBase='sec'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
            >
                <SecondBasedScoreCell value={row.playbackSecondsMedian} />
            </CellWithTooltip>
        )
    },
    {
        field: 'playbackSecondsRank',
        headerName: getHeaderWithExposureLabel(
            'Playback Seconds (Rank)',
            'inContext'
        ),
        renderHeader: () => 'Playback Seconds (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 185,
        renderCell: ({ row }) => (
            <CellWithTooltip
                field='playbackSecondsRank'
                row={row}
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
            >
                {row.playbackSecondsRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'vtr',
        headerName: getHeaderWithExposureLabel('VTR', 'inContext'),
        renderHeader: () => 'VTR',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='vtr'
                diffField='vtrDiff'
                normField='vtrMedian'
                scoreName='VTR'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.vtr}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'vtrDiff',
        headerName: getHeaderWithExposureLabel('VTR', 'inContext', true),
        renderHeader: () => 'VTR vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='vtrDiff'
                diffField='vtrDiff'
                normField='vtrMedian'
                scoreField='vtr'
                scoreName='VTR'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.vtrDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'vtrMedian',
        headerName: getHeaderWithExposureLabel('VTR (Norm)', 'inContext'),
        renderHeader: () => 'VTR (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='vtrMedian'
                diffField='vtrDiff'
                normField='vtrMedian'
                scoreField='vtr'
                scoreName='VTR'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.vtrMedian}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'vtrRank',
        headerName: getHeaderWithExposureLabel('VTR (Rank)', 'inContext'),
        renderHeader: () => 'VTR (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        renderCell: ({ row }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='vtrRank'
                row={row}
            >
                {row.vtrRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'attentiveSeconds',
        headerName: getHeaderWithExposureLabel(
            'Attentive Seconds',
            'inContext'
        ),
        renderHeader: () => 'Attentive Seconds',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 140,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='attentiveSeconds'
                diffField='attentiveSecondsDiff'
                normField='attentiveSecondsMedian'
                scoreName='Attentive Seconds'
                scoreBase='sec'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <SecondBasedScoreCell value={row.attentiveSeconds} />
            </CellWithTooltip>
        )
    },
    {
        field: 'attentiveSecondsDiff',
        headerName: getHeaderWithExposureLabel(
            'Attentive Seconds',
            'inContext',
            true
        ),
        renderHeader: () => 'Attentive Seconds vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 140,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='attentiveSecondsDiff'
                diffField='attentiveSecondsDiff'
                normField='attentiveSecondsMedian'
                scoreField='attentiveSeconds'
                scoreName='Attentive Seconds'
                scoreBase='sec'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.attentiveSecondsDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'attentiveSecondsMedian',
        headerName: getHeaderWithExposureLabel(
            'Attentive Seconds (Norm)',
            'inContext'
        ),
        renderHeader: () => 'Attentive Seconds (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 185,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='attentiveSecondsMedian'
                diffField='attentiveSecondsDiff'
                normField='attentiveSecondsMedian'
                scoreField='attentiveSeconds'
                scoreName='Attentive Seconds'
                scoreBase='sec'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <SecondBasedScoreCell value={row.attentiveSecondsMedian} />
            </CellWithTooltip>
        )
    },
    {
        field: 'attentiveSecondsRank',
        headerName: getHeaderWithExposureLabel(
            'Attentive Seconds (Rank)',
            'inContext'
        ),
        renderHeader: () => 'Attentive Seconds (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 180,
        renderCell: ({ row }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='attentiveSecondsRank'
                row={row}
            >
                {row.attentiveSecondsRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'attentionAvgIC',
        headerName: getHeaderWithExposureLabel('Attention Avg.', 'inContext'),
        renderHeader: () => 'Attention Avg.',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 110,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='attentionAvgIC'
                diffField='attentionAvgICDiff'
                normField='attentionAvgICMedian'
                scoreName='Attention Avg.'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.attentionAvgIC}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'attentionAvgICDiff',
        headerName: getHeaderWithExposureLabel(
            'Attention Avg.',
            'inContext',
            true
        ),
        renderHeader: () => 'Attention Avg. vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 115,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='attentionAvgICDiff'
                diffField='attentionAvgICDiff'
                normField='attentionAvgICMedian'
                scoreField='attentionAvgIC'
                scoreName='Attention Avg.'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.attentionAvgICDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'attentionAvgICMedian',
        headerName: getHeaderWithExposureLabel(
            'Attention Avg. (Norm)',
            'inContext'
        ),
        renderHeader: () => 'Attention Avg. (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 160,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='attentionAvgICMedian'
                diffField='attentionAvgICDiff'
                normField='attentionAvgICMedian'
                scoreField='attentionAvgIC'
                scoreName='Attention Avg.'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.attentionAvgICMedian}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'attentionAvgICRank',
        headerName: getHeaderWithExposureLabel(
            'Attention Avg. (Rank)',
            'inContext'
        ),
        renderHeader: () => 'Attention Avg. (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 160,
        renderCell: ({ row }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='attentionAvgICRank'
                row={row}
            >
                {row.attentionAvgICRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'attentiveSecondsVTR',
        headerName: getHeaderWithExposureLabel('Attentive VTR', 'inContext'),
        renderHeader: () => 'Attentive VTR',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='attentiveSecondsVTR'
                diffField='attentiveSecondsVTRDiff'
                normField='attentiveSecondsVTRMedian'
                scoreName='Attentive VTR'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.attentiveSecondsVTR}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'attentiveSecondsVTRDiff',
        headerName: getHeaderWithExposureLabel(
            'Attentive VTR',
            'inContext',
            true
        ),
        renderHeader: () => 'Attentive VTR vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='attentiveSecondsVTRDiff'
                diffField='attentiveSecondsVTRDiff'
                normField='attentiveSecondsVTRMedian'
                scoreField='attentiveSecondsVTR'
                scoreName='Attentive VTR'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.attentiveSecondsVTRDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'attentiveSecondsVTRMedian',
        headerName: getHeaderWithExposureLabel(
            'Attentive VTR (Norm)',
            'inContext'
        ),
        renderHeader: () => 'Attentive VTR (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 160,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='attentiveSecondsVTRMedian'
                diffField='attentiveSecondsVTRDiff'
                normField='attentiveSecondsVTRMedian'
                scoreField='attentiveSecondsVTR'
                scoreName='Attentive VTR'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.attentiveSecondsVTRMedian}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'attentiveSecondsVTRRank',
        headerName: getHeaderWithExposureLabel(
            'Attentive VTR (Rank)',
            'inContext'
        ),
        renderHeader: () => 'Attentive VTR (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 160,
        renderCell: ({ row }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='attentiveSecondsVTRRank'
                row={row}
            >
                {row.attentiveSecondsVTRRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'distractionAvgIC',
        headerName: getHeaderWithExposureLabel('Distraction Avg.', 'inContext'),
        renderHeader: () => 'Distraction Avg.',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='distractionAvgIC'
                diffField='distractionAvgICDiff'
                normField='distractionAvgICMedian'
                scoreName='Distraction Avg.'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.distractionAvgIC}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'distractionAvgICDiff',
        headerName: getHeaderWithExposureLabel(
            'Distraction Avg.',
            'inContext',
            true
        ),
        renderHeader: () => 'Distraction Avg. vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 125,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='distractionAvgICDiff'
                diffField='distractionAvgICDiff'
                normField='distractionAvgICMedian'
                scoreField='distractionAvgIC'
                scoreName='Distraction Avg.'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.distractionAvgICDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'distractionAvgICMedian',
        headerName: getHeaderWithExposureLabel(
            'Distraction Avg. (Norm)',
            'inContext'
        ),
        renderHeader: () => 'Distraction Avg. (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 170,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='distractionAvgICMedian'
                diffField='distractionAvgICDiff'
                normField='distractionAvgICMedian'
                scoreField='distractionAvgIC'
                scoreName='Distraction Avg.'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.distractionAvgICMedian}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'distractionAvgICRank',
        headerName: getHeaderWithExposureLabel(
            'Distraction Avg. (Rank)',
            'inContext'
        ),
        renderHeader: () => 'Distraction Avg. (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 170,
        renderCell: ({ row }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='distractionAvgICRank'
                row={row}
            >
                {row.distractionAvgICRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'reactionsIC',
        headerName: getHeaderWithExposureLabel('Reactions Avg.', 'inContext'),
        renderHeader: () => 'Reactions Avg.',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 115,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='reactionsIC'
                diffField='reactionsICDiff'
                normField='reactionsICMedian'
                scoreName='Reactions Avg.'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.reactionsIC}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'reactionsICDiff',
        headerName: getHeaderWithExposureLabel(
            'Reactions Avg.',
            'inContext',
            true
        ),
        renderHeader: () => 'Reactions Avg. vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='reactionsICDiff'
                diffField='reactionsICDiff'
                normField='reactionsICMedian'
                scoreField='reactionsIC'
                scoreName='Reactions Avg.'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.reactionsICDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'reactionsICMedian',
        headerName: getHeaderWithExposureLabel(
            'Reactions Avg. (Norm)',
            'inContext'
        ),
        renderHeader: () => 'Reactions Avg. (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 165,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='reactionsICMedian'
                diffField='reactionsICDiff'
                normField='reactionsICMedian'
                scoreField='reactionsIC'
                scoreName='Reactions Avg.'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.reactionsICMedian}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'reactionsICRank',
        headerName: getHeaderWithExposureLabel(
            'Reactions Avg. (Rank)',
            'inContext'
        ),
        renderHeader: () => 'Reactions Avg. (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 160,
        renderCell: ({ row }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='reactionsICRank'
                row={row}
            >
                {row.reactionsICRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'negativityAvgIC',
        headerName: getHeaderWithExposureLabel('Negativity Avg.', 'inContext'),
        renderHeader: () => 'Negativity Avg.',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 115,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='negativityAvgIC'
                diffField='negativityAvgICDiff'
                normField='negativityAvgICMedian'
                scoreName='Negativity Avg.'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.negativityAvgIC}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'negativityAvgICDiff',
        headerName: getHeaderWithExposureLabel(
            'Negativity Avg.',
            'inContext',
            true
        ),
        renderHeader: () => 'Negativity Avg. vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='negativityAvgICDiff'
                diffField='negativityAvgICDiff'
                normField='negativityAvgICMedian'
                scoreField='negativityAvgIC'
                scoreName='Negativity Avg.'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.negativityAvgICDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'negativityAvgICMedian',
        headerName: getHeaderWithExposureLabel(
            'Negativity Avg. (Norm)',
            'inContext'
        ),
        renderHeader: () => 'Negativity Avg. (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 165,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='negativityAvgICMedian'
                diffField='negativityAvgICDiff'
                normField='negativityAvgICMedian'
                scoreField='negativityAvgIC'
                scoreName='Negativity Avg.'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.negativityAvgICMedian}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'negativityAvgICRank',
        headerName: getHeaderWithExposureLabel(
            'Negativity Avg. (Rank)',
            'inContext'
        ),
        renderHeader: () => 'Negativity Avg. (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 160,
        renderCell: ({ row }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='negativityAvgICRank'
                row={row}
            >
                {row.negativityAvgICRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'happyPeakIC',
        headerName: getHeaderWithExposureLabel('Happy Peak', 'inContext'),
        renderHeader: () => 'Happy Peak',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='happyPeakIC'
                diffField='happyPeakICDiff'
                normField='happyPeakICMedian'
                scoreName='Happy Peak'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.happyPeakIC}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'happyPeakICDiff',
        headerName: getHeaderWithExposureLabel('Happy Peak', 'inContext', true),
        renderHeader: () => 'Happy Peak vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='happyPeakICDiff'
                diffField='happyPeakICDiff'
                normField='happyPeakICMedian'
                scoreField='happyPeakIC'
                scoreName='Happy Peak'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.happyPeakICDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'happyPeakICMedian',
        headerName: getHeaderWithExposureLabel(
            'Happy Peak (Norm)',
            'inContext'
        ),
        renderHeader: () => 'Happy Peak (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 150,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='happyPeakICMedian'
                diffField='happyPeakICDiff'
                normField='happyPeakICMedian'
                scoreField='happyPeakIC'
                scoreName='Happy Peak'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.happyPeakICMedian}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'happyPeakICRank',
        headerName: getHeaderWithExposureLabel(
            'Happy Peak (Rank)',
            'inContext'
        ),
        renderHeader: () => 'Happy Peak (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 140,
        renderCell: ({ row }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='happyPeakICRank'
                row={row}
            >
                {row.happyPeakICRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'surprisePeakIC',
        headerName: getHeaderWithExposureLabel('Surprise Peak', 'inContext'),
        renderHeader: () => 'Surprise Peak',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='surprisePeakIC'
                diffField='surprisePeakICDiff'
                normField='surprisePeakICMedian'
                scoreName='Surprise Peak'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.surprisePeakIC}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'surprisePeakICDiff',
        headerName: getHeaderWithExposureLabel(
            'Surprise Peak',
            'inContext',
            true
        ),
        renderHeader: () => 'Surprise Peak vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='surprisePeakICDiff'
                diffField='surprisePeakICDiff'
                normField='surprisePeakICMedian'
                scoreField='surprisePeakIC'
                scoreName='Surprise Peak'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.surprisePeakICDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'surprisePeakICMedian',
        headerName: getHeaderWithExposureLabel(
            'Surprise Peak (Norm)',
            'inContext'
        ),
        renderHeader: () => 'Surprise Peak (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 160,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='surprisePeakICMedian'
                diffField='surprisePeakICDiff'
                normField='surprisePeakICMedian'
                scoreField='surprisePeakIC'
                scoreName='Surprise Peak'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.surprisePeakICMedian}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'surprisePeakICRank',
        headerName: getHeaderWithExposureLabel(
            'Surprise Peak (Rank)',
            'inContext'
        ),
        renderHeader: () => 'Surprise Peak (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 160,
        renderCell: ({ row }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='surprisePeakICRank'
                row={row}
            >
                {row.surprisePeakICRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'confusionPeakIC',
        headerName: getHeaderWithExposureLabel('Confusion Peak', 'inContext'),
        renderHeader: () => 'Confusion Peak',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 130,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='confusionPeakIC'
                diffField='confusionPeakICDiff'
                normField='confusionPeakICMedian'
                scoreName='Confusion Peak'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.confusionPeakIC}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'confusionPeakICDiff',
        headerName: getHeaderWithExposureLabel(
            'Confusion Peak',
            'inContext',
            true
        ),
        renderHeader: () => 'Confusion Peak vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 130,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='confusionPeakICDiff'
                diffField='confusionPeakICDiff'
                normField='confusionPeakICMedian'
                scoreField='confusionPeakIC'
                scoreName='Confusion Peak'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.confusionPeakICDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'confusionPeakICMedian',
        headerName: getHeaderWithExposureLabel(
            'Confusion Peak (Norm)',
            'inContext'
        ),
        renderHeader: () => 'Confusion Peak (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 170,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='confusionPeakICMedian'
                diffField='confusionPeakICDiff'
                normField='confusionPeakICMedian'
                scoreField='confusionPeakIC'
                scoreName='Confusion Peak'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.confusionPeakICMedian}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'confusionPeakICRank',
        headerName: getHeaderWithExposureLabel(
            'Confusion Peak (Rank)',
            'inContext'
        ),
        renderHeader: () => 'Confusion Peak (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 170,
        renderCell: ({ row }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='confusionPeakICRank'
                row={row}
            >
                {row.confusionPeakICRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'contemptPeakIC',
        headerName: getHeaderWithExposureLabel('Contempt Peak', 'inContext'),
        renderHeader: () => 'Contempt Peak',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 130,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='contemptPeakIC'
                diffField='contemptPeakICDiff'
                normField='contemptPeakICMedian'
                scoreName='Contempt Peak'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.contemptPeakIC}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'contemptPeakICDiff',
        headerName: getHeaderWithExposureLabel(
            'Contempt Peak',
            'inContext',
            true
        ),
        renderHeader: () => 'Contempt Peak vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 130,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='contemptPeakICDiff'
                diffField='contemptPeakICDiff'
                normField='contemptPeakICMedian'
                scoreField='contemptPeakIC'
                scoreName='Contempt Peak'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.contemptPeakICDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'contemptPeakICMedian',
        headerName: getHeaderWithExposureLabel(
            'Contempt Peak (Norm)',
            'inContext'
        ),
        renderHeader: () => 'Contempt Peak (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 170,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='contemptPeakICMedian'
                diffField='contemptPeakICDiff'
                normField='contemptPeakICMedian'
                scoreField='contemptPeakIC'
                scoreName='Contempt Peak'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.contemptPeakICMedian}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'contemptPeakICRank',
        headerName: getHeaderWithExposureLabel(
            'Contempt Peak (Rank)',
            'inContext'
        ),
        renderHeader: () => 'Contempt Peak (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 170,
        renderCell: ({ row }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='contemptPeakICRank'
                row={row}
            >
                {row.contemptPeakICRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'disgustPeakIC',
        headerName: getHeaderWithExposureLabel('Disgust Peak', 'inContext'),
        renderHeader: () => 'Disgust Peak',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='disgustPeakIC'
                diffField='disgustPeakICDiff'
                normField='disgustPeakICMedian'
                scoreName='Disgust Peak'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.disgustPeakIC}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'disgustPeakICDiff',
        headerName: getHeaderWithExposureLabel(
            'Disgust Peak',
            'inContext',
            true
        ),
        renderHeader: () => 'Disgust Peak vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='disgustPeakICDiff'
                diffField='disgustPeakICDiff'
                normField='disgustPeakICMedian'
                scoreField='disgustPeakIC'
                scoreName='Disgust Peak'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.disgustPeakICDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'disgustPeakICMedian',
        headerName: getHeaderWithExposureLabel(
            'Disgust Peak (Norm)',
            'inContext'
        ),
        renderHeader: () => 'Disgust Peak (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 160,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='disgustPeakICMedian'
                diffField='disgustPeakICDiff'
                normField='disgustPeakICMedian'
                scoreField='disgustPeakIC'
                scoreName='Disgust Peak'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.disgustPeakICMedian}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'disgustPeakICRank',
        headerName: getHeaderWithExposureLabel(
            'Disgust Peak (Rank)',
            'inContext'
        ),
        renderHeader: () => 'Disgust Peak (Rank)',

        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 150,
        renderCell: ({ row }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='disgustPeakICRank'
                row={row}
            >
                {row.disgustPeakICRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'brandRecognition',
        headerName: getHeaderWithExposureLabel(
            'Brand Recognition',
            'inContext'
        ),
        renderHeader: () => 'Brand Recognition',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 150,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='brandRecognition'
                diffField='brandRecognitionDiff'
                normField='brandRecognitionMedian'
                scoreName='Brand Recognition'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.brandRecognition}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'brandRecognitionDiff',
        headerName: getHeaderWithExposureLabel(
            'Brand Recognition',
            'inContext',
            true
        ),
        renderHeader: () => 'Brand Recognition vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 150,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='brandRecognitionDiff'
                diffField='brandRecognitionDiff'
                normField='brandRecognitionMedian'
                scoreField='brandRecognition'
                scoreName='Brand Recognition'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.brandRecognitionDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'brandRecognitionMedian',
        headerName: getHeaderWithExposureLabel(
            'Brand Recognition (Norm)',
            'inContext'
        ),
        renderHeader: () => 'Brand Recognition (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 190,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='brandRecognitionMedian'
                diffField='brandRecognitionDiff'
                normField='brandRecognitionMedian'
                scoreField='brandRecognition'
                scoreName='Brand Recognition'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.brandRecognitionMedian}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'brandRecognitionRank',
        headerName: getHeaderWithExposureLabel(
            'Brand Recognition (Rank)',
            'inContext'
        ),
        renderHeader: () => 'Brand Recognition (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 190,
        renderCell: ({ row }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='brandRecognitionRank'
                row={row}
            >
                {row.brandRecognitionRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'adRecognition',
        headerName: getHeaderWithExposureLabel('Ad Recognition', 'inContext'),
        renderHeader: () => 'Ad Recognition',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 130,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='adRecognition'
                diffField='adRecognitionDiff'
                normField='adRecognitionMedian'
                scoreName='Ad Recognition'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.adRecognition}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'adRecognitionDiff',
        headerName: getHeaderWithExposureLabel(
            'Ad Recognition',
            'inContext',
            true
        ),
        renderHeader: () => 'Ad Recognition vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 130,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='adRecognitionDiff'
                diffField='adRecognitionDiff'
                normField='adRecognitionMedian'
                scoreField='adRecognition'
                scoreName='Ad Recognition'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.adRecognitionDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'adRecognitionMedian',
        headerName: getHeaderWithExposureLabel(
            'Ad Recognition (Norm)',
            'inContext'
        ),
        renderHeader: () => 'Ad Recognition (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 170,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='adRecognitionMedian'
                diffField='adRecognitionDiff'
                normField='adRecognitionMedian'
                scoreField='adRecognition'
                scoreName='Ad Recognition'
                scoreBase='%'
                row={row}
                exposureGroup='inContext'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.adRecognitionMedian}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'adRecognitionRank',
        headerName: getHeaderWithExposureLabel(
            'Ad Recognition (Rank)',
            'inContext'
        ),
        renderHeader: () => 'Ad Recognition (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 170,
        renderCell: ({ row }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='adRecognitionRank'
                row={row}
            >
                {row.adRecognitionRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'adformatTextIF',
        headerName: getHeaderWithExposureLabel('Ad Format', 'focused'),
        renderHeader: () => 'Ad Format',
        type: 'string',
        width: 285,
        align: 'center',
        renderCell: ({ row: { adformatTextIF }, rowNode: { type } }) =>
            type === 'pinnedRow' ? (
                1
            ) : (
                <AdformatCell
                    adformatName={adformatTextIF!}
                    platform={'Realeyes'}
                />
            )
    },
    {
        field: 'playbackSecondsIF',
        headerName: getHeaderWithExposureLabel('Playback Seconds', 'focused'),
        renderHeader: () => 'Playback Seconds',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 140,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='playbackSecondsIF'
                diffField='playbackSecondsIFDiff'
                normField='playbackSecondsIFMedian'
                scoreName='Playback Seconds'
                scoreBase='sec'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <SecondBasedScoreCell value={row.playbackSecondsIF} />
            </CellWithTooltip>
        )
    },
    {
        field: 'playbackSecondsIFDiff',
        headerName: getHeaderWithExposureLabel(
            'Playback Seconds',
            'focused',
            true
        ),
        renderHeader: () => 'Playback Seconds vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 140,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='playbackSecondsIFDiff'
                diffField='playbackSecondsIFDiff'
                normField='playbackSecondsIFMedian'
                scoreField='playbackSecondsIF'
                scoreName='Playback Seconds'
                scoreBase='sec'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.playbackSecondsIFDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'playbackSecondsIFMedian',
        headerName: getHeaderWithExposureLabel(
            'Playback Seconds (Norm)',
            'focused'
        ),
        renderHeader: () => 'Playback Seconds (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 185,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='playbackSecondsIFMedian'
                diffField='playbackSecondsIFDiff'
                normField='playbackSecondsIFMedian'
                scoreField='playbackSecondsIF'
                scoreName='Playback Seconds'
                scoreBase='sec'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <SecondBasedScoreCell value={row.playbackSecondsIFMedian} />
            </CellWithTooltip>
        )
    },
    {
        field: 'playbackSecondsIFRank',
        headerName: getHeaderWithExposureLabel(
            'Playback Seconds (Rank)',
            'focused'
        ),
        renderHeader: () => 'Playback Seconds (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 180,
        renderCell: ({ row }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='playbackSecondsIFRank'
                row={row}
            >
                {row.playbackSecondsIFRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'vtrif',
        headerName: getHeaderWithExposureLabel('VTR', 'focused'),
        renderHeader: () => 'VTR',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='vtrif'
                diffField='vtrifDiff'
                normField='vtrifMedian'
                scoreName='VTR'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.vtrif}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'vtrifDiff',
        headerName: getHeaderWithExposureLabel('VTR', 'focused', true),
        renderHeader: () => 'VTR vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 100,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='vtrifDiff'
                diffField='vtrifDiff'
                normField='vtrifMedian'
                scoreField='vtrif'
                scoreName='VTR'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.vtrifDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'vtrifMedian',
        headerName: getHeaderWithExposureLabel('VTR (Norm)', 'focused'),
        renderHeader: () => 'VTR (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='vtrifMedian'
                diffField='vtrifDiff'
                normField='vtrifMedian'
                scoreField='vtrifMedian'
                scoreName='VTR'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.vtrifMedian}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'vtrifRank',
        headerName: getHeaderWithExposureLabel('VTR (Rank)', 'focused'),
        renderHeader: () => 'VTR (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        renderCell: ({ row }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='vtrifRank'
                row={row}
            >
                {row.vtrifRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'attentiveSecondsIF',
        headerName: getHeaderWithExposureLabel('Attentive Seconds', 'focused'),
        renderHeader: () => 'Attentive Seconds',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 140,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='attentiveSecondsIF'
                diffField='attentiveSecondsIFDiff'
                normField='attentiveSecondsIFMedian'
                scoreName='Attentive Seconds'
                scoreBase='sec'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <SecondBasedScoreCell value={row.attentiveSecondsIF} />
            </CellWithTooltip>
        )
    },
    {
        field: 'attentiveSecondsIFDiff',
        headerName: getHeaderWithExposureLabel(
            'Attentive Seconds',
            'focused',
            true
        ),
        renderHeader: () => 'Attentive Seconds vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 140,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='attentiveSecondsIFDiff'
                diffField='attentiveSecondsIFDiff'
                normField='attentiveSecondsIFMedian'
                scoreField='attentiveSecondsIF'
                scoreName='Attentive Seconds'
                scoreBase='sec'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.attentiveSecondsIFDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'attentiveSecondsIFMedian',
        headerName: getHeaderWithExposureLabel(
            'Attentive Seconds  (Norm)',
            'focused'
        ),
        renderHeader: () => 'Attentive Seconds  (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 190,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='attentiveSecondsIFMedian'
                diffField='attentiveSecondsIFDiff'
                normField='attentiveSecondsIFMedian'
                scoreField='attentiveSecondsIF'
                scoreName='Attentive Seconds'
                scoreBase='sec'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <SecondBasedScoreCell value={row.attentiveSecondsIFMedian} />
            </CellWithTooltip>
        )
    },
    {
        field: 'attentiveSecondsIFRank',
        headerName: getHeaderWithExposureLabel(
            'Attentive Seconds  (Rank)',
            'focused'
        ),
        renderHeader: () => 'Attentive Seconds  (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 190,
        renderCell: ({ row }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='attentiveSecondsIFRank'
                row={row}
            >
                {row.attentiveSecondsIFRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'attentionAvgIF',
        headerName: getHeaderWithExposureLabel('Attention Avg.', 'focused'),
        renderHeader: () => 'Attention Avg.',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 110,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='attentionAvgIF'
                diffField='attentionAvgIFDiff'
                normField='attentionAvgIFMedian'
                scoreName='Attentive Avg.'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.attentionAvgIF}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'attentionAvgIFDiff',
        headerName: getHeaderWithExposureLabel(
            'Attention Avg.',
            'focused',
            true
        ),
        renderHeader: () => 'Attention Avg. vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 115,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='attentionAvgIFDiff'
                diffField='attentionAvgIFDiff'
                normField='attentionAvgIFMedian'
                scoreField='attentionAvgIF'
                scoreName='Attentive Avg.'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.attentionAvgIFDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'attentionAvgIFMedian',
        headerName: getHeaderWithExposureLabel(
            'Attention Avg. (Norm)',
            'focused'
        ),
        renderHeader: () => 'Attention Avg. (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 160,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='attentionAvgIFMedian'
                diffField='attentionAvgIFDiff'
                normField='attentionAvgIFMedian'
                scoreField='attentionAvgIF'
                scoreName='Attentive Avg.'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.attentionAvgIFMedian}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'attentionAvgIFRank',
        headerName: getHeaderWithExposureLabel(
            'Attention Avg. (Rank)',
            'focused'
        ),
        renderHeader: () => 'Attention Avg. (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 160,
        renderCell: ({ row }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='attentionAvgIFRank'
                row={row}
            >
                {row.attentionAvgIFRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'attentiveSecondsVTRIF',
        headerName: getHeaderWithExposureLabel('Attentive VTR', 'focused'),
        renderHeader: () => 'Attentive VTR',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='attentiveSecondsVTRIF'
                diffField='attentiveSecondsVTRIFDiff'
                normField='attentiveSecondsVTRIFMedian'
                scoreName='Attentive VTR'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.attentiveSecondsVTRIF}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'attentiveSecondsVTRIFDiff',
        headerName: getHeaderWithExposureLabel(
            'Attentive VTR',
            'focused',
            true
        ),
        renderHeader: () => 'Attentive VTR vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='attentiveSecondsVTRIFDiff'
                diffField='attentiveSecondsVTRIFDiff'
                normField='attentiveSecondsVTRIFMedian'
                scoreField='attentiveSecondsVTRIF'
                scoreName='Attentive VTR'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.attentiveSecondsVTRIFDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'attentiveSecondsVTRIFMedian',
        headerName: getHeaderWithExposureLabel(
            'Attentive VTR (Norm)',
            'focused'
        ),
        renderHeader: () => 'Attentive VTR (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 160,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='attentiveSecondsVTRIFMedian'
                diffField='attentiveSecondsVTRIFDiff'
                normField='attentiveSecondsVTRIFMedian'
                scoreField='attentiveSecondsVTRIF'
                scoreName='Attentive VTR'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.attentiveSecondsVTRIFMedian}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'attentiveSecondsVTRIFRank',
        headerName: getHeaderWithExposureLabel(
            'Attentive VTR (Rank)',
            'focused'
        ),
        renderHeader: () => 'Attentive VTR (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 160,
        renderCell: ({ row }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='attentiveSecondsVTRIFRank'
                row={row}
            >
                {row.attentiveSecondsVTRIFRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'distractionAvgIF',
        headerName: getHeaderWithExposureLabel('Distraction Avg.', 'focused'),
        renderHeader: () => 'Distraction Avg.',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='distractionAvgIF'
                diffField='distractionAvgIFDiff'
                normField='distractionAvgIFMedian'
                scoreName='Distraction Avg.'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.distractionAvgIF}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'distractionAvgIFDiff',
        headerName: getHeaderWithExposureLabel(
            'Distraction Avg.',
            'focused',
            true
        ),
        renderHeader: () => 'Distraction Avg. vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 125,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='distractionAvgIFDiff'
                diffField='distractionAvgIFDiff'
                normField='distractionAvgIFMedian'
                scoreField='distractionAvgIF'
                scoreName='Distraction Avg.'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.distractionAvgIFDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'distractionAvgIFMedian',
        headerName: getHeaderWithExposureLabel(
            'Distraction Avg. (Norm)',
            'focused'
        ),
        renderHeader: () => 'Distraction Avg. (Norm)',

        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 170,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='distractionAvgIFMedian'
                diffField='distractionAvgIFDiff'
                normField='distractionAvgIFMedian'
                scoreField='distractionAvgIF'
                scoreName='Distraction Avg.'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.distractionAvgIFMedian}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'distractionAvgIFRank',
        headerName: getHeaderWithExposureLabel(
            'Distraction Avg. (Rank)',
            'focused'
        ),
        renderHeader: () => 'Distraction Avg. (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 170,
        renderCell: ({ row }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='distractionAvgIFRank'
                row={row}
            >
                {row.distractionAvgIFRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'reactions',
        headerName: getHeaderWithExposureLabel('Reactions Avg.', 'focused'),
        renderHeader: () => 'Reactions Avg.',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 115,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='reactions'
                diffField='reactionsDiff'
                normField='reactionsMedian'
                scoreName='Reactions Avg.'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.reactions}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'reactionsDiff',
        headerName: getHeaderWithExposureLabel(
            'Reactions Avg.',
            'focused',
            true
        ),
        renderHeader: () => 'Reactions Avg. vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='reactionsDiff'
                diffField='reactionsDiff'
                normField='reactionsMedian'
                scoreField='reactions'
                scoreName='Reactions Avg.'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.reactionsDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'reactionsMedian',
        headerName: getHeaderWithExposureLabel(
            'Reactions Avg. (Norm)',
            'focused'
        ),
        renderHeader: () => 'Reactions Avg. (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 165,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='reactionsMedian'
                diffField='reactionsDiff'
                normField='reactionsMedian'
                scoreField='reactions'
                scoreName='Reactions Avg.'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.reactionsMedian}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'reactionsRank',
        headerName: getHeaderWithExposureLabel(
            'Reactions Avg. (Rank)',
            'focused'
        ),
        renderHeader: () => 'Reactions Avg. (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 160,
        renderCell: ({ row }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='reactionsRank'
                row={row}
            >
                {row.reactionsRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'negativityAvgIF',
        headerName: getHeaderWithExposureLabel('Negativity Avg.', 'focused'),
        renderHeader: () => 'Negativity Avg.',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 115,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='negativityAvgIF'
                diffField='negativityAvgIFDiff'
                normField='negativityAvgIFMedian'
                scoreName='Negativity Avg.'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.negativityAvgIF}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'negativityAvgIFDiff',
        headerName: getHeaderWithExposureLabel(
            'Negativity Avg.',
            'focused',
            true
        ),
        renderHeader: () => 'Negativity Avg. vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='negativityAvgIFDiff'
                diffField='negativityAvgIFDiff'
                normField='negativityAvgIFMedian'
                scoreField='negativityAvgIF'
                scoreName='Negativity Avg.'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.negativityAvgIFDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'negativityAvgIFMedian',
        headerName: getHeaderWithExposureLabel(
            'Negativity Avg. (Norm)',
            'focused'
        ),
        renderHeader: () => 'Negativity Avg. (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 165,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='negativityAvgIFMedian'
                diffField='negativityAvgIFDiff'
                normField='negativityAvgIFMedian'
                scoreField='negativityAvgIF'
                scoreName='Negativity Avg.'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.negativityAvgIFMedian}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'negativityAvgIFRank',
        headerName: getHeaderWithExposureLabel(
            'Negativity Avg. (Rank)',
            'focused'
        ),
        renderHeader: () => 'Negativity Avg. (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 160,
        renderCell: ({ row }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='negativityAvgIFRank'
                row={row}
            >
                {row.negativityAvgIFRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'happyPeak',
        headerName: getHeaderWithExposureLabel('Happy Peak', 'focused'),
        renderHeader: () => 'Happy Peak',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='happyPeak'
                diffField='happyPeakDiff'
                normField='happyPeakMedian'
                scoreName='Happy Peak'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.happyPeak}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'happyPeakDiff',
        headerName: getHeaderWithExposureLabel('Happy Peak', 'focused', true),
        renderHeader: () => 'Happy Peak vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='happyPeakDiff'
                diffField='happyPeakDiff'
                normField='happyPeakMedian'
                scoreField='happyPeak'
                scoreName='Happy Peak'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.happyPeakDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'happyPeakMedian',
        headerName: getHeaderWithExposureLabel('Happy Peak (Norm)', 'focused'),
        renderHeader: () => 'Happy Peak (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 150,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='happyPeakMedian'
                diffField='happyPeakDiff'
                normField='happyPeakMedian'
                scoreField='happyPeak'
                scoreName='Happy Peak'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.happyPeakMedian}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'happyPeakRank',
        headerName: getHeaderWithExposureLabel('Happy Peak (Rank)', 'focused'),
        renderHeader: () => 'Happy Peak (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 140,
        renderCell: ({ row }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='happyPeakRank'
                row={row}
            >
                {row.happyPeakRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'surprisePeak',
        headerName: getHeaderWithExposureLabel('Surprise Peak', 'focused'),
        renderHeader: () => 'Surprise Peak',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='surprisePeak'
                diffField='surprisePeakDiff'
                normField='surprisePeakMedian'
                scoreName='Surprise Peak'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.surprisePeak}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'surprisePeakDiff',
        headerName: getHeaderWithExposureLabel(
            'Surprise Peak',
            'focused',
            true
        ),
        renderHeader: () => 'Surprise Peak vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='surprisePeakDiff'
                diffField='surprisePeakDiff'
                normField='surprisePeakMedian'
                scoreField='surprisePeak'
                scoreName='Surprise Peak'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.surprisePeakDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'surprisePeakMedian',
        headerName: getHeaderWithExposureLabel(
            'Surprise Peak (Norm)',
            'focused'
        ),
        renderHeader: () => 'Surprise Peak (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 160,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='surprisePeakMedian'
                diffField='surprisePeakDiff'
                normField='surprisePeakMedian'
                scoreField='surprisePeak'
                scoreName='Surprise Peak'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.surprisePeakMedian}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'surprisePeakRank',
        headerName: getHeaderWithExposureLabel(
            'Surprise Peak (Rank)',
            'focused'
        ),
        renderHeader: () => 'Surprise Peak (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 160,
        renderCell: ({ row }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='surprisePeakRank'
                row={row}
            >
                {row.surprisePeakRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'confusionPeak',
        headerName: getHeaderWithExposureLabel('Confusion Peak', 'focused'),
        renderHeader: () => 'Confusion Peak',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 130,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='confusionPeak'
                diffField='confusionPeakDiff'
                normField='confusionPeakMedian'
                scoreName='Confusion Peak'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.confusionPeak}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'confusionPeakDiff',
        headerName: getHeaderWithExposureLabel(
            'Confusion Peak',
            'focused',
            true
        ),
        renderHeader: () => 'Confusion Peak vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 130,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='confusionPeakDiff'
                diffField='confusionPeakDiff'
                normField='confusionPeakMedian'
                scoreField='confusionPeak'
                scoreName='Confusion Peak'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.confusionPeakDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'confusionPeakMedian',
        headerName: getHeaderWithExposureLabel(
            'Confusion Peak (Norm)',
            'focused'
        ),
        renderHeader: () => 'Confusion Peak (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 170,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='confusionPeakMedian'
                diffField='confusionPeakDiff'
                normField='confusionPeakMedian'
                scoreField='confusionPeak'
                scoreName='Confusion Peak'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.confusionPeakMedian}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'confusionPeakRank',
        headerName: getHeaderWithExposureLabel(
            'Confusion Peak (Rank)',
            'focused'
        ),
        renderHeader: () => 'Confusion Peak (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 170,
        renderCell: ({ row }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='confusionPeakRank'
                row={row}
            >
                {row.confusionPeakRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'contemptPeak',
        headerName: getHeaderWithExposureLabel('Contempt Peak', 'focused'),
        renderHeader: () => 'Contempt Peak',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 130,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='contemptPeak'
                diffField='contemptPeakDiff'
                normField='contemptPeakMedian'
                scoreName='Contempt Peak'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.contemptPeak}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'contemptPeakDiff',
        headerName: getHeaderWithExposureLabel(
            'Contempt Peak',
            'focused',
            true
        ),
        renderHeader: () => 'Contempt Peak vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 130,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='contemptPeakDiff'
                diffField='contemptPeakDiff'
                normField='contemptPeakMedian'
                scoreField='contemptPeak'
                scoreName='Contempt Peak'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.contemptPeakDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'contemptPeakMedian',
        headerName: getHeaderWithExposureLabel(
            'Contempt Peak (Norm)',
            'focused'
        ),
        renderHeader: () => 'Contempt Peak (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 170,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='contemptPeakMedian'
                diffField='contemptPeakDiff'
                normField='contemptPeakMedian'
                scoreField='contemptPeak'
                scoreName='Contempt Peak'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.contemptPeakMedian}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'contemptPeakRank',
        headerName: getHeaderWithExposureLabel(
            'Contempt Peak (Rank)',
            'focused'
        ),
        renderHeader: () => 'Contempt Peak (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 170,
        renderCell: ({ row }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='contemptPeakRank'
                row={row}
            >
                {row.contemptPeakRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'disgustPeak',
        headerName: getHeaderWithExposureLabel('Disgust Peak', 'focused'),
        renderHeader: () => 'Disgust Peak',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='disgustPeak'
                diffField='disgustPeakDiff'
                normField='disgustPeakMedian'
                scoreName='Disgust Peak'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.disgustPeak}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },

    {
        field: 'disgustPeakDiff',
        headerName: getHeaderWithExposureLabel('Disgust Peak', 'focused', true),
        renderHeader: () => 'Disgust Peak vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='disgustPeakDiff'
                diffField='disgustPeakDiff'
                normField='disgustPeakMedian'
                scoreField='disgustPeak'
                scoreName='Disgust Peak'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.disgustPeakDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'disgustPeakMedian',
        headerName: getHeaderWithExposureLabel(
            'Disgust Peak (Norm)',
            'focused'
        ),
        renderHeader: () => 'Disgust Peak (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 160,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='disgustPeakMedian'
                diffField='disgustPeakDiff'
                normField='disgustPeakMedian'
                scoreField='disgustPeak'
                scoreName='Disgust Peak'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.disgustPeakMedian}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'disgustPeakRank',
        headerName: getHeaderWithExposureLabel(
            'Disgust Peak (Rank)',
            'focused'
        ),
        renderHeader: () => 'Disgust Peak (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 150,
        renderCell: ({ row }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='disgustPeakRank'
                row={row}
            >
                {row.disgustPeakRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'adLikeability',
        headerName: getHeaderWithExposureLabel('Ad Likeability', 'focused'),
        renderHeader: () => 'Ad Likeability',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='adLikeability'
                diffField='adLikeabilityDiff'
                normField='adLikeabilityMedian'
                scoreName='Ad Likeability'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                {row.adLikeability?.toFixed(1)}
            </CellWithTooltip>
        )
    },
    {
        field: 'adLikeabilityDiff',
        headerName: getHeaderWithExposureLabel(
            'Ad Likeability',
            'focused',
            true
        ),
        renderHeader: () => 'Ad Likeability vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='adLikeabilityDiff'
                diffField='adLikeabilityDiff'
                normField='adLikeabilityMedian'
                scoreField='adLikeability'
                scoreName='Ad Likeability'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.adLikeabilityDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'adLikeabilityMedian',
        headerName: getHeaderWithExposureLabel(
            'Ad Likeability (Norm)',
            'focused'
        ),
        renderHeader: () => 'Ad Likeability (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 150,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='adLikeabilityMedian'
                diffField='adLikeabilityDiff'
                normField='adLikeabilityMedian'
                scoreField='adLikeability'
                scoreName='Ad Likeability'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                {row.adLikeabilityMedian?.toFixed(1)}
            </CellWithTooltip>
        )
    },
    {
        field: 'adLikeabilityRank',
        headerName: getHeaderWithExposureLabel(
            'Ad Likeability (Rank)',
            'focused'
        ),
        renderHeader: () => 'Ad Likeability (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 150,
        renderCell: ({ row }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='adLikeabilityRank'
                row={row}
            >
                {row.adLikeabilityRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'brandTrust',
        headerName: getHeaderWithExposureLabel('Brand Trust', 'focused'),
        renderHeader: () => 'Brand Trust',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 140,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='brandTrust'
                diffField='brandTrustDiff'
                normField='brandTrustMedian'
                scoreName='Brand Trust'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                {row.brandTrust?.toFixed(1)}
            </CellWithTooltip>
        )
    },
    {
        field: 'brandTrustDiff',
        headerName: getHeaderWithExposureLabel('Brand Trust', 'focused', true),
        renderHeader: () => 'Brand Trust vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 140,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='brandTrustDiff'
                diffField='brandTrustDiff'
                normField='brandTrustMedian'
                scoreField='brandTrust'
                scoreName='Brand Trust'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.brandTrustDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'brandTrustMedian',
        headerName: getHeaderWithExposureLabel('Brand Trust (Norm)', 'focused'),
        renderHeader: () => 'Brand Trust (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 140,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='brandTrustMedian'
                diffField='brandTrustDiff'
                normField='brandTrustMedian'
                scoreField='brandTrust'
                scoreName='Brand Trust'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                {row.brandTrustMedian?.toFixed(1)}
            </CellWithTooltip>
        )
    },
    {
        field: 'brandTrustRank',
        headerName: getHeaderWithExposureLabel('Brand Trust (Rank)', 'focused'),
        renderHeader: () => 'Brand Trust (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 140,
        renderCell: ({ row }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='brandTrustRank'
                row={row}
            >
                {row.brandTrustRank}
            </CellWithTooltip>
        )
    },
    {
        field: 'persuasion',
        headerName: getHeaderWithExposureLabel('Persuasion', 'focused'),
        renderHeader: () => 'Persuasion',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='persuasion'
                diffField='persuasionDiff'
                normField='persuasionMedian'
                scoreName='Persuasion'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.persuasion}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'persuasionDiff',
        headerName: getHeaderWithExposureLabel('Persuasion', 'focused', true),
        renderHeader: () => 'Persuasion vs Norm',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 120,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='persuasionDiff'
                diffField='persuasionDiff'
                normField='persuasionMedian'
                scoreField='persuasion'
                scoreName='Persuasion'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <DiffCell value={row.persuasionDiff} />
            </CellWithTooltip>
        )
    },
    {
        field: 'persuasionMedian',
        headerName: getHeaderWithExposureLabel('Persuasion (Norm)', 'focused'),
        renderHeader: () => 'Persuasion (Norm)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 150,
        renderCell: ({ row, rowNode: { type } }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='persuasionMedian'
                diffField='persuasionDiff'
                normField='persuasionMedian'
                scoreField='persuasion'
                scoreName='Persuasion'
                scoreBase='%'
                row={row}
                exposureGroup='focused'
                showNormTooltip={showNormTooltip}
                segmentKeyLabel={segmentKeyLabel}
                rowType={type}
            >
                <PercentageCell
                    value={row.persuasionMedian}
                    fractionDigits={+(type === 'pinnedRow')}
                />
            </CellWithTooltip>
        )
    },
    {
        field: 'persuasionRank',
        headerName: getHeaderWithExposureLabel('Persuasion (Rank)', 'focused'),
        renderHeader: () => 'Persuasion (Rank)',
        type: 'number',
        align: 'center',
        headerAlign: 'center',
        width: 140,
        renderCell: ({ row }) => (
            <CellWithTooltip
                hasCountry={hasCountry}
                hasIndustry={hasIndustry}
                hasBrand={hasBrand}
                field='persuasionRank'
                row={row}
            >
                {row.persuasionRank}
            </CellWithTooltip>
        )
    }
];

export const defaultCreativePerformanceInContextColumnsVisibility: GridColumnVisibilityModel =
    {
        creationDate: true,
        topCategory: false,
        midCategory: false,
        subCategory: false,
        brand: true,
        order: true,
        customAdSets: false,
        sourceMedia: true,
        duration: true,
        device: false,
        geographicRegion: false,
        country: true,
        views: true,
        qualityScore: false,
        adformatText: true,
        playbackSeconds: false,
        playbackSecondsDiff: false,
        playbackSecondsMedian: false,
        playbackSecondsRank: false,
        vtr: false,
        vtrDiff: false,
        vtrMedian: false,
        vtrRank: false,
        attentiveSeconds: false,
        attentiveSecondsDiff: false,
        attentiveSecondsMedian: false,
        attentiveSecondsRank: false,
        attentionAvgIC: false,
        attentionAvgICDiff: false,
        attentionAvgICMedian: false,
        attentionAvgICRank: false,
        attentiveSecondsVTR: false,
        attentiveSecondsVTRDiff: false,
        attentiveSecondsVTRMedian: false,
        attentiveSecondsVTRRank: false,
        distractionAvgIC: false,
        distractionAvgICDiff: false,
        distractionAvgICMedian: false,
        distractionAvgICRank: false,
        reactionsIC: false,
        reactionsICDiff: false,
        reactionsICMedian: false,
        reactionsICRank: false,
        negativityAvgIC: false,
        negativityAvgICDiff: false,
        negativityAvgICMedian: false,
        negativityAvgICRank: false,
        happyPeakIC: false,
        happyPeakICDiff: false,
        happyPeakICMedian: false,
        happyPeakICRank: false,
        surprisePeakIC: false,
        surprisePeakICDiff: false,
        surprisePeakICMedian: false,
        surprisePeakICRank: false,
        confusionPeakIC: false,
        confusionPeakICDiff: false,
        confusionPeakICMedian: false,
        confusionPeakICRank: false,
        contemptPeakIC: false,
        contemptPeakICDiff: false,
        contemptPeakICMedian: false,
        contemptPeakICRank: false,
        disgustPeakIC: false,
        disgustPeakICDiff: false,
        disgustPeakICMedian: false,
        disgustPeakICRank: false,
        brandRecognition: true,
        brandRecognitionDiff: true,
        brandRecognitionMedian: false,
        brandRecognitionRank: false,
        adRecognition: true,
        adRecognitionDiff: true,
        adRecognitionMedian: false,
        adRecognitionRank: false,
        adformatTextIF: true,
        playbackSecondsIF: false,
        playbackSecondsIFDiff: false,
        playbackSecondsIFMedian: false,
        playbackSecondsIFRank: false,
        vtrif: false,
        vtrifDiff: false,
        vtrifMedian: false,
        vtrifRank: false,
        attentiveSecondsIF: false,
        attentiveSecondsIFDiff: false,
        attentiveSecondsIFMedian: false,
        attentiveSecondsIFRank: false,
        attentionAvgIF: false,
        attentionAvgIFDiff: false,
        attentionAvgIFMedian: false,
        attentionAvgIFRank: false,
        attentiveSecondsVTRIF: false,
        attentiveSecondsVTRIFDiff: false,
        attentiveSecondsVTRIFMedian: false,
        attentiveSecondsVTRIFRank: false,
        distractionAvgIF: false,
        distractionAvgIFDiff: false,
        distractionAvgIFMedian: false,
        distractionAvgIFRank: false,
        reactions: false,
        reactionsDiff: false,
        reactionsMedian: false,
        reactionsRank: false,
        negativityAvgIF: false,
        negativityAvgIFDiff: false,
        negativityAvgIFMedian: false,
        negativityAvgIFRank: false,
        happyPeak: false,
        happyPeakDiff: false,
        happyPeakMedian: false,
        happyPeakRank: false,
        surprisePeak: false,
        surprisePeakDiff: false,
        surprisePeakMedian: false,
        surprisePeakRank: false,
        confusionPeak: false,
        confusionPeakDiff: false,
        confusionPeakMedian: false,
        confusionPeakRank: false,
        contemptPeak: false,
        contemptPeakDiff: false,
        contemptPeakMedian: false,
        contemptPeakRank: false,
        disgustPeak: false,
        disgustPeakDiff: false,
        disgustPeakMedian: false,
        disgustPeakRank: false,
        adLikeability: true,
        adLikeabilityDiff: true,
        adLikeabilityMedian: false,
        adLikeabilityRank: false,
        brandTrust: true,
        brandTrustDiff: true,
        brandTrustMedian: false,
        brandTrustRank: false,
        persuasion: true,
        persuasionDiff: true,
        persuasionMedian: false,
        persuasionRank: false
    };

export const defaultCreativePerformanceInContextSortModel: GridSortingInitialState =
    {
        sortModel: [
            { field: 'creationDate', sort: 'desc' },
            { field: 'brand', sort: 'desc' },
            { field: 'sourceMedia', sort: 'desc' },
            { field: 'adformatText', sort: 'desc' }
        ]
    };

export const creativePerformanceInContextColumnGroupModel: GridColumnGroupingModel =
    [
        {
            groupId: 'inContextGroup',
            headerName: ExposureLabel.InContext,
            children: [
                { field: 'adformatText' },
                { field: 'playbackSeconds' },
                { field: 'playbackSecondsRank' },
                { field: 'playbackSecondsMedian' },
                { field: 'playbackSecondsDiff' },
                { field: 'vtr' },
                { field: 'vtrRank' },
                { field: 'vtrMedian' },
                { field: 'vtrDiff' },
                { field: 'attentiveSeconds' },
                { field: 'attentiveSecondsRank' },
                { field: 'attentiveSecondsMedian' },
                { field: 'attentiveSecondsDiff' },
                { field: 'attentionAvgIC' },
                { field: 'attentionAvgICMedian' },
                { field: 'attentionAvgICRank' },
                { field: 'attentionAvgICDiff' },
                { field: 'attentiveSecondsVTR' },
                { field: 'attentiveSecondsVTRRank' },
                { field: 'attentiveSecondsVTRMedian' },
                { field: 'attentiveSecondsVTRDiff' },
                { field: 'distractionAvgIC' },
                { field: 'distractionAvgICRank' },
                { field: 'distractionAvgICMedian' },
                { field: 'distractionAvgICDiff' },
                { field: 'reactionsIC' },
                { field: 'reactionsICRank' },
                { field: 'reactionsICMedian' },
                { field: 'reactionsICDiff' },
                { field: 'negativityAvgIC' },
                { field: 'negativityAvgICRank' },
                { field: 'negativityAvgICMedian' },
                { field: 'negativityAvgICDiff' },
                { field: 'happyPeakIC' },
                { field: 'happyPeakICRank' },
                { field: 'happyPeakICMedian' },
                { field: 'happyPeakICDiff' },
                { field: 'surprisePeakIC' },
                { field: 'surprisePeakICRank' },
                { field: 'surprisePeakICMedian' },
                { field: 'surprisePeakICDiff' },
                { field: 'confusionPeakIC' },
                { field: 'confusionPeakICRank' },
                { field: 'confusionPeakICMedian' },
                { field: 'confusionPeakICDiff' },
                { field: 'contemptPeakIC' },
                { field: 'contemptPeakICRank' },
                { field: 'contemptPeakICMedian' },
                { field: 'contemptPeakICDiff' },
                { field: 'disgustPeakIC' },
                { field: 'disgustPeakICRank' },
                { field: 'disgustPeakICMedian' },
                { field: 'disgustPeakICDiff' },
                { field: 'brandRecognition' },
                { field: 'brandRecognitionRank' },
                { field: 'brandRecognitionMedian' },
                { field: 'brandRecognitionDiff' },
                { field: 'adRecognition' },
                { field: 'adRecognitionRank' },
                { field: 'adRecognitionMedian' },
                { field: 'adRecognitionDiff' }
            ]
        },
        {
            groupId: 'focusedGroup',
            headerName: ExposureLabel.Focused,
            children: [
                { field: 'adformatTextIF' },
                { field: 'playbackSecondsIF' },
                { field: 'playbackSecondsIFRank' },
                { field: 'playbackSecondsIFMedian' },
                { field: 'playbackSecondsIFDiff' },
                { field: 'adformatTextIFDiff' },
                { field: 'vtrif' },
                { field: 'vtrifRank' },
                { field: 'vtrifMedian' },
                { field: 'vtrifDiff' },
                { field: 'attentiveSecondsIF' },
                { field: 'attentiveSecondsIFRank' },
                { field: 'attentiveSecondsIFMedian' },
                { field: 'attentiveSecondsIFDiff' },
                { field: 'attentionAvgIF' },
                { field: 'attentionAvgIFMedian' },
                { field: 'attentionAvgIFRank' },
                { field: 'attentionAvgIFDiff' },
                { field: 'attentiveSecondsVTRIF' },
                { field: 'attentiveSecondsVTRIFRank' },
                { field: 'attentiveSecondsVTRIFMedian' },
                { field: 'attentiveSecondsVTRIFDiff' },
                { field: 'distractionAvgIF' },
                { field: 'distractionAvgIFRank' },
                { field: 'distractionAvgIFMedian' },
                { field: 'distractionAvgIFDiff' },
                { field: 'reactions' },
                { field: 'reactionsRank' },
                { field: 'reactionsMedian' },
                { field: 'reactionsDiff' },
                { field: 'negativityAvgIF' },
                { field: 'negativityAvgIFRank' },
                { field: 'negativityAvgIFMedian' },
                { field: 'negativityAvgIFDiff' },
                { field: 'happyPeak' },
                { field: 'happyPeakRank' },
                { field: 'happyPeakMedian' },
                { field: 'happyPeakDiff' },
                { field: 'surprisePeak' },
                { field: 'surprisePeakRank' },
                { field: 'surprisePeakMedian' },
                { field: 'surprisePeakDiff' },
                { field: 'confusionPeak' },
                { field: 'confusionPeakRank' },
                { field: 'confusionPeakMedian' },
                { field: 'confusionPeakDiff' },
                { field: 'contemptPeak' },
                { field: 'contemptPeakRank' },
                { field: 'contemptPeakMedian' },
                { field: 'contemptPeakDiff' },
                { field: 'disgustPeak' },
                { field: 'disgustPeakRank' },
                { field: 'disgustPeakMedian' },
                { field: 'disgustPeakDiff' },
                { field: 'adLikeability' },
                { field: 'adLikeabilityRank' },
                { field: 'adLikeabilityMedian' },
                { field: 'adLikeabilityDiff' },
                { field: 'brandTrust' },
                { field: 'brandTrustRank' },
                { field: 'brandTrustMedian' },
                { field: 'brandTrustDiff' },
                { field: 'persuasion' },
                { field: 'persuasionRank' },
                { field: 'persuasionMedian' },
                { field: 'persuasionDiff' }
            ]
        }
    ];
