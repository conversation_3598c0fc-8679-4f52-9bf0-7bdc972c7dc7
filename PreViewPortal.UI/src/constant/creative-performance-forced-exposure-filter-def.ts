import { CreativePerformanceForcedExposureListModel } from '@/interfaces/creative-performance-forced-exposure-list-model';
import { CustomGridFilterDefinition } from '../interfaces/custom-grid-filter-definition';
import { CustomGridFilterInput } from '../interfaces/custom-grid-filter-input';

export const getCreativePerformanceForcedExposureFilterDef = (
    isAdmin: boolean
): CustomGridFilterDefinition<CreativePerformanceForcedExposureListModel>[] => [
    {
        field: 'sourceMedia',
        label: 'Creative',
        filterType: CustomGridFilterInput.TextField,
        defaultVisible: true
    },
    ...(isAdmin
        ? [
              {
                  field: 'isEnabledForClients',
                  label: 'Reporting Status',
                  filterType: CustomGridFilterInput.SelectBoolean,
                  defaultVisible: false,
                  valueToTextMap: new Map<string, string>([
                      ['true', 'Enabled'],
                      ['false', 'Disabled']
                  ])
              } as any
          ]
        : []),
    {
        field: 'order',
        label: 'Order',
        filterType: CustomGridFilterInput.TextField,
        defaultVisible: true
    },
    {
        field: 'customAdSets',
        label: 'Ad Set',
        filterType: CustomGridFilterInput.TextField,
        defaultVisible: false
    },
    {
        field: 'duration',
        label: 'Duration',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'durationOptions',
        step: 1,
        postfix: 's'
    },
    {
        field: 'device',
        label: 'Device',
        filterType: CustomGridFilterInput.Autocomplete,
        defaultVisible: false,
        optionsName: 'deviceOptions'
    },
    {
        field: 'creationDate',
        label: 'Date Tested',
        filterType: CustomGridFilterInput.DatePickerDateTimeOffset,
        defaultVisible: true,
        optionsName: 'creationDateOptions'
    },
    {
        field: 'topCategory',
        label: 'Top level',
        groupLabel: 'Industry Level',
        filterType: CustomGridFilterInput.MultiAutocomplete,
        defaultVisible: false,
        optionsName: 'topCategoryOptions',
        orQueryGroupId: 1
    },
    {
        field: 'midCategory',
        label: 'Mid level',
        filterType: CustomGridFilterInput.MultiAutocomplete,
        defaultVisible: false,
        optionsName: 'midCategoryOptions',
        orQueryGroupId: 1,
        noRender: true
    },
    {
        field: 'subCategory',
        label: 'Sub level',
        filterType: CustomGridFilterInput.MultiAutocomplete,
        defaultVisible: false,
        optionsName: 'subCategoryOptions',
        orQueryGroupId: 1,
        noRender: true
    },
    {
        field: 'brand',
        label: 'Brand',
        filterType: CustomGridFilterInput.Autocomplete,
        defaultVisible: true,
        optionsName: 'brandOptions'
    },
    {
        field: 'country',
        label: 'Country',
        filterType: CustomGridFilterInput.Autocomplete,
        defaultVisible: false,
        optionsName: 'countryOptions'
    },
    {
        field: 'views',
        label: 'Views',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'viewsOptions',
        step: 1
    },
    {
        field: 'geographicRegion',
        label: 'Region',
        filterType: CustomGridFilterInput.Autocomplete,
        defaultVisible: false,
        optionsName: 'regionOptions'
    },
    {
        field: 'playbackSeconds',
        label: 'Playback Seconds',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'playbackSecondsOptions',
        step: 1,
        postfix: 's',
        exposureGroup: 'focused'
    },
    {
        field: 'playbackSecondsDiff',
        label: 'Playback Seconds',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'playbackSecondsDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused',
        isDiff: true
    },
    {
        field: 'playbackSecondsMedian',
        label: 'Playback Seconds (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'playbackSecondsMedianOptions',
        step: 1,
        postfix: 's',
        exposureGroup: 'focused'
    },
    {
        field: 'playbackSecondsRank',
        label: 'Playback Seconds (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'playbackSecondsRankOptions',
        step: 1,
        exposureGroup: 'focused'
    },
    {
        field: 'vtr',
        label: 'VTR',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'vtrOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'vtrDiff',
        label: 'VTR',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'vtrDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused',
        isDiff: true
    },
    {
        field: 'vtrMedian',
        label: 'VTR (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'vtrMedianOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'vtrRank',
        label: 'VTR (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'vtrRankOptions',
        step: 1,
        exposureGroup: 'focused'
    },
    {
        field: 'attentiveSeconds',
        label: 'Attentive Seconds',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'attentiveSecondsOptions',
        step: 1,
        postfix: 's',
        exposureGroup: 'focused'
    },
    {
        field: 'attentiveSecondsDiff',
        label: 'Attentive Seconds',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'attentiveSecondsDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused',
        isDiff: true
    },
    {
        field: 'attentiveSecondsMedian',
        label: 'Attentive Seconds (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'attentiveSecondsMedianOptions',
        step: 1,
        postfix: 's',
        exposureGroup: 'focused'
    },
    {
        field: 'attentiveSecondsRank',
        label: 'Attentive Seconds (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'attentiveSecondsRankOptions',
        step: 1,
        exposureGroup: 'focused'
    },
    {
        field: 'attentionAvg',
        label: 'Attention Avg.',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'attentionAvgOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'attentionAvgDiff',
        label: 'Attention Avg.',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'attentionAvgDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused',
        isDiff: true
    },
    {
        field: 'attentionAvgMedian',
        label: 'Attention Avg. (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'attentionAvgMedianOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'attentionAvgRank',
        label: 'Attention Avg. (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'attentionAvgRankOptions',
        exposureGroup: 'focused'
    },
    {
        field: 'attentiveSecondsVTR',
        label: 'Attentive VTR',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'attentiveSecondsVTROptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'attentiveSecondsVTRDiff',
        label: 'Attentive VTR',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'attentiveSecondsVTRDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'attentiveSecondsVTRMedian',
        label: 'Attentive VTR (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'attentiveSecondsVTRMedianOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'attentiveSecondsVTRRank',
        label: 'Attentive VTR (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'attentiveSecondsVTRRankOptions',
        step: 1,
        exposureGroup: 'focused'
    },
    {
        field: 'distractionAvg',
        label: 'Distraction Avg.',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'distractionAvgOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'distractionAvgDiff',
        label: 'Distraction Avg.',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'distractionAvgDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused',
        isDiff: true
    },
    {
        field: 'distractionAvgMedian',
        label: 'Distraction Avg. (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'distractionAvgMedianOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'distractionAvgRank',
        label: 'Distraction Avg. (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'distractionAvgRankOptions',
        step: 1,
        exposureGroup: 'focused'
    },
    {
        field: 'reactions',
        label: 'Reactions Avg.',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'reactionsOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'reactionsDiff',
        label: 'Reactions Avg.',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'reactionsDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused',
        isDiff: true
    },
    {
        field: 'reactionsMedian',
        label: 'Reactions Avg. (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'reactionsMedianOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'reactionsRank',
        label: 'Reactions Avg. (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'reactionsRankOptions',
        step: 1,
        exposureGroup: 'focused'
    },
    {
        field: 'negativityAvg',
        label: 'Negativity Avg.',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'negativityAvgOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'negativityAvgDiff',
        label: 'Negativity Avg.',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'negativityAvgDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused',
        isDiff: true
    },
    {
        field: 'negativityAvgMedian',
        label: 'Negativity Avg. (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'negativityAvgMedianOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'negativityAvgRank',
        label: 'Negativity Avg. (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'negativityAvgRankOptions',
        step: 1,
        exposureGroup: 'focused'
    },
    {
        field: 'happyPeak',
        label: 'Happy Peak',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'happyPeakOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'happyPeakDiff',
        label: 'Happy Peak',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'happyPeakDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused',
        isDiff: true
    },
    {
        field: 'happyPeakMedian',
        label: 'Happy Peak (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'happyPeakMedianOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'happyPeakRank',
        label: 'Happy Peak (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'happyPeakRankOptions',
        step: 1,
        exposureGroup: 'focused'
    },
    {
        field: 'surprisePeak',
        label: 'Surprise Peak',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'surprisePeakOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'surprisePeakDiff',
        label: 'Surprise Peak',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'surprisePeakDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused',
        isDiff: true
    },
    {
        field: 'surprisePeakMedian',
        label: 'Surprise Peak (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'surprisePeakMedianOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'surprisePeakRank',
        label: 'Surprise Peak (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'surprisePeakRankOptions',
        step: 1,
        exposureGroup: 'focused'
    },
    {
        field: 'confusionPeak',
        label: 'Confusion Peak',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'confusionPeakOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'confusionPeakDiff',
        label: 'Confusion Peak',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'confusionPeakDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused',
        isDiff: true
    },
    {
        field: 'confusionPeakMedian',
        label: 'Confusion Peak (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'confusionPeakMedianOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'confusionPeakRank',
        label: 'Confusion Peak (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'confusionPeakRankOptions',
        step: 1,
        exposureGroup: 'focused'
    },
    {
        field: 'contemptPeak',
        label: 'Contempt Peak',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'contemptPeakOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'contemptPeakDiff',
        label: 'Contempt Peak',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'contemptPeakDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused',
        isDiff: true
    },
    {
        field: 'contemptPeakMedian',
        label: 'Contempt Peak (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'contemptPeakMedianOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'contemptPeakRank',
        label: 'Contempt Peak (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'contemptPeakRankOptions',
        step: 1,
        exposureGroup: 'focused'
    },
    {
        field: 'disgustPeak',
        label: 'Disgust Peak',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'disgustPeakOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'disgustPeakDiff',
        label: 'Disgust Peak',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'disgustPeakDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused',
        isDiff: true
    },
    {
        field: 'disgustPeakMedian',
        label: 'Disgust Peak (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'disgustPeakMedianOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'disgustPeakRank',
        label: 'Disgust Peak (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'disgustPeakRankOptions',
        step: 1,
        exposureGroup: 'focused'
    }
];
