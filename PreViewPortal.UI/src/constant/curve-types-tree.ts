import { CreativeViewerCurveType } from '@/interfaces/creative-viewer-curve-type';

export enum TreeNode {
    First = 43,
    Middle = 36,
    Step = 25,
    Last = 18.7
}

//prettier-ignore
export const curveTypesTree = [
    { type: CreativeViewerCurveType.Playback, level: 0, node: TreeNode.First },
    { type: CreativeViewerCurveType.Distraction, level: 1, node: TreeNode.First },
    { type: CreativeViewerCurveType.Attention, level: 1, node: TreeNode.Last },
    { type: CreativeViewerCurveType.NeutralAttention, level: 2, node: TreeNode.First },
    { type: CreativeViewerCurveType.AllReactions, level: 2, node: TreeNode.Last },
    { type: CreativeViewerCurveType.Happiness, level: 3, node: TreeNode.First },
    { type: CreativeViewerCurveType.Surprise, level:3 , node: TreeNode.Middle },
    { type: CreativeViewerCurveType.Confusion, level: 3, node: TreeNode.Middle },
    { type: CreativeViewerCurveType.Contempt, level: 3, node: TreeNode.Middle },
    { type: CreativeViewerCurveType.Disgust, level: 3, node: TreeNode.Last },
    { type: CreativeViewerCurveType.Negativity, level: 0, node: TreeNode.First }
];