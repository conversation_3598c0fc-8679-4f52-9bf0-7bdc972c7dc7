import { CreativePerformanceInContextListModel } from '@/interfaces/creative-performance-in-context-list-model';
import { CustomGridFilterDefinition } from '../interfaces/custom-grid-filter-definition';
import { CustomGridFilterInput } from '../interfaces/custom-grid-filter-input';

export const getCreativePerformanceInContextFilterDef = (
    isAdmin: boolean
): CustomGridFilterDefinition<CreativePerformanceInContextListModel>[] => [
    {
        field: 'sourceMedia',
        label: 'Creative',
        filterType: CustomGridFilterInput.TextField,
        defaultVisible: true
    },
    ...(isAdmin
        ? [
              {
                  field: 'isEnabledForClients',
                  label: 'Reporting Status',
                  filterType: CustomGridFilterInput.SelectBoolean,
                  defaultVisible: false,
                  valueToTextMap: new Map<string, string>([
                      ['true', 'Enabled'],
                      ['false', 'Disabled']
                  ])
              } as any
          ]
        : []),
    {
        field: 'order',
        label: 'Order',
        filterType: CustomGridFilterInput.TextField,
        defaultVisible: true
    },
    {
        field: 'customAdSets',
        label: 'Ad Set',
        filterType: CustomGridFilterInput.TextField,
        defaultVisible: false
    },
    {
        field: 'creationDate',
        label: 'Date Tested',
        filterType: CustomGridFilterInput.DatePickerDateTimeOffset,
        defaultVisible: true,
        optionsName: 'creationDateOptions'
    },
    {
        field: 'brand',
        label: 'Brand',
        filterType: CustomGridFilterInput.Autocomplete,
        defaultVisible: true,
        optionsName: 'brandOptions'
    },
    {
        field: 'duration',
        label: 'Duration',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'durationOptions',
        step: 1,
        postfix: 's'
    },
    {
        field: 'device',
        label: 'Device',
        filterType: CustomGridFilterInput.Autocomplete,
        defaultVisible: false,
        optionsName: 'deviceOptions'
    },
    {
        field: 'topCategory',
        label: 'Top level',
        groupLabel: 'Industry Level',
        filterType: CustomGridFilterInput.MultiAutocomplete,
        defaultVisible: false,
        optionsName: 'topCategoryOptions',
        orQueryGroupId: 1
    },
    {
        field: 'midCategory',
        label: 'Mid level',
        filterType: CustomGridFilterInput.MultiAutocomplete,
        defaultVisible: false,
        optionsName: 'midCategoryOptions',
        orQueryGroupId: 1,
        noRender: true
    },
    {
        field: 'subCategory',
        label: 'Sub level',
        filterType: CustomGridFilterInput.MultiAutocomplete,
        defaultVisible: false,
        optionsName: 'subCategoryOptions',
        orQueryGroupId: 1,
        noRender: true
    },
    {
        field: 'country',
        label: 'Country',
        filterType: CustomGridFilterInput.Autocomplete,
        defaultVisible: false,
        optionsName: 'countryOptions'
    },
    {
        field: 'geographicRegion',
        label: 'Region',
        filterType: CustomGridFilterInput.Autocomplete,
        defaultVisible: false,
        optionsName: 'regionOptions'
    },
    {
        field: 'views',
        label: 'Views',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'viewsOptions',
        step: 1
    },
    {
        field: 'qualityScore',
        label: 'Quality Score',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'qualityScoreOptions',
        step: 1
    },
    {
        field: 'adformatText',
        label: 'Ad Format',
        filterType: CustomGridFilterInput.Autocomplete,
        defaultVisible: true,
        optionsName: 'adformatTextOptions',
        exposureGroup: 'inContext'
    },
    {
        field: 'playbackSeconds',
        label: 'Playback Seconds',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'playbackSecondsOptions',
        step: 1,
        postfix: 's',
        exposureGroup: 'inContext'
    },
    {
        field: 'playbackSecondsDiff',
        label: 'Playback Seconds',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'playbackSecondsDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext',
        isDiff: true
    },
    {
        field: 'playbackSecondsMedian',
        label: 'Playback Seconds (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'playbackSecondsMedianOptions',
        step: 1,
        postfix: 's',
        exposureGroup: 'inContext'
    },
    {
        field: 'playbackSecondsRank',
        label: 'Playback Seconds (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'playbackSecondsRankOptions',
        step: 1,
        exposureGroup: 'inContext'
    },
    {
        field: 'vtr',
        label: 'VTR',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'vtrOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext'
    },
    {
        field: 'vtrDiff',
        label: 'VTR',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'vtrOptionsDiffoptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext',
        isDiff: true
    },
    {
        field: 'vtrMedian',
        label: 'VTR (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'vtrMedianOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext'
    },
    {
        field: 'vtrRank',
        label: 'VTR (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'vtrRankOptions',
        step: 1,
        exposureGroup: 'inContext'
    },
    {
        field: 'attentiveSeconds',
        label: 'Attentive Seconds',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'attentiveSecondsOptions',
        step: 1,
        postfix: 's',
        exposureGroup: 'inContext'
    },
    {
        field: 'attentiveSecondsDiff',
        label: 'Attentive Seconds',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'attentiveSecondsDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext',
        isDiff: true
    },
    {
        field: 'attentiveSecondsMedian',
        label: 'Attentive Seconds (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'attentiveSecondsMedianOptions',
        step: 1,
        postfix: 's',
        exposureGroup: 'inContext'
    },
    {
        field: 'attentiveSecondsRank',
        label: 'Attentive Seconds (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'attentiveSecondsRankOptions',
        step: 1,
        exposureGroup: 'inContext'
    },
    {
        field: 'attentionAvgIC',
        label: 'Attention Avg.',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'attentionAvgICOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext'
    },
    {
        field: 'attentionAvgICDiff',
        label: 'Attention Avg.',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'attentionAvgICDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext',
        isDiff: true
    },
    {
        field: 'attentionAvgICMedian',
        label: 'Attention Avg. (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'attentionAvgICMedianOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext'
    },
    {
        field: 'attentionAvgICRank',
        label: 'Attention Avg. (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'attentionAvgICRankOptions',
        exposureGroup: 'inContext'
    },
    {
        field: 'attentiveSecondsVTR',
        label: 'Attentive VTR',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'attentiveSecondsVTROptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext'
    },
    {
        field: 'attentiveSecondsVTRDiff',
        label: 'Attentive VTR',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'attentiveSecondsVTRDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext',
        isDiff: true
    },
    {
        field: 'attentiveSecondsVTRMedian',
        label: 'Attentive VTR (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'attentiveSecondsVTRMedianOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext'
    },
    {
        field: 'attentiveSecondsVTRRank',
        label: 'Attentive VTR (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'attentiveSecondsVTRRankOptions',
        step: 1,
        exposureGroup: 'inContext'
    },
    {
        field: 'distractionAvgIC',
        label: 'Distraction Avg.',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'distractionAvgICOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext'
    },
    {
        field: 'distractionAvgICDiff',
        label: 'Distraction Avg.',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'distractionAvgICDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext',
        isDiff: true
    },
    {
        field: 'distractionAvgICMedian',
        label: 'Distraction Avg. (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'distractionAvgICMedianOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext'
    },
    {
        field: 'distractionAvgICRank',
        label: 'Distraction Avg. (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'distractionAvgICRankOptions',
        step: 1,
        exposureGroup: 'inContext'
    },
    {
        field: 'reactionsIC',
        label: 'Reactions Avg.',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'reactionsICOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext'
    },
    {
        field: 'reactionsICDiff',
        label: 'Reactions Avg.',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'reactionsICDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext',
        isDiff: true
    },
    {
        field: 'reactionsICMedian',
        label: 'Reactions Avg. (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'reactionsICMedianOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext'
    },
    {
        field: 'reactionsICRank',
        label: 'Reactions Avg. (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'reactionsICRankOptions',
        step: 1,
        exposureGroup: 'inContext'
    },
    {
        field: 'negativityAvgIC',
        label: 'Negativity Avg.',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'negativityAvgICOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext'
    },
    {
        field: 'negativityAvgICDiff',
        label: 'Negativity Avg.',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'negativityAvgICDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext',
        isDiff: true
    },
    {
        field: 'negativityAvgICMedian',
        label: 'Negativity Avg. (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'negativityAvgICMedianOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext'
    },
    {
        field: 'negativityAvgICRank',
        label: 'Negativity Avg. (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'negativityAvgICRankOptions',
        step: 1,
        exposureGroup: 'inContext'
    },
    {
        field: 'happyPeakIC',
        label: 'Happy Peak %',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'happyPeakICOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext'
    },
    {
        field: 'happyPeakICDiff',
        label: 'Happy Peak %',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'happyPeakICDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext',
        isDiff: true
    },
    {
        field: 'happyPeakICMedian',
        label: 'Happy Peak (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'happyPeakICMedianOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext'
    },
    {
        field: 'happyPeakICRank',
        label: 'Happy Peak (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'happyPeakICRankOptions',
        step: 1,
        exposureGroup: 'inContext'
    },
    {
        field: 'surprisePeakIC',
        label: 'Surprise Peak',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'surprisePeakICOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext'
    },
    {
        field: 'surprisePeakICDiff',
        label: 'Surprise Peak',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'surprisePeakICDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext',
        isDiff: true
    },
    {
        field: 'surprisePeakICMedian',
        label: 'Surprise Peak (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'surprisePeakICMedianOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext'
    },
    {
        field: 'surprisePeakICRank',
        label: 'Surprise Peak (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'surprisePeakICRankOptions',
        step: 1,
        exposureGroup: 'inContext'
    },
    {
        field: 'confusionPeakIC',
        label: 'Confusion Peak',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'confusionPeakICOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext'
    },
    {
        field: 'confusionPeakICDiff',
        label: 'Confusion Peak',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'confusionPeakICDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext',
        isDiff: true
    },
    {
        field: 'confusionPeakMedianIC',
        label: 'Confusion Peak (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'confusionPeakMedianICOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext'
    },
    {
        field: 'confusionPeakICRank',
        label: 'Confusion Peak (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'confusionPeakICRankOptions',
        step: 1,
        exposureGroup: 'inContext'
    },
    {
        field: 'contemptPeakIC',
        label: 'Contempt Peak',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'contemptPeakICOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext'
    },
    {
        field: 'contemptPeakICDiff',
        label: 'Contempt Peak',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'contemptPeakICDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext',
        isDiff: true
    },
    {
        field: 'contemptPeakICMedian',
        label: 'Contempt Peak (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'contemptPeakICMedianOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext'
    },
    {
        field: 'contemptPeakICRank',
        label: 'Contempt Peak (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'contemptPeakICRankOptions',
        step: 1,
        exposureGroup: 'inContext'
    },
    {
        field: 'disgustPeakIC',
        label: 'Disgust Peak',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'disgustPeakICOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext'
    },
    {
        field: 'disgustPeakICDiff',
        label: 'Disgust Peak',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'disgustPeakICDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext',
        isDiff: true
    },
    {
        field: 'disgustPeakICMedian',
        label: 'Disgust Peak (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'disgustPeakICMedianOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext'
    },
    {
        field: 'disgustPeakICRank',
        label: 'Disgust Peak (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'disgustPeakICRankOptions',
        step: 1,
        exposureGroup: 'inContext'
    },
    {
        field: 'brandRecognition',
        label: 'Brand Recognition',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'brandRecognitionOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext'
    },
    {
        field: 'brandRecognitionDiff',
        label: 'Brand Recognition',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'brandRecognitionDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext',
        isDiff: true
    },
    {
        field: 'brandRecognitionMedian',
        label: 'Brand Recognition (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'brandRecognitionMedianOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext'
    },
    {
        field: 'brandRecognitionRank',
        label: 'Brand Recognition (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'brandRecognitionRankOptions',
        step: 1,
        exposureGroup: 'inContext'
    },
    {
        field: 'adRecognition',
        label: 'Ad Recognition',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'adRecognitionOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext'
    },
    {
        field: 'adRecognitionDiff',
        label: 'Ad Recognition',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'adRecognitionDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext',
        isDiff: true
    },
    {
        field: 'adRecognitionMedian',
        label: 'Ad Recognition (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'adRecognitionMedianOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'inContext'
    },
    {
        field: 'adRecognitionRank',
        label: 'Ad Recognition (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'adRecognitionRankOptions',
        step: 1,
        exposureGroup: 'inContext'
    },
    {
        field: 'playbackSecondsIF',
        label: 'Playback Seconds',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'playbackSecondsIFOptions',
        step: 1,
        postfix: 's',
        exposureGroup: 'focused'
    },
    {
        field: 'playbackSecondsIFDiff',
        label: 'Playback Seconds',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'playbackSecondsIFDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused',
        isDiff: true
    },
    {
        field: 'playbackSecondsIFMedian',
        label: 'Playback Seconds (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'playbackSecondsIFMedianOptions',
        step: 1,
        postfix: 's',
        exposureGroup: 'focused'
    },
    {
        field: 'playbackSecondsIFRank',
        label: 'Playback Seconds (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'playbackSecondsIFRankOptions',
        step: 1,
        exposureGroup: 'focused'
    },
    {
        field: 'vtrif',
        label: 'VTR',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'vtrifOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'vtrifDiff',
        label: 'VTR',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'vtrifDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused',
        isDiff: true
    },
    {
        field: 'vtrifMedian',
        label: 'VTR (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'vtrifMedianOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'vtrifRank',
        label: 'VTR (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'vtrifRankOptions',
        step: 1,
        exposureGroup: 'focused'
    },
    {
        field: 'attentiveSecondsIF',
        label: 'Attentive Seconds',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'attentiveSecondsIFOptions',
        step: 1,
        postfix: 's',
        exposureGroup: 'focused'
    },
    {
        field: 'attentiveSecondsIFDiff',
        label: 'Attentive Seconds',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'attentiveSecondsIFDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused',
        isDiff: true
    },
    {
        field: 'attentiveSecondsIFMedian',
        label: 'Attentive Seconds (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'attentiveSecondsIFMedianOptions',
        step: 1,
        postfix: 's',
        exposureGroup: 'focused'
    },
    {
        field: 'attentiveSecondsIFRank',
        label: 'Attentive Seconds (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'attentiveSecondsIFRankOptions',
        step: 1,
        exposureGroup: 'focused'
    },
    {
        field: 'attentionAvgIF',
        label: 'Attention Avg.',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'attentionAvgIFOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'attentionAvgIFDiff',
        label: 'Attention Avg.',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'attentionAvgIFDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused',
        isDiff: true
    },
    {
        field: 'attentionAvgIFMedian',
        label: 'Attention Avg. (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'attentionAvgIFMedianOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'attentionAvgIFRank',
        label: 'Attention Avg. (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'attentionAvgIFRankOptions',
        exposureGroup: 'focused'
    },
    {
        field: 'attentiveSecondsVTRIF',
        label: 'Attentive VTR',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'attentiveSecondsVTRIFOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'attentiveSecondsVTRIFDiff',
        label: 'Attentive VTR',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'attentiveSecondsVTRIFDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused',
        isDiff: true
    },
    {
        field: 'attentiveSecondsVTRIFMedian',
        label: 'Attentive VTR (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'attentiveSecondsVTRIFMedianOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'attentiveSecondsVTRIFRank',
        label: 'Attentive VTR (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'attentiveSecondsVTRIFRankOptions',
        step: 1,
        exposureGroup: 'focused'
    },
    {
        field: 'distractionAvgIF',
        label: 'Distraction Avg.',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'distractionAvgIFOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'distractionAvgIFDiff',
        label: 'Distraction Avg.',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'distractionAvgIFDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused',
        isDiff: true
    },
    {
        field: 'distractionAvgIFMedian',
        label: 'Distraction Avg. (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'distractionAvgIFMedianOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'distractionAvgIFRank',
        label: 'Distraction Avg. (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'distractionAvgIFRankOptions',
        step: 1,
        exposureGroup: 'focused'
    },
    {
        field: 'reactions',
        label: 'Reactions Avg.',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'reactionsOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'reactionsDiff',
        label: 'Reactions Avg.',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'reactionsDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused',
        isDiff: true
    },
    {
        field: 'reactionsMedian',
        label: 'Reactions Avg. (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'reactionsMedianOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'reactionsRank',
        label: 'Reactions Avg. (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'reactionsRankOptions',
        step: 1,
        exposureGroup: 'focused'
    },
    {
        field: 'negativityAvgIF',
        label: 'Negativity Avg.',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'negativityAvgIFOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'negativityAvgIFDiff',
        label: 'Negativity Avg.',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'negativityAvgIFDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused',
        isDiff: true
    },
    {
        field: 'negativityAvgIFMedian',
        label: 'Negativity Avg. (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'negativityAvgIFMedianOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'negativityAvgIFRank',
        label: 'Negativity Avg. (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'negativityAvgIFRankOptions',
        step: 1,
        exposureGroup: 'focused'
    },
    {
        field: 'happyPeak',
        label: 'Happy Peak',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'happyPeakOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'happyPeakDiff',
        label: 'Happy Peak',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'happyPeakOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused',
        diff: true
    },
    {
        field: 'happyPeakMedian',
        label: 'Happy Peak (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'happyPeakMedianOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'happyPeakRank',
        label: 'Happy Peak (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'happyPeakRankOptions',
        step: 1,
        exposureGroup: 'focused'
    },
    {
        field: 'surprisePeak',
        label: 'Surprise Peak',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'surprisePeakOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'surprisePeakDiff',
        label: 'Surprise Peak',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'surprisePeakDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused',
        isDiff: true
    },
    {
        field: 'surprisePeakMedian',
        label: 'Surprise Peak (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'surprisePeakMedianOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'surprisePeakRank',
        label: 'Surprise Peak (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'surprisePeakRankOptions',
        step: 1,
        exposureGroup: 'focused'
    },
    {
        field: 'confusionPeak',
        label: 'Confusion Peak',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'confusionPeakOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'confusionPeakDiff',
        label: 'Confusion Peak',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'confusionPeakDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused',
        isDiff: true
    },
    {
        field: 'confusionPeakMedian',
        label: 'Confusion Peak (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'confusionPeakMedianOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'confusionPeakRank',
        label: 'Confusion Peak (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'confusionPeakRankOptions',
        step: 1,
        exposureGroup: 'focused'
    },
    {
        field: 'contemptPeak',
        label: 'Contempt Peak',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'contemptPeakOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'contemptPeakDiff',
        label: 'Contempt Peak',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'contemptPeakDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused',
        isDiff: true
    },
    {
        field: 'contemptPeakMedian',
        label: 'Contempt Peak (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'contemptPeakMedianOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'contemptPeakRank',
        label: 'Contempt Peak (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'contemptPeakRankOptions',
        step: 1,
        exposureGroup: 'focused'
    },
    {
        field: 'disgustPeak',
        label: 'Disgust Peak',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'disgustPeakOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'disgustPeakDiff',
        label: 'Disgust Peak',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'disgustPeakDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused',
        isDiff: true
    },
    {
        field: 'disgustPeakMedian',
        label: 'Disgust Peak (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'disgustPeakMedianOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'disgustPeakRank',
        label: 'Disgust Peak (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'disgustPeakRankOptions',
        step: 1,
        exposureGroup: 'focused'
    },
    {
        field: 'adLikeability',
        label: 'Ad Likeability',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'adLikeabilityOptions',
        step: 0.01,
        exposureGroup: 'focused'
    },
    {
        field: 'adLikeabilityDiff',
        label: 'Ad Likeability',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'adLikeabilityDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused',
        isDiff: true
    },
    {
        field: 'adLikeabilityMedian',
        label: 'Ad Likeability (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'adLikeabilityMedianOptions',
        step: 0.01,
        exposureGroup: 'focused'
    },
    {
        field: 'adLikeabilityRank',
        label: 'Ad Likeability (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'adLikeabilityRankOptions',
        step: 1,
        exposureGroup: 'focused'
    },
    {
        field: 'brandTrust',
        label: 'Brand Trust',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'brandTrustOptions',
        step: 0.1,
        exposureGroup: 'focused'
    },
    {
        field: 'brandTrustDiff',
        label: 'Brand Trust',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'brandTrustDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused',
        isDiff: true
    },
    {
        field: 'brandTrustMedian',
        label: 'Brand Trust (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'brandTrustMedianOptions',
        step: 0.1,
        exposureGroup: 'focused'
    },
    {
        field: 'brandTrustRank',
        label: 'Brand Trust (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'brandTrustRankOptions',
        step: 1,
        exposureGroup: 'focused'
    },
    {
        field: 'persuasion',
        label: 'Persuasion',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'persuasionOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'persuasionDiff',
        label: 'Persuasion',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'persuasionDiffOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused',
        isDiff: true
    },
    {
        field: 'persuasionMedian',
        label: 'Persuasion (Norm)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'persuasionMedianOptions',
        postfix: '%',
        displayMultiplier: 100,
        exposureGroup: 'focused'
    },
    {
        field: 'persuasionRank',
        label: 'Persuasion (Rank)',
        filterType: CustomGridFilterInput.NumericRange,
        defaultVisible: false,
        optionsName: 'persuasionRankOptions',
        step: 1,
        exposureGroup: 'focused'
    }
];
