import { CreativeViewerCurveType } from '../interfaces/creative-viewer-curve-type';

export const curveTypeLabelMap: Map<CreativeViewerCurveType, string> = new Map([
    [CreativeViewerCurveType.Distraction, 'Distraction'],
    [CreativeViewerCurveType.Happiness, 'Happiness'],
    [CreativeViewerCurveType.Negativity, 'Negativity'],
    [CreativeViewerCurveType.Contempt, 'Contempt'],
    [CreativeViewerCurveType.Surprise, 'Surprise'],
    [CreativeViewerCurveType.Confusion, 'Confusion'],
    [CreativeViewerCurveType.Disgust, 'Disgust'],
    [CreativeViewerCurveType.Attention, 'Attention'],
    [CreativeViewerCurveType.AllReactions, 'Reactions'],
    [CreativeViewerCurveType.Playback, 'Playback'],
    [CreativeViewerCurveType.NeutralAttention, 'Neutral Attention']
]);
