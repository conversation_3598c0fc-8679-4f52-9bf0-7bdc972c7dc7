import { NormCategory } from '@/interfaces/norm-category';
import { NormData } from '@/interfaces/norm-data';
import { ExposureGroupType } from '@/contexts/creative-viewer-context';
import { TableData } from '@/components/TooltipDetailTable';

const surveyFields = new Set([
    'brandrecognition',
    'brandrecognitionrank',
    'brandrecognitionmedian',
    'brandrecognitiondiff',
    'adlikeability',
    'adlikeabilityrank',
    'adlikeabilitymedian',
    'adlikeabilitydiff',
    'adrecognition',
    'adrecognitionrank',
    'adrecognitionmedian',
    'adrecognitiondiff',
    'brandtrust',
    'brandtrustrank',
    'brandtrustmedian',
    'brandtrustdiff',
    'persuasion',
    'persuasionrank',
    'persuasionmedian',
    'persuasiondiff'
]);

const reactionsFields = new Set([
    'reactions',
    'reactionsrank',
    'reactionsmedian',
    'reactionsdiff',
    'happypeak',
    'happypeakrank',
    'happypeakmedian',
    'happypeakdiff',
    'surprisepeak',
    'surprisepeakrank',
    'surprisepeakmedian',
    'surprisepeakdiff',
    'confusionpeak',
    'confusionpeakrank',
    'confusionpeakmedian',
    'confusionpeakdiff',
    'contemptpeak',
    'contemptpeakrank',
    'contemptpeakmedian',
    'contemptpeakdiff',
    'disgustpeak',
    'disgustpeakrank',
    'disgustpeakmedian',
    'disgustpeakdiff',
    'negativityavg',
    'negativityavgrank',
    'negativityavgmedian',
    'negativityavgdiff',
    'neutralattentionavg',
    'neutralattentionavgmedian',
    'distractionavg',
    'distractionavgrank',
    'distractionavgmedian',
    'distractionavgicmedian',
    'distractionavgdiff',
    'attentionpeak',
    'attentionpeakrank',
    'attentionpeakmedian',
    'happyavg',
    'happyavgrank',
    'happyavgmedian',
    'surpriseavg',
    'surpriseavgrank',
    'surpriseavgmedian',
    'confusionavg',
    'confusionavgrank',
    'confusionavgmedian',
    'contemptavg',
    'contemptavgrank',
    'contemptavgmedian',
    'disgustavg',
    'disgustavgrank',
    'disgustavgmedian',
    'allreactions',
    'distraction',
    'happiness',
    'negativity',
    'contempt',
    'surprise',
    'confusion',
    'disgust',
    'neutralattention',
    'reactionsic',
    'reactionsicrank',
    'reactionsicmedian',
    'reactionsicdiff',
    'happypeakic',
    'happypeakicrank',
    'happypeakicmedian',
    'happypeakicdiff',
    'surprisepeakic',
    'surprisepeakicrank',
    'surprisepeakicmedian',
    'surprisepeakicdiff',
    'confusionpeakic',
    'confusionpeakicrank',
    'confusionpeakicmedian',
    'confusionpeakicdiff',
    'contemptpeakic',
    'contemptpeakicrank',
    'contemptpeakicmedian',
    'contemptpeakicdiff',
    'disgustpeakic',
    'disgustpeakicrank',
    'disgustpeakicmedian',
    'disgustpeakicdiff',
    'negativityavgic',
    'negativityavgicrank',
    'negativityavgicmedian',
    'negativityavgicdiff',
    'negativityavgif',
    'negativityavgifrank',
    'negativityavgifmedian',
    'negativityavgifdiff',
    'attentionavgif',
    'attentionavgifrank',
    'attentionavgifmedian',
    'attentionavgifdiff',
    'distractionavgif',
    'distractionavgifrank',
    'distractionavgifmedian',
    'distractionavgifdiff'
]);

const attentionFields = new Set([
    'attentiveseconds',
    'attentivesecondsrank',
    'attentivesecondsmedian',
    'attentivesecondsdiff',
    'attentivesecondsvtr',
    'attentivesecondsvtrrank',
    'attentivesecondsvtrmedian',
    'attentivesecondsvtrdiff',
    'vtr',
    'vtrrank',
    'vtrmedian',
    'vtrdiff',
    'playbackseconds',
    'playbacksecondsrank',
    'playbacksecondsmedian',
    'playbacksecondsdiff',
    'attentionavg',
    'attentionavgrank',
    'attentionavgmedian',
    'attentionavgdiff',
    'predictedretention',
    'reactiveattention',
    'attention',
    'playback',
    'attentivesecondsif',
    'attentivesecondsifrank',
    'attentivesecondsifmedian',
    'attentivesecondsifdiff',
    'attentivesecondsvtrif',
    'attentivesecondsvtrifrank',
    'attentivesecondsvtrifmedian',
    'attentivesecondsvtrifdiff',
    'vtrif',
    'vtrifrank',
    'vtrifmedian',
    'vtriffdiff',
    'playbacksecondsif',
    'playbacksecondsifrank',
    'playbacksecondsifmedian',
    'playbacksecondsifdiff',
    'attentionavgicmedian'
]);

export function getNormCategory(field?: string): NormCategory | undefined {
    if (!field) return undefined;

    const normalizedField = field.toLowerCase();

    if (surveyFields.has(normalizedField)) {
        return NormCategory.SurveyNorm;
    } else if (reactionsFields.has(normalizedField)) {
        return NormCategory.ReactionsNorm;
    } else if (attentionFields.has(normalizedField)) {
        return NormCategory.AttentionNorm;
    }
    return undefined;
}

export function getNormValue(
    type:
        | 'Device'
        | 'Format'
        | 'Duration'
        | 'AdFormat'
        | 'EnvironmentCategory'
        | 'Region'
        | 'SampleSize'
        | 'RefreshDate'
        | 'AdFormatName',
    normCategory?: NormCategory,
    exposureGroup?: string,
    row?: NormData
): string | null {
    if (!normCategory || !row) return '-';

    const normMap: Record<NormCategory, keyof NormData> = {
        [NormCategory.AttentionNorm]: `attentionNorm${type}`,
        [NormCategory.ReactionsNorm]: `reactionsNorm${type}`,
        [NormCategory.SurveyNorm]: `surveyNorm${type}`
    };

    const normICMap: Record<NormCategory, keyof NormData> = {
        [NormCategory.AttentionNorm]: `attentionNorm${type}IC`,
        [NormCategory.ReactionsNorm]: `reactionsNorm${type}IC`,
        [NormCategory.SurveyNorm]: `surveyNorm${type}IC`
    };

    const normIFMap: Record<NormCategory, keyof NormData> = {
        [NormCategory.AttentionNorm]: `attentionNorm${type}IF`,
        [NormCategory.ReactionsNorm]: `reactionsNorm${type}IF`,
        [NormCategory.SurveyNorm]: `surveyNorm${type}IF`
    };

    const value =
        exposureGroup === 'inContext'
            ? row[normICMap[normCategory]]
            : row[normIFMap[normCategory]];

    if (typeof value === 'string' || typeof value === 'number') {
        return String(value);
    }

    const fallbackValue = row[normMap[normCategory]];

    return typeof fallbackValue === 'string' ||
        typeof fallbackValue === 'number'
        ? String(fallbackValue)
        : null;
}

export const getSampleSize = (
    normCategory?: NormCategory,
    exposureGroup?: string,
    row?: NormData
): string => {
    const rawValue = getNormValue(
        'SampleSize',
        normCategory,
        exposureGroup,
        row
    );
    const num = Number(rawValue);

    if (isNaN(num)) return '-';

    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

export const getNormDuration = (
    normCategory?: NormCategory,
    exposureGroup?: string,
    row?: NormData
) => {
    if (normCategory === NormCategory.SurveyNorm) {
        return '-';
    }

    const normValue = getNormValue(
        'Duration',
        normCategory,
        exposureGroup,
        row
    );

    return normValue === 'all' ? '-' : `${normValue} sec`;
};

export const getExposure = (
    normCategory?: NormCategory,
    exposureGroup?: string,
    row?: NormData
): string => {
    if (!row || !exposureGroup) return '-';

    return exposureGroup === 'inContext'
        ? 'In-Context Exposure'
        : 'Focused Exposure';
};

export const getAdFormat = (
    normCategory?: NormCategory,
    exposureGroup?: string,
    row?: NormData
): string | null => {
    if (!row || !normCategory) return '-';

    const normValue = getNormValue(
        'AdFormatName',
        normCategory,
        exposureGroup,
        row
    );

    return normValue === null ||
        normValue === 'realeyesInFocus' ||
        normValue === 'all'
        ? null
        : normValue;
};

export const getEnvironmentCategory = (
    normCategory?: NormCategory,
    exposureGroup?: string,
    row?: NormData
): string[] => {
    if (!row || !normCategory) return ['-'];

    const isInContext = exposureGroup === 'inContext';

    let environmentArray: string[] | undefined = hasCustomNorms(
        row,
        exposureGroup
    )
        ? normCategory === NormCategory.SurveyNorm
            ? (row.surveyNormCustomNormFilters?.environment ??
              (isInContext
                  ? row.surveyNormCustomNormFiltersIC?.environment
                  : row.surveyNormCustomNormFiltersIF?.environment))
            : normCategory === NormCategory.AttentionNorm
              ? (row.attentionNormCustomNormFilters?.environment ??
                (isInContext
                    ? row.attentionNormCustomNormFiltersIC?.environment
                    : row.attentionNormCustomNormFiltersIF?.environment))
              : normCategory === NormCategory.ReactionsNorm
                ? (row.reactionsNormCustomNormFilters?.environment ??
                  (isInContext
                      ? row.reactionsNormCustomNormFiltersIC?.environment
                      : row.reactionsNormCustomNormFiltersIF?.environment))
                : undefined
        : undefined;

    let normValue = getNormValue(
        'AdFormatName',
        normCategory,
        exposureGroup,
        row
    );

    if (!environmentArray || environmentArray.length === 0) {
        normValue = getNormValue(
            'EnvironmentCategory',
            normCategory,
            exposureGroup,
            row
        );

        environmentArray = normValue != null ? [normValue] : ['-'];
    }

    return environmentArray.map(value =>
        value === 'realeyesInFocus' ? '-' : value
    );
};

export const getBrands = (
    normCategory?: NormCategory,
    exposureGroup?: string,
    row?: NormData
): string[] | null | undefined => {
    if (!row || !normCategory) return ['-'];

    const isInContext = exposureGroup === 'inContext';

    return hasCustomNorms(row, exposureGroup)
        ? normCategory === NormCategory.SurveyNorm
            ? (row.surveyNormCustomNormFilters?.brand ??
              (isInContext
                  ? row.surveyNormCustomNormFiltersIC?.brand
                  : row.surveyNormCustomNormFiltersIF?.brand))
            : normCategory === NormCategory.AttentionNorm
              ? (row.attentionNormCustomNormFilters?.brand ??
                (isInContext
                    ? row.attentionNormCustomNormFiltersIC?.brand
                    : row.attentionNormCustomNormFiltersIF?.brand))
              : normCategory === NormCategory.ReactionsNorm
                ? (row.reactionsNormCustomNormFilters?.brand ??
                  (isInContext
                      ? row.reactionsNormCustomNormFiltersIC?.brand
                      : row.reactionsNormCustomNormFiltersIF?.brand))
                : null
        : null;
};

export const getMidLevelIndustry = (
    normCategory?: NormCategory,
    exposureGroup?: string,
    row?: NormData
): string[] | null | undefined => {
    if (!row || !normCategory) return ['-'];

    const isInContext = exposureGroup === 'inContext';

    return hasCustomNorms(row, exposureGroup)
        ? normCategory === NormCategory.SurveyNorm
            ? (row.surveyNormCustomNormFilters?.category ??
              (isInContext
                  ? row.surveyNormCustomNormFiltersIC?.category
                  : row.surveyNormCustomNormFiltersIF?.category))
            : normCategory === NormCategory.AttentionNorm
              ? (row.attentionNormCustomNormFilters?.category ??
                (isInContext
                    ? row.attentionNormCustomNormFiltersIC?.category
                    : row.attentionNormCustomNormFiltersIF?.category))
              : normCategory === NormCategory.ReactionsNorm
                ? (row.reactionsNormCustomNormFilters?.category ??
                  (isInContext
                      ? row.reactionsNormCustomNormFiltersIC?.category
                      : row.reactionsNormCustomNormFiltersIF?.category))
                : null
        : null;
};

export const getTopLevelIndustry = (
    normCategory?: NormCategory,
    exposureGroup?: string,
    row?: NormData
): string[] | null | undefined => {
    if (!row || !normCategory) return ['-'];

    const isInContext = exposureGroup === 'inContext';

    return hasCustomNorms(row, exposureGroup)
        ? normCategory === NormCategory.SurveyNorm
            ? (row.surveyNormCustomNormFilters?.industry ??
              (isInContext
                  ? row.surveyNormCustomNormFiltersIC?.industry
                  : row.surveyNormCustomNormFiltersIF?.industry))
            : normCategory === NormCategory.AttentionNorm
              ? (row.attentionNormCustomNormFilters?.industry ??
                (isInContext
                    ? row.attentionNormCustomNormFiltersIC?.industry
                    : row.attentionNormCustomNormFiltersIF?.industry))
              : normCategory === NormCategory.ReactionsNorm
                ? (row.reactionsNormCustomNormFilters?.industry ??
                  (isInContext
                      ? row.reactionsNormCustomNormFiltersIC?.industry
                      : row.reactionsNormCustomNormFiltersIF?.industry))
                : null
        : null;
};

export const hasBrandInList = (
    dataList: NormData[] = [],
    normCategory?: NormCategory,
    exposureGroup?: string
): boolean => {
    return dataList.some(row => hasBrand(row, normCategory, exposureGroup));
};

export const hasCountryInList = (
    dataList: NormData[] = [],
    normCategory?: NormCategory,
    exposureGroup?: string
): boolean => {
    return dataList.some(row => hasCountry(row, normCategory, exposureGroup));
};

export const hasAnyIndustryInList = (
    dataList: NormData[] = [],
    normCategory?: NormCategory,
    exposureGroup?: string
): boolean => {
    return dataList.some(row =>
        hasAnyIndustry(row, normCategory, exposureGroup)
    );
};

export const hasBrand = (
    row?: NormData,
    normCategory?: NormCategory,
    exposureGroup?: string
): boolean => {
    if (!row) return false;

    const isInContext = exposureGroup === 'inContext';

    if (normCategory) {
        const brand =
            normCategory === NormCategory.SurveyNorm
                ? (row.surveyNormCustomNormFilters?.brand ??
                  (isInContext
                      ? row.surveyNormCustomNormFiltersIC?.brand
                      : row.surveyNormCustomNormFiltersIF?.brand))
                : normCategory === NormCategory.AttentionNorm
                  ? (row.attentionNormCustomNormFilters?.brand ??
                    (isInContext
                        ? row.attentionNormCustomNormFiltersIC?.brand
                        : row.attentionNormCustomNormFiltersIF?.brand))
                  : normCategory === NormCategory.ReactionsNorm
                    ? (row.reactionsNormCustomNormFilters?.brand ??
                      (isInContext
                          ? row.reactionsNormCustomNormFiltersIC?.brand
                          : row.reactionsNormCustomNormFiltersIF?.brand))
                    : null;

        return isValidArray(brand);
    }

    const brandValues = [
        row.surveyNormCustomNormFilters?.brand,
        row.surveyNormCustomNormFiltersIC?.brand,
        row.surveyNormCustomNormFiltersIF?.brand,
        row.attentionNormCustomNormFilters?.brand,
        row.attentionNormCustomNormFiltersIC?.brand,
        row.attentionNormCustomNormFiltersIF?.brand,
        row.reactionsNormCustomNormFilters?.brand,
        row.reactionsNormCustomNormFiltersIC?.brand,
        row.reactionsNormCustomNormFiltersIF?.brand
    ];

    return brandValues.some(isValidArray);
};

export const hasAnyIndustry = (
    row?: NormData,
    normCategory?: NormCategory,
    exposureGroup?: string
): boolean => {
    if (!row) return false;

    const isInContext = exposureGroup === 'inContext';

    if (normCategory) {
        const category =
            normCategory === NormCategory.SurveyNorm
                ? (row.surveyNormCustomNormFilters?.category ??
                  (isInContext
                      ? row.surveyNormCustomNormFiltersIC?.category
                      : row.surveyNormCustomNormFiltersIF?.category))
                : normCategory === NormCategory.AttentionNorm
                  ? (row.attentionNormCustomNormFilters?.category ??
                    (isInContext
                        ? row.attentionNormCustomNormFiltersIC?.category
                        : row.attentionNormCustomNormFiltersIF?.category))
                  : normCategory === NormCategory.ReactionsNorm
                    ? (row.reactionsNormCustomNormFilters?.category ??
                      (isInContext
                          ? row.reactionsNormCustomNormFiltersIC?.category
                          : row.reactionsNormCustomNormFiltersIF?.category))
                    : null;

        const industry =
            normCategory === NormCategory.SurveyNorm
                ? (row.surveyNormCustomNormFilters?.industry ??
                  (isInContext
                      ? row.surveyNormCustomNormFiltersIC?.industry
                      : row.surveyNormCustomNormFiltersIF?.industry))
                : normCategory === NormCategory.AttentionNorm
                  ? (row.attentionNormCustomNormFilters?.industry ??
                    (isInContext
                        ? row.attentionNormCustomNormFiltersIC?.industry
                        : row.attentionNormCustomNormFiltersIF?.industry))
                  : normCategory === NormCategory.ReactionsNorm
                    ? (row.reactionsNormCustomNormFilters?.industry ??
                      (isInContext
                          ? row.reactionsNormCustomNormFiltersIC?.industry
                          : row.reactionsNormCustomNormFiltersIF?.industry))
                    : null;

        return isValidArray(category) || isValidArray(industry);
    }

    const industryValues = [
        row.surveyNormCustomNormFilters?.category,
        row.surveyNormCustomNormFiltersIC?.category,
        row.surveyNormCustomNormFiltersIF?.category,
        row.attentionNormCustomNormFilters?.category,
        row.attentionNormCustomNormFiltersIC?.category,
        row.attentionNormCustomNormFiltersIF?.category,
        row.reactionsNormCustomNormFilters?.category,
        row.reactionsNormCustomNormFiltersIC?.category,
        row.reactionsNormCustomNormFiltersIF?.category,

        row.surveyNormCustomNormFilters?.industry,
        row.surveyNormCustomNormFiltersIC?.industry,
        row.surveyNormCustomNormFiltersIF?.industry,
        row.attentionNormCustomNormFilters?.industry,
        row.attentionNormCustomNormFiltersIC?.industry,
        row.attentionNormCustomNormFiltersIF?.industry,
        row.reactionsNormCustomNormFilters?.industry,
        row.reactionsNormCustomNormFiltersIC?.industry,
        row.reactionsNormCustomNormFiltersIF?.industry
    ];

    return industryValues.some(isValidArray);
};

export const hasCountry = (
    row?: NormData,
    normCategory?: NormCategory,
    exposureGroup?: string
): boolean => {
    if (!row) return false;

    const isInContext = exposureGroup === 'inContext';

    if (normCategory) {
        const country =
            normCategory === NormCategory.SurveyNorm
                ? (row.surveyNormCustomNormFilters?.country ??
                  (isInContext
                      ? row.surveyNormCustomNormFiltersIC?.country
                      : row.surveyNormCustomNormFiltersIF?.country))
                : normCategory === NormCategory.AttentionNorm
                  ? (row.attentionNormCustomNormFilters?.country ??
                    (isInContext
                        ? row.attentionNormCustomNormFiltersIC?.country
                        : row.attentionNormCustomNormFiltersIF?.country))
                  : normCategory === NormCategory.ReactionsNorm
                    ? (row.reactionsNormCustomNormFilters?.country ??
                      (isInContext
                          ? row.reactionsNormCustomNormFiltersIC?.country
                          : row.reactionsNormCustomNormFiltersIF?.country))
                    : null;

        return isValidArray(country);
    }

    const countryValues = [
        row.surveyNormCustomNormFilters?.country,
        row.surveyNormCustomNormFiltersIC?.country,
        row.surveyNormCustomNormFiltersIF?.country,
        row.attentionNormCustomNormFilters?.country,
        row.attentionNormCustomNormFiltersIC?.country,
        row.attentionNormCustomNormFiltersIF?.country,
        row.reactionsNormCustomNormFilters?.country,
        row.reactionsNormCustomNormFiltersIC?.country,
        row.reactionsNormCustomNormFiltersIF?.country
    ];

    return countryValues.some(isValidArray);
};

export const getCountry = (
    normCategory?: NormCategory,
    exposureGroup?: string,
    row?: NormData
): string[] | null | undefined => {
    if (!row || !normCategory) return ['-'];

    const isInContext = exposureGroup === 'inContext';

    return hasCustomNorms(row, exposureGroup)
        ? normCategory === NormCategory.SurveyNorm
            ? (row.surveyNormCustomNormFilters?.country ??
              (isInContext
                  ? row.surveyNormCustomNormFiltersIC?.country
                  : row.surveyNormCustomNormFiltersIF?.country))
            : normCategory === NormCategory.AttentionNorm
              ? (row.attentionNormCustomNormFilters?.country ??
                (isInContext
                    ? row.attentionNormCustomNormFiltersIC?.country
                    : row.attentionNormCustomNormFiltersIF?.country))
              : normCategory === NormCategory.ReactionsNorm
                ? (row.reactionsNormCustomNormFilters?.country ??
                  (isInContext
                      ? row.reactionsNormCustomNormFiltersIC?.country
                      : row.reactionsNormCustomNormFiltersIF?.country))
                : null
        : null;
};

const isValidArray = (value?: string[] | null): boolean =>
    !!value && value.length > 0 && !(value.length === 1 && value[0] === '-');

export const getRegion = (
    normCategory?: NormCategory,
    exposureGroup?: string,
    row?: NormData
): string[] => {
    if (!row || !normCategory) return ['-'];

    const isInContext = exposureGroup === 'inContext';

    let regionArray: string[] = [];

    if (hasCustomNorms(row, exposureGroup)) {
        if (normCategory === NormCategory.SurveyNorm) {
            regionArray =
                (row.surveyNormCustomNormFilters?.region ??
                    (isInContext
                        ? row.surveyNormCustomNormFiltersIC?.region
                        : row.surveyNormCustomNormFiltersIF?.region)) ||
                [];
        } else if (normCategory === NormCategory.AttentionNorm) {
            regionArray =
                (row.attentionNormCustomNormFilters?.region ??
                    (isInContext
                        ? row.attentionNormCustomNormFiltersIC?.region
                        : row.attentionNormCustomNormFiltersIF?.region)) ||
                [];
        } else if (normCategory === NormCategory.ReactionsNorm) {
            regionArray =
                (row.reactionsNormCustomNormFilters?.region ??
                    (isInContext
                        ? row.reactionsNormCustomNormFiltersIC?.region
                        : row.reactionsNormCustomNormFiltersIF?.region)) ||
                [];
        }
    }

    if (regionArray.length === 0) {
        const normValue = getNormValue(
            'Region',
            normCategory,
            exposureGroup,
            row
        );

        if (normValue != null) {
            regionArray = [normValue];
        } else {
            regionArray = ['-'];
        }
    }

    return regionArray.map(value => (value != null ? value : '-'));
};

export const hasCustomNorms = (
    row: NormData | null | undefined,
    exposureGroup: string | undefined
) => {
    if (!row) return false;

    const isInContext = exposureGroup === 'inContext';

    return isInContext
        ? row.attentionNormCustomNormFilters != null ||
              row.attentionNormCustomNormFiltersIC != null ||
              row.reactionsNormCustomNormFilters != null ||
              row.reactionsNormCustomNormFiltersIC != null ||
              row.surveyNormCustomNormFilters != null ||
              row.surveyNormCustomNormFiltersIC != null
        : row.attentionNormCustomNormFilters != null ||
              row.attentionNormCustomNormFiltersIF != null ||
              row.reactionsNormCustomNormFilters != null ||
              row.reactionsNormCustomNormFiltersIF != null ||
              row.surveyNormCustomNormFilters != null ||
              row.surveyNormCustomNormFiltersIF != null;
};

export const hasCustomNormsForMetric = (
    row: NormData | null | undefined,
    exposureGroup: string | undefined,
    normCategory?: NormCategory
) => {
    if (!row || !normCategory) return false;

    const isInContext = exposureGroup === 'inContext';

    const baseField = normCategory
        .toLowerCase()
        .replace('norm', 'NormCustomNormFilters');

    const fieldsToCheck = isInContext
        ? [baseField, `${baseField}IC`]
        : [baseField, `${baseField}IF`];

    return fieldsToCheck.some(field => (row as any)[field] != null);
};

export function capitalizeFirstLetter(str?: string): string {
    if (!str) return '';
    return str.charAt(0).toUpperCase() + str.slice(1);
}

export function getNormDataTable(
    showFullNormData?: boolean,
    hasAccountCustomNorms?: boolean,
    normCategory?: NormCategory,
    exposureGroup?: ExposureGroupType,
    tooltipModel?: NormData,
    normFallback?: number,
    audience?: string,
    hasCountry?: boolean,
    hasIndustry?: boolean,
    hasBrand?: boolean
): TableData[] {
    if (!tooltipModel || !normCategory) {
        return [{ label: '-', hasFallback: false, values: ['-'] }];
    }

    const tableData: TableData[] = [
        {
            label: 'Device Type',
            hasFallback: false,
            values: [
                capitalizeFirstLetter(
                    getNormValue(
                        'Device',
                        normCategory,
                        exposureGroup,
                        tooltipModel
                    ) ?? '-'
                )
            ]
        },
        {
            label: 'Format',
            hasFallback: false,
            values: [
                capitalizeFirstLetter(
                    getNormValue(
                        'Format',
                        normCategory,
                        exposureGroup,
                        tooltipModel
                    ) ?? '-'
                )
            ]
        },
        {
            label: 'Ad Duration',
            hasFallback: false,
            values: [
                capitalizeFirstLetter(
                    getNormDuration(normCategory, exposureGroup, tooltipModel)
                )
            ]
        },
        {
            label: 'Exposure',
            hasFallback: false,
            values: [getExposure(normCategory, exposureGroup, tooltipModel)]
        }
    ];

    const adFormat = getAdFormat(normCategory, exposureGroup, tooltipModel);

    tableData.push(
        {
            label: '(Context) Ad Environment',
            hasFallback: normFallback === 0 && adFormat != null,
            values: getEnvironmentCategory(
                normCategory,
                exposureGroup,
                tooltipModel
            ).map(environment => capitalizeFirstLetter(environment))
        },
        {
            label: '(Context) Ad Format',
            hasFallback: normFallback !== 0 && adFormat != null,
            values: [capitalizeFirstLetter(adFormat ?? '-')]
        }
    );

    // === CUSTOM Norms / Industry / Brand ===
    if (showFullNormData || hasAccountCustomNorms) {
        if (showFullNormData || hasBrand) {
            const brands = getBrands(normCategory, exposureGroup, tooltipModel);

            tableData.push({
                label: '(Industry) Brand',
                hasFallback: false,
                values: brands && brands.length > 0 ? brands : ['-']
            });
        }

        if (showFullNormData || hasIndustry) {
            const midLevelIndustry = getMidLevelIndustry(
                normCategory,
                exposureGroup,
                tooltipModel
            );
            const topLevelIndustry = getTopLevelIndustry(
                normCategory,
                exposureGroup,
                tooltipModel
            );

            tableData.push(
                {
                    label: '(Industry) Top-level Industry',
                    hasFallback: false,
                    values:
                        topLevelIndustry && topLevelIndustry.length > 0
                            ? topLevelIndustry
                            : ['-']
                },
                {
                    label: '(Industry) Mid-level Industry',
                    hasFallback: false,
                    values:
                        midLevelIndustry && midLevelIndustry.length > 0
                            ? midLevelIndustry
                            : ['-']
                }
            );
        }
    }

    // === AREA: Region & Country ===
    tableData.push({
        label: '(Area) Region',
        hasFallback: false,
        values: getRegion(normCategory, exposureGroup, tooltipModel).map(
            region => capitalizeFirstLetter(region)
        )
    });

    if (showFullNormData || (hasAccountCustomNorms && hasCountry)) {
        tableData.push({
            label: '(Area) Country',
            hasFallback: false,
            values: getCountry(normCategory, exposureGroup, tooltipModel) || [
                '-'
            ]
        });
    }

    // === AUDIENCE, SAMPLE SIZE, SYNC DATE ===
    tableData.push(
        {
            label: 'Audience',
            hasFallback: false,
            values: [audience ?? '-']
        },
        {
            label: 'Creatives in Norm',
            hasFallback: false,
            values: [
                capitalizeFirstLetter(
                    getSampleSize(normCategory, exposureGroup, tooltipModel)
                )
            ]
        },
        {
            label: 'Norm Sync Date',
            hasFallback: false,
            values: [
                capitalizeFirstLetter(
                    getNormValue(
                        'RefreshDate',
                        normCategory,
                        exposureGroup,
                        tooltipModel
                    ) ?? '-'
                )
            ]
        }
    );

    return tableData;
}
