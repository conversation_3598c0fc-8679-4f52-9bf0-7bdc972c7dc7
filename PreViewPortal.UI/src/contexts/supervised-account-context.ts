import { createContext } from 'react';
import { AccountLookupModel } from '@/interfaces/account-lookup-model';

interface SupervisedAccountContextModel {
    supervisedAccount: AccountLookupModel | null;
    setSupervisedAccount: (account: AccountLookupModel) => void;
}

export const SupervisedAccountContext =
    createContext<SupervisedAccountContextModel>({
        supervisedAccount: null,
        setSupervisedAccount: () => {}
    });
