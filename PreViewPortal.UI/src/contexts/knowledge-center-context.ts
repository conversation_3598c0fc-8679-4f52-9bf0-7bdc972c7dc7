import { createContext } from 'react';

interface KnowledgeCenterContextModel {
    markdown?: string;
    currentAnchor?: string;
    isKnowledgeCenterOpen: boolean;
    setCurrentAnchor: (anchor?: string) => void;
    setIsKnowledgeCenterOpen: (isOpen: boolean) => void;
}

const KnowledgeCenterContext = createContext<KnowledgeCenterContextModel>({
    isKnowledgeCenterOpen: false,
    setCurrentAnchor: () => {},
    setIsKnowledgeCenterOpen: () => {}
});

export default KnowledgeCenterContext;
