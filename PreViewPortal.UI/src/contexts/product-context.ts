import { ProductOptions } from '@/interfaces/product-options';
import { createContext } from 'react';

interface ProductContextModel {
    accountProduct: ProductOptions[];
    selectedProduct: ProductOptions | null;
    setAccountProduct: (product: ProductOptions[]) => void;
    setSelectedProduct: (product: ProductOptions) => void;
}

export const ProductContext = createContext<ProductContextModel>({
    accountProduct: [],
    selectedProduct: null,
    setAccountProduct: () => {},
    setSelectedProduct: () => {}
});
