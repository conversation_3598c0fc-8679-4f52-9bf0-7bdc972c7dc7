import { createContext } from 'react';

interface NormTooltipContextModel {
    showDetails?: boolean;
    setShowDetails: (showDetails: boolean) => void;
    showFullNormData?: boolean;
    setShowFullNormData: (showFullNormData: boolean) => void;
}

export const NormTooltipContext = createContext<NormTooltipContextModel>({
    showDetails: false,
    setShowDetails: () => {},
    showFullNormData: false,
    setShowFullNormData: () => {}
});
