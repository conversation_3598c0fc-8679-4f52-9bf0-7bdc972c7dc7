import { createContext } from 'react';
import { CreativeViewerCurveType } from '@/interfaces/creative-viewer-curve-type';
import { CreativeViewerChartModel } from '@/interfaces/creative-viewer-chart-model';
import { CreativeViewerVideoModel } from '@/interfaces/creative-viewer-video-model';
import { SegmentKeysInfo } from '@/interfaces/segment-keys-info';
import { SurveyScore, SurveyScoreOption } from '@/constant/survey-scores-def';
import { NormData } from '@/interfaces/norm-data';

export type SelectedCurveType = {
    type: CreativeViewerCurveType;
    exposureGroup: ExposureGroupType;
};

export type SelectedCurveColor = SelectedCurveType & {
    color: string;
    isNorm: boolean;
};

export type ExposureGroupType = 'focused' | 'inContext';

export interface CurveToolTipLabelProps {
    segmentKey?: string;
    sourceMedia: string;
    isNorm: boolean;
    type: CreativeViewerCurveType;
    nameLimit?: number;
}

interface CreativeViewerContextModel {
    videoData: CreativeViewerVideoModel[];
    selectedCurveTypes: SelectedCurveType[];
    selectedCurves: CreativeViewerChartModel[];
    curveDataForAxises: CreativeViewerChartModel[];
    selectedCurveSegments: string[];
    multipleMediaSelected: boolean;
    curveColors: Map<string, SelectedCurveColor>;
    focusedId: string | undefined;
    customLeftAxisRange: number[] | undefined;
    customRightAxisRange: number[] | undefined;
    leftCurvesRange: number[];
    rightCurvesRange: number[];
    segmentData: SegmentKeysInfo | undefined;
    surveyScoreData?: SurveyScore[];
    normData?: NormData[];
    selectedSurveyScore: SurveyScoreOption | null | undefined;
    setSelectedSurveyScore: (
        score: SurveyScoreOption | null | undefined
    ) => void;
    setSelectedCurveTypes: (type: SelectedCurveType[]) => void;
    setSelectedCurveSegments: (segments: string[]) => void;
    setCustomLeftAxisRange: (range: number[]) => void;
    setCustomRightAxisRange: (range: number[]) => void;
    setLeftCurvesRange: (range: number[]) => void;
    setRightCurvesRange: (range: number[]) => void;
    setFocusedId: (id: string | undefined) => void;
    getDisplayFullSegmentAsArea: (
        segmentKey: string | undefined,
        isNorm: boolean
    ) => boolean;
    getSegmentKeyTooltipLabel: (segmentKey: string | undefined) => string;
    getCurveLabel: (model: CurveToolTipLabelProps) => string;
    zoomScale: number;
    hasNoCurves: boolean;
}

export const CreativeViewerContext = createContext<CreativeViewerContextModel>({
    videoData: [],
    surveyScoreData: [],
    normData: [],
    selectedCurveTypes: [],
    selectedCurveSegments: [],
    selectedCurves: [],
    curveDataForAxises: [],
    multipleMediaSelected: false,
    curveColors: new Map(),
    focusedId: undefined,
    customLeftAxisRange: undefined,
    customRightAxisRange: undefined,
    leftCurvesRange: [],
    rightCurvesRange: [],
    segmentData: undefined,
    selectedSurveyScore: undefined,
    setSelectedSurveyScore: () => {},
    setCustomLeftAxisRange: () => {},
    setCustomRightAxisRange: () => {},
    setLeftCurvesRange: () => {},
    setRightCurvesRange: () => {},
    setSelectedCurveTypes: () => {},
    setSelectedCurveSegments: () => {},
    setFocusedId: () => {},
    getDisplayFullSegmentAsArea: () => false,
    getSegmentKeyTooltipLabel: () => '',
    getCurveLabel: () => '',
    zoomScale: 1,
    hasNoCurves: false
});
