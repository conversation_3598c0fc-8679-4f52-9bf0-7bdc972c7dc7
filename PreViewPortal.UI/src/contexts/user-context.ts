import { createContext } from 'react';
import { UserGridSettingsModel } from '../components/UserProvider';
import { UserInfoModel } from '../interfaces/user-info-model';

export type NormSettingsMode = 'never' | 'always' | 'hovering';

interface UserContextModel {
    user: UserInfoModel | null;
    setUser: (u: UserInfoModel | null) => void;
    gridSettings: UserGridSettingsModel[];
    setGridSettings: (settings: UserGridSettingsModel[]) => void;
    isGridSettingsLoading: boolean;
}

const UserContext = createContext<UserContextModel>({
    user: null,
    setUser: () => {},
    gridSettings: [],
    setGridSettings: () => {},
    isGridSettingsLoading: true
});

export default UserContext;
