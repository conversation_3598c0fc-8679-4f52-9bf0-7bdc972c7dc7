import { createContext } from 'react';
import { ExportResult } from '@/interfaces/export-result';

interface ExportStatusContextModel {
    exportResult?: ExportResult | null;
    setExportResult: (exportResult?: ExportResult) => void;
    cancelExport: () => void;
    setShareKey: (key: string | undefined) => void;
    isExportCanceled: boolean;
    isAutoCompleteDownloaded: boolean;
    setIsAutoCompleteDownloaded: (isDownloaded: boolean) => void;
}

export const ExportStatusContext = createContext<ExportStatusContextModel>({
    exportResult: null,
    setExportResult: () => {},
    cancelExport: () => {},
    setShareKey: () => {},
    isExportCanceled: false,
    isAutoCompleteDownloaded: false,
    setIsAutoCompleteDownloaded: () => {}
});
