import { createContext } from 'react';
import {
    CurveTypeYAxisMap,
    defaultCurveTypeYAxisGroups
} from '@/constant/default-curve-type-y-axis-groups';
import {
    defaultExposureGroupStlye,
    ExposureGroupStyleMap
} from '@/constant/default-exposure-group-style';

export type NormSettingsMode = 'never' | 'always' | 'hovering';

interface ChartSettingsContextModel {
    curveTypeYAxisGroups: CurveTypeYAxisMap;
    setCurveTypeYAxisGroups: (newMap: CurveTypeYAxisMap) => void;
    exposureGroupStyles: ExposureGroupStyleMap;
    setExposureGroupStyles: (newMap: ExposureGroupStyleMap) => void;
    showLegendBar: boolean;
    setShowLegendBar: (show: boolean) => void;
    showAxisMarkers: boolean;
    setShowAxisMarkers: (show: boolean) => void;
    normSettingsMode: NormSettingsMode;
    setNormSettingsMode: (mode: NormSettingsMode) => void;
    showNormDetails: boolean;
    setShowNormDetails: (show: boolean) => void;
    smoothingLevel: number;
    setSmoothingLevel: (level: number) => void;
}

const ChartSettingsContext = createContext<ChartSettingsContextModel>({
    curveTypeYAxisGroups: defaultCurveTypeYAxisGroups,
    setCurveTypeYAxisGroups: () => {},
    exposureGroupStyles: defaultExposureGroupStlye,
    setExposureGroupStyles: () => {},
    showLegendBar: false,
    setShowLegendBar: () => {},
    showAxisMarkers: false,
    setShowAxisMarkers: () => {},
    normSettingsMode: 'hovering',
    setNormSettingsMode: () => {},
    showNormDetails: false,
    setShowNormDetails: () => {},
    smoothingLevel: 0,
    setSmoothingLevel: () => {}
});

export default ChartSettingsContext;
