import { CreativeViewerCurveType } from '../interfaces/creative-viewer-curve-type';

interface ColorCodes {
    hexa: string;
    hue: number;
    saturation: number;
    topLightness: number;
    bottomLightness: number;
}

const minColorCountByType = 4;

export const getCurveColorByType = (
    type: CreativeViewerCurveType,
    n?: number,
    nth?: number
) => {
    n = n || 1;
    nth = nth || 1;

    const { hue, saturation, bottomLightness, topLightness } =
        curveTypeColorMap.get(type)!;

    const step =
        (topLightness - bottomLightness) /
        Math.max(n - 1, minColorCountByType - 1);

    if (n === 2 && nth === 2) nth = 3;

    let lightness = topLightness - step * (nth - 1);

    if (
        type === CreativeViewerCurveType.Playback ||
        type === CreativeViewerCurveType.Negativity
    )
        lightness = bottomLightness + step * (nth - 1);

    return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
};

export const curveTypeColorMap: Map<CreativeViewerCurveType, ColorCodes> =
    new Map([
        [
            CreativeViewerCurveType.Distraction,
            {
                hexa: '#EE5A8F',
                hue: 339,
                saturation: 81,
                bottomLightness: 10,
                topLightness: 64
            }
        ],
        [
            CreativeViewerCurveType.Happiness,
            {
                hexa: '#FFC107',
                hue: 45,
                saturation: 100,
                bottomLightness: 15,
                topLightness: 51
            }
        ],
        [
            CreativeViewerCurveType.Negativity,
            {
                hexa: '#5F7CE3',
                hue: 227,
                saturation: 70,
                bottomLightness: 50,
                topLightness: 95
            }
        ],
        [
            CreativeViewerCurveType.Contempt,
            {
                hexa: '#BA7BE1',
                hue: 277,
                saturation: 63,
                bottomLightness: 15,
                topLightness: 68
            }
        ],
        [
            CreativeViewerCurveType.Surprise,
            {
                hexa: '#88B15A',
                hue: 88,
                saturation: 36,
                bottomLightness: 15,
                topLightness: 52
            }
        ],
        [
            CreativeViewerCurveType.Confusion,
            {
                hexa: '#E87954',
                hue: 15,
                saturation: 76,
                bottomLightness: 10,
                topLightness: 62
            }
        ],
        [
            CreativeViewerCurveType.Disgust,
            {
                hexa: '#AF8950',
                hue: 36,
                saturation: 37,
                bottomLightness: 15,
                topLightness: 50
            }
        ],
        [
            CreativeViewerCurveType.AllReactions,
            {
                hexa: '#0FC7FF',
                hue: 194,
                saturation: 100,
                bottomLightness: 10,
                topLightness: 53
            }
        ],
        [
            CreativeViewerCurveType.Attention,
            {
                hexa: '#27EABF',
                hue: 167,
                saturation: 82,
                bottomLightness: 10,
                topLightness: 54
            }
        ],
        [
            CreativeViewerCurveType.Playback,
            {
                hexa: '#768489',
                hue: 193,
                saturation: 15,
                bottomLightness: 18,
                topLightness: 90
            }
        ],
        [
            CreativeViewerCurveType.NeutralAttention,
            {
                hexa: '#CBC1B0',
                hue: 38,
                saturation: 35,
                bottomLightness: 45,
                topLightness: 85
            }
        ]
    ]);
