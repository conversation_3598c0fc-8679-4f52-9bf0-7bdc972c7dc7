export const customGridAutocompleteRenderText = (
    checkedOptions: string[]
): string => {
    const maxTextLength = 17;

    const numberOfCheckedOptions = checkedOptions.length;

    if (!numberOfCheckedOptions) return 'Any';

    let filterText = '';
    let numberOfDisplayedOptions = 0;

    for (const orderedCheckedOption of checkedOptions) {
        const newFilterText =
            filterText +
            `${
                numberOfDisplayedOptions > 0 ? ', ' : ''
            }${orderedCheckedOption}`;

        if (newFilterText.length > maxTextLength) {
            if (numberOfDisplayedOptions === 0) {
                numberOfDisplayedOptions++;
                filterText = newFilterText.slice(0, maxTextLength) + '...';
            }

            const numberOfNotDisplayedOptions =
                numberOfCheckedOptions - numberOfDisplayedOptions;

            if (numberOfNotDisplayedOptions)
                filterText += ` (+${numberOfNotDisplayedOptions})`;

            break;
        }

        filterText = newFilterText;
        numberOfDisplayedOptions++;
    }

    return filterText;
};
