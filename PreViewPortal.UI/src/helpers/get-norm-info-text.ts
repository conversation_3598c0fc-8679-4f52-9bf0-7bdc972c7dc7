import { ExposureGroupType } from '@/contexts/creative-viewer-context';
import { ExposureLabel } from '@/interfaces/exposure-label';
import { NormCategory } from '@/interfaces/norm-category';
import { NormData } from '@/interfaces/norm-data';
import {
    getAdFormat,
    getCountry,
    getNormValue,
    hasCustomNormsForMetric
} from '@/constant/norm-tooltip-helper';

export interface NormInfoTextProps {
    exposureGroup: ExposureGroupType;
    normFallback: number | undefined;
    segmentKey: string | undefined;
    normSegmentKey: string | undefined;
    segmentKeyLabel?: string;
    normCategory?: NormCategory;
    norm?: NormData;
}

export const getNormInfoText = ({
    exposureGroup,
    segmentKey,
    normFallback,
    segmentKeyLabel,
    norm,
    normCategory
}: NormInfoTextProps) => {
    if (!norm) return null;

    if (hasCustomNormsForMetric(norm, exposureGroup, normCategory)) {
        return "Personalized norms applied. See 'Details' for specifics.";
    }

    const isInContextExposure = exposureGroup === 'inContext';

    const exposureLabel = isInContextExposure
        ? ExposureLabel.InContext
        : ExposureLabel.Focused;

    const adformatName =
        normFallback !== 0
            ? '-'
            : (getAdFormat(normCategory, exposureGroup, norm) ?? '-');

    const environmentCategory = getNormValue(
        'EnvironmentCategory',
        normCategory,
        exposureGroup,
        norm
    );

    const duration = Number.parseInt(
        getNormValue('Duration', normCategory, exposureGroup, norm) ?? '-'
    );

    const country = getCountry(normCategory, exposureGroup, norm)?.[0];

    const geographicRegion = getNormValue(
        'Region',
        normCategory,
        exposureGroup,
        norm
    );

    const normBasement = isInContextExposure
        ? normFallback === 0
            ? ` (${adformatName})`
            : ` (${environmentCategory} category)`
        : '';

    const durationPart =
        normCategory === NormCategory.SurveyNorm
            ? 'all'
            : ` ${Math.min(duration || 0, 60)} sec of`;

    const region =
        normFallback === 2 ? 'all regions' : (country ?? geographicRegion);

    const segmentFiltering =
        segmentKey !== 'all'
            ? ` filtered by the segment: ${segmentKeyLabel}`
            : ' among all audiences';

    return `${exposureLabel}${normBasement} norm based on ${durationPart} creatives tested in ${region}${segmentFiltering}.`;
};
