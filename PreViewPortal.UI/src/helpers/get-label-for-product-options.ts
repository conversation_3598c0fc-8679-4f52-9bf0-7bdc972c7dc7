import { ProductOptions } from '@/interfaces/product-options';

export const getLabelForProductOptions = (
    productOptions: ProductOptions | null,
    isAdmin?: boolean
) => {
    switch (productOptions) {
        case ProductOptions.InContext:
            return 'PreView In-Context';
        case ProductOptions.NewForcedExposure:
            return isAdmin
                ? 'PreView Focused Exposure - NEL6'
                : 'PreView Focused Exposure';
        default:
            return '';
    }
};
