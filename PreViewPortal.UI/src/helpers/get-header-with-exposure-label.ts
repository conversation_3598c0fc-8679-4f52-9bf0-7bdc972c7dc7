import { ExposureGroupType } from '@/contexts/creative-viewer-context';
import { ExposureLabel } from '@/interfaces/exposure-label';

export const getHeaderWithExposureLabel = (
    header: any,
    exposure: ExposureGroupType | undefined,
    diff?: boolean
): string =>
    exposure
        ? `${
              exposure === 'inContext'
                  ? ExposureLabel.InContext
                  : ExposureLabel.Focused
          } - ${header}${diff ? ' vs Norm' : ''}`
        : header;
