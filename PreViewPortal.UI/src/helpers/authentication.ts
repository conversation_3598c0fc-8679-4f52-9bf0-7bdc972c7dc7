import { oauthSettings } from '../constant/runtime-config';

export class Authentication {
    static login = () =>
        (window.location.href = `${oauthSettings.authServiceUrl}/login?client_id=${oauthSettings.clientId}&response_type=${oauthSettings.responseType}&scope=${oauthSettings.scopes}&redirect_uri=${oauthSettings.callbackUrl}`);

    static logout = () =>
        (window.location.href = `${oauthSettings.authServiceUrl}/logout?client_id=${oauthSettings.clientId}&logout_uri=${oauthSettings.logoutUrl}`);
}
