import { AccountLookupModel } from '@/interfaces/account-lookup-model';

export class SupervisedAccountInfo {
    static readonly headerName = 'RE-SUPERVISED-ACCOUNT';

    static get persistedAccount(): AccountLookupModel | null {
        const account = localStorage.getItem(this.headerName);

        return account ? JSON.parse(account) : null;
    }

    static set persistedAccount(account: AccountLookupModel | null) {
        if (!account) {
            localStorage.removeItem(this.headerName);
            return;
        }

        localStorage.setItem(this.headerName, JSON.stringify(account));
    }
}
