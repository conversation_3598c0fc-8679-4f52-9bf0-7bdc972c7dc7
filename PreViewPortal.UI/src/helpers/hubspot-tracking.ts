import { hubspot } from '../constant/runtime-config';

export class HubSpotTracking {
    private static _hsq = (window._hsq = window._hsq || []);

    static initalize() {
        if (!hubspot.isTrackingEnabled) return;

        const script = document.createElement('script');
        script.type = 'text/javascript';
        script.id = 'hs-script-loader';
        script.async = true;
        script.defer = true;
        script.src = `//js.hs-scripts.com/${hubspot.accoundId}.js`;

        document.body.appendChild(script);
    }

    static sendEvent(
        name: 'setPath' | 'identify' | 'trackPageView',
        value?: any
    ) {
        if (!this._hsq.push) return;

        this._hsq.push([name, value]);
    }
}
