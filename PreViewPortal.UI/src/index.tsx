import { LicenseInfo } from '@mui/x-data-grid-premium';
import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import { materialLicense } from './constant/runtime-config';
import { HubSpotTracking } from './helpers/hubspot-tracking';
import './index.css';
import '../node_modules/flag-icons/css/flag-icons.min.css';

LicenseInfo.setLicenseKey(materialLicense);

HubSpotTracking.initalize();

ReactDOM.createRoot(document.getElementById('root')!).render(
    <React.StrictMode>
        <App />
    </React.StrictMode>
);
