import { ExposureGroupType } from '@/contexts/creative-viewer-context';
import { CreativeViewerCurveSecondModel } from './creative-viewer-curve-second-model';
import { CreativeViewerCurveType } from './creative-viewer-curve-type';

export interface CreativeViewerCurveModel {
    type: CreativeViewerCurveType;
    segmentKey?: string;
    isNorm: boolean;
    exposureGroup: ExposureGroupType;
    views?: number;
    values: CreativeViewerCurveSecondModel[];
}
