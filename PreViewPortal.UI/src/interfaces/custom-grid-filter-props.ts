import { CustomGridFilterDefinition } from '../interfaces/custom-grid-filter-definition';
import { CustomGridFilterValueType } from './custom-grid-filter-value-type';
import { CustomGridMultiOptionType } from './custom-grid-multi-option-type';

export interface CustomGridFilterProps<
    TValue extends CustomGridFilterValueType
> {
    value: TValue;
    options: string[];
    multiOptions?: CustomGridMultiOptionType[];
    filterDefinition: CustomGridFilterDefinition<unknown>;
    onValueChange: (field: string, value: CustomGridFilterValueType) => void;
    onClearValue: (field: string) => void;
    onUpdateFilter: () => void;
    onRemove?: (field: string) => void;
    maxHeight?: number;
    customAutoGridRenderTextList?: string[];
}
