import { NormData } from '@/interfaces/norm-data';

export interface CreativePerformanceForcedExposureListModel extends NormData {
    sourceMediaID?: number;
    sourceMedia?: string;
    testID?: number;
    taskID?: string;
    customAdSets?: string;
    order?: string
    surveyKeyAlias?: string;
    orderExternalKey ?: string;
    orderAdSetID?: number;
    platform?: string;
    device?: string;
    deviceCount?: number;
    adformat?: string;
    adformatCount?: number;
    adformatText?: string;
    brand?: string;
    brandID?: number;
    brandCount?: number;
    country?: string;
    countryCount?: number;
    country_code?: string;
    geographicRegionCount?: number;
    creationDate?: Date;
    subCategory?: string;
    subCategoryCount?: number;
    midCategory?: string;
    midCategoryCount?: number;
    topCategory?: string;
    topCategoryCount?: number;
    brandLogoUrl?: string;
    thumbnailUrl?: string;
    adformatTextIF?: string;
    qualityScore?: number;
    qualityScore_index?: number;
    attentionAvg?: number;
    attentionAvgMedian?: number;
    attentionAvgRank?: number;
    attentionAvgDiff?: number;
    attentiveSeconds?: number;
    attentiveSecondsMedian?: number;
    attentiveSecondsRank?: number;
    attentiveSecondsDiff?: number;
    attentiveSecondsVTR?: number;
    attentiveSecondsVTRMedian?: number;
    attentiveSecondsVTRRank?: number;
    attentiveSecondsVTRDiff?: number;
    reactions?: number;
    reactionsMedian?: number;
    reactionsRank?: number;
    reactionsDiff?: number;
    negativityAvg?: number;
    negativityAvgMedian?: number;
    negativityAvgRank?: number;
    negativityAvgDiff?: number;
    happyPeak?: number;
    happyPeakMedian?: number;
    happyPeakRank?: number;
    happyPeakDiff?: number;
    vtr?: number;
    vtrRank?: number;
    vtrMedian?: number;
    vtrDiff?: number;
    playbackSeconds?: number;
    playbackSecondsRank?: number;
    playbackSecondsMedian?: number;
    playbackSecondsDiff?: number;
    surprisePeak?: number;
    surprisePeakRank?: number;
    surprisePeakMedian?: number;
    surprisePeakDiff?: number;
    confusionPeak?: number;
    confusionPeakRank?: number;
    confusionPeakMedian?: number;
    confusionPeakDiff?: number;
    contemptPeak?: number;
    contemptPeakRank?: number;
    contemptPeakMedian?: number;
    contemptPeakDiff?: number;
    disgustPeak?: number;
    disgustPeakRank?: number;
    disgustPeakMedian?: number;
    disgustPeakDiff?: number;
    distractionAvg?: number;
    distractionAvgRank?: number;
    distractionAvgMedian?: number;
    distractionAvgDiff?: number;
}
