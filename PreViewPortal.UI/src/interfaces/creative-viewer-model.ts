import { SurveyScore } from '@/constant/survey-scores-def';
import { CreativeViewerCurvesModel } from './creative-viewer-curves-model';
import { CreativeViewerVideoModel } from './creative-viewer-video-model';
import { SegmentKeysInfo } from './segment-keys-info';
import { CreativeSelectionModel } from '@/interfaces/creative-selection-model';
import { ProductOptions } from '@/interfaces/product-options';
import { NormData } from '@/interfaces/norm-data';

export interface CreativeViewerModel {
    videoData: CreativeViewerVideoModel[];
    curveData: CreativeViewerCurvesModel[];
    media: CreativeSelectionModel[];
    selectedProduct?: ProductOptions;
    shareKey: string | null;
    segmentData?: SegmentKeysInfo;
    surveyScoreData?: SurveyScore[];
    normData?: NormData[];
}
