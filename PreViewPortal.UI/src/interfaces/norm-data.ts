import { NormFilters } from '@/interfaces/norm-filters';

export interface NormData {
    videoId?: string;
    segmentKey?: string;

    views?: number;
    segmentLabel?: string;
    normSegmentKeyIC?: string;
    normSegmentKeyIF?: string;
    normSegmentKey?: string;
    normFallbackIC?: number;
    normFallbackIF?: number;
    normFallback?: number;
    normFallbackSurveyIC?: number;
    normFallbackSurveyIF?: number;
    duration?: number;
    geographicRegion?: string;

    reactionsNormCustomNormFilters?: NormFilters;
    attentionNormCustomNormFilters?: NormFilters;
    surveyNormCustomNormFilters?: NormFilters;
    reactionsNormCustomNormFiltersIC?: NormFilters;
    attentionNormCustomNormFiltersIC?: NormFilters;
    surveyNormCustomNormFiltersIC?: NormFilters;
    reactionsNormCustomNormFiltersIF?: NormFilters;
    attentionNormCustomNormFiltersIF?: NormFilters;
    surveyNormCustomNormFiltersIF?: NormFilters;

    attentionNormDevice?: string;
    reactionsNormDevice?: string;
    surveyNormDevice?: string;
    attentionNormDeviceIC?: string;
    reactionsNormDeviceIC?: string;
    surveyNormDeviceIC?: string;
    attentionNormDeviceIF?: string;
    reactionsNormDeviceIF?: string;
    surveyNormDeviceIF?: string;

    attentionNormFormat?: string;
    reactionsNormFormat?: string;
    surveyNormFormat?: string;
    attentionNormFormatIC?: string;
    reactionsNormFormatIC?: string;
    surveyNormFormatIC?: string;
    attentionNormFormatIF?: string;
    reactionsNormFormatIF?: string;
    surveyNormFormatIF?: string;

    attentionNormDuration?: number;
    reactionsNormDuration?: number;
    surveyNormDuration?: number;
    attentionNormDurationIC?: number;
    reactionsNormDurationIC?: number;
    surveyNormDurationIC?: number;
    attentionNormDurationIF?: number;
    reactionsNormDurationIF?: number;
    surveyNormDurationIF?: number;

    attentionNormAdFormat?: string;
    reactionsNormAdFormat?: string;
    surveyNormAdFormat?: string;
    attentionNormAdFormatIC?: string;
    reactionsNormAdFormatIC?: string;
    surveyNormAdFormatIC?: string;
    attentionNormAdFormatIF?: string;
    reactionsNormAdFormatIF?: string;
    surveyNormAdFormatIF?: string;

    attentionNormAdFormatName?: string;
    reactionsNormAdFormatName?: string;
    surveyNormAdFormatName?: string;
    attentionNormAdFormatNameIC?: string;
    reactionsNormAdFormatNameIC?: string;
    surveyNormAdFormatNameIC?: string;
    attentionNormAdFormatNameIF?: string;
    reactionsNormAdFormatNameIF?: string;
    surveyNormAdFormatNameIF?: string;

    attentionNormEnvironmentCategory?: string;
    reactionsNormEnvironmentCategory?: string;
    surveyNormEnvironmentCategory?: string;
    attentionNormEnvironmentCategoryIC?: string;
    reactionsNormEnvironmentCategoryIC?: string;
    surveyNormEnvironmentCategoryIC?: string;
    attentionNormEnvironmentCategoryIF?: string;
    reactionsNormEnvironmentCategoryIF?: string;
    surveyNormEnvironmentCategoryIF?: string;

    attentionNormRegion?: string;
    reactionsNormRegion?: string;
    surveyNormRegion?: string;
    attentionNormRegionIC?: string;
    reactionsNormRegionIC?: string;
    surveyNormRegionIC?: string;
    attentionNormRegionIF?: string;
    reactionsNormRegionIF?: string;
    surveyNormRegionIF?: string;

    attentionNormSampleSize?: number;
    reactionsNormSampleSize?: number;
    surveyNormSampleSize?: number;
    attentionNormSampleSizeIC?: number;
    reactionsNormSampleSizeIC?: number;
    surveyNormSampleSizeIC?: number;
    attentionNormSampleSizeIF?: number;
    reactionsNormSampleSizeIF?: number;
    surveyNormSampleSizeIF?: number;

    attentionNormRefreshDate?: string;
    reactionsNormRefreshDate?: string;
    surveyNormRefreshDate?: string;
    attentionNormRefreshDateIC?: string;
    reactionsNormRefreshDateIC?: string;
    surveyNormRefreshDateIC?: string;
    attentionNormRefreshDateIF?: string;
    reactionsNormRefreshDateIF?: string;
    surveyNormRefreshDateIF?: string;
}
