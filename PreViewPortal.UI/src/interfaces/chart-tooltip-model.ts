import { CreativeViewerCurveType } from './creative-viewer-curve-type';

export interface ChartTooltipDataModel {
    id: string;
    type: CreativeViewerCurveType;
    segmentKey?: string;
    pct?: number;
    sourceMedia: string;
    isNorm: boolean;
    views?: number;
}

export interface ChartTooltipModel {
    inContextExposureGroup: ChartTooltipDataModel[];
    focusedExposureGroup: ChartTooltipDataModel[];
}
