import { NormData } from '@/interfaces/norm-data';

export interface CreativePerformanceInContextListModel extends NormData {
    sourceMediaID?: number;
    sourceMedia?: string;
    testID?: number;
    inFocusTestID?: number;
    surveyKeyAlias?: string;
    taskID?: string;
    order?: string;
    customAdSets?: string;
    orderExternalKey?: string;
    orderAdSetID?: number;
    platform?: string;
    device?: string;
    deviceCount?: number;
    adformat?: string;
    adformatCount?: number;
    adformatText?: string;
    adformatTextIF?: string;
    brand?: string;
    brandID?: number;
    brandCount?: number;
    country?: string;
    countryCount?: number;
    country_code?: string;
    geographicRegionCount?: number;
    creationDate?: Date;
    subCategory?: string;
    subCategoryCount?: number;
    midCategory?: string;
    midCategoryCount?: number;
    topCategory?: string;
    topCategoryCount?: number;
    brandLogoUrl?: string;
    thumbnailUrl?: string;
    isEnabledForClient?: boolean;
    qualityScore?: number;
    qualityScore_index?: number;
    attentiveSeconds?: number;
    attentiveSecondsMedian?: number;
    attentiveSecondsRank?: number;
    attentiveSecondsDiff?: number;
    attentiveSecondsIF?: number;
    attentiveSecondsIFMedian?: number;
    attentiveSecondsIFRank?: number;
    attentiveSecondsIFDiff?: number;
    attentiveSecondsVTR?: number;
    attentiveSecondsVTRMedian?: number;
    attentiveSecondsVTRRank?: number;
    attentiveSecondsVTRDiff?: number;
    attentiveSecondsVTRIF?: number;
    attentiveSecondsVTRIFMedian?: number;
    attentiveSecondsVTRIFRank?: number;
    attentiveSecondsVTRIFDiff?: number;
    attentionAvgIC?: number;
    attentionAvgICMedian?: number;
    attentionAvgICRank?: number;
    attentionAvgICDiff?: number;
    attentionAvgIF?: number;
    attentionAvgIFMedian?: number;
    attentionAvgIFRank?: number;
    attentionAvgIFDiff?: number;
    reactions?: number;
    reactionsMedian?: number;
    reactionsRank?: number;
    reactionsDiff?: number;
    reactionsIC?: number;
    reactionsICMedian?: number;
    reactionsICRank?: number;
    reactionsICDiff?: number;
    negativityAvgIC?: number;
    negativityAvgICMedian?: number;
    negativityAvgICRank?: number;
    negativityAvgICDiff?: number;
    negativityAvgIF?: number;
    negativityAvgIFMedian?: number;
    negativityAvgIFRank?: number;
    negativityAvgIFDiff?: number;
    brandRecognition?: number;
    brandRecognitionMedian?: number;
    brandRecognitionRank?: number;
    brandRecognitionDiff?: number;
    adLikeability?: number;
    adLikeabilityMedian?: number;
    adLikeabilityRank?: number;
    adLikeabilityDiff?: number;
    happyPeak?: number;
    happyPeakMedian?: number;
    happyPeakRank?: number;
    happyPeakDiff?: number;
    happyPeakIC?: number;
    happyPeakICMedian?: number;
    happyPeakICRank?: number;
    happyPeakICDiff?: number;
    vtr?: number;
    vtrRank?: number;
    vtrMedian?: number;
    vtrDiff?: number;
    vtrif?: number;
    vtrifRank?: number;
    vtrifMedian?: number;
    vtrifDiff?: number;
    playbackSeconds?: number;
    playbackSecondsRank?: number;
    playbackSecondsMedian?: number;
    playbackSecondsDiff?: number;
    playbackSecondsIF?: number;
    playbackSecondsIFRank?: number;
    playbackSecondsIFMedian?: number;
    playbackSecondsIFDiff?: number;
    adRecognition?: number;
    adRecognitionRank?: number;
    adRecognitionMedian?: number;
    adRecognitionDiff?: number;
    surprisePeak?: number;
    surprisePeakRank?: number;
    surprisePeakMedian?: number;
    surprisePeakDiff?: number;
    surprisePeakIC?: number;
    surprisePeakICRank?: number;
    surprisePeakICMedian?: number;
    surprisePeakICDiff?: number;
    confusionPeak?: number;
    confusionPeakRank?: number;
    confusionPeakMedian?: number;
    confusionPeakDiff?: number;
    confusionPeakIC?: number;
    confusionPeakICRank?: number;
    confusionPeakICMedian?: number;
    confusionPeakICDiff?: number;
    contemptPeak?: number;
    contemptPeakRank?: number;
    contemptPeakMedian?: number;
    contemptPeakDiff?: number;
    contemptPeakIC?: number;
    contemptPeakICRank?: number;
    contemptPeakICMedian?: number;
    contemptPeakICDiff?: number;
    disgustPeak?: number;
    disgustPeakRank?: number;
    disgustPeakMedian?: number;
    disgustPeakDiff?: number;
    disgustPeakIC?: number;
    disgustPeakICRank?: number;
    disgustPeakICMedian?: number;
    disgustPeakICDiff?: number;
    brandTrust?: number;
    brandTrustRank?: number;
    brandTrustMedian?: number;
    brandTrustDiff?: number;
    persuasion?: number;
    persuasionRank?: number;
    persuasionMedian?: number;
    persuasionDiff?: number;
    distractionAvgIC?: number;
    distractionAvgICRank?: number;
    distractionAvgICMedian?: number;
    distractionAvgICDiff?: number;
    distractionAvgIF?: number;
    distractionAvgIFRank?: number;
    distractionAvgIFMedian?: number;
    distractionAvgIFDiff?: number;
}
