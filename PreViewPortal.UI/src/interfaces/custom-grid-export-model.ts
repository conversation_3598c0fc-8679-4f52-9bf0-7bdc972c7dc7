import { CreativeSelectionModel } from './creative-selection-model';
import { CustomGridFilterItemModel } from './custom-grid-filter-item-model';
import { CustomGridSortItemModel } from './custom-grid-sort-item-model';
import { SelectedCurveType } from '@/contexts/creative-viewer-context';

export interface CustomGridExportModel {
    filters: CustomGridFilterItemModel[];
    visibleColumnsOrder: string[];
    sorting: CustomGridSortItemModel[];
    media: CreativeSelectionModel[];
    segmentKeys: string[];
    exportFileName: string;
    selectedCurveTypes?: SelectedCurveType[];
}
