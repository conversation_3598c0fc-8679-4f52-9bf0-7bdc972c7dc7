import { ExposureGroupType } from '@/contexts/creative-viewer-context';
import { CustomGridFilterInput } from '../interfaces/custom-grid-filter-input';


export interface CustomGridFilterDefinition<TModel> {
    field: keyof TModel & string;
    label: string;
    groupLabel?: string;
    filterType: CustomGridFilterInput;
    defaultVisible: boolean;
    optionsName?: string;
    step?: number;
    prefix?: string;
    postfix?: string;
    displayMultiplier?: number;
    valueToTextMap?: Map<string, string>;
    orQueryGroupId?: number;
    noRender?: boolean;
    exposureGroup?: ExposureGroupType;
    isDiff?: boolean;
}
