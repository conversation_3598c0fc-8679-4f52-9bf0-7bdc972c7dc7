import React, { useContext } from 'react';
import {
    Stack,
    Switch,
    Tooltip,
    TooltipProps,
    Typography
} from '@mui/material';
import { minViewThresholdForScore } from '@/constant/runtime-config';
import {
    getNormInfoText,
    NormInfoTextProps
} from '@/helpers/get-norm-info-text';
import {
    DiffCell,
    PercentageCell,
    SecondBasedScoreCell
} from './GridRenderCell';
import { ScoreBase } from '@/interfaces/score-base';
import { NormTooltipContext } from '@/contexts/norm-tooltip-context';
import TooltipDetailTable from '@/components/TooltipDetailTable';
import {
    getNormCategory,
    getNormDataTable
} from '@/constant/norm-tooltip-helper';
import { useSupervisedAccount } from '@/hooks/use-supervised-account';
import { NormData } from '@/interfaces/norm-data';
import { NormCategory } from '@/interfaces/norm-category';
import { useUser } from '@/hooks/use-user';

interface Props<T> extends Partial<NormInfoTextProps> {
    field: keyof T;
    scoreField?: keyof T;
    diffField?: keyof T;
    normField?: keyof T;
    scoreName?: string;
    scoreBase?: ScoreBase;
    showNormTooltip?: boolean;
    row: T;
    normData?: NormData;
    rowType?: string;
    zoomScale?: number;
    tooltipOffset?: [number, number];
    tooltipPlacement?: TooltipProps['placement'];
    hasCountry?: boolean;
    hasIndustry?: boolean;
    hasBrand?: boolean;
    test?: boolean;
}

export const CellWithTooltip = <T extends NormData>({
    field,
    scoreField,
    diffField,
    normField,
    row,
    scoreName,
    scoreBase,
    exposureGroup,
    showNormTooltip,
    rowType,
    normData,
    segmentKeyLabel,
    children,
    tooltipPlacement,
    hasCountry,
    hasIndustry,
    hasBrand
}: React.PropsWithChildren<Props<T>>) => {
    const views = row.views;
    const {
        showDetails,
        setShowDetails,
        showFullNormData,
        setShowFullNormData
    } = useContext(NormTooltipContext);
    const { supervisedAccount } = useSupervisedAccount();
    const { isAdmin } = useUser();

    if (!views || views < minViewThresholdForScore) {
        return (
            <Tooltip
                placement='right'
                title={`Min. ${minViewThresholdForScore} views are required to calculate this score. Further tests are required.`}
            >
                <span>{'-'}</span>
            </Tooltip>
        );
    }

    const value = row[field];
    if (value === null || value === undefined) return <span>{'-'}</span>;

    const isPinnedRow = rowType === 'pinnedRow';
    const normValue = row[normField!];

    if (isPinnedRow || normValue === null || normValue === undefined) {
        return <span>{children}</span>;
    }

    const scoreValue = +row[scoreField || field];

    const normCategory = getNormCategory(normField?.toString());

    return (
        <Tooltip
            placement={tooltipPlacement || 'right-start'}
            slotProps={{
                popper: {
                    modifiers: [
                        {
                            name: 'preventOverflow',
                            enabled: true,
                            options: {
                                altAxis: false,
                                altBoundary: true,
                                tether: false,
                                rootBoundary: 'document',
                                padding: 32
                            }
                        }
                    ]
                },
                tooltip: {
                    sx: {
                        backgroundColor: 'unset',
                        fontWeight: 'unset',
                        fontSize: 14,
                        maxWidth: showNormTooltip ? 450 : undefined,
                        overflow: 'hidden'
                    }
                }
            }}
            title={
                <Stack gap={0.5}>
                    <Stack>
                        <Stack
                            bgcolor='#273235'
                            border={`1px solid #D4D6D7`}
                            borderRadius={'5px'}
                            p={2}
                            display='block'
                        >
                            <span
                                style={{
                                    fontFamily: 'Roboto',
                                    fontSize: '14px'
                                }}
                            >
                                {scoreName}:
                            </span>
                            &nbsp;
                            <strong
                                style={{
                                    fontFamily: 'Roboto',
                                    fontSize: '14px',
                                    fontWeight: 'bold'
                                }}
                            >
                                {scoreBase === 'sec' ? (
                                    <SecondBasedScoreCell value={scoreValue} />
                                ) : scoreBase === '%' ? (
                                    <PercentageCell value={scoreValue} />
                                ) : (
                                    scoreValue.toFixed(1)
                                )}
                            </strong>
                            &nbsp; (
                            <DiffCell
                                value={+row[diffField!]}
                                isGridMode={false}
                            />
                            )
                            <br />
                            <span
                                style={{
                                    fontFamily: 'Roboto',
                                    fontSize: '14px'
                                }}
                            >
                                Norm:
                            </span>
                            &nbsp;
                            <strong
                                style={{
                                    fontFamily: 'Roboto',
                                    fontSize: '14px',
                                    fontWeight: 'bold'
                                }}
                            >
                                {scoreBase === 'sec' ? (
                                    <SecondBasedScoreCell value={+normValue} />
                                ) : scoreBase === '%' ? (
                                    <PercentageCell value={+normValue} />
                                ) : (
                                    (+normValue).toFixed(1)
                                )}
                            </strong>
                        </Stack>
                    </Stack>

                    {showNormTooltip && (
                        <Stack
                            bgcolor='white'
                            color='black'
                            border={`1px solid #D4D6D7`}
                            borderRadius={'5px'}
                            p={2}
                            pb={1}
                        >
                            {(() => {
                                const {
                                    normFallback,
                                    normFallbackIC,
                                    normSegmentKeyIC,
                                    normFallbackIF,
                                    normSegmentKeyIF,
                                    normFallbackSurveyIF,
                                    normFallbackSurveyIC,
                                    segmentKey,
                                    segmentLabel
                                } = normData ?? row;

                                const normFallbackForExposure =
                                    normFallback ||
                                    (normCategory === NormCategory.SurveyNorm
                                        ? exposureGroup === 'inContext'
                                            ? normFallbackSurveyIC
                                            : normFallbackSurveyIF
                                        : exposureGroup === 'inContext'
                                          ? normFallbackIC
                                          : normFallbackIF);

                                const normSegmentKeyForExposure =
                                    segmentKey ||
                                    (exposureGroup === 'inContext'
                                        ? normSegmentKeyIC
                                        : normSegmentKeyIF);

                                const normInfoText = getNormInfoText({
                                    normCategory: normCategory,
                                    exposureGroup: exposureGroup!,
                                    normFallback: normFallbackForExposure,
                                    segmentKey,
                                    norm: normData ?? row,
                                    normSegmentKey: normSegmentKeyForExposure,
                                    segmentKeyLabel:
                                        segmentKeyLabel || segmentLabel
                                });

                                const audienceLabel =
                                    segmentKey !== 'all'
                                        ? normSegmentKeyForExposure ===
                                          segmentKey
                                            ? segmentKeyLabel || segmentLabel
                                            : 'Total Audience'
                                        : 'Total Audience';

                                const tableData = getNormDataTable(
                                    showFullNormData,
                                    supervisedAccount?.hasCustomNorm,
                                    normCategory,
                                    exposureGroup,
                                    normData ?? row,
                                    normFallbackForExposure,
                                    audienceLabel,
                                    hasCountry,
                                    hasIndustry,
                                    hasBrand
                                );

                                return (
                                    <>
                                        <Typography
                                            fontSize={14}
                                            fontFamily='Roboto'
                                            color='#273235'
                                            mb={1}
                                        >
                                            Norm:&nbsp;{scoreName}
                                        </Typography>
                                        <Typography
                                            variant='caption'
                                            fontFamily='Roboto'
                                            color='#505B5F'
                                            fontSize={14}
                                            display='block'
                                            lineHeight={1.4}
                                        >
                                            {normInfoText}
                                        </Typography>
                                        <Stack
                                            direction='row'
                                            alignItems='center'
                                            pt={1}
                                        >
                                            <Typography
                                                variant='body1'
                                                fontSize={14}
                                                fontFamily='Roboto'
                                                color='#273235'
                                            >
                                                Details
                                            </Typography>
                                            <Switch
                                                checked={showDetails}
                                                onChange={event =>
                                                    setShowDetails(
                                                        event.target.checked
                                                    )
                                                }
                                            />
                                        </Stack>

                                        {showDetails && (
                                            <TooltipDetailTable
                                                showFullNormData={
                                                    showFullNormData
                                                }
                                                setShowFullNormData={
                                                    setShowFullNormData
                                                }
                                                isAdmin={isAdmin}
                                                tableData={tableData}
                                            />
                                        )}
                                    </>
                                );
                            })()}
                        </Stack>
                    )}
                </Stack>
            }
        >
            <span>{children}</span>
        </Tooltip>
    );
};
