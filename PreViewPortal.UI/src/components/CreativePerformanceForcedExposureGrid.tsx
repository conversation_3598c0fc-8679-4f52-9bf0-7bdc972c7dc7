import { Box, Stack } from '@mui/material';
import { CustomGrid } from './custom-grid/CustomGrid';
import { useMemo, useState } from 'react';
import { CustomGridColumnMenu } from './custom-grid/CustomGridColumnMenu';
import {
    GRID_CHECKBOX_SELECTION_COL_DEF,
    GridRowParams,
    GridRowSelectionModel
} from '@mui/x-data-grid-premium';
import { PreViewGrid } from '@/interfaces/preview-grid';
import { ProductTypeSelector } from './ProductTypeSelector';
import { CreativePerformanceForcedExposurePageModel } from '@/interfaces/creative-performance-forced-exposure-model';
import { CreativePerformanceForcedExposureListModel } from '@/interfaces/creative-performance-forced-exposure-list-model';
import {
    creativePerformanceForcedExposureColumnGroupModel,
    defaultCreativePerformanceForcedExposureColumnsVisibility,
    defaultCreativePerformanceForcedExposureSortModel,
    getCreativePerformanceForcedExposureColDef
} from '@/constant/creative-performance-forced-exposure-col-def';
import { CreativeViewerDrawer } from './creative-viewer/CreativeViewerDrawer';
import { getCreativePerformanceForcedExposureFilterDef } from '@/constant/creative-performance-forced-exposure-filter-def';
import { useUser } from '@/hooks/use-user';
import { SegmentKeyLabel } from '@/interfaces/segment-key-label';
import { fullSegment } from '@/constant/full-segment';
import { GridSegmentSelector } from './GridSegmentSelector';
import {
    CustomToolbar,
    CustomToolbarProps
} from './custom-grid/CustomGridToolbar';
import { getSegmentQuestionAndAnswerLabel } from '@/helpers/get-segment-question-and-answer-label';
import { creativeViewerMediaLimit } from '@/constant/runtime-config';
import { useSnackbar } from 'notistack';
import {
    CustomGridColumnHeader,
    CustomGridColumnHeaderProps
} from './custom-grid/CustomGridColumnHeader';
import { ScoringAndNormsPanel } from './ScoringAndNormsPanel';
import {
    hasAnyIndustryInList,
    hasBrandInList,
    hasCountryInList
} from '@/constant/norm-tooltip-helper';
import { isTextHighlighted } from '@/helpers/is-text-highlighted';

const endpointUrl = 'creativeperformanceforcedexposure';

export const CreativePerformanceForcedExposureGrid = () => {
    const [gridData, setGridData] =
        useState<CreativePerformanceForcedExposurePageModel>();

    const [isLoading, setIsLoading] = useState<boolean>();

    const [selectionModel, setSelectionModel] = useState<GridRowSelectionModel>(
        []
    );

    const [isCreativeViewerOpen, setIsCreativeViewerOpen] = useState(false);

    const [showNormTooltip, setShowNormTooltip] = useState(true);

    const [selectedSegment, setSelectedSegment] =
        useState<SegmentKeyLabel>(fullSegment);

    const { isAdmin } = useUser();
    const { enqueueSnackbar } = useSnackbar();

    const columns = useMemo(() => {
        const segmentKeyLabel =
            getSegmentQuestionAndAnswerLabel(selectedSegment);
        const rows = gridData?.rows ?? [];

        const hasCountry = hasCountryInList(rows);
        const hasIndustry = hasAnyIndustryInList(rows);
        const hasBrand = hasBrandInList(rows);

        return getCreativePerformanceForcedExposureColDef(
            isAdmin,
            showNormTooltip,
            segmentKeyLabel,
            hasCountry,
            hasIndustry,
            hasBrand
        );
    }, [gridData?.rows, isAdmin, showNormTooltip, selectedSegment]);

    const filtersDef = useMemo(
        () => getCreativePerformanceForcedExposureFilterDef(isAdmin),
        [isAdmin]
    );

    const handleCloseCreativeViewer = () => {
        setIsCreativeViewerOpen(false);
        setSelectionModel([]);
    };

    const handleSegmentChange = (segmentKey: SegmentKeyLabel) => {
        setSelectedSegment(segmentKey);

        setSelectionModel([]);
    };

    const handleRowClick = ({
        id
    }: GridRowParams<CreativePerformanceForcedExposureListModel>) => {
        if (isAdmin && isTextHighlighted()) return;

        const countOfSelectedRows = selectionModel.length;

        if (!countOfSelectedRows) {
            setSelectionModel([id]);
        }

        if (selectionModel.length > creativeViewerMediaLimit) {
            enqueueSnackbar(
                `Maximum ${creativeViewerMediaLimit} test can be compared.`,
                {
                    variant: 'warning'
                }
            );

            return;
        }

        setIsCreativeViewerOpen(true);
    };

    const handleClickOnCreativeViewer = () => {
        setIsCreativeViewerOpen(true);
    };

    const selectedCreatives = useMemo(
        () =>
            selectionModel.map(id => {
                const [sourceMediaID, testID, orderAdSetID] = (
                    id as string
                ).split(';');

                return {
                    sourceMediaID: +sourceMediaID,
                    testID: +testID,
                    orderAdSetID: +orderAdSetID
                };
            }),
        [selectionModel]
    );

    const gridQueryParams = useMemo(
        () => ({
            segmentKey: selectedSegment.segmentKey
        }),
        [selectedSegment]
    );

    return (
        <Stack gap={1.5}>
            <Stack direction='row' gap={1.5}>
                <ProductTypeSelector />

                <ScoringAndNormsPanel />
            </Stack>

            <GridSegmentSelector
                isLoading={isLoading}
                endpointUrl={endpointUrl}
                selectedSegment={selectedSegment}
                onSelectedSegmentChange={handleSegmentChange}
            />

            <Box height={1000} mb={80}>
                <CustomGrid<CreativePerformanceForcedExposurePageModel>
                    gridData={gridData}
                    getRowId={r =>
                        `${r.sourceMediaID};${r.testID};${r.orderAdSetID}`
                    }
                    filterDefinitions={filtersDef}
                    endpointUrl={endpointUrl}
                    customQueryParams={gridQueryParams}
                    exportFileName='preview_forced-exposure_export'
                    selectedCreatives={selectedCreatives}
                    gridId={PreViewGrid.CreativePerformanceForcedExposure}
                    rowSelectionModel={selectionModel}
                    onRowClick={handleRowClick}
                    onRowSelectionModelChange={setSelectionModel}
                    checkboxSelection
                    slots={{
                        toolbar: CustomToolbar,
                        columnMenu: CustomGridColumnMenu,
                        columnHeaders: CustomGridColumnHeader
                    }}
                    slotProps={{
                        toolbar: {
                            onClickCreativeViewer: handleClickOnCreativeViewer,
                            showNormTooltip: showNormTooltip,
                            onShowNormTooltipChange: setShowNormTooltip,
                            withDefinitions: true
                        } as CustomToolbarProps,
                        columnHeaders: {
                            gridData,
                            selectedCreatives
                        } as CustomGridColumnHeaderProps
                    }}
                    disableRowSelectionOnClick
                    onRowsChange={setGridData}
                    onLoadingChange={setIsLoading}
                    initialState={{
                        columns: {
                            columnVisibilityModel:
                                defaultCreativePerformanceForcedExposureColumnsVisibility
                        },
                        pinnedColumns: {
                            left: [GRID_CHECKBOX_SELECTION_COL_DEF.field]
                        },
                        sorting:
                            defaultCreativePerformanceForcedExposureSortModel
                    }}
                    columns={columns}
                    columnGroupingModel={
                        creativePerformanceForcedExposureColumnGroupModel
                    }
                />
            </Box>

            <CreativeViewerDrawer
                open={isCreativeViewerOpen}
                selectedCreatives={selectedCreatives}
                onClose={handleCloseCreativeViewer}
            />
        </Stack>
    );
};
