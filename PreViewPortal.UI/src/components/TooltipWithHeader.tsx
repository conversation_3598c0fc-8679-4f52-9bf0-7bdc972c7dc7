import {
    Box,
    Divider,
    Tooltip,
    TooltipProps as MuiTooltipProps
} from '@mui/material';

interface TooltipProps extends Omit<MuiTooltipProps, 'title'> {
    body: React.ReactNode;
    width?: number;
    title?: React.ReactNode;
    footer?: React.ReactNode;
}

export const TooltipWithHeader = ({
    body,
    footer,
    width,
    title,
    ...props
}: TooltipProps) => (
    <Tooltip
        {...props}
        componentsProps={{
            tooltip: {
                sx: {
                    padding: 0,
                    backgroundColor: '#ffffff',
                    color: 'black',
                    borderRadius: 0,
                    border: `1px solid #ADADAD`,
                    width: width
                }
            }
        }}
        title={
            <>
                <Box
                    p={0.5}
                    pl={1}
                    sx={{
                        backgroundColor: '#273235',
                        color: 'white',
                        fontSize: 16
                    }}
                >
                    {title}
                </Box>
                <Divider
                    sx={{
                        borderColor: '#ADADAD'
                    }}
                />
                <Box p={1}>{body}</Box>
                {footer && (
                    <Box p={1} sx={{ backgroundColor: '#9997971f' }}>
                        {footer}
                    </Box>
                )}
            </>
        }
    />
);
