import React, { useContext, useEffect, useRef } from 'react';
import { ExportStatusContext } from '@/contexts/export-status-context';
import {
    Button,
    CircularProgress,
    Slide,
    Stack,
    Typography
} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import { STATUS_BAR_HEIGHT } from '@/constant/status-bar';

const ExportStatusBar = () => {
    const {
        exportResult,
        cancelExport,
        setExportResult,
        isAutoCompleteDownloaded,
        setIsAutoCompleteDownloaded
    } = useContext(ExportStatusContext);
    const containerRef = useRef(null);
    const isVisible = !!exportResult;

    const {
        jobStatus = '',
        exportUrl = '',
        exportFileName = ''
    } = exportResult || {};

    useEffect(() => {
        if (jobStatus === 'InitialLoading') {
            setIsAutoCompleteDownloaded(false);
        } else if (jobStatus === 'Completed') {
            setIsAutoCompleteDownloaded(true);
        }
        // eslint-disable-next-line
    }, [jobStatus]);

    const handleClick = () => {
        cancelExport();
    };

    const getBackgroundColor = () => {
        switch (jobStatus) {
            case 'Completed':
                return '#5AC57D';
            case 'Failed':
                return '#FB5C63';
            case 'Pending':
            case 'InitialLoading':
                return '#A5ABBF';
            default:
                return '';
        }
    };

    const getTitle = () => {
        switch (jobStatus) {
            case 'Completed':
                return 'Export completed';
            case 'Failed':
                return 'Export failed';
            case 'Pending':
            case 'InitialLoading':
                return 'Exporting report';
            default:
                return '';
        }
    };

    const getDescription = () => {
        switch (jobStatus) {
            case 'Pending':
            case 'InitialLoading':
                return 'This process can take a few minutes to complete. The file will be automatically downloaded.';
            case 'Failed':
                return 'An unexpected error occurred during the process. Please try restarting the export. If the problem persists, contact us for assistance.';
            default:
                return '';
        }
    };

    const handleDownload = (event?: React.MouseEvent) => {
        if (jobStatus !== 'Completed') {
            return;
        }

        event?.preventDefault();

        const link = document.createElement('a');
        link.href = exportUrl;
        link.setAttribute('download', exportFileName);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        setIsAutoCompleteDownloaded(true);

        if (exportUrl === '') {
            return;
        }
    };

    useEffect(() => {
        if (jobStatus === 'Completed' && !isAutoCompleteDownloaded) {
            handleDownload();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [jobStatus, isAutoCompleteDownloaded, exportUrl, exportFileName]);

    const handleCloseClick = () => {
        setExportResult(undefined);
    };

    if (!isVisible) {
        return;
    }

    return (
        <Stack
            ref={containerRef}
            alignItems='center'
            justifyContent='center'
            position='fixed'
            style={{
                background: getBackgroundColor()
            }}
            height={`${STATUS_BAR_HEIGHT}px`}
            width='100vw'
            zIndex={9999}
        >
            <Slide in={isVisible} container={containerRef.current}>
                <div
                    style={{
                        textAlign: 'center',
                        width: '100%',
                        height: '100%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        padding: '20px 40px'
                    }}
                >
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                        {(jobStatus === 'Pending' ||
                            jobStatus === 'InitialLoading') && (
                            <CircularProgress
                                color='inherit'
                                size={20}
                                sx={{ color: 'white', marginRight: 1 }}
                            />
                        )}
                        {jobStatus === 'Completed' && (
                            <CheckCircleIcon
                                sx={{ color: 'white', marginRight: 1 }}
                            />
                        )}
                        {jobStatus === 'Failed' && (
                            <ErrorIcon
                                sx={{ color: 'white', marginRight: 1 }}
                            />
                        )}

                        <Stack direction='row' spacing={0} alignItems='center'>
                            <Typography
                                variant='subtitle2'
                                style={{ margin: 0, color: 'white' }}
                            >
                                {getTitle()}
                            </Typography>
                            {(jobStatus === 'Pending' ||
                                jobStatus === 'Failed' ||
                                jobStatus === 'InitialLoading') && (
                                <Typography
                                    variant='body2'
                                    style={{
                                        margin: 0,
                                        color: 'white',
                                        marginLeft: '60px'
                                    }}
                                >
                                    {getDescription()}
                                </Typography>
                            )}
                            {jobStatus === 'Completed' && (
                                <Typography
                                    variant='body2'
                                    style={{
                                        margin: 0,
                                        color: 'white',
                                        marginLeft: '60px',
                                        cursor: 'pointer',
                                        textDecoration: 'underline'
                                    }}
                                    onClick={handleDownload}
                                >
                                    {exportFileName}
                                </Typography>
                            )}
                        </Stack>
                    </div>
                    {(jobStatus === 'Pending' ||
                        jobStatus === 'InitialLoading') && (
                        <Button
                            variant='text'
                            onClick={handleClick}
                            sx={{
                                marginLeft: 2,
                                color: 'white',
                                borderColor: 'white',
                                '&:hover': {
                                    color: 'white',
                                    borderColor: 'white',
                                    backgroundColor: 'rgba(255, 255, 255, 0.1)'
                                },
                                '&:focus': {
                                    color: 'white',
                                    borderColor: 'white'
                                },
                                '&:active': {
                                    color: 'white',
                                    borderColor: 'white'
                                }
                            }}
                        >
                            Cancel Export
                        </Button>
                    )}
                    {jobStatus === 'Failed' && (
                        <Button
                            variant='text'
                            onClick={handleCloseClick}
                            sx={{
                                marginLeft: 2,
                                color: 'white',
                                borderColor: 'white',
                                '&:hover': {
                                    color: 'white',
                                    borderColor: 'white',
                                    backgroundColor: 'rgba(255, 255, 255, 0.1)'
                                },
                                '&:focus': {
                                    color: 'white',
                                    borderColor: 'white'
                                },
                                '&:active': {
                                    color: 'white',
                                    borderColor: 'white'
                                }
                            }}
                        >
                            Close
                        </Button>
                    )}
                    {jobStatus === 'Completed' && (
                        <Button
                            variant='text'
                            onClick={handleCloseClick}
                            sx={{
                                marginLeft: 2,
                                color: 'white',
                                borderColor: 'white',
                                '&:hover': {
                                    color: 'white',
                                    borderColor: 'white',
                                    backgroundColor: 'rgba(255, 255, 255, 0.1)'
                                },
                                '&:focus': {
                                    color: 'white',
                                    borderColor: 'white'
                                },
                                '&:active': {
                                    color: 'white',
                                    borderColor: 'white'
                                }
                            }}
                        >
                            Done
                        </Button>
                    )}
                </div>
            </Slide>
        </Stack>
    );
};

export default ExportStatusBar;
