import { Close } from '@mui/icons-material';
import {
    Box,
    Button,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    Divider,
    IconButton,
    Typography
} from '@mui/material';
import React from 'react';

interface Props {
    title: string;
    isOpen: boolean;
    isConfirmDisabled?: boolean;
    cancelText?: string;
    confirmText?: string;
    onCancel: () => void;
    onConfirm: () => void;
    onClose?: () => void;
    dialogWidth?: number;
    errorConfirmColor?: boolean;
    confirmStartIcon?: React.ReactNode;
    cancelStartIcon?: React.ReactNode;
    footer?: React.ReactNode;
}

export const ConfirmDialog = ({
    title,
    isOpen,
    isConfirmDisabled = false,
    cancelText = 'Cancel',
    confirmText = 'Ok',
    children,
    dialogWidth = 600,
    errorConfirmColor,
    confirmStartIcon,
    cancelStartIcon,
    footer,
    onCancel,
    onConfirm,
    onClose
}: React.PropsWithChildren<Props>) => (
    <Dialog open={isOpen} onClose={onClose}>
        <DialogTitle sx={{ m: 0, p: 1 }}>
            <Box
                sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                }}
            >
                <Typography variant='h6'>{title}</Typography>
                <IconButton onClick={onClose}>
                    <Close />
                </IconButton>
            </Box>
        </DialogTitle>
        {children ? (
            <DialogContent sx={{ width: dialogWidth }} dividers>
                {children}
            </DialogContent>
        ) : (
            <Divider sx={{ width: dialogWidth }} />
        )}
        {footer && <DialogContent>{footer}</DialogContent>}
        <DialogActions>
            <Button
                variant='outlined'
                onClick={onCancel}
                startIcon={cancelStartIcon}
            >
                {cancelText}
            </Button>
            <Button
                variant='contained'
                disabled={isConfirmDisabled}
                onClick={onConfirm}
                color={errorConfirmColor ? 'error' : undefined}
                startIcon={confirmStartIcon}
            >
                {confirmText}
            </Button>
        </DialogActions>
    </Dialog>
);
