import React from 'react';
import {
    Paper,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableRow,
    Typography
} from '@mui/material';

export interface TableData {
    label: string;
    values?: string[];
    hasFallback: boolean;
}

interface TooltipDetailTableProps {
    tableData: TableData[];
    normFallback?: number;
    isAdmin?: boolean;
    showFullNormData?: boolean;
    setShowFullNormData: (showFullNormData: boolean) => void;
}

const TooltipDetailTable: React.FC<TooltipDetailTableProps> = ({
    isAdmin,
    tableData,
    showFullNormData,
    setShowFullNormData
}) => {
    return (
        <>
            <TableContainer
                component={Paper}
                sx={{
                    width: '100%',
                    marginTop: 1,
                    marginBottom: 1,
                    boxShadow: 'none',
                    border: 'none',
                    backgroundColor: 'transparent'
                }}
            >
                <Table size='small'>
                    <TableBody>
                        {tableData.map((item, index) => (
                            <TableRow
                                key={index}
                                sx={{
                                    boxShadow: 'none',
                                    border: 'none',
                                    backgroundColor:
                                        index % 2 === 0
                                            ? '#F2F2F2 !important'
                                            : 'white !important'
                                }}
                            >
                                <TableCell
                                    sx={{
                                        boxShadow: 'none',
                                        border: 'none',
                                        padding: '7px 10px',
                                        fontFamily: 'Roboto',
                                        fontSize: '12px',
                                        color: '#505B5F'
                                    }}
                                >
                                    {item.label}
                                </TableCell>
                                <TableCell
                                    sx={{
                                        boxShadow: 'none',
                                        border: 'none',
                                        whiteSpace: 'pre-line',
                                        wordWrap: 'break-word',
                                        fontFamily: 'Roboto',
                                        fontSize: '12px',
                                        color: '#273235',
                                        opacity: item.hasFallback ? 0.2 : 1
                                    }}
                                >
                                    {item.values?.join('\n')}
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </TableContainer>

            {isAdmin && (
                <Typography
                    onClick={() => setShowFullNormData(!showFullNormData)}
                    sx={{
                        color: '#273235',
                        fontFamily: 'Roboto',
                        fontSize: '12px',
                        textDecoration: 'underline',
                        textAlign: 'center',
                        cursor: 'pointer',
                        mt: 2,
                        mb: 1
                    }}
                >
                    {showFullNormData
                        ? 'Hide unused norm dimensions'
                        : 'Show unused norm dimensions'}
                </Typography>
            )}
        </>
    );
};

export default TooltipDetailTable;
