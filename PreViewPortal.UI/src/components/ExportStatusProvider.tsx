import React, { useEffect, useState } from 'react';
import { ExportStatusContext } from '@/contexts/export-status-context';
import { useHttp } from '@/hooks/use-http';
import { ExportResult } from '@/interfaces/export-result';

export const ExportStatusProvider = ({
    children
}: React.PropsWithChildren<{}>) => {
    const [exportResult, setExportResult] = useState<ExportResult | null>();
    const [isAutoCompleteDownloaded, setIsAutoCompleteDownloaded] =
        useState<boolean>(false);
    const [shareKey, setShareKey] = useState<string | undefined>();
    const { http, abort } = useHttp();
    const [polling, setPolling] = useState<NodeJS.Timeout | null>(null);
    const [startTime, setStartTime] = useState<number | null>(null);

    const [isExportCanceled, setExportCanceled] = useState<boolean>(false);

    const checkExportStatus = async () => {
        try {
            if (isExportCanceled) {
                return;
            }

            const queryParams = new URLSearchParams();

            if (exportResult?.jobStatus === 'InitialLoading') {
                return;
            }

            if (
                exportResult?.exportFolderPath &&
                exportResult.exportFolderPath.trim() !== ''
            ) {
                queryParams.append(
                    'exportPath',
                    exportResult.exportFolderPath ?? ''
                );
            }

            if (
                exportResult?.bucketName &&
                exportResult.bucketName.trim() !== ''
            ) {
                queryParams.append(
                    'exportBucket',
                    exportResult.bucketName ?? ''
                );
            }

            const response = await http
                .get(`export/export-status?${queryParams.toString()}`, {
                    headers: {
                        'X-Share-Key': shareKey || ''
                    }
                })
                .json<ExportResult>();

            setExportResult(response);

            if (
                response.jobStatus === 'Completed' ||
                response.jobStatus === 'Failed'
            ) {
                if (polling) {
                    clearInterval(polling);
                    setPolling(null);
                }
            }
        } catch (error) {
            console.error('Error fetching export status:', error);
        }
    };

    const cancelExport = async () => {
        setExportResult(null);
        setExportCanceled(true);

        cancelPolling();
        abort();
    };

    const cancelPolling = () => {
        if (polling) {
            clearInterval(polling);
            setPolling(null);
            setStartTime(null);
        }
    };

    useEffect(() => {
        setExportCanceled(false);

        if (exportResult?.jobStatus === 'InitialLoading') {
            return;
        }

        if (exportResult?.jobStatus === 'Pending' && !polling) {
            const intervalId = setInterval(checkExportStatus, 5000);
            setPolling(intervalId);
            setStartTime(Date.now());
        }

        if (
            exportResult?.jobStatus === 'Completed' ||
            exportResult?.jobStatus === 'Failed'
        ) {
            cancelPolling();
        }

        const currentTime = Date.now();
        if (startTime && currentTime - startTime >= 3 * 60 * 1000) {
            cancelPolling();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [exportResult, http, polling]);

    return (
        <ExportStatusContext.Provider
            value={{
                exportResult,
                setExportResult,
                cancelExport,
                setShareKey,
                isExportCanceled,
                isAutoCompleteDownloaded,
                setIsAutoCompleteDownloaded
            }}
        >
            {children}
        </ExportStatusContext.Provider>
    );
};
