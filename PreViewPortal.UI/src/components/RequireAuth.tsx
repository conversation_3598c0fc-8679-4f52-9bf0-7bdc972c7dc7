import { Navigate } from 'react-router';
import { useUser } from '../hooks/use-user';
import { Authentication } from '@/helpers/authentication';

export const RequireAuth = ({ children }: { children: React.JSX.Element }) => {
    const { isLoggedIn, hasAccess } = useUser();

    if (!isLoggedIn) {
        Authentication.login();
        return;
    }

    if (!hasAccess) {
        return <Navigate to='/Unauthorized' />;
    }

    return children;
};
