import { useReducer } from 'react';
import { ConfirmDialogContext } from '../contexts/confirm-dialog-context';
import { ConfirmDialogOptions } from '../interfaces/confirm-dialog-options';
import {
    confirmDialogReducer,
    dialogInitialState,
    HIDE_CONFIRM,
    SHOW_CONFIRM
} from '../reducers/confirm-dialog-reducer';
import { ConfirmDialog } from './ConfirmDialog';

let confirmResolveCallback: (isConfirmed: boolean | undefined) => void;

export const ConfirmDialogProvider = ({
    children
}: React.PropsWithChildren<{}>) => {
    const [{ isOpen, options }, dispatch] = useReducer(
        confirmDialogReducer,
        dialogInitialState
    );

    const handleConfirm = () => {
        closeConfirm();
        confirmResolveCallback(true);
    };

    const handleCancel = () => {
        closeConfirm();
        confirmResolveCallback(false);
    };

    const handleClose = () => {
        closeConfirm();
        confirmResolveCallback(undefined);
    };

    const confirm = (
        options: ConfirmDialogOptions
    ): Promise<boolean | undefined> => {
        dispatch({ type: SHOW_CONFIRM, options });

        return new Promise<boolean | undefined>(resolve => {
            confirmResolveCallback = resolve;
        });
    };

    const closeConfirm = () => {
        dispatch({ type: HIDE_CONFIRM });
    };

    return (
        <ConfirmDialogContext.Provider value={{ confirm }}>
            <ConfirmDialog
                isOpen={isOpen}
                title={options?.title || ''}
                confirmText={options?.confirmText || 'Ok'}
                cancelText={options?.cancelText || 'Cancel'}
                onCancel={handleCancel}
                onConfirm={handleConfirm}
                onClose={handleClose}
                dialogWidth={options?.dialogWidth}
                cancelStartIcon={options?.cancelStartIcon}
                confirmStartIcon={options?.confirmStartIcon}
                errorConfirmColor={options?.errorConfirmColor}
                footer={options?.footer}
            >
                {options?.body}
            </ConfirmDialog>
            {children}
        </ConfirmDialogContext.Provider>
    );
};
