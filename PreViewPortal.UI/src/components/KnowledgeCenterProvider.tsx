import { Portal } from '@mui/material';
import { useEffect, useState } from 'react';
import { Rnd } from 'react-rnd';
import { useWindowScroll, useWindowSize } from 'react-use';
import KnowledgeCenterMarkdownDoc from '../assets/docs/knowledge-center.md';
import KnowledgeCenterContext from '../contexts/knowledge-center-context';
import { ResizeBottomRightIcon } from '../icons/ResizeBottomRightIcon';
import { KnowledgeCenter } from './KnowledgeCenter';

export const KnowledgeCenterProvider = ({
    children
}: React.PropsWithChildren<{}>) => {
    const [markdown, setMarkdown] = useState('');
    const [currentAnchor, setCurrentAnchor] = useState<string>();
    const [isKnowledgeCenterOpen, setIsKnowledgeCenterOpen] = useState(false);
    const [size, setSize] = useState<{
        width: number | string;
        height: number | string;
    }>({
        width: 700,
        height: 700
    });
    const [position, setPosition] = useState<{ x: number; y: number }>({
        x: 0,
        y: 0
    });
    const { y: windowY, x: windowX } = useWindowScroll();
    const { width: windowWidth } = useWindowSize();

    useEffect(() => {
        fetch(KnowledgeCenterMarkdownDoc)
            .then(res => res.text())
            .then(setMarkdown);
    }, []);

    useEffect(() => {
        const sizeWidth = +size.width.toString().replace('px', '');

        setPosition({
            y: windowY,
            x: windowWidth + windowX - sizeWidth
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isKnowledgeCenterOpen]);

    const padding = 30;

    return (
        <KnowledgeCenterContext.Provider
            value={{
                markdown,
                isKnowledgeCenterOpen,
                currentAnchor,
                setCurrentAnchor,
                setIsKnowledgeCenterOpen
            }}
        >
            {isKnowledgeCenterOpen && (
                <Portal>
                    <Rnd
                        style={{
                            padding,
                            zIndex: 1501,
                            overflow: 'hidden',
                            display: 'block'
                        }}
                        size={size}
                        position={position}
                        bounds='window'
                        minWidth={450}
                        minHeight={480}
                        enableResizing={{
                            top: true,
                            bottom: true,
                            left: true,
                            right: true,
                            bottomRight: true
                        }}
                        resizeHandleStyles={{
                            top: {
                                top: padding
                            },
                            bottom: {
                                bottom: padding
                            },
                            right: {
                                right: padding
                            },
                            left: {
                                left: padding
                            },
                            bottomRight: {
                                right: padding,
                                bottom: padding
                            }
                        }}
                        resizeHandleComponent={{
                            bottomRight: (
                                <ResizeBottomRightIcon
                                    sx={{ color: 'primary.light' }}
                                    fontSize='small'
                                />
                            )
                        }}
                        dragHandleClassName='KnowledgeCenter-dragHandle'
                        onDragStop={(_, d) => setPosition({ x: d.x, y: d.y })}
                        onResizeStop={(e, d, ref, delta, position) => {
                            setSize({
                                width: ref.style.width,
                                height: ref.style.height
                            });
                            setPosition(position);
                        }}
                    >
                        <KnowledgeCenter />
                    </Rnd>
                </Portal>
            )}
            {children}
        </KnowledgeCenterContext.Provider>
    );
};
