import { useEffect, useState } from 'react';
import { useSupervisedAccount } from '@/hooks/use-supervised-account';
import { ProductContext } from '@/contexts/product-context';
import { ProductOptions } from '@/interfaces/product-options';

export const ProductProvider = ({ children }: React.PropsWithChildren<{}>) => {
    const [accountProduct, setAccountProduct] = useState<ProductOptions[]>([]);
    const [selectedProduct, setSelectedProduct] =
        useState<ProductOptions | null>(null);

    const { supervisedAccount } = useSupervisedAccount();

    useEffect(() => {
        if (!supervisedAccount) return;

        const { hasREInContext, hasAccessToNewForcedExposure } =
            supervisedAccount;

        const newAccountProduct: ProductOptions[] = [];

        if (hasREInContext) {
            newAccountProduct.push(ProductOptions.InContext);
        }

        if (hasAccessToNewForcedExposure) {
            newAccountProduct.push(ProductOptions.NewForcedExposure);
        }

        const defaultProduct = newAccountProduct[0];

        setAccountProduct(newAccountProduct);
        setSelectedProduct(defaultProduct);
    }, [supervisedAccount]);

    return (
        <ProductContext.Provider
            value={{
                accountProduct,
                selectedProduct,
                setAccountProduct,
                setSelectedProduct
            }}
        >
            {children}
        </ProductContext.Provider>
    );
};
