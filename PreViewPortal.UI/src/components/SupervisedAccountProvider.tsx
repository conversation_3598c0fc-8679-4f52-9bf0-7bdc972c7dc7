import { useEffect, useState } from 'react';
import { SupervisedAccountContext } from '../contexts/supervised-account-context';
import { SupervisedAccountInfo } from '../helpers/supervised-account-info';
import { useUser } from '../hooks/use-user';
import { AccountLookupModel } from '@/interfaces/account-lookup-model';
import { useHttp } from '@/hooks/use-http';

export const SupervisedAccountProvider = ({
    children
}: React.PropsWithChildren<{}>) => {
    const { user } = useUser();
    const [supervisedAccount, setSupervisedAccount] = useState(
        SupervisedAccountInfo.persistedAccount
    );

    const { http } = useHttp();

    useEffect(() => {
        SupervisedAccountInfo.persistedAccount = supervisedAccount;
    }, [supervisedAccount]);

    useEffect(() => {
        if (!user?.account) return;

        if (supervisedAccount?.id && supervisedAccount?.name) return;

        http.get(`user/account/${user.account}`)
            .json<AccountLookupModel>()
            .then(setSupervisedAccount);
    }, [user?.account, supervisedAccount, http]);

    useEffect(() => {
        if (!user) return;

        if (user.isAdmin) return;

        if (!supervisedAccount) return;

        if (user.accounts.includes(supervisedAccount.id)) return;

        const account: AccountLookupModel | null = user.account
            ? {
                  id: user.account,
                  name: '',
                  hasREInContext: false,
                  hasAccessToNewForcedExposure: false,
                  hasCustomNorm: false
              }
            : null;

        setSupervisedAccount(account);
    }, [user, supervisedAccount]);

    return (
        <SupervisedAccountContext.Provider
            value={{
                supervisedAccount,
                setSupervisedAccount
            }}
        >
            {children}
        </SupervisedAccountContext.Provider>
    );
};
