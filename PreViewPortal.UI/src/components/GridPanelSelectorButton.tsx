import { Stack, Typography, Button } from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';

interface Props {
    onClick?: (e: React.MouseEvent<HTMLElement, MouseEvent>) => void;
    label: string;
    iconLabel?: string;
    icon?: React.ReactNode;
    withoutIcon?: boolean;
    disabled?: boolean;
}

export const GridPanelSelectorButton = ({
    onClick,
    label,
    icon,
    iconLabel,
    disabled,
    withoutIcon
}: Props) => (
    <Stack
        direction='row'
        onClick={e => {
            if (disabled || !onClick) return;

            onClick(e);
        }}
    >
        <Typography
            variant='h6'
            px={3}
            py={1}
            bgcolor='#F2F2F2'
            border='1px solid #e0e0e0'
            borderRadius='4px'
            sx={{
                '&:hover': {
                    cursor: disabled ? 'default' : 'pointer'
                }
            }}
        >
            {label}
        </Typography>

        {!withoutIcon && (
            <Button
                startIcon={icon ?? <EditIcon />}
                disableRipple
                disabled={disabled}
                sx={{
                    '&:hover': {
                        color: 'primary.main'
                    }
                }}
            >
                {iconLabel}
            </Button>
        )}
    </Stack>
);
