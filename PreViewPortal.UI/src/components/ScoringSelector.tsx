import { useProduct } from '@/hooks/use-product';
import {
    Autocomplete,
    Chip,
    Stack,
    SxProps,
    TextField,
    Typography
} from '@mui/material';
import { InfoIcon } from '@/icons/InfoIcon';
import { useState } from 'react';
import { ConfirmPopover } from './ConfirmPopover';
import { ExposureLabel } from '@/interfaces/exposure-label';
import { ProductOptions } from '@/interfaces/product-options';
import { GridPanelSelectorButton } from './GridPanelSelectorButton';

const InContextFormula = () => (
    <Stack>
        <Typography variant='body2' color='#AFB9BB' pb={0.5}>
            {ExposureLabel.InContext}
        </Typography>
        <Typography variant='body2'>
            {'17%'}&emsp;
            {'Attentive Seconds'}
        </Typography>
        <Typography variant='body2'>
            {'17%'}&emsp;
            {'Attentive VTR'}
        </Typography>
        <Typography variant='body2'>
            {'17%'}&emsp;
            {'Brand Recognition'}
        </Typography>
        <Typography variant='body2' color='#AFB9BB' py={0.5}>
            {ExposureLabel.Focused}
        </Typography>
        <Typography variant='body2'>
            {'17%'}&emsp;
            {'Reactions Avg.'}
        </Typography>
        <Typography variant='body2'>
            {'17%'}&emsp;
            {'Happy Peak'}
        </Typography>
        <Typography variant='body2'>
            {'17%'}&emsp;
            {'Ad Likeability'}
        </Typography>
    </Stack>
);

const NewExposureFormula = () => (
    <Stack>
        <Typography variant='body2' color='#AFB9BB' pb={0.5}>
            {ExposureLabel.Focused}
        </Typography>
        <Typography variant='body2'>
            {'25%'}&emsp;
            {'Attentive Seconds '}
        </Typography>
        <Typography variant='body2'>
            {'25%'}&emsp;
            {'Attentive VTR '}
        </Typography>
        <Typography variant='body2'>
            {'25%'}&emsp;
            {'Reactions Avg.'}
        </Typography>
        <Typography variant='body2'>
            {'25%'}&emsp;
            {'Happy Peak'}
        </Typography>
    </Stack>
);

const chipProps: SxProps = {
    width: '14px',
    height: '14px',
    borderRadius: '2px'
};

export const ScoringSelector = () => {
    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);

    const { selectedProduct } = useProduct();

    const isInContextProduct = selectedProduct === ProductOptions.InContext;

    const handleClose = () => {
        setAnchorEl(null);
    };

    return (
        <>
            <Stack gap={2} maxWidth={350}>
                <Typography variant='h6'>{'Scoring Framework'}</Typography>

                <GridPanelSelectorButton
                    label='Quality Score'
                    onClick={e => setAnchorEl(e.currentTarget)}
                    iconLabel='Details'
                    icon={<InfoIcon sx={{ ml: 2 }} />}
                />

                <Typography variant='body2'>
                    {
                        'Test results integrate survey-based scores, supported by attention and reaction metrics, benchmarked for consistent comparison across contexts and formats.'
                    }
                </Typography>
            </Stack>

            <ConfirmPopover
                anchorEl={anchorEl}
                title='Score Settings'
                onClose={handleClose}
                onAccept={handleClose}
                disabledAccept
                width={1000}
                body={
                    <Stack>
                        <Stack direction='row' gap={4}>
                            <Stack width={310}>
                                <Autocomplete
                                    sx={{ width: '100%' }}
                                    value={'Quality Score'}
                                    options={[]}
                                    disableClearable
                                    disabled
                                    renderInput={props => (
                                        <TextField {...props} />
                                    )}
                                />

                                <Typography variant='body2' mt={1}>
                                    {
                                        'Test results combine survey-based scores - supported by attention, reaction - benchmarked for consistent comparison across contexts and formats. '
                                    }
                                </Typography>
                            </Stack>
                            <Stack
                                border='1.5px dashed #AFB9BB'
                                borderRadius='5px'
                                p={2}
                                direction='row'
                                gap={6}
                            >
                                <Typography variant='body2' fontWeight='bold'>
                                    {'Formula'}
                                </Typography>
                                {isInContextProduct ? (
                                    <InContextFormula />
                                ) : (
                                    <NewExposureFormula />
                                )}
                            </Stack>
                            <Stack
                                border='1.5px dashed #AFB9BB'
                                borderRadius='5px'
                                p={2}
                                direction='row'
                                gap={6}
                            >
                                <Typography variant='body2' fontWeight='bold'>
                                    {'Ranges'}
                                </Typography>
                                <Stack gap={1}>
                                    <Stack
                                        direction='row'
                                        alignItems='center'
                                        gap={1}
                                    >
                                        <Chip color='success' sx={chipProps} />
                                        <Typography variant='body2'>
                                            {'Excellent (71-100)'}
                                        </Typography>
                                    </Stack>
                                    <Stack
                                        direction='row'
                                        alignItems='center'
                                        gap={1}
                                    >
                                        <Chip color='warning' sx={chipProps} />
                                        <Typography variant='body2'>
                                            {'Average (40-70)'}
                                        </Typography>
                                    </Stack>
                                    <Stack
                                        direction='row'
                                        alignItems='center'
                                        gap={1}
                                    >
                                        <Chip color='error' sx={chipProps} />
                                        <Typography variant='body2'>
                                            {'Below Average (1-39)'}
                                        </Typography>
                                    </Stack>
                                </Stack>
                            </Stack>
                        </Stack>
                    </Stack>
                }
                footer={
                    <Typography variant='body2' color='#AFB9BB' width={560}>
                        {
                            '* Customise your overall performance score by combining the indexed versions (Ranks) of your preferred metrics after exporting the data.'
                        }
                    </Typography>
                }
            />
        </>
    );
};
