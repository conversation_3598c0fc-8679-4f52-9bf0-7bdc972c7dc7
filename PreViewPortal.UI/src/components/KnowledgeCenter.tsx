import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import CloseIcon from '@mui/icons-material/Close';
import SearchIcon from '@mui/icons-material/Search';
import UnfoldLessIcon from '@mui/icons-material/UnfoldLess';
import {
    Autocomplete,
    AutocompleteInputChangeReason,
    Box,
    Divider,
    IconButton,
    Link as MuiLink,
    Paper,
    Stack,
    TextField,
    Tooltip,
    Typography,
    LinkProps
} from '@mui/material';
import { debounce } from 'lodash';
import { useCallback, useEffect, useRef, useState } from 'react';
import ReactMarkdown from 'react-markdown';
import rehypeRaw from 'rehype-raw';
import { useKnowledgeCenter } from '../hooks/use-knowledge-center';
import { KnowledgeCenterMatchModel } from '../interfaces/knowledge-center-match-model';
import './KnowledgeCenter.css';

export const KnowledgeCenter = () => {
    const {
        markdown = '',
        currentAnchor,
        setCurrentAnchor,
        setIsKnowledgeCenterOpen
    } = useKnowledgeCenter();

    const markdownContainerRef = useRef<HTMLDivElement | null>(null);

    const [foundMatches, setFoundMatches] = useState<
        KnowledgeCenterMatchModel[]
    >([]);

    const [history, setHistory] = useState<string[]>([]);

    const [prevAnchor, setPrevAnchor] = useState<string>();

    useEffect(() => {
        if (!currentAnchor) return;

        openDetails(currentAnchor);
        // eslint-disable-next-line
    }, [currentAnchor]);

    useEffect(() => {
        if (!currentAnchor) return;

        //ToDo: This is a hack and ideally should not exist.
        setTimeout(() => scrollToDetails(currentAnchor), 10);
        setTimeout(() => setCurrentAnchor(undefined), 25);
        // eslint-disable-next-line
    }, [currentAnchor]);

    useEffect(() => {
        if (!prevAnchor) return;

        if (!history.includes(prevAnchor)) {
            setPrevAnchor(undefined);
        }
    }, [prevAnchor, history]);

    // eslint-disable-next-line
    const handleSearchTextChange = useCallback(
        debounce(
            (
                _: React.SyntheticEvent<Element>,
                value: string,
                reason: AutocompleteInputChangeReason
            ) => {
                if (reason !== 'input') return;

                if (value === '') {
                    setFoundMatches([]);
                    return;
                }

                const markdownContainer = markdownContainerRef.current;
                if (!markdownContainer) return;

                const elementsIncludingText = (e: Element, _: number) => {
                    if (e.textContent === null) return false;

                    return e.textContent
                        .toLowerCase()
                        .includes(value.toLowerCase());
                };

                const elementsToOptions = (e: Element, i: number) => {
                    const details = e.closest('details')!;

                    let label = e.textContent!;
                    if (e.localName === 'p' || e.localName === 'li') {
                        const summary =
                            details.getElementsByTagName('summary')[0];
                        const title = summary?.textContent;

                        const text = e.textContent!;
                        const matchLocation = text.indexOf(value);
                        const matchWithSurroundings = text.substring(
                            matchLocation - value.length - 10,
                            matchLocation + value.length + 10
                        );

                        label = `... ${matchWithSurroundings} ... (${title})`;
                    }

                    return {
                        label,
                        id: i,
                        detailsId: details.id
                    };
                };

                const byTextLength = (
                    a: KnowledgeCenterMatchModel,
                    b: KnowledgeCenterMatchModel
                ) => a.label.length - b.label.length;

                const elements = markdownContainer.querySelectorAll(
                    'h1, h2, h3, h4, h5, p, li'
                );
                const matches = Array.from(elements)
                    .filter(elementsIncludingText)
                    .map(elementsToOptions)
                    .sort(byTextLength);

                setFoundMatches(matches);
            },
            250
        ),
        [markdownContainerRef]
    );

    const openDetails = (id: string) => {
        const details = document.getElementById(
            id
        ) as HTMLDetailsElement | null;

        if (!details) return;

        const newHistory: string[] = [...history];

        let parent = details.parentElement as HTMLDetailsElement | null;
        while (parent && parent.localName === 'details') {
            if (history.includes(parent.id)) {
                break;
            }

            newHistory.push(parent.id);

            parent = parent.parentElement as HTMLDetailsElement | null;
        }

        if (!history.includes(details.id)) {
            newHistory.push(details.id);
        }

        setHistory(newHistory);
    };

    const scrollToDetails = (id: string) => {
        const details = document.getElementById(
            id
        ) as HTMLDetailsElement | null;

        if (!markdownContainerRef.current || !details) return;

        const mdContainer = markdownContainerRef.current!;
        mdContainer.scrollTop = details.offsetTop - mdContainer.offsetTop;
    };

    const handleClickAnchor =
        (href?: string) => (e?: React.MouseEvent<HTMLElement>) => {
            e?.preventDefault();
            e?.stopPropagation();

            if (!href) return;

            const id = href.substr(1);

            setCurrentAnchor(id);

            const parentDetail = e?.currentTarget.closest('details');
            setPrevAnchor(parentDetail?.id);
        };

    const handleClickSummary = (e: React.MouseEvent<HTMLElement>) => {
        e.preventDefault();

        const details = e.currentTarget.parentElement;
        if (!details) return;

        const isOpen = history.includes(details.id);
        let newHistory = isOpen
            ? history.filter(id => id !== details.id)
            : [...history, details.id];

        const childDetails = details.getElementsByTagName('details');
        for (const childDetail of childDetails) {
            newHistory = newHistory.filter(id => id !== childDetail.id);
        }

        setHistory(newHistory);
    };

    const handleClickOnBack = () => {
        setPrevAnchor(undefined);
        setCurrentAnchor(prevAnchor);
    };

    const handleClickOnClose = () => {
        setFoundMatches([]);
        setIsKnowledgeCenterOpen(false);
    };

    const handleCollapseAll = () => {
        setHistory([]);
        setPrevAnchor(undefined);
    };

    return (
        <Paper className='KnowledgeCenter' sx={{ height: '100%' }}>
            <Stack height='100%' direction='column' overflow='hidden'>
                <Stack
                    px={1}
                    color='white'
                    minHeight={56}
                    direction='row'
                    position='relative'
                    alignItems='center'
                    bgcolor='primary.light'
                    sx={{ cursor: 'move' }}
                    justifyContent='space-between'
                    className='KnowledgeCenter-dragHandle'
                >
                    <Typography
                        align='center'
                        color='inherit'
                        position='absolute'
                        left={0}
                        right={0}
                        fontWeight={700}
                    >
                        {'Knowledge Center'}
                    </Typography>
                    <Box>
                        {!!prevAnchor && (
                            <Tooltip
                                PopperProps={{ disablePortal: true }}
                                title='Back'
                            >
                                <IconButton
                                    color='inherit'
                                    onClick={handleClickOnBack}
                                    disabled={!prevAnchor}
                                >
                                    <ArrowBackIcon />
                                </IconButton>
                            </Tooltip>
                        )}
                        {!!history.length && (
                            <Tooltip
                                PopperProps={{ disablePortal: true }}
                                title='Collapse all'
                            >
                                <IconButton
                                    color='inherit'
                                    onClick={handleCollapseAll}
                                    disabled={!history.length}
                                >
                                    <UnfoldLessIcon />
                                </IconButton>
                            </Tooltip>
                        )}
                    </Box>

                    <IconButton color='inherit' onClick={handleClickOnClose}>
                        <CloseIcon />
                    </IconButton>
                </Stack>
                <Autocomplete
                    sx={{
                        m: 1,
                        '& .MuiAutocomplete-popupIndicator': {
                            transform: 'none'
                        }
                    }}
                    options={foundMatches}
                    filterOptions={m => m}
                    onInputChange={handleSearchTextChange}
                    onChange={(_, match) => {
                        if (match) {
                            setCurrentAnchor(match.detailsId);
                        }
                    }}
                    popupIcon={<SearchIcon />}
                    renderInput={props => (
                        <TextField
                            {...props}
                            //otherwise it wont work on cv drawer
                            onFocus={e => e.stopPropagation()}
                            placeholder='Search help'
                        />
                    )}
                    disablePortal
                />
                <Box
                    ref={markdownContainerRef}
                    flex={1}
                    mx={1}
                    mt={1}
                    mb={2}
                    pr={1}
                    overflow='hidden auto'
                >
                    <ReactMarkdown
                        components={{
                            a: props => (
                                <MuiLink
                                    {...(props as LinkProps)}
                                    onClick={
                                        !props.target
                                            ? handleClickAnchor(props.href)
                                            : undefined
                                    }
                                >
                                    {props.children}
                                </MuiLink>
                            ),
                            details: ({ id, children }) => {
                                const open = id && history.includes(id);
                                return (
                                    <details
                                        {...(open ? { open: true } : {})}
                                        id={id}
                                    >
                                        {children}
                                    </details>
                                );
                            },
                            summary: ({ children }) => (
                                <summary onClick={handleClickSummary}>
                                    {children}
                                </summary>
                            )
                        }}
                        // eslint-disable-next-line react/no-children-prop
                        children={markdown}
                        rehypePlugins={[rehypeRaw]}
                    />
                    <Stack my={2} alignItems='center'>
                        <Divider sx={{ width: 30 }} />
                        <Typography variant='body2' mt={2}>
                            {'Need more help?'}
                        </Typography>
                        <Typography variant='body2'>
                            {'Reach out to your Realeyes representatives.'}
                        </Typography>
                    </Stack>
                </Box>
            </Stack>
        </Paper>
    );
};
