import { Box, Stack } from '@mui/material';
import { getCreativePerformanceInContextFilterDef } from '@/constant/creative-performance-in-context-filter-def';
import { CustomGrid } from './custom-grid/CustomGrid';
import { CreativePerformanceInContextPageModel } from '@/interfaces/creative-performance-in-context-page-model';
import { useMemo, useState } from 'react';
import { CustomGridColumnMenu } from './custom-grid/CustomGridColumnMenu';
import {
    GRID_CHECKBOX_SELECTION_COL_DEF,
    GridRowParams,
    GridRowSelectionModel
} from '@mui/x-data-grid-premium';
import { PreViewGrid } from '@/interfaces/preview-grid';
import {
    getCreativePerformanceInContextColDef,
    defaultCreativePerformanceInContextColumnsVisibility,
    defaultCreativePerformanceInContextSortModel,
    creativePerformanceInContextColumnGroupModel
} from '@/constant/creative-performance-in-context-col-def';
import { ProductTypeSelector } from './ProductTypeSelector';
import { CreativePerformanceInContextListModel } from '@/interfaces/creative-performance-in-context-list-model';
import { useUser } from '@/hooks/use-user';
import { CreativeViewerDrawer } from './creative-viewer/CreativeViewerDrawer';
import { GridSegmentSelector } from './GridSegmentSelector';
import { SegmentKeyLabel } from '@/interfaces/segment-key-label';
import { fullSegment } from '@/constant/full-segment';
import {
    CustomToolbar,
    CustomToolbarProps
} from './custom-grid/CustomGridToolbar';
import { getSegmentQuestionAndAnswerLabel } from '@/helpers/get-segment-question-and-answer-label';
import { creativeViewerMediaLimit } from '@/constant/runtime-config';
import { useSnackbar } from 'notistack';
import {
    CustomGridColumnHeader,
    CustomGridColumnHeaderProps
} from './custom-grid/CustomGridColumnHeader';
import { ScoringAndNormsPanel } from './ScoringAndNormsPanel';
import {
    hasAnyIndustryInList,
    hasBrandInList,
    hasCountryInList
} from '@/constant/norm-tooltip-helper';
import { isTextHighlighted } from '@/helpers/is-text-highlighted';

const endpointUrl = 'creativeperformanceincontext';

export const CreativePerformanceInContextGrid = () => {
    const [gridData, setGridData] =
        useState<CreativePerformanceInContextPageModel>();

    const [isLoading, setIsLoading] = useState<boolean>();

    const [selectionModel, setSelectionModel] = useState<GridRowSelectionModel>(
        []
    );

    const [isCreativeViewerOpen, setIsCreativeViewerOpen] = useState(false);

    const [showNormTooltip, setShowNormTooltip] = useState(true);

    const [selectedSegment, setSelectedSegment] =
        useState<SegmentKeyLabel>(fullSegment);

    const { isAdmin } = useUser();
    const { enqueueSnackbar } = useSnackbar();

    const columns = useMemo(() => {
        const segmentKeyLabel =
            getSegmentQuestionAndAnswerLabel(selectedSegment);
        const rows = gridData?.rows ?? [];

        const hasCountry = hasCountryInList(rows);
        const hasIndustry = hasAnyIndustryInList(rows);
        const hasBrand = hasBrandInList(rows);

        return getCreativePerformanceInContextColDef(
            isAdmin,
            showNormTooltip,
            segmentKeyLabel,
            hasCountry,
            hasIndustry,
            hasBrand
        );
    }, [gridData?.rows, isAdmin, showNormTooltip, selectedSegment]);

    const filtersDef = useMemo(
        () => getCreativePerformanceInContextFilterDef(isAdmin),
        [isAdmin]
    );

    const handleCloseCreativeViewer = () => {
        setIsCreativeViewerOpen(false);
        setSelectionModel([]);
    };

    const handleSegmentChange = (segmentKey: SegmentKeyLabel) => {
        setSelectedSegment(segmentKey);

        setSelectionModel([]);
    };

    const handleRowClick = ({
        id
    }: GridRowParams<CreativePerformanceInContextListModel>) => {
        if (isAdmin && isTextHighlighted()) return;

        const countOfSelectedRows = selectionModel.length;

        if (!countOfSelectedRows) {
            setSelectionModel([id]);
        }

        if (selectionModel.length > creativeViewerMediaLimit) {
            enqueueSnackbar(
                `Maximum ${creativeViewerMediaLimit} test can be compared.`,
                {
                    variant: 'warning'
                }
            );

            return;
        }

        setIsCreativeViewerOpen(true);
    };

    const handleClickOnCreativeViewer = () => {
        setIsCreativeViewerOpen(true);
    };

    const selectedCreatives = useMemo(
        () =>
            selectionModel.map(id => {
                const [sourceMediaID, testID, taskID, orderAdSetID] = (
                    id as string
                ).split(';');

                return {
                    sourceMediaID: +sourceMediaID,
                    testID: +testID,
                    taskID,
                    orderAdSetID: +orderAdSetID
                };
            }),
        [selectionModel]
    );

    const gridQueryParams = useMemo(
        () => ({
            segmentKey: selectedSegment.segmentKey
        }),
        [selectedSegment]
    );

    return (
        <Stack gap={1.5}>
            <Stack direction='row' gap={1.5}>
                <ProductTypeSelector />

                <ScoringAndNormsPanel />
            </Stack>

            <GridSegmentSelector
                isLoading={isLoading}
                endpointUrl={endpointUrl}
                selectedSegment={selectedSegment}
                onSelectedSegmentChange={handleSegmentChange}
            />

            <Box height={1000} mb={80}>
                <CustomGrid<CreativePerformanceInContextPageModel>
                    gridData={gridData}
                    getRowId={r =>
                        `${r.sourceMediaID};${r.testID};${r.taskID};${r.orderAdSetID}`
                    }
                    filterDefinitions={filtersDef}
                    endpointUrl={endpointUrl}
                    customQueryParams={gridQueryParams}
                    exportFileName='preview_in-context_export'
                    selectedCreatives={selectedCreatives}
                    gridId={PreViewGrid.CreativePerformanceInContext}
                    rowSelectionModel={selectionModel}
                    columnGroupingModel={
                        creativePerformanceInContextColumnGroupModel
                    }
                    onRowClick={handleRowClick}
                    onRowSelectionModelChange={setSelectionModel}
                    checkboxSelection
                    slots={{
                        toolbar: CustomToolbar,
                        columnMenu: CustomGridColumnMenu,
                        columnHeaders: CustomGridColumnHeader
                    }}
                    slotProps={{
                        toolbar: {
                            onClickCreativeViewer: handleClickOnCreativeViewer,
                            showNormTooltip: showNormTooltip,
                            onShowNormTooltipChange: setShowNormTooltip,
                            withDefinitions: true
                        } as CustomToolbarProps,
                        columnHeaders: {
                            gridData,
                            selectedCreatives
                        } as CustomGridColumnHeaderProps
                    }}
                    disableRowSelectionOnClick
                    onRowsChange={setGridData}
                    onLoadingChange={setIsLoading}
                    initialState={{
                        columns: {
                            columnVisibilityModel:
                                defaultCreativePerformanceInContextColumnsVisibility
                        },
                        pinnedColumns: {
                            left: [GRID_CHECKBOX_SELECTION_COL_DEF.field]
                        },
                        sorting: defaultCreativePerformanceInContextSortModel
                    }}
                    columns={columns}
                />
            </Box>

            <CreativeViewerDrawer
                selectedCreatives={selectedCreatives}
                onClose={handleCloseCreativeViewer}
                open={isCreativeViewerOpen}
            />
        </Stack>
    );
};
