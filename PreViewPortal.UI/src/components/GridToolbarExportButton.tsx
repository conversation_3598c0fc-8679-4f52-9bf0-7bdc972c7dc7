import {
    <PERSON>,
    <PERSON><PERSON>,
    Divider,
    FormControlLabel,
    IconButton,
    Popover,
    Radio,
    RadioGroup,
    Stack,
    SvgIcon,
    Tooltip,
    Typography
} from '@mui/material';
import { stringify } from 'qs';
import React, { useContext, useEffect, useState } from 'react';
import { useHttpWithoutAbort } from '@/hooks/use-http';
import { CustomGridExportModel } from '@/interfaces/custom-grid-export-model';
import { CustomGridProps } from './custom-grid/CustomGrid';
import { excelExportRowLimit } from '@/constant/runtime-config';
import ExportSvg from '../assets/icons/export-icon.svg?react';
import { CreativeSelectionModel } from '@/interfaces/creative-selection-model';
import { fullSegment } from '@/constant/full-segment';
import { ExportStatusContext } from '@/contexts/export-status-context';
import { ExportResult } from '@/interfaces/export-result';
import { STATUS_BAR_RESTART_DELAY } from '@/constant/status-bar';
import moment from 'moment';
import { CustomGridFilterModel } from '@/interfaces/custom-grid-filter-model';
import { getFilterModelWithJoinedValues } from '@/helpers/getFilterModelWithJoinedValues';
import { SelectedCurveType } from '@/contexts/creative-viewer-context';

enum ExportOption {
    VisibleDataCurrentAudience = 'VisibleDataCurrentAudience',
    AllDataCurrentAudience = 'AllDataCurrentAudience',
    AllAudiences = 'AllAudiences'
}

export type GridToolbarExportButtonProps = Pick<
    CustomGridProps<any>,
    | 'endpointUrl'
    | 'exportFileName'
    | 'columnVisibilityModel'
    | 'customQueryParams'
    | 'customExportQueryParams'
    | 'rowCount'
    | 'sortModel'
> & {
    isFromCreativeViewer?: boolean;
    filterModel: CustomGridFilterModel;
    shareKeyParam?: string;
    selectedCreatives?: CreativeSelectionModel[];
    selectedSegments?: string[];
    selectedCurveTypes?: SelectedCurveType[];
};

const ExportIcon = () => (
    <SvgIcon component={ExportSvg} inheritViewBox color='inherit' />
);

export const GridToolbarExportButton = ({
    endpointUrl,
    exportFileName,
    filterModel,
    customQueryParams,
    customExportQueryParams,
    columnVisibilityModel,
    sortModel,
    rowCount,
    isFromCreativeViewer,
    shareKeyParam,
    selectedCreatives,
    selectedSegments,
    selectedCurveTypes
}: GridToolbarExportButtonProps) => {
    const [exportAnchorEl, setExportAnchorEl] = React.useState(null);
    const [cancelExportAnchorEl, setCancelExportAnchorEl] =
        React.useState(null);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [exportOption, setExportOption] = useState<ExportOption>(
        ExportOption.AllDataCurrentAudience
    );
    const { http, handleError } = useHttpWithoutAbort();
    const {
        setExportResult,
        setShareKey,
        cancelExport,
        exportResult,
        isExportCanceled
    } = useContext(ExportStatusContext);

    const [abortController, setAbortController] =
        useState<AbortController | null>(null);

    const handleExportClick = (event: any) => {
        const exportJobStatus = exportResult?.jobStatus;

        if (
            exportJobStatus === 'Pending' ||
            exportJobStatus === 'InitialLoading'
        ) {
            setCancelExportAnchorEl(event.currentTarget);
        } else {
            setExportAnchorEl(event.currentTarget);
        }
    };

    useEffect(() => {
        if (isExportCanceled) {
            abortController?.abort();
        }
    }, [isExportCanceled, abortController]);

    const handleExportClose = () => {
        setExportAnchorEl(null);
    };

    const handleCancelExportClose = () => {
        setCancelExportAnchorEl(null);
    };

    const handleExportOptionChange = (
        event: React.ChangeEvent<HTMLInputElement>
    ) => {
        const value = event.target.value as ExportOption;
        console.log('Selected value:', value);
        setExportOption(value);
    };

    const getUrl = () => {
        const today = moment(new Date()).format('DD-MM-YYYY h:mm');

        const visibleColumnsOrder =
            exportOption === ExportOption.VisibleDataCurrentAudience
                ? Object.entries(columnVisibilityModel || {})
                      .filter(([_, isVisible]) => isVisible)
                      .map(([columnName]) => columnName)
                      .filter(c => !['order', 'customAdSets'].includes(c))
                : Object.entries(columnVisibilityModel || {})
                      .map(([columnName]) => columnName)
                      .filter(c => !['order', 'customAdSets'].includes(c));

        const sorting =
            sortModel?.map(({ field, sort }) => ({
                field,
                direction: sort
            })) || [];

        const media = selectedCreatives || [];

        let segmentKeys: string[] = [customQueryParams?.segmentKey || ''];

        if (exportOption === ExportOption.AllAudiences) {
            segmentKeys = [];
        } else if (selectedSegments && selectedSegments.length > 0) {
            segmentKeys = [...selectedSegments];
            if (segmentKeys.length > 1) {
                segmentKeys = segmentKeys.filter(
                    s => s !== fullSegment.segmentKey
                );
            }
        }

        const customFilterModel = getFilterModelWithJoinedValues(filterModel);

        const exportModel: CustomGridExportModel = {
            ...customFilterModel,
            sorting,
            visibleColumnsOrder,
            selectedCurveTypes:
                exportOption === ExportOption.VisibleDataCurrentAudience
                    ? selectedCurveTypes
                    : [],
            media,
            segmentKeys,
            exportFileName: `${exportFileName || 'Export'}_${today}`
        };

        const queryParams = stringify(
            {
                ...exportModel,
                ...(customExportQueryParams || customQueryParams || {})
            },
            {
                allowDots: true,
                encode: true,
                addQueryPrefix: true
            }
        );

        return `${endpointUrl || ''}/export${queryParams}`;
    };

    const handleCancelExport = async () => {
        handleCancelExportClose();

        cancelExport();
        const delay = (ms: number) =>
            new Promise(resolve => setTimeout(resolve, ms));
        await delay(STATUS_BAR_RESTART_DELAY);

        await handleExport();
    };

    const handleExport = async () => {
        setExportAnchorEl(null);
        setCancelExportAnchorEl(null);

        const controller = new AbortController();
        setAbortController(controller);

        try {
            setExportResult({
                jobStatus: 'InitialLoading'
            });

            const url = getUrl();
            const exportJob = await http
                .extend({ signal: controller.signal })
                .get(url, {
                    headers: {
                        'X-Share-Key': shareKeyParam || ''
                    }
                })
                .json<ExportResult>();

            setShareKey(shareKeyParam);
            setExportResult(exportJob);
        } catch (e) {
            if (e instanceof DOMException && e.name === 'AbortError') {
                console.log('Request was aborted');
            } else {
                handleError(e);
                setExportResult({
                    jobStatus: 'Failed'
                });
            }
        } finally {
            setIsLoading(false);
            handleExportClose();
        }
    };

    const exceedsExportLimit =
        selectedCreatives && selectedCreatives.length > 0
            ? selectedCreatives.length > excelExportRowLimit
            : (rowCount ?? 0) > excelExportRowLimit;

    const isExportDisabled = isLoading || !rowCount || exceedsExportLimit;

    const tooltipTitle =
        rowCount && exceedsExportLimit
            ? `The export can manage max. ${excelExportRowLimit} tests (with time series data). Refine the data filtering to activate this function.`
            : isFromCreativeViewer
              ? 'Export data to XLS'
              : '';

    return (
        <>
            <Tooltip
                title={tooltipTitle}
                slotProps={{
                    popper: {
                        disablePortal: true
                    },
                    tooltip: {
                        sx: {
                            maxWidth: 450
                        }
                    }
                }}
            >
                <span>
                    {isFromCreativeViewer ? (
                        <IconButton
                            onClick={handleExportClick}
                            disabled={isExportDisabled}
                            sx={{
                                '&:hover': {
                                    color: '#2DC0A2'
                                },
                                color: '#273235'
                            }}
                        >
                            <ExportIcon />
                        </IconButton>
                    ) : (
                        <Button
                            onClick={handleExportClick}
                            startIcon={<ExportIcon />}
                            disabled={isExportDisabled}
                            disableRipple
                        >
                            {'Export'}
                        </Button>
                    )}
                </span>
            </Tooltip>
            <Popover
                open={!!exportAnchorEl}
                onClose={handleExportClose}
                anchorEl={exportAnchorEl}
                sx={{ zIndex: 2000 }}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'left'
                }}
                onClick={e => isFromCreativeViewer && e.stopPropagation()}
            >
                <Box width={450} sx={{ bgcolor: 'white' }}>
                    <Box p={3}>
                        <Typography variant='h6' fontWeight='500' mb={3}>
                            Export Data to XLS
                        </Typography>

                        <Stack
                            direction='row'
                            spacing={2}
                            alignItems='center'
                            mb={3}
                        >
                            <Typography
                                variant='body2'
                                minWidth={50}
                                color='text.secondary'
                            >
                                Rows:
                            </Typography>
                            <Typography variant='body2'>
                                {(selectedCreatives &&
                                selectedCreatives.length > 0
                                    ? selectedCreatives.length
                                    : rowCount) || '-'}
                            </Typography>
                        </Stack>

                        <Stack
                            direction='row'
                            spacing={2}
                            alignItems='flex-start'
                        >
                            <Typography
                                variant='body2'
                                color='text.secondary'
                                pt={1}
                            >
                                Include:
                            </Typography>

                            <RadioGroup
                                value={exportOption}
                                onChange={handleExportOptionChange}
                            >
                                <FormControlLabel
                                    value={
                                        ExportOption.VisibleDataCurrentAudience
                                    }
                                    control={
                                        <Radio color='secondary' size='small' />
                                    }
                                    label={
                                        <Typography variant='body2'>
                                            Visible data (current audience)
                                            {isFromCreativeViewer && (
                                                <>
                                                    <br />
                                                    <span
                                                        style={{
                                                            fontWeight: 'bold'
                                                        }}
                                                    >
                                                        Best for Excel chart
                                                        editing
                                                    </span>
                                                </>
                                            )}
                                        </Typography>
                                    }
                                    sx={{ mb: 1 }}
                                />
                                <FormControlLabel
                                    value={ExportOption.AllDataCurrentAudience}
                                    control={
                                        <Radio color='secondary' size='small' />
                                    }
                                    label={
                                        <Typography variant='body2'>
                                            All data (current audience)
                                        </Typography>
                                    }
                                    sx={{ mb: 1 }}
                                />
                                <FormControlLabel
                                    value={ExportOption.AllAudiences}
                                    control={
                                        <Radio color='secondary' size='small' />
                                    }
                                    label={
                                        <Typography variant='body2'>
                                            All data (all audience breakdowns)
                                        </Typography>
                                    }
                                />
                            </RadioGroup>
                        </Stack>
                    </Box>
                    <Divider />
                    <Box p={2} display='flex' justifyContent='flex-end' gap={1}>
                        <Button
                            onClick={handleExportClose}
                            variant='text'
                            sx={{
                                color: 'text.primary',
                                fontWeight: 500
                            }}
                        >
                            CANCEL
                        </Button>
                        <Button
                            variant='contained'
                            onClick={handleExport}
                            disabled={isExportDisabled}
                            sx={{
                                bgcolor: '#273235',
                                '&:hover': { bgcolor: '#1a2224' },
                                fontWeight: 500
                            }}
                        >
                            EXPORT
                        </Button>
                    </Box>
                </Box>
            </Popover>
            <Popover
                open={!!cancelExportAnchorEl}
                onClose={handleCancelExportClose}
                anchorEl={cancelExportAnchorEl}
                sx={{ zIndex: 2000 }}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'left'
                }}
                onClick={e => e.stopPropagation()}
            >
                <Box width={500}>
                    <Stack ml={2} my={2} gap={2}>
                        <Typography variant='h6'>
                            {'Interrupt current export?'}
                        </Typography>
                        <Typography variant='body2'>
                            The ongoing export will be stopped and a new export
                            will be started.
                        </Typography>
                        <Typography variant='body2'>
                            Do you want to proceed?
                        </Typography>
                    </Stack>
                    <Divider />
                    <Stack
                        m={2}
                        flexDirection='row'
                        justifyContent='end'
                        gap={3}
                    >
                        <Button onClick={handleCancelExportClose}>
                            {'Cancel'}
                        </Button>
                        <Button
                            variant='contained'
                            onClick={handleCancelExport}
                        >
                            {'Continue'}
                        </Button>
                    </Stack>
                </Box>
            </Popover>
        </>
    );
};
