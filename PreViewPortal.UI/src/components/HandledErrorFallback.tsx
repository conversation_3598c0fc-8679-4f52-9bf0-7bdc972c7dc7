import { Typography } from '@mui/material';
import { useEffect } from 'react';
import { FallbackProps } from 'react-error-boundary';
import { useConfirmDialog } from '../hooks/use-confirm-dialog';
import { ConfirmDialogOptions } from '../interfaces/confirm-dialog-options';
import { useNavigate } from 'react-router';
import { useUser } from '../hooks/use-user';

const checkAuthError = (error: Error) => {
    console.log(error);
    if (error.message.includes('401 Unauthorized')) {
        return true;
    }
    return false;
};

export const HandledErrorFallback = ({
    error,
    resetErrorBoundary
}: FallbackProps) => {
    const { confirm } = useConfirmDialog();
    const navigate = useNavigate();
    const { setUser } = useUser();

    useEffect(() => {
        (async () => {
            if (checkAuthError(error)) {
                setUser(null);
                navigate('/ExpiredPage');
            } else {
                const options: ConfirmDialogOptions = {
                    title: 'Oops!',
                    confirmText: 'Refresh',
                    body: (
                        <Typography sx={{ mt: 2, mb: 4 }} variant='body2'>
                            An unexpected error happened in the background.
                        </Typography>
                    )
                };

                const shouldReset = await confirm(options);

                if (!shouldReset) return;

                resetErrorBoundary();
            }
        })();
        //eslint-disable-next-line
    }, [error]);

    return null;
};
