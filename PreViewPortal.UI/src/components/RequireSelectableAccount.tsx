import { useSupervisedAccount } from '@/hooks/use-supervised-account';
import { useUser } from '@/hooks/use-user';
import { Navigate } from 'react-router';

export const RequireSelectableAccount = ({
    children
}: {
    children: React.JSX.Element;
}) => {
    const { supervisedAccount } = useSupervisedAccount();
    const { user, isAdmin } = useUser();

    if (isAdmin) {
        return children;
    }

    if (user?.account && !supervisedAccount) {
        return <Navigate to='/' />;
    }

    return children;
};
