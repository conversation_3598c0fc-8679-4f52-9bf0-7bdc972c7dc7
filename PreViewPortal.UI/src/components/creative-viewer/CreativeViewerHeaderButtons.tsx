import { useCreativeViewer } from '@/hooks/use-creative-viewer';
import { Icon<PERSON>utton, Stack } from '@mui/material';
import { useProduct } from '@/hooks/use-product';
import { ProductOptions } from '@/interfaces/product-options';
import { GridToolbarExportButton } from '../GridToolbarExportButton';
import {
    defaultCreativePerformanceForcedExposureColumnsVisibility,
    defaultCreativePerformanceForcedExposureSortModel
} from '@/constant/creative-performance-forced-exposure-col-def';
import { CreativeSelectionModel } from '@/interfaces/creative-selection-model';
import { CreativeViewerShareButton } from '@/components/creative-viewer/CreativeViewerShareButton';
import {
    defaultCreativePerformanceInContextColumnsVisibility,
    defaultCreativePerformanceInContextSortModel
} from '@/constant/creative-performance-in-context-col-def';
import HelpIcon from '@mui/icons-material/Help';
import { useKnowledgeCenter } from '@/hooks/use-knowledge-center';
import { SelectedCurveType } from '@/contexts/creative-viewer-context';

interface Props {
    isShare: boolean;
    shareKeyParam: string | null;
    selectedCreatives: CreativeSelectionModel[];
    selectedCurveTypes: SelectedCurveType[];
}

export const CreativeViewerHeaderButtons = ({
    isShare,
    shareKeyParam = null,
    selectedCreatives,
    selectedCurveTypes
}: Props) => {
    const { selectedProduct } = useProduct();

    const isInContextProduct = selectedProduct === ProductOptions.InContext;

    const { selectedCurveSegments, hasNoCurves } = useCreativeViewer();

    const { isKnowledgeCenterOpen, setIsKnowledgeCenterOpen } =
        useKnowledgeCenter();

    return (
        !hasNoCurves && (
            <Stack
                alignItems='end'
                pb={2}
                flexDirection={'row'}
                justifyContent={'flex-end'}
            >
                <GridToolbarExportButton
                    endpointUrl={
                        isInContextProduct
                            ? 'creativeperformanceincontext'
                            : 'creativeperformanceforcedexposure'
                    }
                    exportFileName={
                        isInContextProduct
                            ? 'preview_in-context_export'
                            : 'preview_focused-exposure_export'
                    }
                    filterModel={{
                        filters: []
                    }}
                    selectedCreatives={selectedCreatives}
                    selectedSegments={selectedCurveSegments}
                    selectedCurveTypes={selectedCurveTypes}
                    shareKeyParam={shareKeyParam || ''}
                    sortModel={
                        isInContextProduct
                            ? defaultCreativePerformanceInContextSortModel.sortModel
                            : defaultCreativePerformanceForcedExposureSortModel.sortModel
                    }
                    rowCount={selectedCreatives.length}
                    columnVisibilityModel={
                        isInContextProduct
                            ? defaultCreativePerformanceInContextColumnsVisibility
                            : defaultCreativePerformanceForcedExposureColumnsVisibility
                    }
                    isFromCreativeViewer
                />

                <CreativeViewerShareButton
                    isShare={isShare}
                    selectedProduct={selectedProduct!}
                    selectedCreatives={selectedCreatives}
                    shareKeyParam={shareKeyParam}
                />

                <IconButton
                    sx={{
                        '&:hover': {
                            color: '#2DC0A2'
                        },
                        color: '#273235'
                    }}
                    onClick={() =>
                        setIsKnowledgeCenterOpen(!isKnowledgeCenterOpen)
                    }
                >
                    <HelpIcon />
                </IconButton>
            </Stack>
        )
    );
};
