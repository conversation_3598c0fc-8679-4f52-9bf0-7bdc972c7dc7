import {
    Accordion,
    AccordionDetails,
    AccordionSummary,
    Autocomplete,
    Divider,
    FormControlLabel,
    IconButton,
    Paper,
    Portal,
    Radio,
    RadioGroup,
    Slider,
    Stack,
    Switch,
    TextField,
    Typography
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { useEffect, useState } from 'react';
import { Rnd } from 'react-rnd';
import { useProduct } from '@/hooks/use-product';
import { ProductOptions } from '@/interfaces/product-options';
import { curveTypesTree } from '@/constant/curve-types-tree';
import { curveTypeLabelMap } from '@/constant/curve-type-label-map';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import { generateIdByCurveParams } from './CreativeViewer';
import {
    defaultExposureGroupStlye,
    ExposureGroupStyle,
    ExposureGroupStyleMap
} from '@/constant/default-exposure-group-style';
import { useChartConfigurator } from '@/hooks/use-chart-configurator';
import { useWindowScroll, useWindowSize } from 'react-use';
import { getPercentageValue } from '@/helpers/get-percentage-value';
import { useCreativeViewer } from '@/hooks/use-creative-viewer';
import { ExposureGroupType } from '@/contexts/creative-viewer-context';
import { ExposureLabel } from '@/interfaces/exposure-label';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

export type AnchorType = { x: number; y: number };

interface Props {
    anchor: AnchorType | undefined;
    onClose: () => void;
}

interface ArrowSwitchProps {
    checked: boolean;
    onChange: (checked: boolean) => void;
}

const ArrowSwitch = ({ checked, onChange }: ArrowSwitchProps) => (
    <Stack position='relative'>
        <PlayArrowIcon
            fontSize='small'
            sx={{
                position: 'absolute',
                top: 8,
                left: checked ? 29 : 8,
                zIndex: 1000,
                pointerEvents: 'none',
                color: checked ? 'white' : '#d8d8d8',
                transform: `rotate(${checked ? 0 : -180}deg)`
            }}
        />
        <Switch
            checked={checked}
            onChange={(_, checked) => onChange(checked)}
        />
    </Stack>
);

const sliderSxProps = {
    width: '95%',
    '& .MuiSlider-valueLabel': {
        p: '1px 4px',
        fontWeight: 'unset'
    },
    '& .MuiSlider-thumb': {
        height: 15,
        width: 15
    }
};

const width = 340;

export const CreativeViewerChartConfigurator = ({ anchor, onClose }: Props) => {
    const [position, setPosition] = useState<{ x: number; y: number }>();

    const [isExpanded, setIsExpanded] = useState(false);

    const { selectedProduct } = useProduct();
    const { height: windowHeight } = useWindowSize();
    const { y: windowY } = useWindowScroll();

    const isInContextProduct = selectedProduct === ProductOptions.InContext;

    const { normSettingsMode, setNormSettingsMode, setShowNormDetails } =
        useChartConfigurator();

    useEffect(() => {
        const newPosition = anchor
            ? {
                  x: anchor.x - width,
                  y: windowY + windowHeight * 0.2
              }
            : undefined;

        setPosition(newPosition);
    }, [anchor, windowY, windowHeight]);

    const {
        curveTypeYAxisGroups,
        setCurveTypeYAxisGroups,
        exposureGroupStyles,
        setExposureGroupStyles,
        customLeftAxisRange,
        setCustomLeftAxisRange,
        customRightAxisRange,
        setCustomRightAxisRange,
        showLegendBar,
        setShowLegendBar,
        showAxisMarkers,
        setShowAxisMarkers,
        smoothingLevel,
        setSmoothingLevel
    } = useChartConfigurator();

    const { leftCurvesRange, rightCurvesRange } = useCreativeViewer();

    const [yCurveMinLeft, yCurveMaxLeft] = leftCurvesRange;
    const [yCurveMinRight, yCurveMaxRight] = rightCurvesRange;

    useEffect(() => {
        const newDefaultExposureGroupStlye: ExposureGroupStyleMap =
            isInContextProduct
                ? defaultExposureGroupStlye
                : { focused: 'Solid' };

        setExposureGroupStyles(newDefaultExposureGroupStlye);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isInContextProduct]);

    useEffect(() => {
        setCustomLeftAxisRange([yCurveMinLeft, yCurveMaxLeft]);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [yCurveMinLeft, yCurveMaxLeft]);

    useEffect(() => {
        setCustomRightAxisRange([yCurveMinRight, yCurveMaxRight]);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [yCurveMinRight, yCurveMaxRight]);

    const handleNormSettingsModeChange = (e: any) => {
        const newMode = e.target.value;

        setNormSettingsMode(newMode);
        setShowNormDetails(true);
    };

    const handleCustomLeftAxisRangeChange = (values: number[]) => {
        const [min, max] = values;
        setCustomLeftAxisRange([
            Math.min(min, yCurveMinLeft),
            Math.max(max, yCurveMaxLeft)
        ]);
    };
    const handleCustomRightAxisRangeChange = (values: number[]) => {
        const [min, max] = values;
        setCustomRightAxisRange([
            Math.min(min, yCurveMinRight),
            Math.max(max, yCurveMaxRight)
        ]);
    };

    const handleExposureGroupStyleChange = (
        exposureGroup: ExposureGroupType,
        style: ExposureGroupStyle
    ) => {
        const otherExposureGroup =
            exposureGroup === 'inContext' ? 'focused' : 'inContext';

        const otherStyle = style === 'Double' ? 'Solid' : 'Double';

        setExposureGroupStyles({
            [exposureGroup]: style,
            [otherExposureGroup]: otherStyle
        });
    };

    const handleChecked = (checked: boolean, key: string) =>
        setCurveTypeYAxisGroups({
            ...curveTypeYAxisGroups,
            [key]: checked ? 'right' : 'left'
        });

    const exposureGroupOptions: ExposureGroupStyle[] = ['Solid', 'Double'];

    return (
        position && (
            <Portal>
                <Rnd
                    style={{
                        padding: 30,
                        zIndex: 1501
                    }}
                    position={position}
                    bounds='window'
                    minWidth={width}
                    dragHandleClassName='ChartConfigurator-dragHandle'
                    onDragStop={(_, d) => setPosition({ x: d.x, y: d.y })}
                >
                    <Paper>
                        <Stack px={2}>
                            <Stack
                                minHeight={50}
                                direction='row'
                                alignItems='center'
                                sx={{ cursor: 'move' }}
                                justifyContent='space-between'
                                className='ChartConfigurator-dragHandle'
                            >
                                <Typography variant='h6' color='inherit'>
                                    {'General Chart Settings'}
                                </Typography>
                                <IconButton
                                    color='inherit'
                                    onClick={onClose}
                                    sx={{ p: 0 }}
                                >
                                    <CloseIcon />
                                </IconButton>
                            </Stack>

                            <Divider />
                        </Stack>

                        <Stack
                            px={2}
                            maxHeight={windowHeight * 0.6}
                            overflow='hidden auto'
                        >
                            <Typography
                                fontWeight='bold'
                                variant='body2'
                                pt={2}
                            >
                                {'Chart Descriptions'}
                            </Typography>

                            <Stack
                                sx={{ width: '100%' }}
                                alignItems='center'
                                justifyContent='space-between'
                                direction='row'
                            >
                                <Typography>{'Y-axis markers'}</Typography>
                                <Switch
                                    checked={showAxisMarkers}
                                    onChange={(_, checked) =>
                                        setShowAxisMarkers(checked)
                                    }
                                />
                            </Stack>

                            <Stack
                                sx={{ width: '100%' }}
                                alignItems='center'
                                justifyContent='space-between'
                                direction='row'
                                pb={1}
                            >
                                <Typography>{'Legends'}</Typography>
                                <Switch
                                    checked={showLegendBar}
                                    onChange={(_, checked) =>
                                        setShowLegendBar(checked)
                                    }
                                />
                            </Stack>

                            <Typography fontWeight='bold' variant='body2'>
                                {'Show Norms'}
                            </Typography>

                            <RadioGroup
                                value={normSettingsMode}
                                onChange={handleNormSettingsModeChange}
                                sx={{
                                    margin: 2
                                }}
                            >
                                <FormControlLabel
                                    value={'never'}
                                    control={
                                        <Radio size='small' sx={{ py: 0 }} />
                                    }
                                    label={
                                        <Typography variant='body2'>
                                            {'Never'}
                                        </Typography>
                                    }
                                />
                                <FormControlLabel
                                    value={'hovering'}
                                    control={
                                        <Radio size='small' sx={{ py: 1 }} />
                                    }
                                    label={
                                        <Typography variant='body2'>
                                            {'When hovering on curves'}
                                        </Typography>
                                    }
                                />
                                <FormControlLabel
                                    value={'always'}
                                    control={
                                        <Radio size='small' sx={{ py: 0 }} />
                                    }
                                    label={
                                        <Typography variant='body2'>
                                            {'Always'}
                                        </Typography>
                                    }
                                />
                            </RadioGroup>

                            {isInContextProduct && (
                                <>
                                    <Divider />
                                    <Typography
                                        fontWeight='bold'
                                        variant='body2'
                                        py={2}
                                    >
                                        {'Line Styles'}
                                    </Typography>
                                    <Stack gap={1} pb={2}>
                                        <Stack
                                            direction='row'
                                            alignItems='center'
                                            justifyContent='space-between'
                                        >
                                            <Typography>
                                                {ExposureLabel.InContext}
                                            </Typography>

                                            <Autocomplete
                                                color='primary'
                                                size='small'
                                                disableClearable
                                                sx={{ width: 120 }}
                                                options={exposureGroupOptions}
                                                value={
                                                    exposureGroupStyles[
                                                        'inContext'
                                                    ]
                                                }
                                                onChange={(_, value) =>
                                                    handleExposureGroupStyleChange(
                                                        'inContext',
                                                        value
                                                    )
                                                }
                                                disablePortal={true}
                                                renderInput={props => (
                                                    <TextField
                                                        {...props}
                                                        label={''}
                                                    />
                                                )}
                                            />
                                        </Stack>
                                        <Stack
                                            direction='row'
                                            alignItems='center'
                                            justifyContent='space-between'
                                        >
                                            <Typography>
                                                {ExposureLabel.Focused}
                                            </Typography>
                                            <Autocomplete
                                                color='secondary'
                                                size='small'
                                                disableClearable
                                                sx={{ width: 120 }}
                                                options={exposureGroupOptions}
                                                value={
                                                    exposureGroupStyles[
                                                        'focused'
                                                    ]
                                                }
                                                onChange={(_, value) =>
                                                    handleExposureGroupStyleChange(
                                                        'focused',
                                                        value
                                                    )
                                                }
                                                disablePortal={true}
                                                renderInput={props => (
                                                    <TextField
                                                        {...props}
                                                        label={''}
                                                    />
                                                )}
                                            />
                                        </Stack>
                                    </Stack>
                                </>
                            )}

                            <Divider />

                            <Typography
                                fontWeight='bold'
                                variant='body2'
                                py={2}
                            >
                                {'Level of Line Smoothing'}
                            </Typography>
                            <Stack
                                sx={{ my: 2, width: '100%' }}
                                alignItems='center'
                            >
                                <Slider
                                    value={smoothingLevel}
                                    step={0.01}
                                    min={0}
                                    max={0.1}
                                    marks
                                    sx={sliderSxProps}
                                    onChange={(_, value) =>
                                        setSmoothingLevel(value as number)
                                    }
                                    valueLabelDisplay='on'
                                    valueLabelFormat={e => (e * 100).toFixed(0)}
                                />
                            </Stack>

                            <Divider />

                            <Accordion
                                expanded={isExpanded}
                                onChange={(_, expanded) =>
                                    setIsExpanded(expanded)
                                }
                                sx={{
                                    boxShadow: 'unset'
                                }}
                                disableGutters
                            >
                                <AccordionSummary
                                    sx={{
                                        p: 0
                                    }}
                                    expandIcon={<ExpandMoreIcon />}
                                >
                                    <Typography variant='h6' fontSize={16}>
                                        {'Y-Axis preferences'}
                                    </Typography>
                                </AccordionSummary>
                                <AccordionDetails
                                    sx={{
                                        p: 0
                                    }}
                                >
                                    <Typography
                                        fontWeight='bold'
                                        variant='body2'
                                        py={2}
                                    >
                                        Scale of Y<sub>1</sub>-Axis
                                    </Typography>
                                    <Stack
                                        sx={{ my: 2, width: '100%' }}
                                        alignItems='center'
                                    >
                                        <Slider
                                            value={customLeftAxisRange}
                                            step={0.01}
                                            min={0}
                                            max={1}
                                            sx={sliderSxProps}
                                            onChange={(_, values) =>
                                                handleCustomLeftAxisRangeChange(
                                                    values as number[]
                                                )
                                            }
                                            valueLabelDisplay='on'
                                            disableSwap
                                            valueLabelFormat={e =>
                                                getPercentageValue(e)
                                            }
                                        />
                                    </Stack>

                                    <Typography
                                        fontWeight='bold'
                                        variant='body2'
                                        py={2}
                                    >
                                        Scale of Y<sub>2</sub>-Axis
                                    </Typography>
                                    <Stack
                                        sx={{ my: 2, width: '100%' }}
                                        alignItems='center'
                                    >
                                        <Slider
                                            value={customRightAxisRange}
                                            step={0.01}
                                            min={0}
                                            max={1}
                                            sx={sliderSxProps}
                                            onChange={(_, values) =>
                                                handleCustomRightAxisRangeChange(
                                                    values as number[]
                                                )
                                            }
                                            valueLabelDisplay='on'
                                            disableSwap
                                            valueLabelFormat={e =>
                                                getPercentageValue(e)
                                            }
                                        />
                                    </Stack>

                                    <Divider />

                                    <Typography
                                        fontWeight='bold'
                                        variant='body2'
                                        py={2}
                                    >
                                        {'Data Signal Display'}
                                    </Typography>
                                    <Typography
                                        variant='caption'
                                        display='inline-block'
                                        width={210}
                                    >
                                        {
                                            'Select whether to plot time series on primary or secondary axis'
                                        }
                                    </Typography>
                                    <Stack
                                        direction='row'
                                        ml={20}
                                        gap={2}
                                        py={0.5}
                                    >
                                        {isInContextProduct && (
                                            <Typography
                                                variant='body2'
                                                textAlign='center'
                                                width={70}
                                            >
                                                {ExposureLabel.InContext}
                                            </Typography>
                                        )}
                                        <Typography
                                            variant='body2'
                                            textAlign='center'
                                            width={70}
                                        >
                                            {ExposureLabel.Focused}
                                        </Typography>
                                    </Stack>

                                    {curveTypesTree.map(({ type }) => {
                                        const label =
                                            curveTypeLabelMap.get(type);

                                        const yAxisGroupKeyIC =
                                            generateIdByCurveParams({
                                                type,
                                                exposureGroup: 'inContext'
                                            });

                                        const yAxisGroupKeyIF =
                                            generateIdByCurveParams({
                                                type,
                                                exposureGroup: 'focused'
                                            });

                                        const isICChecked =
                                            curveTypeYAxisGroups[
                                                yAxisGroupKeyIC
                                            ] === 'right';

                                        const isIFChecked =
                                            curveTypeYAxisGroups[
                                                yAxisGroupKeyIF
                                            ] === 'right';

                                        return (
                                            <Stack
                                                key={`YAxisSwitcher-${type}-${isICChecked}-${isIFChecked}`}
                                                direction='row'
                                                alignItems='center'
                                                gap={3}
                                            >
                                                <Typography minWidth={145}>
                                                    {label}
                                                </Typography>
                                                {isInContextProduct && (
                                                    <ArrowSwitch
                                                        checked={isICChecked}
                                                        onChange={checked =>
                                                            handleChecked(
                                                                checked,
                                                                yAxisGroupKeyIC
                                                            )
                                                        }
                                                    />
                                                )}
                                                <ArrowSwitch
                                                    checked={isIFChecked}
                                                    onChange={checked =>
                                                        handleChecked(
                                                            checked,
                                                            yAxisGroupKeyIF
                                                        )
                                                    }
                                                />
                                            </Stack>
                                        );
                                    })}
                                </AccordionDetails>
                            </Accordion>
                        </Stack>
                    </Paper>
                </Rnd>
            </Portal>
        )
    );
};
