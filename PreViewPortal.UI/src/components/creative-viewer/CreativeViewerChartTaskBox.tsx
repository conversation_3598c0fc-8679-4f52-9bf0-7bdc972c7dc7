import { useChartConfigurator } from '@/hooks/use-chart-configurator';
import { useCreativeViewer } from '@/hooks/use-creative-viewer';
import { ChartTooltipDataModel } from '@/interfaces/chart-tooltip-model';
import { Stack, Tooltip, Typography } from '@mui/material';
import { CreativeViewerCurveSignalIcon } from './CreativeViewerCurveSignallcon';
import { ExposureGroupType } from '@/contexts/creative-viewer-context';
import { ExposureLabel } from '@/interfaces/exposure-label';
import { NormCurveCustomAnchorElement } from './CreativeViewerChartLegendBar';
import { CreativeViewerNormDetailsBox } from '@/components/creative-viewer/CreativeViewerNormDetailsBox';

interface Props {
    exposureGroup: ExposureGroupType;
    secondData: ChartTooltipDataModel[];
    multipleExposureGroupSelected?: boolean;
    isWrapping?: boolean;
    isLegendMode?: boolean;
    onHoveredNormCurveElChange?: (el: NormCurveCustomAnchorElement) => void;
}

export const CreativeViewerChartTaskBox = ({
    exposureGroup,
    secondData,
    multipleExposureGroupSelected,
    isWrapping,
    isLegendMode,
    onHoveredNormCurveElChange
}: Props) => {
    const {
        curveColors,
        getDisplayFullSegmentAsArea,
        focusedId,
        getCurveLabel
    } = useCreativeViewer();

    const { exposureGroupStyles, showNormDetails } = useChartConfigurator();

    const exposureLabelOpacity =
        focusedId && !focusedId.includes(exposureGroup) ? 0.1 : 1;

    const tooltipMode = !isLegendMode;

    const isNormDetailsEnabled = isLegendMode && showNormDetails;

    const showexposureGroupLabel = tooltipMode || multipleExposureGroupSelected;

    const stackGap = isLegendMode ? 2 : 0;

    const handleMouseMoveOnCurveLabel =
        (isNorm: boolean) => (_e: React.MouseEvent<HTMLSpanElement>) => {
            if (!onHoveredNormCurveElChange || !isNorm || !isNormDetailsEnabled)
                return;
        };

    const handleMouseLeaveOnCurveLabel = () => {
        if (!onHoveredNormCurveElChange) return;

        onHoveredNormCurveElChange(null);
    };

    return (
        <Stack
            direction={isLegendMode ? 'row' : 'column'}
            p={isLegendMode ? 0 : `5px 10px`}
            gap={stackGap}
        >
            {showexposureGroupLabel && (
                <Typography
                    variant='caption'
                    color='#93999a'
                    noWrap
                    sx={{
                        opacity: exposureLabelOpacity
                    }}
                >
                    {exposureGroup === 'inContext'
                        ? ExposureLabel.InContext
                        : ExposureLabel.Focused}
                </Typography>
            )}
            <Stack
                direction={tooltipMode || isWrapping ? 'column' : 'row'}
                gap={stackGap}
            >
                {secondData?.map(
                    ({
                        id,
                        type,
                        segmentKey,
                        isNorm,
                        pct,
                        sourceMedia,
                        views
                    }) => {
                        if (tooltipMode && pct === undefined) return null;

                        const color = curveColors.get(id)?.color || null;

                        const displayFullSegmentAsArea =
                            getDisplayFullSegmentAsArea(segmentKey, isNorm);

                        const isDoubleExposureGroupStyle =
                            exposureGroupStyles[exposureGroup] === 'Double';

                        const curveLabelOpacity =
                            focusedId &&
                            !id.includes(focusedId) &&
                            !displayFullSegmentAsArea
                                ? 0.1
                                : 1;

                        const pctLabel =
                            tooltipMode && (pct! * 100).toFixed(1) + '%';

                        const label = getCurveLabel({
                            sourceMedia,
                            type,
                            segmentKey,
                            isNorm,
                            nameLimit: tooltipMode ? 60 : undefined
                        });

                        const normCurveHoveringEnabled =
                            isNorm && isNormDetailsEnabled;

                        const viewsLabel = isLegendMode && `(${views} views)`;

                        return (
                            <Stack
                                key={`${id}-tooltipLabel`}
                                flexDirection='row'
                                alignItems='center'
                                gap={1}
                                sx={{
                                    opacity: curveLabelOpacity
                                }}
                            >
                                <CreativeViewerCurveSignalIcon
                                    color={color}
                                    isNorm={isNorm}
                                    isDoubleExposureGroupStyle={
                                        isDoubleExposureGroupStyle
                                    }
                                    displayFullSegmentAsArea={
                                        displayFullSegmentAsArea
                                    }
                                />

                                <Tooltip
                                    placement='right'
                                    slotProps={{
                                        tooltip: {
                                            sx: {
                                                backgroundColor: 'unset',
                                                fontWeight: 'unset',
                                                fontSize: 14,
                                                maxWidth: 600,
                                                overflow: 'hidden'
                                            }
                                        }
                                    }}
                                    title={
                                        <Stack gap={2}>
                                            <CreativeViewerNormDetailsBox
                                                normCurveId={id}
                                                sx={{ width: 450 }}
                                                showDetailsTooltip={true}
                                            />
                                        </Stack>
                                    }
                                >
                                    <Typography
                                        variant='caption'
                                        color='primary'
                                        noWrap
                                        onMouseMove={handleMouseMoveOnCurveLabel(
                                            isNorm
                                        )}
                                        onMouseLeave={
                                            handleMouseLeaveOnCurveLabel
                                        }
                                        sx={
                                            normCurveHoveringEnabled
                                                ? {
                                                      textDecoration:
                                                          'underline',
                                                      cursor: 'default'
                                                  }
                                                : {}
                                        }
                                    >
                                        {label}
                                    </Typography>
                                </Tooltip>

                                {pctLabel && (
                                    <Typography
                                        variant='caption'
                                        fontWeight='bold'
                                        ml='auto'
                                        color='primary'
                                    >
                                        {pctLabel}
                                    </Typography>
                                )}

                                {viewsLabel && (
                                    <Typography
                                        variant='caption'
                                        ml='auto'
                                        color='primary'
                                    >
                                        {viewsLabel}
                                    </Typography>
                                )}
                            </Stack>
                        );
                    }
                )}
            </Stack>
        </Stack>
    );
};
