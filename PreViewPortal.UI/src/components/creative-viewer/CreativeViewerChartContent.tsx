import { Box } from '@mui/system';
import { useEffect, useMemo, useState } from 'react';
import { useCreativeViewer } from '../../hooks/use-creative-viewer';
import { CreativeViewerVideoModel } from '../../interfaces/creative-viewer-video-model';
import { CreativeViewerRow } from './CreativeViewerRow';
import { CreativeViewerChart } from './CreativeViewerChart';
import { CreativeViewerControlPanel } from './CreativeViewerControlPanel';
import { generateIdByCurveParams } from './CreativeViewer';
import { IconButton, Stack, Tooltip, Typography } from '@mui/material';
import { CreativeViewerChartLegendBar } from './CreativeViewerChartLegendBar';
import { CreatieViewerFloatingVideo } from './CreatieViewerFloatingVideo';
import { CreativeViewerCards } from './CreativeViewerCards';
import {
    AnchorType,
    CreativeViewerChartConfigurator
} from './CreativeViewerChartConfigurator';
import SettingsIcon from '@mui/icons-material/Settings';

export const CreativeViewerChartContent = () => {
    const [currentFrame, setCurrentFame] = useState(0);
    const [secondToJumpOnVideo, setSecondToJumpOnVideo] = useState(0);

    const [isPlaying, setIsPlaying] = useState(false);

    const [selectedVideoToPlay, setSelectedVideoToPlay] =
        useState<CreativeViewerVideoModel>();

    const [chartConfiguratorAnchor, setChartConfiguratorAnchor] =
        useState<AnchorType>();

    const {
        selectedCurves,
        selectedCurveTypes,
        selectedCurveSegments,
        multipleMediaSelected,
        hasNoCurves,
        focusedId,
        setFocusedId,
        getCurveLabel,
        curveColors
    } = useCreativeViewer();

    const changeFocusesId = () => {
        let newFocusedId = undefined;

        const playingVideoWithOneChart =
            isPlaying &&
            selectedCurveTypes.length === 1 &&
            selectedCurveSegments.length === 1;

        if (playingVideoWithOneChart) {
            newFocusedId = generateIdByCurveParams({
                videoId: selectedVideoToPlay?.id,
                type: selectedCurveTypes[0].type,
                segmentKey: selectedCurveSegments[0],
                exposureGroup: selectedCurveTypes[0].exposureGroup,
                isNorm: false
            });
        }

        setFocusedId(newFocusedId);
    };

    useEffect(() => {
        changeFocusesId();

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [
        isPlaying,
        selectedCurveTypes,
        selectedCurveSegments,
        selectedVideoToPlay
    ]);

    const handlePlayVideo = () => {
        setIsPlaying(true);
    };

    const handlePauseVideo = () => {
        setIsPlaying(false);
    };

    const handleVideoTimeUpdate = ({ target: { currentTime } }: any) => {
        if (!focusedId) changeFocusesId();

        let secondToApply = Math.floor(currentTime);

        const currentVideoDuration = selectedVideoToPlay?.duration || 0;

        const roundLastSecondToDuration =
            Math.round(currentTime) === currentVideoDuration;

        if (roundLastSecondToDuration) secondToApply = currentVideoDuration;

        setCurrentFame(secondToApply);
    };

    const handleClickOnChart = (sec: number) => {
        setSecondToJumpOnVideo(sec);
        setCurrentFame(sec);
    };

    const handleCloseVideo = () => {
        setSelectedVideoToPlay(undefined);
        setIsPlaying(false);
        setCurrentFame(0);
        setSecondToJumpOnVideo(0);
    };

    const handleClickOnChartContainer = (
        e: React.MouseEvent<HTMLDivElement, MouseEvent>
    ) => {
        const elementTagName = (e.target as HTMLElement).tagName;

        if (elementTagName !== 'DIV') return;

        handleCloseVideo();
    };

    const handleOpenVideo = (video: CreativeViewerVideoModel) => () => {
        let curveColor = '';

        if (multipleMediaSelected) {
            const curveId = selectedCurves.find(
                c => !c.isNorm && c.videoId === video.id
            )?.id;

            curveColor = curveColors.get(curveId || '')?.color || '';
        }

        video.color = curveColor;

        setSelectedVideoToPlay(video);
    };

    const curvesWithoutDataLabels = useMemo(() => {
        if (hasNoCurves)
            return (
                <Typography variant='caption' color='error'>
                    {`No data for signal(s)`}
                </Typography>
            );

        const emptyCurves = selectedCurves.filter(
            s => !s.isNorm && !s.values.length
        );

        const countOfEmptyCurves = emptyCurves.length;

        if (!countOfEmptyCurves) return null;

        if (countOfEmptyCurves < 3) {
            return emptyCurves.map(ec => {
                const curveLabel = getCurveLabel(ec);

                return (
                    <Typography
                        variant='caption'
                        color='error'
                        key={`${ec.id}-NoCurveDataLabel`}
                    >
                        {`Not enough sample for ${curveLabel}`}
                    </Typography>
                );
            });
        }

        return (
            <Typography
                variant='caption'
                color='error'
            >{`Not enough sample for ${countOfEmptyCurves} data signal(s)`}</Typography>
        );

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [selectedCurves, multipleMediaSelected, selectedCurveSegments]);

    return (
        <>
            <CreativeViewerRow
                flex={1}
                left={<CreativeViewerControlPanel />}
                onClickRight={handleClickOnChartContainer}
                right={
                    <Box pb='300px'>
                        <CreativeViewerCards
                            currentFrame={currentFrame}
                            onPlayClick={handleOpenVideo}
                            playingVideoId={selectedVideoToPlay?.id}
                        />

                        <Stack alignItems='end' pr={3} pt={1} pb={1.5}>
                            <Tooltip
                                title='Edit General Chart Settings'
                                slotProps={{
                                    popper: {
                                        disablePortal: true
                                    }
                                }}
                            >
                                <span>
                                    <IconButton
                                        onClick={e =>
                                            setChartConfiguratorAnchor({
                                                x: e.clientX,
                                                y: e.clientY
                                            })
                                        }
                                        sx={{
                                            '&:hover': {
                                                color: '#2DC0A2'
                                            },
                                            color: '#273235'
                                        }}
                                    >
                                        <SettingsIcon color='inherit' />
                                    </IconButton>
                                </span>
                            </Tooltip>
                        </Stack>

                        <CreativeViewerChart
                            allowHover={!isPlaying}
                            currentFrame={currentFrame}
                            onCurrentFrameChange={setCurrentFame}
                            onClick={handleClickOnChart}
                        />

                        {curvesWithoutDataLabels && (
                            <Stack ml={5} mt={4}>
                                {curvesWithoutDataLabels}
                            </Stack>
                        )}

                        <Stack mx={5} mt={3}>
                            <CreativeViewerChartLegendBar />
                        </Stack>
                    </Box>
                }
            />

            {selectedVideoToPlay && (
                <CreatieViewerFloatingVideo
                    secondToJump={secondToJumpOnVideo}
                    creative={selectedVideoToPlay}
                    onPlay={handlePlayVideo}
                    onPause={handlePauseVideo}
                    onClose={handleCloseVideo}
                    onTimeUpdate={handleVideoTimeUpdate}
                />
            )}

            <CreativeViewerChartConfigurator
                anchor={chartConfiguratorAnchor}
                onClose={() => setChartConfiguratorAnchor(undefined)}
            />
        </>
    );
};
