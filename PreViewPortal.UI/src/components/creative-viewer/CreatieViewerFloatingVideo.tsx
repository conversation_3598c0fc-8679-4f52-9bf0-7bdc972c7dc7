import { Box, IconButton, Portal, Stack } from '@mui/material';
import { useEffect, useRef, useState } from 'react';
import { CreativeViewerVideoModel } from '../../interfaces/creative-viewer-video-model';
import { Rnd } from 'react-rnd';
import { useCreativeViewer } from '@/hooks/use-creative-viewer';
import CloseIcon from '@mui/icons-material/Close';
import { useWindowScroll, useWindowSize } from 'react-use';

type VideoProps = React.VideoHTMLAttributes<HTMLVideoElement>;

interface Props {
    creative: CreativeViewerVideoModel;
    secondToJump: number;
    onPlay: () => void;
    onPause: () => void;
    onTimeUpdate: VideoProps['onTimeUpdate'];
    onClose: () => void;
}

export const CreatieViewerFloatingVideo = ({
    creative,
    secondToJump,
    onPlay,
    onPause,
    onClose,
    onTimeUpdate
}: Props) => {
    const [position, setPosition] = useState<{ x: number; y: number }>();
    const [isHovering, setIsHovering] = useState(false);
    const [isPlaying, setIsPlaying] = useState(true);

    const [size, setSize] = useState<{
        width: number | string;
        height: number | string;
    }>({
        width: 400,
        height: 250
    });

    const { zoomScale } = useCreativeViewer();

    const { width: windowWidth, height: windowHeight } = useWindowSize();
    const { y: windowY } = useWindowScroll();

    useEffect(() => {
        setPosition({
            y: (windowHeight * 0.09 + windowY) / zoomScale,
            x: (windowWidth * 0.7) / zoomScale
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const ref = useRef<HTMLVideoElement>(null);

    useEffect(() => {
        const video = ref.current;

        if (!video) return;

        video.pause();
        video.currentTime = secondToJump;
    }, [secondToJump]);

    if (!position) return null;

    const handleOnPlay = () => {
        setIsPlaying(true);
        onPlay();
    };
    const handleOnPause = () => {
        setIsPlaying(false);
        onPause();
    };

    return (
        <Portal>
            <Rnd
                style={{
                    zIndex: 1501,
                    overflow: 'hidden',
                    zoom: zoomScale
                }}
                scale={zoomScale}
                size={size}
                minHeight={150}
                minWidth={150}
                position={position}
                bounds='window'
                dragHandleClassName='video-dragHandle'
                onDragStop={(_, d) => setPosition({ x: d.x, y: d.y })}
                onResizeStop={(e, d, ref, delta, position) => {
                    setSize({
                        width: ref.style.width,
                        height: ref.style.height
                    });
                    setPosition(position);
                }}
                enableResizing={{
                    bottom: false,
                    top: false,
                    left: false,
                    right: false,
                    topLeft: true,
                    topRight: true,
                    bottomLeft: true,
                    bottomRight: true
                }}
            >
                {creative.color && (
                    <Stack
                        bgcolor={creative.color}
                        width='100%'
                        height={10}
                        borderRadius={'5px 5px 0px 0px'}
                    />
                )}
                <Box
                    position='relative'
                    height='100%'
                    width='100%'
                    bgcolor='black'
                    onClick={e => e.stopPropagation()}
                    className='video-dragHandle'
                    onMouseEnter={() => setIsHovering(true)}
                    onMouseLeave={() => setIsHovering(false)}
                >
                    <Stack
                        height='20%'
                        width='100%'
                        zIndex={2}
                        position='absolute'
                        justifyContent='center'
                        alignItems='end'
                        sx={{
                            background: 'linear-gradient(black, transparent)',
                            opacity: 0.75,
                            display:
                                isPlaying && !isHovering ? 'none' : undefined
                        }}
                    >
                        <IconButton
                            onClick={onClose}
                            sx={{
                                p: 0,
                                right: 10,
                                '&:hover': {
                                    backgroundColor: 'rgba(0, 0, 0, 0.6)'
                                },
                                color: 'white',
                                zIndex: 2
                            }}
                        >
                            <CloseIcon />
                        </IconButton>
                    </Stack>

                    <video
                        ref={ref}
                        height='100%'
                        width='100%'
                        src={creative.sourceMediaUrl}
                        poster={creative.thumbnailUrl}
                        onClick={e => e.preventDefault()}
                        controls
                        controlsList='nodownload noplaybackrate'
                        onPlay={handleOnPlay}
                        onPause={handleOnPause}
                        onTimeUpdate={onTimeUpdate}
                        disablePictureInPicture
                        disableRemotePlayback
                        autoPlay
                    />
                </Box>
            </Rnd>
        </Portal>
    );
};
