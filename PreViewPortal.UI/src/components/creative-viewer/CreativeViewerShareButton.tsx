import React, {useState} from 'react';
import {Box, Button, Divider, IconButton, Popover, Stack, TextField, Tooltip, Typography} from '@mui/material';
import {useSnackbar} from 'notistack';
import ShareIcon from '@mui/icons-material/Share';
import AddIcon from '@mui/icons-material/Add';
import {useHttp} from "@/hooks/use-http";
import {CreativeSelectionModel} from "@/interfaces/creative-selection-model";
import {ProductOptions} from "@/interfaces/product-options";
import {ShareKeyModel} from "@/interfaces/share-key-model";
import {LinkIcon} from "@/icons/LinkIcon";
import CloseIcon from '@mui/icons-material/Close';

interface Props {
    isShare: boolean,
    selectedCreatives: CreativeSelectionModel[];
    selectedProduct: ProductOptions;
    shareKeyParam: string | null;
}

export const CreativeViewerShareButton: React.FC<Props> = ({
                                                               isShare,
                                                               selectedCreatives,
                                                               selectedProduct,
                                                               shareKeyParam
                                                           }) => {
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const {enqueueSnackbar} = useSnackbar();
    const {http, handleError} = useHttp();
    const [shareKey, setShareKey] = useState<string | null>(shareKeyParam);

    const handleClick = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const createShareUrl = async () => {
        setIsLoading(true);
        try {
            const response = await http.post('share/createShareKey', {
                json: {
                    media: selectedCreatives,
                    productType: selectedProduct
                }
            });
            const shareKeyModel = await response.json<ShareKeyModel>();
            setShareKey(shareKeyModel.shareKey);

            await navigator.clipboard.writeText(getPrefixedShareKey(shareKeyModel.shareKey ? shareKeyModel.shareKey : ''));
            linkCopied();
        } catch (error) {
            console.error('Error creating share key:', error);
            handleError(error);
        } finally {
            setIsLoading(false);
        }
    };

    const handleRevoke = async () => {
        setIsLoading(true);
        try {
            await http.post('share/revertShareKey', {
                json: shareKey
            });
            setShareKey(null);
            enqueueSnackbar('Link revoked. No one with the link can access the test results until it is re-shared.', {variant: 'info'});
        } catch (error) {
            console.error('Error reverting share key:', error);
            handleError(error);
        } finally {
            setIsLoading(false);
        }
    };

    const handleCopy = () => {
        navigator.clipboard.writeText(getPrefixedShareKey(shareKey ? shareKey : ''));
        linkCopied()
    };

    const getPrefixedShareKey = (shareKey: string) => {
        return `${window.location.origin}/Creative/Share?shareKey=${shareKey ? `${shareKey}` : ''}`;
    };

    const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
        if (event.key === 'Backspace' || event.key === 'Delete') {
            event.preventDefault();
        } else if ((event.ctrlKey || event.metaKey) && event.key === 'c') {
            linkCopied();
        }
    };
    const linkCopied = () => {
        enqueueSnackbar('Link copied. The link of the test results is copied to the clipboard.', {variant: 'success'});
    };

    return (
        <>
            <Tooltip
                title='Share Test Results'
                slotProps={{
                    popper: {
                        disablePortal: true
                    }
                }}
            >
                <span>
                    <IconButton
                        sx={{
                            '&:hover': {
                                color: '#2DC0A2'
                            },
                            color: '#273235'
                        }}
                        onClick={handleClick}
                    >
                        <ShareIcon/>
                    </IconButton>
                    </span>
            </Tooltip>
            <Popover
                open={!!anchorEl}
                onClose={handleClose}
                anchorEl={anchorEl}
                sx={{
                    zIndex: 9999
                }}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'left'
                }}
            >
                <Box width={450} position="relative">
                    <Stack direction="row" justifyContent="space-between" alignItems="center" ml={2} mr={2} my={2}>
                        <Typography variant='h6'>
                            {'Share Time Series Data'}
                        </Typography>
                        <IconButton
                            color='inherit'
                            onClick={handleClose}
                            sx={{padding: 0}}
                        >
                            <CloseIcon/>
                        </IconButton>
                    </Stack>
                    <Divider/>
                    <Stack ml={2} my={2} gap={2}>
                        <Typography variant='body2'>
                            Anyone with the link can access the test results.
                        </Typography>
                    </Stack>
                    {shareKey && (
                        <Box m={2}>
                            <TextField
                                fullWidth
                                variant="outlined"
                                value={getPrefixedShareKey(shareKey)}
                                onFocus={(event) => event.target.select()}
                                onKeyDown={handleKeyDown}
                                InputProps={{
                                    readOnly: true,
                                    sx: {
                                        fontSize: 14,
                                        padding: '4px 8px'
                                    }
                                }}
                                sx={{
                                    border: '1px solid',
                                    borderColor: 'grey.400',
                                    marginBottom: 2,
                                    '& .MuiOutlinedInput-input': {
                                        fontSize: 14,
                                        padding: '4px 8px'
                                    }
                                }}
                            />
                        </Box>
                    )}
                    <Divider/>
                    <Stack
                        m={2}
                        flexDirection='row'
                        justifyContent='end'
                        gap={3}
                    >
                        {shareKey ? (
                            <>
                                {!isShare && (
                                    <Button onClick={handleRevoke} disabled={isLoading}>
                                        {'Revoke Link'}
                                    </Button>
                                )}
                                <Button variant='contained' onClick={handleCopy} disabled={isLoading}
                                        startIcon={<LinkIcon/>}>
                                    {'Copy Link'}
                                </Button>
                            </>
                        ) : (
                            <>
                                {!isShare && (
                                    <Button variant='contained' onClick={createShareUrl} disabled={isLoading}
                                            startIcon={<AddIcon/>}>
                                        {'Create Link'}
                                    </Button>
                                )}
                            </>
                        )}
                    </Stack>
                </Box>
            </Popover>
        </>
    );
};
