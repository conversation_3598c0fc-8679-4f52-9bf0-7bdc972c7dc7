import { Box } from '@mui/system';
import { useCreativeViewer } from '../../hooks/use-creative-viewer';
import { CreativeViewerVideoModel } from '../../interfaces/creative-viewer-video-model';
import {
    <PERSON><PERSON>,
    Collapse,
    Divider,
    IconButton,
    Paper,
    Stack,
    SvgIcon,
    Tooltip,
    TooltipProps,
    Typography
} from '@mui/material';
import {
    AdformatCell,
    BrandCell,
    CountryCell,
    MultiLineCell,
    PercentageCell
} from '../GridRenderCell';
import PlayIconSvg from '@/assets/icons/play-icon.svg?react';
import EditNormSvg from '@/assets/icons/edit-norm-icon.svg?react';
import { fullSegment } from '@/constant/full-segment';
import React, { useState } from 'react';
import { useProduct } from '@/hooks/use-product';
import { ProductOptions } from '@/interfaces/product-options';
import { CellWithTooltip } from '../CellWithTooltip';
import { SurveyScore, SurveyScoreOption } from '@/constant/survey-scores-def';
import { useChartConfigurator } from '@/hooks/use-chart-configurator';
import { NormData } from '@/interfaces/norm-data';
import {
    hasAnyIndustryInList,
    hasBrandInList,
    hasCountryInList
} from '@/constant/norm-tooltip-helper';
import { NormCategory } from '@/interfaces/norm-category';
import { useUser } from '@/hooks/use-user';
import { MediaAndTestIdsPopover } from '../MediaAndTestIdsPopover';

interface CustomTooltipProps {
    name?: string;
    text?: React.ReactNode;
    customElement?: React.ReactNode;
    placement?: TooltipProps['placement'];
}

const CustomTooltip = ({
    name,
    text,
    customElement,
    placement,
    children
}: React.PropsWithChildren<CustomTooltipProps>) => {
    const { zoomScale } = useCreativeViewer();

    return (
        <Tooltip
            slotProps={{
                tooltip: {
                    sx: {
                        backgroundColor: 'unset',
                        padding: 0
                    }
                }
            }}
            placement={placement}
            title={
                <Stack
                    bgcolor='#273235'
                    borderRadius={'5px'}
                    p={1}
                    width='min-content'
                    gap={1}
                    sx={{
                        zoom: zoomScale
                    }}
                >
                    <Stack
                        direction='row'
                        alignItems='center'
                        justifyContent='space-between'
                        gap={2}
                        pb={customElement ? 1 : 0}
                    >
                        {name && (
                            <Typography
                                variant='body2'
                                sx={{ color: '#D5DDE4' }}
                                noWrap
                            >
                                {name}
                            </Typography>
                        )}
                        <Typography variant='body2' color='inherit' noWrap>
                            {text}
                        </Typography>
                    </Stack>
                    {customElement}
                </Stack>
            }
        >
            <span>{children}</span>
        </Tooltip>
    );
};

interface ScorePaperProps {
    selectedSurveyScore: SurveyScoreOption;
    scoreValues: SurveyScore;
    multipleSegments?: boolean;
    normData?: NormData[];
}

const ScorePaper = ({
    selectedSurveyScore,
    normData,
    scoreValues,
    multipleSegments
}: ScorePaperProps) => {
    const { score, norm, vsNorm, label, scoreBase, exposureGroup } =
        selectedSurveyScore;

    const { showNormDetails } = useChartConfigurator();

    const { zoomScale } = useCreativeViewer();

    const labelToDisplay = multipleSegments ? scoreValues.segmentLabel : label;

    const filteredNormData = normData?.filter(
        n => n.segmentKey === scoreValues.segmentKey
    );

    const normItem = filteredNormData?.find(n => {
        return (
            n.videoId === scoreValues.id &&
            n.segmentKey === scoreValues.segmentKey
        );
    });

    const hasCountry = hasCountryInList(
        filteredNormData,
        NormCategory.SurveyNorm,
        exposureGroup
    );
    const hasBrand = hasBrandInList(
        filteredNormData,
        NormCategory.SurveyNorm,
        exposureGroup
    );
    const hasIndustry = hasAnyIndustryInList(
        filteredNormData,
        NormCategory.SurveyNorm,
        exposureGroup
    );

    return (
        <Paper
            sx={{
                borderRadius: '5px',
                px: 1,
                py: 2,
                display: 'flex',
                gap: 2,
                alignItems: 'center',
                minWidth: multipleSegments ? 250 : undefined
            }}
        >
            <Typography variant='body2'>{labelToDisplay}</Typography>

            <Stack direction='row' gap={1.5} ml='auto'>
                <CellWithTooltip
                    field={score}
                    diffField={vsNorm}
                    normField={norm}
                    scoreName={label}
                    scoreBase={scoreBase}
                    normData={normItem}
                    row={scoreValues}
                    segmentKeyLabel={scoreValues.segmentLabel}
                    exposureGroup={exposureGroup}
                    showNormTooltip={showNormDetails}
                    zoomScale={zoomScale}
                    tooltipPlacement={'bottom-start'}
                    hasCountry={hasCountry}
                    hasBrand={hasBrand}
                    hasIndustry={hasIndustry}
                >
                    <Typography variant='body2' fontWeight='bold'>
                        {selectedSurveyScore!.scoreBase === '%' ? (
                            <PercentageCell value={+scoreValues[score]!} />
                        ) : (
                            (+scoreValues[score]!).toFixed(1)
                        )}
                    </Typography>
                </CellWithTooltip>
                <CellWithTooltip
                    field={score}
                    diffField={vsNorm}
                    normField={norm}
                    scoreName={label}
                    scoreBase={scoreBase}
                    row={scoreValues}
                    normData={normItem}
                    segmentKeyLabel={scoreValues.segmentLabel}
                    exposureGroup={exposureGroup}
                    showNormTooltip={showNormDetails}
                    zoomScale={zoomScale}
                    tooltipPlacement={'bottom-start'}
                    hasCountry={hasCountry}
                    hasBrand={hasBrand}
                    hasIndustry={hasIndustry}
                >
                    <Stack direction='row' alignItems='center' gap={0.5}>
                        <SvgIcon
                            component={EditNormSvg}
                            inheritViewBox
                            fontSize='inherit'
                        />
                        <Typography variant='body2'>
                            {selectedSurveyScore!.scoreBase === '%' ? (
                                <PercentageCell value={+scoreValues[norm]!} />
                            ) : (
                                (+scoreValues[norm]!).toFixed(1)
                            )}
                        </Typography>
                    </Stack>
                </CellWithTooltip>
            </Stack>
        </Paper>
    );
};

interface Props {
    currentFrame: number;
    playingVideoId: string | undefined;
    onPlayClick: (video: CreativeViewerVideoModel) => () => void;
}

export const CreativeViewerCards = ({
    currentFrame,
    playingVideoId,
    onPlayClick
}: Props) => {
    const {
        videoData,
        selectedCurves,
        selectedCurveSegments,
        multipleMediaSelected,
        curveColors,
        surveyScoreData,
        selectedSurveyScore,
        getSegmentKeyTooltipLabel,
        normData
    } = useCreativeViewer();

    const { selectedProduct } = useProduct();

    const { isAdmin } = useUser();

    const isInContextProduct = selectedProduct === ProductOptions.InContext;

    const [isVideoDetailsOpen, setIsVideoDetailsOpen] = useState(false);

    const [anchorEl, setAnchorEl] = useState<HTMLSpanElement | null>(null);

    const handleClickOnCreative = (e: React.MouseEvent<HTMLSpanElement>) => {
        if (!isAdmin) return;

        setAnchorEl(e.currentTarget);
    };

    return (
        <Stack justifyContent='center' gap={2} direction='row'>
            {videoData.map(video => {
                const {
                    id: videoId,
                    duration,
                    sourceMedia,
                    sourceMediaID,
                    testID,
                    inFocusTestID,
                    brand,
                    brandLogoUrl,
                    country,
                    country_code,
                    platform,
                    thumbstripUrl,
                    adformatText,
                    adformatTextIF,
                    surveyKeyAlias
                } = video;

                let curveColor = '';

                if (multipleMediaSelected) {
                    const curveId = selectedCurves.find(
                        c => !c.isNorm && c.videoId === videoId
                    )?.id;

                    curveColor = curveColors.get(curveId || '')?.color || '';
                }

                const isVideoPlaying = playingVideoId === videoId;

                const scoresByVideo = surveyScoreData
                    ?.filter(
                        s =>
                            s.id === videoId &&
                            selectedCurveSegments.includes(s.segmentKey!)
                    )
                    .map(s => ({
                        ...s,
                        segmentLabel:
                            s.segmentKey === fullSegment.segmentKey
                                ? fullSegment.answer
                                : getSegmentKeyTooltipLabel(s.segmentKey)
                    }))
                    .sort((a, b) => {
                        if (a.segmentKey === fullSegment.segmentKey) return 1;

                        if (b.segmentKey === fullSegment.segmentKey) return -1;

                        return a.segmentLabel.localeCompare(b.segmentLabel);
                    });

                let singleScoreToDisplay;

                if (
                    selectedSurveyScore &&
                    scoresByVideo &&
                    multipleMediaSelected
                ) {
                    singleScoreToDisplay = scoresByVideo[0];
                }

                const frameWidth = 240;
                const frameHeight = 140;
                const frameCount = duration + 1;

                let offset: number;
                if (frameCount === 1) {
                    offset = 0;
                } else if (currentFrame === frameCount) {
                    offset = currentFrame * frameWidth - frameWidth;
                } else {
                    offset = currentFrame * frameWidth;
                }

                const durationLabel = duration + 's';

                const adformatLabel = (
                    <>
                        {isInContextProduct && (
                            <>
                                {adformatText}
                                <br />
                                {'+ '}
                            </>
                        )}
                        {adformatTextIF}
                    </>
                );

                const inContextTestID = inFocusTestID && testID;

                return (
                    <Stack key={`${videoId}-videoCard`} direction='row' gap={2}>
                        <Stack width={frameWidth} gap={1}>
                            <Paper
                                sx={{
                                    borderRadius: '5px',
                                    width: '100%'
                                }}
                            >
                                <Stack
                                    bgcolor={curveColor}
                                    width='100%'
                                    height={10}
                                    borderRadius={'5px 5px 0px 0px'}
                                />

                                <Stack px={1}>
                                    <Stack
                                        direction='row'
                                        alignItems='center'
                                        minHeight={70}
                                        gap={2}
                                    >
                                        <MediaAndTestIdsPopover
                                            anchorEl={anchorEl}
                                            isAdmin={isAdmin}
                                            onClose={() => setAnchorEl(null)}
                                            sourceMedia={sourceMedia}
                                            sourceMediaID={sourceMediaID}
                                            inFocusTestID={
                                                inFocusTestID || testID
                                            }
                                            inContextTestID={inContextTestID}
                                            surveyKeyAlias={surveyKeyAlias}
                                        />
                                        <MultiLineCell
                                            value={sourceMedia}
                                            onClick={handleClickOnCreative}
                                        />

                                        <CustomTooltip
                                            name={'Brand'}
                                            text={brand}
                                        >
                                            <BrandCell
                                                brand={''}
                                                brandLogoUrl={brandLogoUrl}
                                                onlyLogo
                                            />
                                        </CustomTooltip>
                                    </Stack>

                                    <Stack
                                        direction='row'
                                        alignItems='center'
                                        gap={2}
                                        width='100%'
                                    >
                                        {!isVideoDetailsOpen && (
                                            <>
                                                <CustomTooltip
                                                    name={'Ad Format'}
                                                    text={adformatLabel}
                                                >
                                                    <AdformatCell
                                                        platform={platform!}
                                                    />
                                                </CustomTooltip>

                                                <CustomTooltip
                                                    name={'Country'}
                                                    text={country!}
                                                >
                                                    <CountryCell
                                                        country={country!}
                                                        countryCode={
                                                            country_code!
                                                        }
                                                        disableTooltip
                                                    />
                                                </CustomTooltip>

                                                <CustomTooltip
                                                    name={'Device'}
                                                    text={'Video'}
                                                >
                                                    <Typography variant='caption'>
                                                        {'Video'}
                                                    </Typography>
                                                </CustomTooltip>

                                                <CustomTooltip
                                                    name={'Duration'}
                                                    text={durationLabel}
                                                >
                                                    <Typography variant='caption'>
                                                        {durationLabel}
                                                    </Typography>
                                                </CustomTooltip>

                                                <Button
                                                    size='small'
                                                    disableRipple
                                                    sx={{
                                                        textTransform: 'none'
                                                    }}
                                                    onClick={() =>
                                                        setIsVideoDetailsOpen(
                                                            true
                                                        )
                                                    }
                                                >
                                                    {'... more'}
                                                </Button>
                                            </>
                                        )}
                                    </Stack>

                                    <Collapse
                                        in={isVideoDetailsOpen}
                                        timeout='auto'
                                        sx={{ width: '100%', pt: 1 }}
                                    >
                                        <Stack gap={0.5}>
                                            <CustomTooltip
                                                name={'Brand'}
                                                text={brand}
                                                placement='right-start'
                                            >
                                                <Typography variant='caption'>
                                                    {brand}
                                                </Typography>
                                            </CustomTooltip>
                                            <Divider />

                                            <CustomTooltip
                                                name={'Ad Format'}
                                                text={adformatLabel}
                                                placement='right-start'
                                            >
                                                <Typography variant='caption'>
                                                    {adformatLabel}
                                                </Typography>
                                            </CustomTooltip>
                                            <Divider />

                                            <CustomTooltip
                                                name={'Country'}
                                                text={country!}
                                                placement='right-start'
                                            >
                                                <Typography variant='caption'>
                                                    {country}
                                                </Typography>
                                            </CustomTooltip>
                                            <Divider />

                                            <CustomTooltip
                                                name={'Device'}
                                                text={'Video'}
                                                placement='right-start'
                                            >
                                                <Typography variant='caption'>
                                                    {'Video'}
                                                </Typography>
                                            </CustomTooltip>
                                            <Divider />

                                            <CustomTooltip
                                                name={'Duration'}
                                                text={durationLabel}
                                                placement='right-start'
                                            >
                                                <Typography variant='caption'>
                                                    {durationLabel}
                                                </Typography>
                                            </CustomTooltip>
                                        </Stack>

                                        <Stack width='100%' alignItems='end'>
                                            <Button
                                                size='small'
                                                disableRipple
                                                sx={{
                                                    textTransform: 'none'
                                                }}
                                                onClick={() =>
                                                    setIsVideoDetailsOpen(false)
                                                }
                                            >
                                                {'Show less'}
                                            </Button>
                                        </Stack>
                                    </Collapse>
                                </Stack>

                                <Stack position='relative'>
                                    {isVideoPlaying ? (
                                        <Typography
                                            sx={{
                                                color: 'white',
                                                position: 'absolute',
                                                bottom: 10,
                                                left: 10,
                                                p: 0.5,
                                                backgroundColor:
                                                    'rgba(0, 0, 0, 0.6)'
                                            }}
                                        >
                                            {'Now Playing'}
                                        </Typography>
                                    ) : (
                                        <IconButton
                                            disableRipple
                                            sx={{
                                                position: 'absolute',
                                                bottom: 0,
                                                color: 'grey'
                                            }}
                                            onClick={onPlayClick(video)}
                                        >
                                            <SvgIcon
                                                sx={{
                                                    fontSize: 33
                                                }}
                                                component={PlayIconSvg}
                                                inheritViewBox
                                                color='inherit'
                                            />
                                        </IconButton>
                                    )}
                                    <Box
                                        sx={{
                                            width: frameWidth,
                                            height: frameHeight,
                                            borderRadius: '0px 0px 5px 5px',
                                            backgroundSize: 'cover',
                                            backgroundRepeat: 'no-repeat',
                                            backgroundImage: `url(${thumbstripUrl})`,
                                            backgroundPositionX: -offset
                                        }}
                                    />
                                </Stack>
                            </Paper>
                            {singleScoreToDisplay && (
                                <ScorePaper
                                    scoreValues={singleScoreToDisplay}
                                    selectedSurveyScore={selectedSurveyScore!}
                                    normData={normData}
                                />
                            )}
                        </Stack>

                        {!multipleMediaSelected && selectedSurveyScore && (
                            <Stack>
                                <Typography
                                    variant='caption'
                                    color='primary'
                                    fontWeight='bold'
                                    pl={1}
                                    pb={1}
                                >
                                    {selectedSurveyScore.label}
                                </Typography>
                                <Stack gap={1} flexWrap='wrap' maxHeight={250}>
                                    {scoresByVideo?.map(sv => (
                                        <span
                                            key={`${sv.id}-${sv.segmentKey}-scorePaper`}
                                        >
                                            <ScorePaper
                                                scoreValues={sv}
                                                selectedSurveyScore={
                                                    selectedSurveyScore
                                                }
                                                normData={normData}
                                                multipleSegments
                                            />
                                        </span>
                                    ))}
                                </Stack>
                            </Stack>
                        )}
                    </Stack>
                );
            })}
        </Stack>
    );
};
