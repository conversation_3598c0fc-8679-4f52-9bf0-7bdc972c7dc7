import {
    Autocomplete,
    Button,
    IconButton,
    Stack,
    TextField,
    Typography
} from '@mui/material';
import { surveyScoreOptions } from '@/constant/survey-scores-def';
import CloseIcon from '@mui/icons-material/Close';
import { Add } from '@mui/icons-material';
import { useCreativeViewer } from '@/hooks/use-creative-viewer';

export const CreativeViewerSurveySelector = () => {
    const { selectedSurveyScore, setSelectedSurveyScore } = useCreativeViewer();

    return (
        <Stack mb='30px'>
            <Typography variant='h6' mb={'10px'}>
                {'Survey Performance Score'}
            </Typography>
            <Stack gap={0.5} direction='row'>
                {selectedSurveyScore === undefined ? (
                    <Button
                        startIcon={<Add />}
                        onClick={() => setSelectedSurveyScore(null)}
                    >
                        {'Add'}
                    </Button>
                ) : (
                    <>
                        <Autocomplete
                            sx={{
                                '& .MuiAutocomplete-popupIndicator': {
                                    color: 'white'
                                },
                                '& .MuiInputBase-root': {
                                    paddingRight: '6px !important'
                                },
                                '& .MuiAutocomplete-clearIndicator': {
                                    display: 'none'
                                }
                            }}
                            fullWidth
                            size='small'
                            value={selectedSurveyScore}
                            options={surveyScoreOptions}
                            isOptionEqualToValue={(o, v) => o.score === v.score}
                            onChange={(_, o) => setSelectedSurveyScore(o)}
                            renderInput={props => <TextField {...props} />}
                            renderOption={(props, option) => (
                                <li {...props}>
                                    <Typography color='black' fontSize={15}>
                                        {option.label}
                                    </Typography>
                                </li>
                            )}
                        />
                        <IconButton
                            onClick={() => setSelectedSurveyScore(undefined)}
                            sx={{ p: 0 }}
                        >
                            <CloseIcon color='primary' />
                        </IconButton>
                    </>
                )}
            </Stack>
        </Stack>
    );
};
