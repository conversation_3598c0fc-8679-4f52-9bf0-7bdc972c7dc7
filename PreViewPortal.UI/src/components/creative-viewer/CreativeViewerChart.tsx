import { Box } from '@mui/system';
import * as d3 from 'd3';
import moment from 'moment';
import { useEffect, useMemo, useRef, useState } from 'react';
import { CreativeViewerChartModel } from '../../interfaces/creative-viewer-chart-model';
import { CreativeViewerCurveSecondModel } from '../../interfaces/creative-viewer-curve-second-model';
import { useCreativeViewer } from '@/hooks/use-creative-viewer';
import { CreativeViewerChartTooltip } from './CreativeViewerChartTooltip';
import { generateIdByCurveParams } from './CreativeViewer';
import { useChartConfigurator } from '@/hooks/use-chart-configurator';
import { ExposureGroupType } from '@/contexts/creative-viewer-context';
import { CustomAnchorElement } from '@/interfaces/custom-anchor-element';
import { SideType } from '@/interfaces/side-type';

interface Props {
    currentFrame: number;
    onCurrentFrameChange: (sec: number) => void;
    allowHover: boolean;
    height?: number;
    onClick: (sec: number) => void;
}

export const chartMargin = { top: 0, right: 0, bottom: 0, left: 80 };
export const curveWidth = 1055;
const tickPadding = 10;
const tickSizeInner = 5;
const fontSizeOnAxis = '12px';
const yPositionSeekbar = 10;

type SeekbarType = d3.Selection<Element, undefined, null, undefined>;

export const CreativeViewerChart = ({
    currentFrame,
    allowHover,
    height = 400,
    onClick,
    onCurrentFrameChange
}: Props) => {
    const svgRef = useRef<SVGSVGElement>(null);
    const curveElementsRef = useRef<any[]>([]);
    const seekbarRef = useRef<SeekbarType>(null);

    const [isSeekbarVisible, setIsSeekbarVisible] = useState(false);
    const [seekbarFixedSecond, setSeekbarFixedSecond] = useState<number>();

    const [tooltipAnchorEl, setTooltipAnchorEl] =
        useState<CustomAnchorElement | null>(null);

    const width = curveWidth - chartMargin.left - chartMargin.right;
    const svgHeight = (height = height - chartMargin.top - chartMargin.bottom);

    const {
        selectedCurves: curves,
        curveDataForAxises,
        curveColors,
        getDisplayFullSegmentAsArea,
        focusedId,
        setFocusedId,
        setLeftCurvesRange,
        setRightCurvesRange
    } = useCreativeViewer();

    const {
        curveTypeYAxisGroups,
        exposureGroupStyles,
        customLeftAxisRange,
        customRightAxisRange,
        showAxisMarkers,
        normSettingsMode,
        smoothingLevel
    } = useChartConfigurator();

    const isSmoothingMode = smoothingLevel !== 0;
    const smoothingBeta = 1 - smoothingLevel + 0.01;

    const {
        yMinLeft,
        yMaxLeft,
        yScaleLeft,
        yMinRight,
        yMaxRight,
        yScaleRight
    } = useMemo(() => {
        const calculateValuesForYAxis = (side: SideType) => {
            const data = curveDataForAxises
                .filter(c => {
                    const yAxisGroupKey = generateIdByCurveParams({
                        type: c.type,
                        exposureGroup: c.exposureGroup
                    });
                    return curveTypeYAxisGroups[yAxisGroupKey] === side;
                })
                .flatMap(c => c.values)
                .map(v => v.pct);

            if (!data.length) return {};

            const divisor = 0.05;

            const [min, max] = d3.extent(data) as [number, number];

            let yMin = Math.floor(min / divisor) * divisor;
            let yMax = Math.ceil(max / divisor) * divisor || divisor;

            const curvesRange = [yMin, yMax];

            if (side === 'left') setLeftCurvesRange(curvesRange);
            else setRightCurvesRange(curvesRange);

            const customRange =
                side === 'left' ? customLeftAxisRange : customRightAxisRange;

            const [yCustomMin, yCustomMax] = customRange || [];
            yMin = Math.min(yMin, yCustomMin);
            yMax = Math.max(yMax, yCustomMax);

            const yScale = d3
                .scaleLinear()
                .domain([yMin, yMax])
                .range([height, 0]);

            return { yMin, yMax, yScale };
        };

        const {
            yMin: yMinLeft,
            yMax: yMaxLeft,
            yScale: yScaleLeft
        } = calculateValuesForYAxis('left');

        const {
            yMin: yMinRight,
            yMax: yMaxRight,
            yScale: yScaleRight
        } = calculateValuesForYAxis('right');

        return {
            yMinLeft,
            yMaxLeft,
            yScaleLeft,
            yMinRight,
            yMaxRight,
            yScaleRight
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [
        curveTypeYAxisGroups,
        curveDataForAxises,
        height,
        customLeftAxisRange,
        customRightAxisRange
    ]);

    const formatDuration = (sec: number | d3.NumberValue) => {
        const ms = (sec as number) * 1000;
        return moment.utc(ms).format('mm:ss');
    };

    const drawXAxis = () => {
        const [min, max] = xDomain;
        const tickValues = Array.from(
            { length: max - min + 1 },
            (_, i) => i + min
        );
        const tickFormat = (sec: number | d3.NumberValue) => {
            if (sec !== min && sec !== max) {
                return '';
            }

            return formatDuration(sec);
        };

        const axis = d3
            .axisBottom(xScale)
            .tickValues(tickValues)
            .tickPadding(tickPadding)
            .tickSizeInner(tickSizeInner)
            .tickFormat(tickFormat);

        const svg = d3.create('svg:svg').attr('overflow', 'visible');

        const group = svg
            .append('g')
            .style('font-size', fontSizeOnAxis)
            .attr('transform', 'translate(0,' + svgHeight + ')');

        group.call(axis);

        group
            .select('.domain')
            .attr('stroke', '#AFB9BB')
            .attr('stroke-dasharray', 2);

        group
            .selectAll('line')
            .attr('stroke', '#AFB9BB')
            .attr('stroke-dasharray', 2);

        return svg;
    };

    const drawYAxis = (side: SideType) => {
        let yAxis = d3.axisLeft(
            d3.scaleLinear().domain([0, 0]).range([height, 0])
        );

        let tickValues: number[] = [];

        const getTickValues = (yMin: number, yMax: number) => [
            yMin,
            (yMin + yMax) / 2,
            yMax
        ];

        if (side === 'left' && yScaleLeft) {
            yAxis = d3.axisLeft(yScaleLeft);
            tickValues = getTickValues(yMinLeft!, yMaxLeft!);
        }

        if (side === 'right' && yScaleRight) {
            yAxis = d3.axisRight(yScaleRight);
            tickValues = getTickValues(yMinRight!, yMaxRight!);
        }

        yAxis
            .ticks(3)
            .tickValues(tickValues)
            .tickPadding(tickPadding)
            .tickFormat(d3.format('.1%'));

        const group = d3.create('svg:g');

        const yAxisGroup = group
            .append('g')
            .style('font-size', fontSizeOnAxis)
            .call(yAxis);

        if (side === 'right')
            yAxisGroup.attr('transform', 'translate(' + width + ',0)');

        yAxisGroup
            .select('.domain')
            .attr('stroke', '#AFB9BB')
            .attr('stroke-dasharray', 2);
        yAxisGroup
            .selectAll('line')
            .attr('stroke', '#AFB9BB')
            .attr('stroke-dasharray', 2);

        return group;
    };

    const drawCurve = (curve: CreativeViewerChartModel, isSpline: boolean) => {
        const curveToAdd = {
            ...curve,
            isSpline,
            id: isSpline ? `${curve.id}-Spline` : curve.id
        };

        const { type, values, id, isNorm, segmentKey, exposureGroup } =
            curveToAdd;

        const group = d3.create('svg:g').datum(curveToAdd);

        const originalCurve = isSmoothingMode && !isSpline;

        const toHide =
            (isNorm && normSettingsMode === 'hovering') || originalCurve;

        if (toHide) group.attr('opacity', 0);

        const lineGroup = group.append('g');

        if (isSpline && !isNorm) {
            group.on('mouseover', function () {
                setFocusedId(curve.id);
            });

            group.on('mouseleave', function () {
                setFocusedId(undefined);
            });
        }

        const yAxisGroupKey = generateIdByCurveParams({
            type,
            exposureGroup
        });

        const curveBelongsToLeftAxis =
            curveTypeYAxisGroups[yAxisGroupKey] === 'left';

        const yScale = curveBelongsToLeftAxis ? yScaleLeft! : yScaleRight!;

        const displayFullSegmentAsArea = getDisplayFullSegmentAsArea(
            segmentKey,
            isNorm
        );

        if (!isSpline && displayFullSegmentAsArea) {
            //area under the curve
            const yMin = curveBelongsToLeftAxis ? yMinLeft! : yMinRight!;

            const defs = lineGroup.append('defs');

            const gradientId = `gradient-${id}`;

            const gradient = defs
                .append('linearGradient')
                .attr('id', gradientId)
                .attr('x1', '0%')
                .attr('x2', '0%')
                .attr('y1', '0%')
                .attr('y2', '100%');

            gradient
                .append('stop')
                .attr('offset', '0%')
                .attr('stop-color', '#D4D6D7')
                .attr('stop-opacity', 0.25);

            const area = d3
                .area<CreativeViewerCurveSecondModel>()
                .x(d => xScale(d.second))
                .y0(yScale(yMin))
                .y1(d => yScale(d.pct));

            lineGroup
                .append('path')
                .datum(values)
                .attr('fill', `url(#${gradientId})`)
                .attr('d', area)
                .attr('pointer-events', 'none');

            return { group, lineGroup, yScale };
        }

        const exposureGroupStyle = exposureGroupStyles[exposureGroup];

        const line = d3
            .line<CreativeViewerCurveSecondModel>()
            .x(d => xScale(d.second))
            .y(d => yScale(d.pct));

        if (isSpline) line.curve(d3.curveBundle.beta(smoothingBeta));

        const curveColor = curveColors.get(curve.id)?.color || null;

        if (isNorm && !originalCurve) group.attr('stroke-dasharray', 4);

        const isDoubleCurve = !originalCurve && exposureGroupStyle === 'Double';

        const strokeWidth = isDoubleCurve ? 4 : originalCurve ? 1 : 2;

        lineGroup
            .append('path')
            .datum(values)
            .attr('class', `curve-${type}`)
            .attr('fill', 'none')
            .attr('stroke', curveColor)
            .attr('stroke-width', strokeWidth)
            .attr('d', line);

        if (isDoubleCurve) {
            lineGroup
                .append('path')
                .datum(values)
                .attr('fill', 'none')
                .attr('stroke', 'white')
                .attr('stroke-width', 2)
                .attr('d', line);
        }

        return { group, lineGroup, yScale };
    };

    const drawSeekbar = () => {
        const group = d3.create('svg:g').style('opacity', '0');

        const hasNoCurveData = !curves.find(c => c.values.length);

        if (hasNoCurveData) return group;

        group
            .append('line')
            .attr('y1', -tickSizeInner)
            .attr('y2', svgHeight + tickSizeInner + tickPadding)
            .attr('stroke', '#D9D9D9')
            .attr('stroke-width', 1)
            .attr('pointer-events', 'none');

        const seekbarPolygonPoints = `0,${-tickSizeInner} 5,-10 5,-16 -5,-16 -5,-10`;

        group
            .append('polygon')
            .attr('points', seekbarPolygonPoints)
            .attr('fill', '#D9D9D9')
            .attr('stroke-width', 2);

        group
            .append('polygon')
            .attr('points', seekbarPolygonPoints)
            .attr('transform', `translate(0, ${svgHeight + 7}) scale(1,-1) `)
            .attr('fill', '#D9D9D9')
            .attr('stroke-width', 2);

        curves.forEach(({ id, isNorm }) => {
            const circleGroup = group
                .append('g')
                .attr('class', `circle-${id}`)
                .attr('opacity', 0);

            if (isNorm) {
                //norm node can overlap its curve node and focusing doesnt work
                circleGroup.attr('pointer-events', 'none');
            } else {
                circleGroup.on('mousemove', function () {
                    setFocusedId(id);
                });

                circleGroup.on('mouseleave', function () {
                    setFocusedId(undefined);
                });
            }

            circleGroup
                .append('circle')
                .attr('class', 'pct')
                .attr('r', 4)
                .attr('fill', curveColors.get(id)?.color || null)
                .attr('stroke-width', 2);
        });

        return group;
    };

    const drawAxisMarkers = (side: SideType) => {
        const group = d3
            .create('svg:g')
            .attr('class', 'markers')
            .attr('opacity', +showAxisMarkers);

        [...curveColors.entries()]
            .filter(([_, { type, isNorm, exposureGroup }]) => {
                const yAxisGroupKey = generateIdByCurveParams({
                    type,
                    exposureGroup
                });
                return !isNorm && curveTypeYAxisGroups[yAxisGroupKey] === side;
            })
            .forEach(([id, { color, exposureGroup }], idx) => {
                const xTranslate =
                    side === 'left' ? 8 + 14 * idx : width - 8 - 14 * idx;

                const circleGroup = group
                    .append('g')
                    .attr('class', `marker-${id}`)
                    .attr('transform', `translate(${xTranslate}, -8)`);

                const isDoubleExposureGroupStyle =
                    exposureGroupStyles[exposureGroup] === 'Double';

                circleGroup.append('circle').attr('r', 4).attr('fill', color);

                if (isDoubleExposureGroupStyle) {
                    //inner circle
                    circleGroup
                        .append('circle')
                        .attr('r', 4)
                        .attr('fill', 'white')
                        .attr('transform', `scale(0.4)`);
                }
            });

        return group;
    };

    const moveSeekbar = (second: number) => {
        if (!seekbarRef.current) return;

        if (second < xDomain[0]) return;

        const xPosition = xScale(second);

        setTooltipAnchorEl({ x: xPosition, y: yPositionSeekbar });

        seekbarRef.current.attr('transform', `translate(${xPosition}, 0)`);

        curveElementsRef.current.forEach(({ group, yScale }) => {
            const { id, values, isNorm } = group.datum();
            const pct = values[second - 1]?.pct;

            const circleGroup = seekbarRef.current!.select(`.circle-${id}`);

            if (pct === undefined) {
                circleGroup.attr('opacity', 0);
                return;
            }

            const yPosition = yScale(pct);

            circleGroup.select('.pct').attr('cy', yPosition);

            if (!focusedId && (normSettingsMode === 'always' || !isNorm))
                circleGroup.attr('opacity', 1);
        });

        onCurrentFrameChange(second);
    };

    const displaySeekbar = () => {
        seekbarRef.current?.style('opacity', 1);
        setIsSeekbarVisible(true);
    };

    const hideSeekbar = () => {
        seekbarRef.current?.style('opacity', 0);
        setIsSeekbarVisible(false);
    };

    const getNearestPointFromMousePosition = (e: any) => {
        if (!longestCurve) return;

        const bisect = d3.bisector<CreativeViewerCurveSecondModel, number>(
            d => d.second
        ).center;

        const x0 = xScale.invert(d3.pointer(e, svgRef.current)[0]);

        const i = bisect(longestCurve.values, x0);

        const nearestPoint = longestCurve.values[i];
        return nearestPoint;
    };

    const handleMouseOver = () => {
        if (!allowHover) return;

        displaySeekbar();
    };

    const handleMouseOut = () => {
        if (seekbarFixedSecond) {
            moveSeekbar(seekbarFixedSecond);
            return;
        }

        if (!!currentFrame) {
            moveSeekbar(currentFrame);
            return;
        }

        hideSeekbar();
    };

    const handleMouseMove = function (this: SVGRectElement, e: any) {
        if (!allowHover) return;

        const nearestPoint = getNearestPointFromMousePosition(e);
        if (!nearestPoint) return;

        moveSeekbar(nearestPoint.second);
    };

    const handleClick = function (this: SVGRectElement, e: any) {
        const nearestPoint = getNearestPointFromMousePosition(e);
        if (!nearestPoint) return;

        const second = nearestPoint.second;

        onClick(second);
        setSeekbarFixedSecond(second);
    };

    useEffect(() => {
        if (!curves.length) return;

        const svgEl = d3.select(svgRef.current);
        svgEl.selectAll('*').remove();

        const curveElements: any[] = [];

        const addSplinesOrNot = isSmoothingMode ? [true, false] : [false];

        curves.forEach(curve => {
            addSplinesOrNot.forEach(isSpline => {
                const curveElement = drawCurve(curve, isSpline);

                curveElements.push(curveElement);

                svgEl.append(() => curveElement.group.node());
            });
        });

        curveElementsRef.current = curveElements;

        const xAxis = drawXAxis();
        svgEl.append(() => xAxis.node());

        const leftYAxis = drawYAxis('left');
        svgEl.append(() => leftYAxis.node());

        const rightYAxis = drawYAxis('right');
        svgEl.append(() => rightYAxis.node());

        const seekbar = drawSeekbar();
        svgEl.append(() => seekbar.node());
        seekbarRef.current = seekbar;

        const leftMarkers = drawAxisMarkers('left');
        svgEl.append(() => leftMarkers.node());

        const rightMarkers = drawAxisMarkers('right');
        svgEl.append(() => rightMarkers.node());

        if (currentFrame) {
            displaySeekbar();
            moveSeekbar(currentFrame);
        }
        // eslint-disable-next-line
    }, [
        curves,
        curveTypeYAxisGroups,
        exposureGroupStyles,
        width,
        customLeftAxisRange,
        customRightAxisRange,
        showAxisMarkers,
        normSettingsMode,
        smoothingLevel
    ]);

    useEffect(() => {
        if (!currentFrame) {
            hideSeekbar();
            setSeekbarFixedSecond(undefined);
            return;
        }

        displaySeekbar();
        moveSeekbar(currentFrame);
        // eslint-disable-next-line
    }, [currentFrame]);

    useEffect(() => {
        curveElementsRef.current.forEach(({ group }) => {
            const { id, isNorm, segmentKey, isSpline } = group.datum();

            const isFocused = id.includes(focusedId);
            const isNotFocusedAtFocusing = focusedId && !isFocused;

            const originalCurve = isSmoothingMode && !isSpline;

            let curveOpacity = originalCurve ? 0.3 : 1;

            const displayFullSegmentAsArea = getDisplayFullSegmentAsArea(
                segmentKey,
                isNorm
            );

            if (isNotFocusedAtFocusing) curveOpacity = 0.1;

            if (displayFullSegmentAsArea) curveOpacity = 1;

            if (isNotFocusedAtFocusing && originalCurve) curveOpacity = 0;

            if (!isSeekbarVisible && originalCurve) curveOpacity = 0;

            if (!isFocused && isNorm && normSettingsMode === 'hovering')
                curveOpacity = 0;

            const circleOpacity =
                curveOpacity === 0 ? 0 : isNotFocusedAtFocusing ? 0.1 : 1;

            const markerOpacity = isNotFocusedAtFocusing ? 0.1 : 1;

            const circleGroup = seekbarRef.current!.select(`.circle-${id}`);
            const markerGroup = d3.select(`.marker-${id}`);

            group.attr('opacity', curveOpacity);
            circleGroup.attr('opacity', circleOpacity);
            markerGroup.attr('opacity', markerOpacity);
        });
        // eslint-disable-next-line
    }, [
        focusedId,
        isSeekbarVisible,
        smoothingLevel,
        customLeftAxisRange,
        customRightAxisRange,
        curveTypeYAxisGroups,
        exposureGroupStyles,
        normSettingsMode
    ]);

    const { longestCurve, xDomain, xScale } = useMemo(() => {
        const curveLengths = curveDataForAxises.map(c => c.values.length);

        const longestCurveIndex = curveLengths.indexOf(
            Math.max(...curveLengths)
        );

        const longestCurve =
            longestCurveIndex === -1
                ? undefined
                : curveDataForAxises[longestCurveIndex];

        const xValues = longestCurve?.values.map(v => v.second) || [];
        const xDomain = d3.extent(xValues) as [number, number];
        const xScale = d3.scaleLinear().domain(xDomain).range([0, width]);

        return {
            longestCurve,
            xDomain,
            xScale
        };
    }, [curveDataForAxises, width]);

    const tooltipData = useMemo(() => {
        const getExposureGroupTooltipData = (
            exposureGroupforTooltip: ExposureGroupType
        ) => {
            return curveElementsRef.current
                .filter(({ group, yScale }) => {
                    const { isNorm, id, exposureGroup, values, isSpline } =
                        group.datum();

                    const normsFiltering =
                        !isNorm ||
                        normSettingsMode === 'always' ||
                        id.includes(focusedId);

                    return (
                        !isSpline &&
                        values.length &&
                        exposureGroupforTooltip === exposureGroup &&
                        !!yScale &&
                        normsFiltering
                    );
                })
                .map(({ group, yScale }) => {
                    const {
                        id,
                        type,
                        values,
                        isNorm,
                        segmentKey,
                        sourceMedia
                    } = group.datum();

                    const currentPct = currentFrame
                        ? values[currentFrame - 1]?.pct
                        : undefined;

                    return {
                        id,
                        type,
                        segmentKey,
                        isNorm,
                        sourceMedia,
                        pct: currentPct,
                        yScale
                    };
                })
                .sort((a, b) => a.yScale(a.pct) - b.yScale(b.pct));
        };

        return {
            inContextExposureGroup: getExposureGroupTooltipData('inContext'),
            focusedExposureGroup: getExposureGroupTooltipData('focused')
        };

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [
        currentFrame,
        yScaleLeft,
        yScaleRight,
        curveElementsRef.current,
        curveTypeYAxisGroups,
        exposureGroupStyles,
        focusedId,
        customLeftAxisRange,
        customRightAxisRange,
        normSettingsMode
    ]);

    return (
        <>
            <Box
                onMouseMove={handleMouseMove}
                onMouseOver={handleMouseOver}
                onMouseOut={handleMouseOut}
                onClick={handleClick}
                position='relative'
            >
                <svg
                    ref={svgRef}
                    width='100%'
                    overflow='visible'
                    height={svgHeight}
                    viewBox={`0 0 ${width} ${svgHeight}`}
                />
                {tooltipAnchorEl && (
                    <CreativeViewerChartTooltip
                        second={currentFrame}
                        secondData={tooltipData}
                        showTooltips={isSeekbarVisible}
                        tooltipAnchorEl={tooltipAnchorEl}
                    />
                )}
            </Box>
        </>
    );
};
