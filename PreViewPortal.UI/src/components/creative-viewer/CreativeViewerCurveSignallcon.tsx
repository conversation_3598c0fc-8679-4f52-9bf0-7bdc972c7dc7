import { SvgIcon } from '@mui/material';
import NormCurveSvg from '../../assets/icons/norm-curve-icon.svg?react';
import CurveSvg from '../../assets/icons/curve-icon.svg?react';
import SecondCurveSvg from '../../assets/icons/second-curve-icon.svg?react';
import SecondNormCurveSvg from '../../assets/icons/second-norm-curve-icon.svg?react';

interface Props {
    color: string | null;
    isDoubleExposureGroupStyle: boolean;
    isNorm: boolean;
    displayFullSegmentAsArea: boolean;
}

export const CreativeViewerCurveSignalIcon = ({
    color,
    isDoubleExposureGroupStyle,
    isNorm,
    displayFullSegmentAsArea
}: Props) => {
    color = displayFullSegmentAsArea ? '#D4D6D7' : color;

    const icon = isDoubleExposureGroupStyle
        ? isNorm
            ? SecondNormCurveSvg
            : SecondCurveSvg
        : isNorm
          ? NormCurveSvg
          : CurveSvg;

    return (
        <SvgIcon component={icon} inheritViewBox sx={{ color, fontSize: 14 }} />
    );
};
