import { Drawer, IconButton, Stack } from '@mui/material';
import { CreativeSelectionModel } from '../../interfaces/creative-selection-model';
import CloseIcon from '@mui/icons-material/Close';
import { CreativeViewer } from './CreativeViewer';
import { useWindowSize } from 'react-use';
import { creativeViewerWidth } from './CreativeViewerRow';
import { ExportStatusContext } from '@/contexts/export-status-context';
import { STATUS_BAR_HEIGHT } from '@/constant/status-bar';
import { useContext } from 'react';

interface Props {
    shareKeyParam?: string;
    selectedCreatives: CreativeSelectionModel[];
    onClose: () => void;
    open: boolean;
}

export const CreativeViewerDrawer = ({
    shareKeyParam,
    selectedCreatives,
    onClose,
    open
}: Props) => {
    const { exportResult } = useContext(ExportStatusContext);
    const { width: windowWidth } = useWindowSize();

    const fullHeaderHeight = !shareKeyParam
        ? exportResult
            ? 80
            : 40
        : exportResult
          ? 40
          : 0;

    const zoomScale =
        windowWidth < creativeViewerWidth
            ? windowWidth / creativeViewerWidth
            : 1;

    const headerHeight = fullHeaderHeight * zoomScale;

    return (
        <>
            {open && !shareKeyParam && (
                <IconButton onClick={onClose}>
                    <CloseIcon
                        sx={{
                            position: 'fixed',
                            zoom: zoomScale,
                            mt: exportResult ? `${STATUS_BAR_HEIGHT}px` : 0,
                            top: 8,
                            right: 8,
                            zIndex: 2000,
                            color: 'white',
                            fontSize: 24
                        }}
                    />
                </IconButton>
            )}
            <Drawer
                keepMounted={false}
                anchor='bottom'
                open={open}
                onClose={onClose}
                sx={{
                    '& .MuiDrawer-paper': {
                        height: `calc(100vh - ${headerHeight}px)`
                    },
                    '& .MuiBackdrop-root': {
                        background: 'black',
                        opacity: '0.85 !important'
                    },
                    zIndex: 999
                }}
            >
                <Stack
                    sx={{
                        height: 'inherit',
                        zoom: zoomScale
                    }}
                >
                    <CreativeViewer
                        shareKeyParam={shareKeyParam || ''}
                        selectedCreativesParam={selectedCreatives}
                        zoomScale={zoomScale}
                    />
                </Stack>
            </Drawer>
        </>
    );
};
