import { formatDuration } from '@/helpers/format-duration';
import { Box, Divider, Typography } from '@mui/material';
import { CreativeViewerChartTaskBox } from './CreativeViewerChartTaskBox';
import { ChartTooltipModel } from '@/interfaces/chart-tooltip-model';
import { useMeasure } from 'react-use';
import { chartMargin, curveWidth } from './CreativeViewerChart';
import { useCreativeViewer } from '@/hooks/use-creative-viewer';
import { CreativeViewerNormDetailsBox } from './CreativeViewerNormDetailsBox';
import { useChartConfigurator } from '@/hooks/use-chart-configurator';
import { CustomAnchorElement } from '@/interfaces/custom-anchor-element';

interface Props {
    second: number | undefined;
    secondData: ChartTooltipModel;
    showTooltips: boolean;
    tooltipAnchorEl: CustomAnchorElement;
}

const offSet = { x: 3, y: 20 };

export const CreativeViewerChartTooltip = ({
    second,
    secondData,
    showTooltips,
    tooltipAnchorEl
}: Props) => {
    const { inContextExposureGroup, focusedExposureGroup } = secondData;

    const [boxRef, { width: tooltipWidth }] = useMeasure<HTMLDivElement>();

    const { focusedId } = useCreativeViewer();
    const { showNormDetails } = useChartConfigurator();

    if (!showTooltips) return null;

    const chartWidth = curveWidth - chartMargin.left - chartMargin.right;

    const flip =
        tooltipAnchorEl.x + tooltipWidth >= chartWidth ? tooltipWidth : 0;

    const offSetY = offSet.y * (flip ? -1 : 1);

    const isNormDetailsBoxEnabled = showNormDetails && focusedId;

    const normDetailsBoxWidth = Math.max(275, tooltipWidth);

    const focusedNormId = `${focusedId}-Norm`;

    return (
        <Box
            position='absolute'
            top={tooltipAnchorEl.y + offSet.x}
            left={40 + tooltipAnchorEl.x + offSetY - flip}
            zIndex={1000}
            onClick={e => e.stopPropagation()}
            sx={{ pointerEvents: 'none' }}
        >
            <Box
                ref={boxRef}
                border={`1px solid #D4D6D7`}
                borderRadius={'5px'}
                bgcolor='white'
                sx={{ opacity: 0.95 }}
            >
                <Typography
                    variant='caption'
                    fontWeight='bold'
                    p='2px 10px'
                    color='primary'
                >
                    {formatDuration(second)}
                </Typography>
                <Divider />
                {!!inContextExposureGroup.length && (
                    <CreativeViewerChartTaskBox
                        exposureGroup={'inContext'}
                        secondData={inContextExposureGroup}
                    />
                )}
                <Divider />
                {!!focusedExposureGroup.length && (
                    <CreativeViewerChartTaskBox
                        exposureGroup={'focused'}
                        secondData={focusedExposureGroup}
                    />
                )}
            </Box>
            {isNormDetailsBoxEnabled && (
                <CreativeViewerNormDetailsBox
                    normCurveId={focusedNormId}
                    sx={{ width: normDetailsBoxWidth, mt: 0.5 }}
                    showDetailsTooltip={false}
                />
            )}
        </Box>
    );
};
