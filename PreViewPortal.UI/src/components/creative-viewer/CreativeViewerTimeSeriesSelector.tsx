import { curveTypeLabelMap } from '@/constant/curve-type-label-map';
import { curveTypesTree, TreeNode } from '@/constant/curve-types-tree';
import { useCreativeViewer } from '@/hooks/use-creative-viewer';
import { useProduct } from '@/hooks/use-product';
import { CreativeViewerCurveType } from '@/interfaces/creative-viewer-curve-type';
import { ProductOptions } from '@/interfaces/product-options';
import {
    Stack,
    Typography,
    FormGroup,
    RadioGroup,
    Box,
    Tooltip,
    IconButton,
    Button,
    Divider
} from '@mui/material';
import {
    ExposureGroupType,
    SelectedCurveType
} from '@/contexts/creative-viewer-context';
import { curveTypeColorMap } from '@/helpers/get-curve-color-by-type';
import { CustomSelectionComponent } from '../CustomSelectionComponent';
import { ExposureLabel } from '@/interfaces/exposure-label';
import { InfoIcon } from '@/icons/InfoIcon';
import { useKnowledgeCenter } from '@/hooks/use-knowledge-center';
import { CurveTypeTooltipIcon } from '@/icons/CurveTypeTooltipIcon';

interface CurveTypeLabelProps {
    label: string | undefined;
    type: CreativeViewerCurveType;
}

const CurveTypeLabel = ({ label, type }: CurveTypeLabelProps) => {
    const { zoomScale } = useCreativeViewer();

    const { setIsKnowledgeCenterOpen, setCurrentAnchor } = useKnowledgeCenter();

    const withTooltip = [
        CreativeViewerCurveType.Playback,
        CreativeViewerCurveType.AllReactions,
        CreativeViewerCurveType.Attention,
        CreativeViewerCurveType.Negativity
    ].includes(type);

    const handleOpenScoreDescription = (type: CreativeViewerCurveType) => {
        let anchor = '';

        if (type === CreativeViewerCurveType.Playback)
            anchor = 'playback-seconds';

        if (type === CreativeViewerCurveType.AllReactions) anchor = 'reactions';

        if (type === CreativeViewerCurveType.Negativity) anchor = 'negativity';

        if (type === CreativeViewerCurveType.Attention) anchor = 'attention';

        if (!anchor) return;

        setCurrentAnchor(anchor);
        setIsKnowledgeCenterOpen(true);
    };

    return (
        <Tooltip
            slotProps={{
                tooltip: {
                    sx: {
                        backgroundColor: 'unset'
                    }
                }
            }}
            placement='right'
            disableHoverListener={!withTooltip}
            title={
                <Stack
                    bgcolor='#273235'
                    borderRadius={'5px'}
                    p={2}
                    gap={1}
                    sx={{
                        zoom: zoomScale
                    }}
                >
                    <Typography variant='body2' pb={1}>
                        {label}
                    </Typography>

                    {(() => {
                        switch (type) {
                            case CreativeViewerCurveType.Playback:
                                return (
                                    <Typography variant='body2'>
                                        {'Attention and Distraction metrics'}
                                        <br />
                                        {'shall add up to Playback.'}
                                    </Typography>
                                );
                            case CreativeViewerCurveType.AllReactions:
                                return (
                                    <>
                                        <Typography variant='body2'>
                                            {'Consists of multiple signals:'}
                                        </Typography>
                                        <Typography component='ul' pl={3}>
                                            <Typography
                                                variant='body2'
                                                component='li'
                                            >
                                                {'Happiness'}
                                            </Typography>
                                            <Typography
                                                variant='body2'
                                                component='li'
                                            >
                                                {'Surprise'}
                                            </Typography>
                                            <Typography
                                                variant='body2'
                                                component='li'
                                            >
                                                {'Confusion'}
                                            </Typography>
                                            <Typography
                                                variant='body2'
                                                component='li'
                                            >
                                                {'Contempt'}
                                            </Typography>
                                            <Typography
                                                variant='body2'
                                                component='li'
                                            >
                                                {'Disgust'}
                                            </Typography>
                                        </Typography>
                                    </>
                                );
                            case CreativeViewerCurveType.Negativity:
                                return (
                                    <>
                                        <Typography variant='body2'>
                                            {'Consists of multiple signals:'}
                                        </Typography>
                                        <Typography component='ul' pl={3}>
                                            <Typography
                                                variant='body2'
                                                component='li'
                                            >
                                                {'Confusion'}
                                            </Typography>
                                            <Typography
                                                variant='body2'
                                                component='li'
                                            >
                                                {'Contempt'}
                                            </Typography>
                                            <Typography
                                                variant='body2'
                                                component='li'
                                            >
                                                {'Disgust'}
                                            </Typography>
                                        </Typography>
                                    </>
                                );

                            case CreativeViewerCurveType.Attention:
                                return (
                                    <Typography variant='body2'>
                                        {
                                            'Neutral Attention and Reactions  metrics shall add up to Attention.'
                                        }
                                    </Typography>
                                );
                        }
                    })()}

                    <Button
                        size='small'
                        sx={{ alignSelf: 'end' }}
                        onClick={() => handleOpenScoreDescription(type)}
                    >
                        {'Definition'}
                    </Button>
                </Stack>
            }
        >
            <Stack direction='row' gap={0.5} alignItems='center'>
                <Typography variant='body2' noWrap>
                    {label}
                </Typography>
                {withTooltip && <CurveTypeTooltipIcon />}
            </Stack>
        </Tooltip>
    );
};

export const CreativeViewerTimeSeriesSelector = () => {
    const {
        selectedCurveTypes,
        multipleMediaSelected,
        hasNoCurves,
        setSelectedCurveTypes
    } = useCreativeViewer();

    const { setIsKnowledgeCenterOpen, setCurrentAnchor } = useKnowledgeCenter();

    const { selectedProduct } = useProduct();

    const isInContextProduct = selectedProduct === ProductOptions.InContext;

    const handleCurveTypeChange =
        (type: CreativeViewerCurveType, exposureGroup: ExposureGroupType) =>
        (_: React.ChangeEvent<HTMLInputElement>) => {
            const isDeselection = !!selectedCurveTypes.find(
                sc => sc.type === type && sc.exposureGroup === exposureGroup
            );

            if (isDeselection && selectedCurveTypes.length === 1) return;

            let newSelectedCurveTypes: SelectedCurveType[];
            if (isDeselection) {
                newSelectedCurveTypes = selectedCurveTypes.filter(
                    t => !(t.type === type && t.exposureGroup === exposureGroup)
                );
            } else {
                const newOption = { type, exposureGroup };

                newSelectedCurveTypes = multipleMediaSelected
                    ? [newOption]
                    : [...selectedCurveTypes, newOption];
            }

            setSelectedCurveTypes(newSelectedCurveTypes);
        };

    const SelectionGroupComponent = multipleMediaSelected
        ? RadioGroup
        : FormGroup;

    const handleOpenMetrics = () => {
        setCurrentAnchor('metrics');
        setIsKnowledgeCenterOpen(true);
    };

    return (
        <>
            <Stack direction='row' gap={0.5} mb={'10px'}>
                <Typography variant='h6'>{'Time Series'}</Typography>
                <IconButton
                    color='primary'
                    size='small'
                    sx={{
                        '&:hover': {
                            color: '#27EABF'
                        }
                    }}
                    onClick={handleOpenMetrics}
                >
                    <InfoIcon fontSize='inherit' />
                </IconButton>
            </Stack>
            <Stack border={'1px solid white'} py={2} px={1.2}>
                <Stack direction='row' justifyContent='end' gap={1} pb={'6px'}>
                    {isInContextProduct && (
                        <Tooltip
                            slotProps={{
                                tooltip: {
                                    sx: {
                                        bgcolor: '#273235'
                                    }
                                }
                            }}
                            title={
                                <Box p={1}>
                                    <Typography variant='caption'>
                                        <>
                                            <b>{ExposureLabel.InContext}</b>
                                            {
                                                ' involves presenting the creative to the audience in an organic or real-world setting, mimicking how users would naturally come across the content in their everyday online experiences across various media platforms.'
                                            }
                                        </>
                                    </Typography>
                                </Box>
                            }
                        >
                            <Typography
                                variant='body2'
                                textAlign='center'
                                width={70}
                            >
                                {ExposureLabel.InContext}
                            </Typography>
                        </Tooltip>
                    )}

                    <Tooltip
                        slotProps={{
                            tooltip: {
                                sx: {
                                    bgcolor: '#273235'
                                }
                            }
                        }}
                        title={
                            <Box p={1}>
                                <Typography variant='caption'>
                                    <>
                                        <b>{ExposureLabel.Focused}</b>
                                        {
                                            ' involves deliberately exposing the audience to the test creative in a controlled manner, outside of the natural context. The audience is explicitly informed that they are part of a test, and the creative is intentionally brought to their attention.'
                                        }
                                    </>
                                </Typography>
                            </Box>
                        }
                    >
                        <Typography
                            variant='body2'
                            textAlign='center'
                            width={70}
                        >
                            {ExposureLabel.Focused}
                        </Typography>
                    </Tooltip>
                </Stack>

                <SelectionGroupComponent>
                    {curveTypesTree.map(({ type, level, node }) => {
                        const color = curveTypeColorMap.get(type)?.hexa;
                        const label = curveTypeLabelMap.get(type);

                        const isICChecked = !!selectedCurveTypes.find(
                            sc =>
                                sc.type === type &&
                                sc.exposureGroup === 'inContext'
                        );

                        const isIFChecked = !!selectedCurveTypes.find(
                            sc =>
                                sc.type === type &&
                                sc.exposureGroup === 'focused'
                        );

                        const mtOfVerticalDivider =
                            +[TreeNode.First, TreeNode.Step].includes(node) &&
                            -0.8;

                        return (
                            <span
                                key={`selectorRow-${type}-${isICChecked}-${isIFChecked}`}
                            >
                                {type ===
                                    CreativeViewerCurveType.Negativity && (
                                    <Stack
                                        color='#FFFFFF80'
                                        direction='row'
                                        alignItems='center'
                                        gap={1}
                                        py={'6px'}
                                    >
                                        <Typography
                                            color='inherit'
                                            fontSize={12}
                                        >
                                            {'Other Aggregated Signals'}
                                        </Typography>
                                        <Stack
                                            flex={1}
                                            sx={{
                                                borderTop: '1.4px dashed'
                                            }}
                                        />
                                    </Stack>
                                )}

                                <Stack
                                    direction='row'
                                    alignItems='center'
                                    ml={level * 1.3}
                                >
                                    {!!level && (
                                        <>
                                            <Divider
                                                orientation='vertical'
                                                sx={{
                                                    alignSelf: 'start',
                                                    mt: mtOfVerticalDivider,
                                                    height: node,
                                                    border: '1px solid #FFFFFF80'
                                                }}
                                            />

                                            <Divider
                                                sx={{
                                                    width: 6,
                                                    mr: 0.2,
                                                    border: '1px solid #FFFFFF80'
                                                }}
                                            />
                                        </>
                                    )}

                                    <CurveTypeLabel label={label} type={type} />

                                    <Stack
                                        direction='row'
                                        justifyContent={'center'}
                                        mr={2}
                                        ml='auto'
                                        gap={5}
                                    >
                                        {isInContextProduct && (
                                            <CustomSelectionComponent
                                                checked={isICChecked}
                                                filled={color!}
                                                disabled={hasNoCurves}
                                                singleSelection={
                                                    multipleMediaSelected
                                                }
                                                onChange={handleCurveTypeChange(
                                                    type,
                                                    'inContext'
                                                )}
                                            />
                                        )}

                                        <CustomSelectionComponent
                                            checked={isIFChecked}
                                            filled={color!}
                                            disabled={hasNoCurves}
                                            singleSelection={
                                                multipleMediaSelected
                                            }
                                            onChange={handleCurveTypeChange(
                                                type,
                                                'focused'
                                            )}
                                        />
                                    </Stack>
                                </Stack>
                            </span>
                        );
                    })}
                </SelectionGroupComponent>
            </Stack>
        </>
    );
};
