import {
    Di<PERSON>r,
    FormControlLabel,
    IconButton,
    Popover,
    Radio,
    RadioGroup,
    Stack,
    SvgIcon,
    Tooltip,
    Typography
} from '@mui/material';
import EditNormSvg from '@/assets/icons/edit-norm-icon.svg?react';
import CloseIcon from '@mui/icons-material/Close';
import { CSSProperties, useState } from 'react';
import { useChartConfigurator } from '@/hooks/use-chart-configurator';

export type NormSettingsMode = 'never' | 'always' | 'hovering';

export const CreativeViewerNormSettingsButton = () => {
    const [anchorEl, setAnchorEl] = useState(null);

    const {
        normSettingsMode,
        setNormSettingsMode,
        showNormDetails,
        setShowNormDetails
    } = useChartConfigurator();

    const handleClose = () => setAnchorEl(null);

    const handleNormSettingsModeChange = (e: any) => {
        const newMode = e.target.value;

        setNormSettingsMode(newMode);

        if (newMode === 'never') setShowNormDetails(false);
    };

    const handleNormDetailsChange = (e: any) => {
        const isEnabled = e.target.value === 'true';

        setShowNormDetails(isEnabled);
    };

    const disabledStyleForNormDetails: CSSProperties =
        normSettingsMode === 'never'
            ? { opacity: 0.2, pointerEvents: 'none' }
            : {};

    return (
        <>
            <Tooltip
                title='Edit Norm Settings'
                slotProps={{
                    popper: {
                        disablePortal: true
                    }
                }}
            >
                <span>
                    <IconButton
                        onClick={(e: any) => setAnchorEl(e.currentTarget)}
                        sx={{
                            '&:hover': {
                                color: '#2DC0A2'
                            },
                            color: '#273235'
                        }}
                    >
                        <SvgIcon
                            component={EditNormSvg}
                            inheritViewBox
                            color='inherit'
                        />
                    </IconButton>
                </span>
            </Tooltip>
            <Popover
                open={!!anchorEl}
                onClose={handleClose}
                anchorEl={anchorEl}
                sx={{ zIndex: 2000 }}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'left'
                }}
                onClick={e => e.stopPropagation()}
            >
                <Stack p={2} width={375} gap={1}>
                    <Stack
                        alignItems='center'
                        direction='row'
                        justifyContent='space-between'
                    >
                        <Typography variant='h6'>{'Norm Settings'}</Typography>
                        <IconButton
                            color='inherit'
                            onClick={handleClose}
                            sx={{ p: 0 }}
                        >
                            <CloseIcon />
                        </IconButton>
                    </Stack>
                    <Divider />
                    <Typography variant='body2' fontWeight='bold'>
                        {'Show Norms'}
                    </Typography>

                    <RadioGroup
                        value={normSettingsMode}
                        onChange={handleNormSettingsModeChange}
                        sx={{ pl: 1 }}
                    >
                        <FormControlLabel
                            value={'never'}
                            control={<Radio size='small' sx={{ py: 0 }} />}
                            label={
                                <Typography variant='body2'>
                                    {'Never'}
                                </Typography>
                            }
                        />
                        <FormControlLabel
                            value={'hovering'}
                            control={<Radio size='small' sx={{ py: 1 }} />}
                            label={
                                <Typography variant='body2'>
                                    {'When hovering on curves'}
                                </Typography>
                            }
                        />
                        <FormControlLabel
                            value={'always'}
                            control={<Radio size='small' sx={{ py: 0 }} />}
                            label={
                                <Typography variant='body2'>
                                    {'Always'}
                                </Typography>
                            }
                        />
                    </RadioGroup>

                    <Typography variant='body2' fontWeight='bold'>
                        {'Show Norm Details'}
                    </Typography>

                    <span style={disabledStyleForNormDetails}>
                        <RadioGroup
                            value={showNormDetails}
                            onChange={handleNormDetailsChange}
                            sx={{ pl: 1 }}
                        >
                            <FormControlLabel
                                value={false}
                                control={<Radio size='small' sx={{ py: 0 }} />}
                                label={
                                    <Typography variant='body2'>
                                        {'Never'}
                                    </Typography>
                                }
                            />
                            <FormControlLabel
                                value={true}
                                control={<Radio size='small' sx={{ py: 1 }} />}
                                label={
                                    <Typography variant='body2'>
                                        {
                                            'When hovering on curves, legends and scores'
                                        }
                                    </Typography>
                                }
                            />
                        </RadioGroup>
                    </span>
                </Stack>
            </Popover>
        </>
    );
};
