import { Box, Collapse, Stack, Typography } from '@mui/material';
import { useCreativeViewer } from '@/hooks/use-creative-viewer';
import { useChartConfigurator } from '@/hooks/use-chart-configurator';
import { useMemo } from 'react';
import { ExposureGroupType } from '@/contexts/creative-viewer-context';
import { useMeasure } from 'react-use';
import { chartMargin, curveWidth } from './CreativeViewerChart';
import { CreativeViewerChartTaskBox } from './CreativeViewerChartTaskBox';
import { CustomAnchorElement } from '@/interfaces/custom-anchor-element';

export type NormCurveCustomAnchorElement =
    | (CustomAnchorElement & { id: string })
    | null;

export const CreativeViewerChartLegendBar = () => {
    const [unWrappedFirstTaskBoxRef, { width: unWrappedFirstTaskBoxWidth }] =
        useMeasure<HTMLDivElement>();
    const [unWrappedSecondTaskBoxRef, { width: unWrappedSecondTaskBoxWidth }] =
        useMeasure<HTMLDivElement>();
    const [wrappedFirstTaskBoxRef, { width: wrappedFirstTaskBoxWidth }] =
        useMeasure<HTMLDivElement>();
    const [wrappedSecondTaskBoxRef, { width: wrappedSecondTaskBoxWidth }] =
        useMeasure<HTMLDivElement>();

    const { selectedCurves, multipleMediaSelected, zoomScale } =
        useCreativeViewer();

    const { normSettingsMode } = useChartConfigurator();

    const { inContextExposureGroup, focusedExposureGroup } = useMemo(() => {
        const getExposureGroupData = (
            searchedExposureGroup: ExposureGroupType
        ) => {
            return selectedCurves
                .filter(
                    ({ values, exposureGroup, isNorm }) =>
                        (normSettingsMode === 'always' ||
                            normSettingsMode === 'hovering' ||
                            !isNorm) &&
                        values.length &&
                        searchedExposureGroup === exposureGroup
                )
                .map(
                    ({ id, type, isNorm, segmentKey, sourceMedia, views }) => ({
                        id,
                        type,
                        segmentKey,
                        isNorm,
                        sourceMedia,
                        views
                    })
                );
        };

        return {
            inContextExposureGroup: getExposureGroupData('inContext'),
            focusedExposureGroup: getExposureGroupData('focused')
        };
    }, [selectedCurves, normSettingsMode]);

    const { showLegendBar } = useChartConfigurator();

    const multipleExposureGroupSelected = useMemo(
        () => !!inContextExposureGroup.length && !!focusedExposureGroup.length,
        [inContextExposureGroup, focusedExposureGroup]
    );

    const chartWidth = curveWidth - chartMargin.left - chartMargin.right;
    const halfChartWidth = chartWidth / 2;

    const taskBoxesGap = 36;

    const maxTaskBoxWidth = multipleExposureGroupSelected
        ? halfChartWidth
        : chartWidth;

    const isWrapping =
        Math.round(unWrappedFirstTaskBoxWidth) +
            (multipleExposureGroupSelected ? taskBoxesGap : 0) >=
            maxTaskBoxWidth ||
        Math.round(unWrappedSecondTaskBoxWidth) >= maxTaskBoxWidth;

    const orderTaskBoxesToColumn =
        Math.round(wrappedFirstTaskBoxWidth) >= halfChartWidth ||
        Math.round(wrappedSecondTaskBoxWidth) >= halfChartWidth;

    const singleSelectedMedia =
        !multipleMediaSelected && selectedCurves[0]?.sourceMedia;

    return (
        <Collapse in={showLegendBar}>
            <Box
                ref={unWrappedFirstTaskBoxRef}
                visibility='hidden'
                position='absolute'
                maxWidth={'100%'}
                overflow='hidden'
                sx={{ zoom: zoomScale }}
            >
                <CreativeViewerChartTaskBox
                    exposureGroup={'inContext'}
                    secondData={inContextExposureGroup}
                    multipleExposureGroupSelected={
                        multipleExposureGroupSelected
                    }
                    isWrapping={false}
                    isLegendMode
                />
            </Box>

            <Box
                ref={unWrappedSecondTaskBoxRef}
                visibility='hidden'
                position='absolute'
                maxWidth={'100%'}
                overflow='hidden'
                sx={{ zoom: zoomScale }}
            >
                <CreativeViewerChartTaskBox
                    exposureGroup={'focused'}
                    secondData={focusedExposureGroup}
                    multipleExposureGroupSelected={
                        multipleExposureGroupSelected
                    }
                    isWrapping={false}
                    isLegendMode
                />
            </Box>

            {singleSelectedMedia && (
                <Stack
                    direction='row'
                    gap={1}
                    justifyContent='center'
                    mb={2}
                    fontSize={12}
                >
                    <b>{'Creative:'}</b>
                    <Typography fontSize='inherit'>
                        {singleSelectedMedia}
                    </Typography>
                </Stack>
            )}

            <Stack
                display={orderTaskBoxesToColumn ? 'grid' : 'flex'}
                direction={!orderTaskBoxesToColumn ? 'row' : undefined}
                justifyContent={'center'}
                gap={`${taskBoxesGap}px`}
            >
                {!!inContextExposureGroup.length && (
                    <Box
                        ref={wrappedFirstTaskBoxRef}
                        maxWidth={'100%'}
                        overflow='hidden'
                    >
                        <CreativeViewerChartTaskBox
                            exposureGroup={'inContext'}
                            secondData={inContextExposureGroup}
                            multipleExposureGroupSelected={
                                multipleExposureGroupSelected
                            }
                            isWrapping={isWrapping}
                            isLegendMode
                        />
                    </Box>
                )}
                {!!focusedExposureGroup.length && (
                    <Box
                        ref={wrappedSecondTaskBoxRef}
                        maxWidth={'100%'}
                        overflow='hidden'
                    >
                        <CreativeViewerChartTaskBox
                            exposureGroup={'focused'}
                            secondData={focusedExposureGroup}
                            multipleExposureGroupSelected={
                                multipleExposureGroupSelected
                            }
                            isWrapping={isWrapping}
                            isLegendMode
                        />
                    </Box>
                )}
            </Stack>
        </Collapse>
    );
};
