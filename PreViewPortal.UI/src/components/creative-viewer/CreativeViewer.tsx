import { CircularProgress, Stack, Typography } from '@mui/material';
import { useEffect, useMemo, useState } from 'react';
import {
    CreativeViewerContext,
    CurveToolTipLabelProps,
    ExposureGroupType,
    SelectedCurveColor,
    SelectedCurveType
} from '../../contexts/creative-viewer-context';
import { useHttp } from '../../hooks/use-http';
import { CreativeViewerModel } from '../../interfaces/creative-viewer-model';
import { CreativeSelectionModel } from '../../interfaces/creative-selection-model';
import { CreativeViewerRow } from './CreativeViewerRow';
import { useProduct } from '@/hooks/use-product';
import { ProductOptions } from '@/interfaces/product-options';
import CreativeViewerIcon from '@/assets/images/creative-viewer.png';
import { CreativeViewerCurveType } from '@/interfaces/creative-viewer-curve-type';
import { CreativeViewerChartModel } from '@/interfaces/creative-viewer-chart-model';
import { getCurveColorByType } from '@/helpers/get-curve-color-by-type';
import { useChartConfigurator } from '@/hooks/use-chart-configurator';
import { curveTypeLabelMap } from '@/constant/curve-type-label-map';
import { CreativeViewerChartContent } from './CreativeViewerChartContent';
import { CreativeViewerHeaderButtons } from './CreativeViewerHeaderButtons';
import {
    defaultSurveyScore,
    SurveyScoreOption
} from '@/constant/survey-scores-def';
import { getSegmentQuestionAndAnswerLabel } from '@/helpers/get-segment-question-and-answer-label';

interface Props {
    selectedCreativesParam: CreativeSelectionModel[];
    shareKeyParam: string | null;
    zoomScale: number;
}

interface CurveParamsProps {
    videoId?: string;
    type: CreativeViewerCurveType;
    segmentKey?: string;
    exposureGroup: ExposureGroupType;
    isNorm?: boolean;
}

export const generateIdByCurveParams = ({
    videoId,
    type,
    segmentKey,
    exposureGroup,
    isNorm
}: CurveParamsProps) =>
    `${videoId ? `${videoId}-` : ''}${type}${
        segmentKey ? `-${segmentKey}` : ''
    }-${exposureGroup}${isNorm ? `-Norm` : ''}`;

export const CreativeViewer = ({
    selectedCreativesParam,
    shareKeyParam = null,
    zoomScale
}: Props) => {
    const [model, setModel] = useState<CreativeViewerModel>({
        videoData: [],
        curveData: [],
        surveyScoreData: [],
        normData: [],
        media: [],
        shareKey: null
    });

    const { videoData, curveData, surveyScoreData, normData, segmentData } =
        model;

    const [selectedCreatives, setSelectedCreatives] = useState<
        CreativeSelectionModel[]
    >(selectedCreativesParam);

    const [hasNoCurves, setHasNoCurves] = useState(false);

    const [customLeftAxisRange, setCustomLeftAxisRange] = useState<number[]>();
    const [customRightAxisRange, setCustomRightAxisRange] =
        useState<number[]>();

    const [leftCurvesRange, setLeftCurvesRange] = useState<number[]>([]);
    const [rightCurvesRange, setRightCurvesRange] = useState<number[]>([]);

    const { selectedProduct, setSelectedProduct } = useProduct();

    const isInContextProduct = selectedProduct === ProductOptions.InContext;

    const [selectedSurveyScore, setSelectedSurveyScore] = useState<
        SurveyScoreOption | null | undefined
    >(isInContextProduct ? defaultSurveyScore : undefined);

    const getSegmentKeyTooltipLabel = (segmentKey: string | undefined) => {
        const labelData = segmentData?.segmentKeyLabels.find(
            sl => sl.segmentKey === segmentKey
        );

        const labelToDisplay = getSegmentQuestionAndAnswerLabel(labelData);

        return labelToDisplay;
    };

    const [selectedCurveTypes, setSelectedCurveTypes] = useState<
        SelectedCurveType[]
    >([]);

    const [selectedCurveSegments, setSelectedCurveSegments] = useState(['all']);

    const [focusedId, setFocusedId] = useState<string>();

    const [isLoading, setIsLoading] = useState(false);
    const { http, handleError } = useHttp();
    const [shareKey, setShareKey] = useState<string | null>(shareKeyParam);

    const { normSettingsMode } = useChartConfigurator();

    useEffect(() => {
        if (!selectedCreativesParam.length && !shareKeyParam) return;

        (async () => {
            setIsLoading(true);
            try {
                const endpoint = `creativeviewer/${shareKeyParam ? 'creativeViewerByShareKeyHash' : isInContextProduct ? 'incontext' : 'forcedexposure'}`;

                let data: CreativeViewerModel;

                if (shareKeyParam) {
                    data = await http
                        .get(endpoint, {
                            headers: {
                                'X-Share-Key': shareKeyParam || ''
                            }
                        })
                        .json<CreativeViewerModel>();

                    setSelectedProduct(data.selectedProduct!);
                    setSelectedCreatives(data.media);
                } else {
                    data = await http
                        .post(endpoint, { json: selectedCreativesParam })
                        .json<CreativeViewerModel>();
                }

                setShareKey(data.shareKey);
                setModel(data);

                setHasNoCurves(data.curveData.every(d => !d.curves.length));
            } catch (e) {
                handleError(e);
            }
            setIsLoading(false);
        })();

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [selectedCreativesParam, shareKeyParam, http, handleError]);

    useEffect(() => {
        if (!selectedCurveTypes.length) {
            const firstTypeWithData = Object.values(
                CreativeViewerCurveType
            ).find(curveType =>
                curveData.find(
                    d => d.curves.find(c => c.type === curveType)?.values
                )
            );

            if (firstTypeWithData) {
                const defaultSelectedCurve = {
                    type: firstTypeWithData as CreativeViewerCurveType,
                    exposureGroup: (isInContextProduct
                        ? 'inContext'
                        : 'focused') as ExposureGroupType
                };

                setSelectedCurveTypes([defaultSelectedCurve]);
            }
        }
    }, [isInContextProduct, curveData, selectedCurveTypes]);

    const curveDataForAxises = useMemo<CreativeViewerChartModel[]>(() => {
        const curves: CreativeViewerChartModel[] = [];

        videoData.forEach(({ id: videoId, sourceMedia }) => {
            const curvesByVideo = curveData.find(
                cd => cd.id === videoId
            )?.curves;

            selectedCurveTypes.forEach(({ type, exposureGroup }) => {
                [...new Set([...selectedCurveSegments, 'all'])].forEach(
                    segmentKey => {
                        [false, true].forEach(isNorm => {
                            const searchedCurve = curvesByVideo?.find(
                                vc =>
                                    vc.type === type &&
                                    isNorm === vc.isNorm &&
                                    (!vc.segmentKey ||
                                        vc.segmentKey === segmentKey) &&
                                    vc.exposureGroup === exposureGroup
                            );

                            const curveToAdd: CreativeViewerChartModel = {
                                ...searchedCurve,
                                id: generateIdByCurveParams({
                                    videoId,
                                    type,
                                    segmentKey,
                                    exposureGroup,
                                    isNorm
                                }),
                                videoId,
                                type,
                                segmentKey,
                                exposureGroup,
                                isNorm,
                                sourceMedia,
                                values: searchedCurve?.values || []
                            };

                            curves.push(curveToAdd);
                        });
                    }
                );
            });
        });

        return curves;
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [
        videoData,
        curveData,
        selectedCurveTypes,
        selectedCurveSegments,
        normSettingsMode
    ]);

    const selectedCurves = useMemo(() => {
        const fullSegmentSelected = selectedCurveSegments.includes('all');

        return curveDataForAxises.filter(
            c =>
                (normSettingsMode !== 'never' || !c.isNorm) &&
                (fullSegmentSelected || c.segmentKey !== 'all')
        );
    }, [curveDataForAxises, selectedCurveSegments, normSettingsMode]);

    const getDisplayFullSegmentAsArea = (
        segmentKey: string | undefined,
        isNorm: boolean
    ) => segmentKey === 'all' && !isNorm && selectedCurveSegments.length !== 1;

    const getCurveLabel = ({
        segmentKey,
        sourceMedia,
        isNorm,
        type,
        nameLimit
    }: CurveToolTipLabelProps) => {
        const displayFullSegmentAsArea = getDisplayFullSegmentAsArea(
            segmentKey,
            isNorm
        );

        if (displayFullSegmentAsArea) return 'Total Audience';

        const isNormToFullSegmentArea =
            segmentKey === 'all' &&
            isNorm &&
            selectedCurveSegments.length !== 1;

        const segmentKeyLabel = getSegmentKeyTooltipLabel(segmentKey);

        const mediaName =
            nameLimit && sourceMedia.length > nameLimit
                ? sourceMedia.substring(0, nameLimit) + '...'
                : sourceMedia;

        const label = `${isNorm ? 'Norm: ' : ''}${
            multipleMediaSelected ? mediaName : curveTypeLabelMap.get(type)!
        } ${segmentKeyLabel ? `- ${segmentKeyLabel}` : ''}${
            isNormToFullSegmentArea ? '(Total Audience)' : ''
        }`;

        return label;
    };

    const curveColors = useMemo(() => {
        const curvesByType = new Map<string, string[]>();

        selectedCurves.forEach(
            ({ type, id, segmentKey, isNorm, values, exposureGroup }) => {
                if (!values.length) return;

                if (isNorm) return;

                const displayFullSegmentAsArea = getDisplayFullSegmentAsArea(
                    segmentKey,
                    isNorm
                );

                if (displayFullSegmentAsArea) return;

                const curveTypeKey = generateIdByCurveParams({
                    type,
                    exposureGroup
                });

                const typeArr = curvesByType.get(curveTypeKey) || [];
                typeArr.push(id);

                curvesByType.set(curveTypeKey, typeArr);
            }
        );

        const newCurvesColor = new Map<string, SelectedCurveColor>();

        selectedCurves.forEach(
            ({ type, id, segmentKey, isNorm, exposureGroup, values }) => {
                if (!values.length) return;

                const curveTypeKey = generateIdByCurveParams({
                    type,
                    exposureGroup
                });

                const strippedId = isNorm ? id.replace('-Norm', '') : id;

                const curvesForType = curvesByType.get(curveTypeKey) || [];
                const nthCurveTpye = curvesForType.indexOf(strippedId) + 1;
                const nCurveTpye = curvesForType.length;

                const displayFullSegmentAsArea = getDisplayFullSegmentAsArea(
                    segmentKey,
                    false
                );

                const color = displayFullSegmentAsArea
                    ? '#D9D9D9'
                    : getCurveColorByType(type, nCurveTpye, nthCurveTpye);

                newCurvesColor.set(id, { color, type, exposureGroup, isNorm });
            }
        );

        return newCurvesColor;
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [selectedCurves]);

    const multipleMediaSelected = selectedCreatives.length > 1;

    console.log(selectedCurveTypes, selectedCurveSegments);

    return (
        <CreativeViewerContext.Provider
            value={{
                selectedCurveTypes,
                selectedCurveSegments,
                selectedCurves,
                curveDataForAxises,
                multipleMediaSelected,
                curveColors,
                videoData,
                surveyScoreData,
                normData,
                focusedId,
                customLeftAxisRange,
                leftCurvesRange,
                segmentData,
                customRightAxisRange,
                rightCurvesRange,
                selectedSurveyScore,
                setSelectedSurveyScore,
                setRightCurvesRange,
                setCustomRightAxisRange,
                setCustomLeftAxisRange,
                setLeftCurvesRange,
                setFocusedId,
                setSelectedCurveTypes,
                setSelectedCurveSegments,
                getDisplayFullSegmentAsArea,
                getSegmentKeyTooltipLabel,
                getCurveLabel,
                zoomScale,
                hasNoCurves
            }}
        >
            {isLoading ? (
                <Stack
                    width='100%'
                    minHeight='100%'
                    direction='row'
                    alignItems='center'
                    justifyContent='center'
                >
                    <CircularProgress />
                </Stack>
            ) : (
                <Stack
                    width='100%'
                    alignItems='center'
                    bgcolor='#D5DDE4'
                    flex={1}
                >
                    <CreativeViewerRow
                        left={
                            <Stack
                                direction='row'
                                alignItems='center'
                                gap={3}
                                pt={1}
                                pl={2}
                            >
                                <img
                                    src={CreativeViewerIcon}
                                    alt=''
                                    style={{ width: 60 }}
                                />
                                <Typography variant='h4' color='white'>
                                    Creative Viewer
                                </Typography>
                            </Stack>
                        }
                        right={
                            <Stack
                                pt={2}
                                px={2}
                                direction='row'
                                alignItems='center'
                                justifyContent='end'
                            >
                                <CreativeViewerHeaderButtons
                                    isShare={!!shareKeyParam}
                                    shareKeyParam={shareKey}
                                    selectedCreatives={selectedCreatives}
                                    selectedCurveTypes={selectedCurveTypes}
                                />
                            </Stack>
                        }
                    />

                    <CreativeViewerChartContent />
                </Stack>
            )}
        </CreativeViewerContext.Provider>
    );
};
