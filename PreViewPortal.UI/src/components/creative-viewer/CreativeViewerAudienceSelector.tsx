import { useCreativeViewer } from '@/hooks/use-creative-viewer';
import { Add } from '@mui/icons-material';
import {
    Stack,
    Typography,
    Autocomplete,
    TextField,
    MenuItem,
    Box,
    Button,
    FormControlLabel,
    IconButton,
    ClickAwayListener
} from '@mui/material';
import { useEffect, useMemo, useState } from 'react';
import CloseIcon from '@mui/icons-material/Close';
import { CustomSelectionComponent } from '../CustomSelectionComponent';
import Popper from '@mui/material/Popper';
import { SegmentGroup } from '@/interfaces/segment-group';

export const CreativeViewerAudienceSelector = () => {
    const {
        selectedCurveTypes,
        selectedCurveSegments,
        selectedCurves,
        multipleMediaSelected,
        segmentData,
        setSelectedCurveSegments
    } = useCreativeViewer();

    const [addButtonAnchorEl, setAddButtonAnchorEl] = useState(null);

    const [
        { question: selectedSegmentGroup, options: segmentKeyOptions },
        setSelectedSegmentGroup
    ] = useState<SegmentGroup>({ question: '', options: [] });

    const [showTotalAudienceChecked, setShowTotalAudienceChecked] =
        useState(false);

    const segmentGroups = segmentData?.segmentGroups || [];

    const handleClickOnAdd = (event: any) => {
        setAddButtonAnchorEl(event?.currentTarget);
    };

    const handleSelectSegmentGroup = (value: SegmentGroup) => () => {
        setSelectedSegmentGroup(value);
        setAddButtonAnchorEl(null);
    };

    const handleSegmentsChange = (segmentKeys: string[] | string) => {
        if (!Array.isArray(segmentKeys)) segmentKeys = [segmentKeys];

        if (segmentKeys.length === 0)
            setSelectedSegmentGroup({ question: '', options: [] });

        setSelectedCurveSegments(segmentKeys);
    };

    const handleDeleteSegmentGroup = () => handleSegmentsChange([]);

    const handleShowTotalAudienceChecked = (checked: boolean) => {
        handleTotalAudienceFiltering(checked);
        setShowTotalAudienceChecked(checked);
    };

    const handleTotalAudienceFiltering = (checked: boolean) => {
        const newSelectedCurveSegments = checked
            ? [...selectedCurveSegments, 'all']
            : selectedCurveSegments.filter(sk => sk !== 'all');

        setSelectedCurveSegments(newSelectedCurveSegments);
    };

    const hasCurveDataForNotFullSegments = useMemo(
        () =>
            !!selectedCurves.find(
                c => c.segmentKey !== 'all' && c.values.length
            ),
        [selectedCurves]
    );

    const fullSegmentSelected = useMemo(
        () => selectedCurveSegments.includes('all'),
        [selectedCurveSegments]
    );

    useEffect(() => {
        if (selectedCurveSegments.length === 0)
            handleTotalAudienceFiltering(true);

        if (selectedCurveSegments.length === 1) return;

        if (!fullSegmentSelected) return;

        const moreFullSegments =
            multipleMediaSelected || selectedCurveTypes.length > 1;

        if (!hasCurveDataForNotFullSegments || moreFullSegments)
            handleTotalAudienceFiltering(false);

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [
        fullSegmentSelected,
        hasCurveDataForNotFullSegments,
        multipleMediaSelected,
        selectedCurveSegments,
        selectedCurveTypes
    ]);

    const onlyFullSegmentSelected =
        selectedCurveSegments.length === 1 && fullSegmentSelected;

    const showTotalAudience =
        !multipleMediaSelected &&
        selectedCurveTypes.length === 1 &&
        !onlyFullSegmentSelected &&
        hasCurveDataForNotFullSegments;

    useEffect(() => {
        if (!showTotalAudience) return;

        if (showTotalAudienceChecked && !fullSegmentSelected)
            handleTotalAudienceFiltering(true);

        if (!showTotalAudienceChecked && fullSegmentSelected)
            handleTotalAudienceFiltering(false);

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [showTotalAudience, showTotalAudienceChecked, fullSegmentSelected]);

    const getLabel = (key: string | undefined) =>
        segmentData?.segmentKeyLabels.find(s => s.segmentKey === key)?.answer ||
        '';

    return (
        <Stack>
            <Typography variant='h6' mb={'10px'}>
                {'Audience Segmentation'}
            </Typography>
            <Stack gap={0.5} direction='row'>
                {selectedSegmentGroup ? (
                    <>
                        <Autocomplete
                            sx={{
                                '& .MuiAutocomplete-popupIndicator': {
                                    color: 'white'
                                },
                                '& .MuiInputBase-root': {
                                    paddingRight: '6px !important'
                                }
                            }}
                            fullWidth
                            size='small'
                            renderTags={value => {
                                const firstLabel = getLabel(value[0]);

                                const selectedOptionsCount = value.length;

                                return `${firstLabel} ${
                                    selectedOptionsCount > 1
                                        ? `(+${selectedOptionsCount - 1})`
                                        : ''
                                }`;
                            }}
                            options={segmentKeyOptions}
                            multiple={!multipleMediaSelected}
                            disableCloseOnSelect={!multipleMediaSelected}
                            disableClearable
                            disablePortal
                            getOptionLabel={getLabel}
                            onChange={(_, value) => handleSegmentsChange(value)}
                            renderInput={props => (
                                <TextField
                                    {...props}
                                    label={selectedSegmentGroup}
                                />
                            )}
                            renderOption={(props, option, { selected }) => {
                                const lastOption =
                                    segmentKeyOptions.indexOf(option) ===
                                    segmentKeyOptions.length - 1;

                                return (
                                    <>
                                        <li
                                            {...props}
                                            style={{
                                                color: 'black'
                                            }}
                                        >
                                            <CustomSelectionComponent
                                                filled='black'
                                                size='small'
                                                singleSelection={
                                                    multipleMediaSelected
                                                }
                                                checked={selected}
                                            />
                                            <Typography
                                                color='black'
                                                fontSize={15}
                                            >
                                                {getLabel(option)}
                                            </Typography>
                                        </li>
                                        {lastOption && (
                                            <Stack>
                                                <IconButton
                                                    onClick={
                                                        handleDeleteSegmentGroup
                                                    }
                                                    sx={{
                                                        alignSelf: 'end',
                                                        paddingRight: 2
                                                    }}
                                                >
                                                    <CloseIcon
                                                        sx={{
                                                            color: '#273235'
                                                        }}
                                                    />
                                                </IconButton>
                                            </Stack>
                                        )}
                                    </>
                                );
                            }}
                        />

                        <IconButton
                            onClick={handleDeleteSegmentGroup}
                            sx={{ p: 0 }}
                        >
                            <CloseIcon color='primary' />
                        </IconButton>
                    </>
                ) : (
                    <Box position='sticky' zIndex={2}>
                        <Button
                            startIcon={<Add />}
                            onClick={handleClickOnAdd}
                            disabled={!segmentGroups.length}
                        >
                            {'Add'}
                        </Button>
                        <Popper
                            anchorEl={addButtonAnchorEl}
                            open={!!addButtonAnchorEl}
                            disablePortal
                        >
                            <ClickAwayListener
                                onClickAway={() => handleClickOnAdd(null)}
                            >
                                <Box sx={{ bgcolor: 'white' }}>
                                    {segmentGroups.map(segmentGroup => (
                                        <MenuItem
                                            key={`option-${segmentGroup.question}`}
                                            onClick={handleSelectSegmentGroup(
                                                segmentGroup
                                            )}
                                            sx={{ color: 'black' }}
                                        >
                                            {segmentGroup.question}
                                        </MenuItem>
                                    ))}
                                </Box>
                            </ClickAwayListener>
                        </Popper>
                    </Box>
                )}
            </Stack>

            <Stack minHeight={50} p='5px'>
                {showTotalAudience && (
                    <FormControlLabel
                        control={
                            <CustomSelectionComponent
                                filled='white'
                                singleSelection={false}
                                checked={showTotalAudienceChecked}
                                onChange={(_, checked) =>
                                    handleShowTotalAudienceChecked(checked)
                                }
                            />
                        }
                        label={
                            <Typography variant='body2'>
                                {'Show Total Audience'}
                            </Typography>
                        }
                    />
                )}
            </Stack>
        </Stack>
    );
};
