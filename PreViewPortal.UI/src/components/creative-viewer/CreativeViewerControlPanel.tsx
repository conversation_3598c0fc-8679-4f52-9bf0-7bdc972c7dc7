import { useProduct } from '@/hooks/use-product';
import { ProductOptions } from '@/interfaces/product-options';
import { Stack, Tooltip, Typography } from '@mui/material';
import { CreativeViewerAudienceSelector } from './CreativeViewerAudienceSelector';
import { CreativeViewerTimeSeriesSelector } from './CreativeViewerTimeSeriesSelector';
import { CreativeViewerSurveySelector } from './CreativeViewerSurveySelector';
import { InfoIcon } from '@/icons/InfoIcon';
import { getLabelForProductOptions } from '@/helpers/get-label-for-product-options';
import { useCreativeViewer } from '@/hooks/use-creative-viewer';

export const CreativeViewerControlPanel = () => {
    const { selectedProduct } = useProduct();

    const isInContextProduct = selectedProduct === ProductOptions.InContext;

    const { zoomScale } = useCreativeViewer();

    const productLabel = getLabelForProductOptions(selectedProduct);

    return (
        <Stack px={3} minWidth={330}>
            <Stack mb={'30px'} gap={'10px'}>
                <Typography variant='h6'>{'Product Type'}</Typography>
                <Stack direction='row' alignItems='center' gap={1} mb={'10px'}>
                    <Typography variant='body2'>{productLabel}</Typography>

                    <Tooltip
                        slotProps={{
                            tooltip: {
                                sx: {
                                    backgroundColor: 'unset'
                                }
                            }
                        }}
                        placement='right'
                        title={
                            <Stack
                                bgcolor='#273235'
                                borderRadius={'5px'}
                                p={2}
                                gap={1}
                                sx={{
                                    zoom: zoomScale
                                }}
                                width={350}
                            >
                                <Typography variant='h6' fontSize={14}>
                                    {productLabel}
                                </Typography>
                                <Typography variant='body2'>
                                    {isInContextProduct
                                        ? 'Viewers engage in multiple ad exposure tasks accompanied by camera measurement complemented by survey questions.'
                                        : 'Viewers complete an ad exposure task in a neutral video player without options for interaction, no survey questions are asked.'}
                                </Typography>
                            </Stack>
                        }
                    >
                        <InfoIcon
                            color='primary'
                            fontSize='inherit'
                            sx={{
                                '&:hover': {
                                    color: '#27EABF'
                                }
                            }}
                        />
                    </Tooltip>
                </Stack>
            </Stack>

            {isInContextProduct && <CreativeViewerSurveySelector />}

            <CreativeViewerAudienceSelector />

            <CreativeViewerTimeSeriesSelector />
        </Stack>
    );
};
