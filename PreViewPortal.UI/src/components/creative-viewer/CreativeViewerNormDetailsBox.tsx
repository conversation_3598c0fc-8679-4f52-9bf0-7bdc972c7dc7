import { getNormInfoText } from '@/helpers/get-norm-info-text';
import { useCreativeViewer } from '@/hooks/use-creative-viewer';
import { Stack, Switch, SxProps, Typography } from '@mui/material';
import { useContext, useMemo } from 'react';
import { NormTooltipContext } from '@/contexts/norm-tooltip-context';
import TooltipDetailTable from '@/components/TooltipDetailTable';
import {
    getNormCategory,
    getNormDataTable,
    hasAnyIndustryInList,
    hasBrandInList,
    hasCountryInList
} from '@/constant/norm-tooltip-helper';
import { CreativeViewerCurveType } from '@/interfaces/creative-viewer-curve-type';
import { useSupervisedAccount } from '@/hooks/use-supervised-account';
import { NormCategory } from '@/interfaces/norm-category';
import { useUser } from '@/hooks/use-user';

interface Props {
    normCurveId: string;
    sx: SxProps;
    showDetailsTooltip: boolean;
}

export const CreativeViewerNormDetailsBox = ({
    normCurveId,
    sx,
    showDetailsTooltip
}: Props) => {
    const {
        selectedCurves,
        getSegmentKeyTooltipLabel,
        getCurveLabel,
        normData
    } = useCreativeViewer();
    const {
        showDetails,
        setShowDetails,
        showFullNormData,
        setShowFullNormData
    } = useContext(NormTooltipContext);
    const { supervisedAccount } = useSupervisedAccount();
    const { isAdmin } = useUser();

    const normCurve = useMemo(
        () => selectedCurves.find(c => c.id === normCurveId),
        [normCurveId, selectedCurves]
    );

    const norm = useMemo(() => {
        if (!normCurve || !normCurve.values.length) return undefined;

        return normData?.find(
            n =>
                n.videoId === normCurve.videoId &&
                n.segmentKey === normCurve.segmentKey
        );
    }, [normCurve, normData]);

    if (!normCurve || !normCurve.values.length) return null;

    if (!norm) return null;

    const { exposureGroup, sourceMedia, type, isNorm } = normCurve;

    const {
        normFallback,
        normFallbackIF,
        normFallbackIC,
        normFallbackSurveyIC,
        normFallbackSurveyIF,
        normSegmentKey,
        segmentKey
    } = norm;

    const curveLabel = getCurveLabel({
        sourceMedia,
        type,
        segmentKey,
        isNorm
    });

    const segmentKeyLabel = getSegmentKeyTooltipLabel(segmentKey);

    const typeString = CreativeViewerCurveType[type] as string;
    const normCategory = getNormCategory(typeString);

    const normFallbackForExposure =
        normFallback ||
        (normCategory === NormCategory.SurveyNorm
            ? exposureGroup === 'inContext'
                ? normFallbackSurveyIC
                : normFallbackSurveyIF
            : exposureGroup === 'inContext'
              ? normFallbackIC
              : normFallbackIF);

    const normInfoText = getNormInfoText({
        exposureGroup,
        normCategory,
        norm,
        normFallback: normFallbackForExposure,
        segmentKey,
        normSegmentKey,
        segmentKeyLabel
    });

    const audienceLabel =
        segmentKey !== 'all' ? segmentKeyLabel : 'Total Audience';

    const tableData = getNormDataTable(
        showFullNormData,
        supervisedAccount?.hasCustomNorm,
        normCategory,
        normCurve.exposureGroup,
        norm,
        normFallbackForExposure,
        audienceLabel,
        hasCountryInList(normData, normCategory, normCurve.exposureGroup),
        hasAnyIndustryInList(normData, normCategory, normCurve.exposureGroup),
        hasBrandInList(normData, normCategory, normCurve.exposureGroup)
    );

    return (
        <Stack
            border={`1px solid #D4D6D7`}
            borderRadius={'5px'}
            bgcolor='white'
            gap={1}
            sx={{ p: 2, ...sx }}
        >
            <Typography
                fontSize={14}
                fontFamily='Roboto'
                color='#273235'
                mb={1}
            >
                {curveLabel}
            </Typography>

            <Typography
                variant='caption'
                fontFamily='Roboto'
                color='#505B5F'
                fontSize={14}
                display='block'
                lineHeight={1.4}
            >
                {normInfoText}
            </Typography>

            {normCurve.isNorm && showDetailsTooltip && (
                <Stack direction='row' alignItems='center' pt={1}>
                    <Typography
                        variant='body1'
                        fontSize={14}
                        fontFamily='Roboto'
                        color='#273235'
                    >
                        Details
                    </Typography>
                    <Switch
                        checked={showDetails}
                        onChange={event => setShowDetails(event.target.checked)}
                    />
                </Stack>
            )}

            {showDetailsTooltip && showDetails && normCurve.isNorm && (
                <TooltipDetailTable
                    showFullNormData={showFullNormData}
                    setShowFullNormData={setShowFullNormData}
                    isAdmin={isAdmin}
                    tableData={tableData}
                />
            )}
        </Stack>
    );
};
