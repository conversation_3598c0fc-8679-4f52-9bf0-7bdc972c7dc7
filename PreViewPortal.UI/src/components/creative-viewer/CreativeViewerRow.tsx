import { Box, BoxProps, Stack, StackProps, ThemeProvider } from '@mui/material';
import { Themes } from '../../theme';

export const creativeViewerWidth = 1420;
const leftSideWidth = 330;
const padding = 16;
const rightSideWidth = 1420 - 330 - padding;

export const CreativeViewerRow = ({
    left,
    leftSx,
    right,
    rightSx,
    onClickRight,
    ...props
}: {
    left?: React.ReactNode;
    leftSx?: BoxProps['sx'];
    right?: React.ReactNode;
    rightSx?: BoxProps['sx'];
    onClickRight?: (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => void;
} & Omit<StackProps, 'left' | 'right'>) => (
    <Stack
        {...props}
        gap={`${padding}px`}
        bgcolor='white'
        direction='row'
        width={creativeViewerWidth}
        justifyContent='center'
    >
        <Stack sx={leftSx} width={leftSideWidth} bgcolor='#30434E'>
            <ThemeProvider theme={Themes.dark}>{left}</ThemeProvider>
        </Stack>

        {right && (
            <Box
                sx={rightSx}
                width={rightSideWidth}
                pr={2}
                onClick={onClickRight}
            >
                {right}
            </Box>
        )}
    </Stack>
);
