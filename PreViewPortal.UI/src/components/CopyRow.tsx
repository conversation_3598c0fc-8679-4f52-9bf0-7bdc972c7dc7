import { CopyIcon } from '@/icons/CopyIcon';
import { Stack, Typography } from '@mui/material';
import { useSnackbar } from 'notistack';

interface Props {
    value: string | number;
    label: string;
    onCopy?: () => void;
}

export const CopyRow = ({ value, label, onCopy }: Props) => {
    const { enqueueSnackbar } = useSnackbar();

    const handleCopy = async (value: string | number) => {
        await navigator.clipboard.writeText(value.toString());

        enqueueSnackbar('Copied to clipboard.', { variant: 'success' });

        if (onCopy) {
            onCopy();
        }
    };

    return (
        <Stack direction='row' alignItems='center' gap={1}>
            <Typography color='#D5DDE4' variant='body2' pr={2} noWrap>
                {label}
            </Typography>
            <Typography color='inherit' variant='body2' ml='auto' noWrap>
                {value}
            </Typography>
            <CopyIcon
                fontSize='inherit'
                sx={{ cursor: 'pointer' }}
                onClick={() => handleCopy(value)}
            />
        </Stack>
    );
};
