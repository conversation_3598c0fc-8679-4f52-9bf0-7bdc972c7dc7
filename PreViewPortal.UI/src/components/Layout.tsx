import { Container, Stack } from '@mui/material';
import Header from './Header';
import ExportStatus from './ExportStatusBar';

interface Props {
    noPadding?: boolean;
    pt?: number;
}

const Layout = ({
    noPadding,
    pt,
    children
}: React.PropsWithChildren<Props>) => (
    <Stack direction='column' height='inherit'>
        <ExportStatus />
        <Header />
        <Container sx={{ py: noPadding ? 0 : 4, pt, flex: 1 }} maxWidth={'xl'}>
            {children}
        </Container>
    </Stack>
);

export default Layout;
