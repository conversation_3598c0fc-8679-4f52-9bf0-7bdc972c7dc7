import { Box, Typography } from '@mui/material';

interface Props {
    score: number;
    color: string;
    size?: number;
    fontSize?: number;
}

const ScoreInColoredBoxCell = ({
    score,
    color,
    size = 50,
    fontSize
}: Props) => (
    <Box
        color='#fff'
        bgcolor={color}
        fontWeight='700'
        display='flex'
        alignItems='center'
        justifyContent='center'
        width={size}
        minWidth={size}
        height={size}
        minHeight={size}
    >
        <Typography
            color='inherit'
            variant={size > 25 ? 'h6' : 'subtitle2'}
            fontSize={fontSize}
        >
            {score}
        </Typography>
    </Box>
);

export default ScoreInColoredBoxCell;
