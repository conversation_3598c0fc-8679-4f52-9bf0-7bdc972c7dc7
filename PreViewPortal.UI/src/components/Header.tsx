import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import HelpIcon from '@mui/icons-material/Help';
import { STATUS_BAR_HEIGHT } from '@/constant/status-bar';
import {
    AppBar,
    Box,
    Container,
    Divider,
    IconButton,
    Menu,
    MenuItem,
    <PERSON>b,
    <PERSON><PERSON>,
    ThemeP<PERSON>ider,
    Too<PERSON>bar,
    Typography
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { useContext, useState } from 'react';
import { Link, useLocation } from 'react-router';
import RealeyesLogo from '../components/RealeyesLogo';
import { Authentication } from '../helpers/authentication';
import { useKnowledgeCenter } from '../hooks/use-knowledge-center';
import { useSupervisedAccount } from '../hooks/use-supervised-account';
import { useUser } from '../hooks/use-user';
import { Themes } from '../theme';
import { ExportStatusContext } from '@/contexts/export-status-context';

const NoOpMenuItem = styled(MenuItem)({
    opacity: 1,
    cursor: 'default',
    backgroundColor: 'transparent !important'
});

const Header = () => {
    const { exportResult } = useContext(ExportStatusContext);
    const { user, isLoggedIn } = useUser();
    const { isKnowledgeCenterOpen, setIsKnowledgeCenterOpen } =
        useKnowledgeCenter();

    const { supervisedAccount } = useSupervisedAccount();

    const location = useLocation();

    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

    const handleMenu = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const routes = !!supervisedAccount
        ? [
              {
                  name: 'Creative Performance',
                  path: '/CreativePerformance'
              }
          ]
        : [];

    const currentPath = routes
        .map(r => r.path)
        .some(p => location.pathname.includes(p))
        ? routes.map(r => r.path).filter(p => location.pathname.includes(p))[0]
        : false;

    return (
        <AppBar
            position='static'
            sx={{
                marginTop: exportResult ? `${STATUS_BAR_HEIGHT}px` : '0px'
            }}
        >
            <Container maxWidth={'xl'}>
                <Toolbar disableGutters>
                    <IconButton
                        color='inherit'
                        sx={{ fontSize: 40 }}
                        component={Link}
                        to='/CreativePerformance'
                    >
                        <RealeyesLogo fontSize='inherit' />
                    </IconButton>

                    {isLoggedIn && (
                        <>
                            <ThemeProvider theme={Themes.dark}>
                                <Tabs
                                    sx={{
                                        flex: 1,
                                        margin: '0 50px'
                                    }}
                                    value={currentPath}
                                    textColor='inherit'
                                >
                                    {routes.map((route, idx) => (
                                        <Tab
                                            sx={{ opacity: 1, minHeight: 64 }}
                                            key={idx}
                                            label={route.name}
                                            value={route.path}
                                            component={Link}
                                            to={route.path}
                                        />
                                    ))}
                                </Tabs>
                            </ThemeProvider>
                            <Box>
                                {supervisedAccount && (
                                    <Typography
                                        variant='caption'
                                        color='inherit'
                                    >
                                        {supervisedAccount.name}
                                    </Typography>
                                )}
                                <IconButton
                                    size='large'
                                    aria-label='account of current user'
                                    aria-controls='menu-appbar'
                                    aria-haspopup='true'
                                    onClick={handleMenu}
                                    color='inherit'
                                >
                                    <AccountCircleIcon />
                                </IconButton>
                                <IconButton
                                    color='inherit'
                                    onClick={() =>
                                        setIsKnowledgeCenterOpen(
                                            !isKnowledgeCenterOpen
                                        )
                                    }
                                >
                                    <HelpIcon />
                                </IconButton>
                                <Menu
                                    id='menu-appbar'
                                    anchorEl={anchorEl}
                                    anchorOrigin={{
                                        vertical: 'bottom',
                                        horizontal: 'right'
                                    }}
                                    keepMounted
                                    transformOrigin={{
                                        vertical: 'top',
                                        horizontal: 'right'
                                    }}
                                    MenuListProps={{
                                        sx: {
                                            pb: 0
                                        }
                                    }}
                                    open={Boolean(anchorEl)}
                                    onClose={handleClose}
                                >
                                    <NoOpMenuItem
                                        sx={{
                                            flexDirection: 'column',
                                            alignItems: 'flex-start'
                                        }}
                                        disableRipple
                                    >
                                        <Typography variant='caption'>
                                            Profile
                                        </Typography>
                                        <Typography variant='body2'>
                                            {user?.email}
                                        </Typography>
                                    </NoOpMenuItem>
                                    <Divider />

                                    <MenuItem
                                        component={Link}
                                        to='/SwitchAccount'
                                    >
                                        Account
                                    </MenuItem>

                                    <MenuItem onClick={Authentication.logout}>
                                        Logout
                                    </MenuItem>
                                </Menu>
                            </Box>
                        </>
                    )}
                </Toolbar>
            </Container>
        </AppBar>
    );
};

export default Header;
