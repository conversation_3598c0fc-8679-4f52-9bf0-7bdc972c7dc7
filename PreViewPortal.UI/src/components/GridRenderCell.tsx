import {
    CardMedia,
    Stack,
    Tooltip,
    Typography,
    Box,
    Popover,
    TypographyProps
} from '@mui/material';
import moment from 'moment';
import { getColorForTrafficLight } from '../helpers/get-color-for-traffic-lights';
import { getPercentageValue } from '../helpers/get-percentage-value';
import { TrafficLight } from '../interfaces/traffic-light';
import ScoreInColoredBoxCell from './ScoreInColoredBoxCell';
import RealeyesLogo from './RealeyesLogo';
import FacebookLogo from './FacebookLogo';
import TikTokLogo from './TikTokLogo';
import TwitterLogo from './TwitterLogo';
import YouTubeLogo from './YouTubeLogo';
import InstagramLogo from './InstagramLogo';
import SnapchatLogo from './SnapchatLogo';
import { useState } from 'react';
import { MediaAndTestIdsPopover } from './MediaAndTestIdsPopover';
import { CopyRow } from './CopyRow';

interface CellProps {
    value: number | undefined;
    isDecile?: boolean;
}

export const DurationCell = ({ value }: CellProps) => <>{`${value || 0}s`}</>;

interface ExtendedCellProps extends CellProps {
    fractionDigits?: number;
}

export const PercentageCell = ({
    value,
    fractionDigits = 0
}: ExtendedCellProps) => <>{getPercentageValue(value, fractionDigits)}</>;

export const SecondBasedScoreCell = ({
    value,
    fractionDigits = 1
}: ExtendedCellProps) => <>{`${value?.toFixed(fractionDigits) || 0}s`}</>;

interface BrandCellProps {
    brand: string;
    brandLogoUrl: string | undefined;
    onlyLogo?: boolean;
    brandCount?: number;
}

export const BrandCell = ({
    brand,
    brandLogoUrl,
    onlyLogo = true,
    brandCount
}: BrandCellProps) =>
    brandCount !== undefined ? (
        <span>{brandCount}</span>
    ) : (
        <Tooltip title={brand} disableHoverListener={!onlyLogo}>
            <Stack direction='row' alignItems='center'>
                {brandLogoUrl && (
                    <Box
                        sx={{
                            width: 60,
                            height: 60,
                            backgroundSize: 'contain',
                            backgroundRepeat: 'no-repeat',
                            backgroundImage: `url(${brandLogoUrl})`,
                            backgroundPosition: 'center'
                        }}
                    />
                )}
                {(!onlyLogo || !brandLogoUrl) && (
                    <MultiLineCell value={brand} />
                )}
            </Stack>
        </Tooltip>
    );

export const AdSetCell = ({ value }: { value: string }) => {
    return (
        <Stack direction='row' alignItems='center'>
            <MultiLineCell value={value} />
        </Stack>
    );
};

interface OrderCellProps {
    name: string;
    externalKey: string;
    isAdmin: boolean;
}

export const OrderCell = ({ name, externalKey, isAdmin }: OrderCellProps) => {
    const [anchorEl, setAnchorEl] = useState<HTMLSpanElement | null>(null);

    const handleClickOnText = (e: React.MouseEvent<HTMLSpanElement>) => {
        if (!isAdmin) return;

        e.stopPropagation();
        setAnchorEl(e.currentTarget);
    };

    return (
        <>
            <Stack direction='row' alignItems='center'>
                <MultiLineCell
                    value={name}
                    onClick={e => isAdmin && handleClickOnText(e)}
                />
            </Stack>

            {isAdmin && (
                <Popover
                    open={!!anchorEl}
                    onClose={() => setAnchorEl(null)}
                    anchorEl={anchorEl}
                    anchorOrigin={{
                        vertical: 'bottom',
                        horizontal: 'center'
                    }}
                >
                    <Stack
                        direction='row'
                        alignItems='center'
                        bgcolor='#273235'
                        borderRadius={'5px'}
                        p={2}
                        gap={1}
                        sx={{
                            color: 'white'
                        }}
                    >
                        <CopyRow
                            label='External Key'
                            value={externalKey}
                            onCopy={() => setAnchorEl(null)}
                        />
                    </Stack>
                </Popover>
            )}
        </>
    );
};

interface CreativeCellProps {
    name: string | undefined;
    thumbnailUrl: string | undefined;
    isAdmin: boolean;
    surveyKeyAlias: string;
    sourceMediaID: number;
    inFocusTestID: number;
    inContextTestID?: number;
}

export const CreativeCell = ({
    name,
    thumbnailUrl,
    sourceMediaID,
    inFocusTestID,
    inContextTestID,
    surveyKeyAlias,
    isAdmin
}: CreativeCellProps) => {
    const [anchorEl, setAnchorEl] = useState<HTMLSpanElement | null>(null);

    const handleClickOnText = (e: React.MouseEvent<HTMLSpanElement>) => {
        if (!isAdmin) return;

        e.stopPropagation();
        setAnchorEl(e.currentTarget);
    };

    return (
        <>
            <Stack direction='row' alignItems='center'>
                <CardMedia
                    component='img'
                    sx={{
                        width: 73,
                        mr: 2
                    }}
                    image={thumbnailUrl}
                />

                <MultiLineCell value={name} onClick={handleClickOnText} />
            </Stack>

            {isAdmin && (
                <MediaAndTestIdsPopover
                    anchorEl={anchorEl}
                    onClose={() => setAnchorEl(null)}
                    sourceMediaID={sourceMediaID}
                    inFocusTestID={inFocusTestID}
                    inContextTestID={inContextTestID}
                    surveyKeyAlias={surveyKeyAlias}
                />
            )}
        </>
    );
};

interface MultiLineCellProps {
    value: React.ReactNode;
    color?: string;
    onClick?: TypographyProps['onClick'];
}

export const MultiLineCell = ({
    value,
    color,
    onClick
}: MultiLineCellProps) => (
    <Typography
        variant='body2'
        margin='revert'
        color={color}
        fontSize={14}
        onClick={onClick}
        sx={{
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden',
            whiteSpace: 'normal',
            my: 0,
            ...(onClick && {
                cursor: 'default',
                '&:hover': {
                    textDecoration: 'underline'
                }
            })
        }}
    >
        {value}
    </Typography>
);

interface CountryCellProps {
    country: string;
    countryCode: string;
    countryCount?: number;
    disableTooltip?: boolean;
}

export const CountryCell = ({
    country,
    countryCode,
    countryCount,
    disableTooltip
}: CountryCellProps) =>
    countryCount !== undefined ? (
        <span>{countryCount}</span>
    ) : (
        <Tooltip title={country} disableHoverListener={disableTooltip}>
            <Box position='relative'>
                <Box
                    sx={{
                        position: 'absolute',
                        width: '24px',
                        height: '18px',
                        bgcolor: 'black',
                        opacity: 0.02,
                        zIndex: 1
                    }}
                />
                <span
                    className={`fi fi-${countryCode?.toLowerCase()}`}
                    style={{
                        fontSize: 18,
                        boxShadow:
                            'rgba(0, 0, 0, 0.1) 0px 0px 5px 0px, rgba(0, 0, 0, 0.1) 0px 0px 1px 0px'
                    }}
                />
            </Box>
        </Tooltip>
    );

interface DateCellProps {
    date: Date | undefined;
}

export const DateCell = ({ date }: DateCellProps) =>
    date ? <>{moment.utc(date).format('DD-MM-YYYY')}</> : null;

interface DiffCellProps {
    value?: number;
    isGridMode?: boolean;
}

export const DiffCell = ({ value, isGridMode = true }: DiffCellProps) => {
    if (value === undefined) return null;

    const percentageNumber = Math.round(Math.abs(value * 100));
    const prtValue =
        percentageNumber === 0
            ? getPercentageValue(0)
            : (value > 0 ? '+ ' : '- ') + getPercentageValue(Math.abs(value));

    if (!isGridMode) return <>{prtValue}</>;

    return (
        <Stack
            sx={{
                alignItems: 'center',
                justifyContent: 'center',
                height: '100%',
                widht: '100%'
            }}
        >
            <Typography
                sx={{
                    color: '#505B5F',
                    border: '1px solid #F2F2F2',
                    width: 50,
                    fontSize: 14
                }}
            >
                {prtValue}
            </Typography>
        </Stack>
    );
};

interface QualityScoreCellProps {
    score: number;
    scoreIndex: TrafficLight;
    fontSize?: number;
    fractionDigits?: number;
}

export const QualityScoreCell = ({
    score,
    scoreIndex,
    fontSize,
    fractionDigits
}: QualityScoreCellProps) => (
    <ScoreInColoredBoxCell
        score={
            fractionDigits !== undefined
                ? +score.toFixed(fractionDigits)
                : score
        }
        color={getColorForTrafficLight(scoreIndex)}
        fontSize={fontSize}
    />
);

interface AdformatCellProps {
    adformatName?: string;
    platform: string;
    adformatCount?: number;
}

export const AdformatCell = ({
    adformatName,
    platform,
    adformatCount
}: AdformatCellProps) => {
    const boxSize = 24;

    return adformatCount !== undefined ? (
        <span style={{ width: '100%', textAlign: 'center' }}>
            {adformatCount}
        </span>
    ) : (
        <Stack direction='row' alignItems='center' gap={1}>
            <Stack
                width={boxSize}
                height={boxSize}
                fontSize={boxSize}
                bgcolor={platform === 'Realeyes' ? 'primary.main' : undefined}
                color='white'
                alignItems='center'
                justifyContent='center'
            >
                {(() => {
                    switch (platform) {
                        case 'Realeyes':
                            return <RealeyesLogo fontSize='small' />;
                        case 'Facebook':
                            return <FacebookLogo fontSize='inherit' />;
                        case 'TikTok':
                            return <TikTokLogo fontSize='inherit' />;
                        case 'Twitter':
                            return <TwitterLogo fontSize='inherit' />;
                        case 'YouTube':
                            return <YouTubeLogo fontSize='inherit' />;
                        case 'Instagram':
                            return <InstagramLogo fontSize='inherit' />;
                        case 'Snapchat':
                            return <SnapchatLogo fontSize='inherit' />;
                        default:
                            return null;
                    }
                })()}
            </Stack>
            {adformatName && (
                <Typography variant='body2'>{adformatName}</Typography>
            )}
        </Stack>
    );
};

interface CountOrValueCellProps {
    value: string | number | undefined;
    count: number | undefined;
}

export const CountOrValueCell = ({ value, count }: CountOrValueCellProps) =>
    count !== undefined ? (
        <span style={{ width: '100%', textAlign: 'center' }}>{count}</span>
    ) : (
        value
    );
