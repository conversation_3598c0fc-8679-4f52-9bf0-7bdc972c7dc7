import ClearIcon from '@mui/icons-material/Clear';
import SearchIcon from '@mui/icons-material/Search';
import {
    Checkbox,
    Divider,
    IconButton,
    List,
    ListItem,
    ListItemButton,
    ListItemIcon,
    ListItemText,
    Stack,
    TextField
} from '@mui/material';
import { isEmpty, isEqual } from 'lodash';
import { memo, useMemo, useState, useCallback } from 'react';
import { CustomGridFilterProps } from '../../interfaces/custom-grid-filter-props';
import { customGridAutocompleteRenderText } from '../../helpers/custom-grid-autocomplete-render-text';

import CustomGridFilterBase from './CustomGridFilterBase';

const defaultValue: string[] = [];
const defaultSearchValue = '';

const CustomGridAutocomplete = (props: CustomGridFilterProps<string[]>) => {
    const {
        value = defaultValue,
        options,
        filterDefinition: { field },
        onValueChange,
        onClearValue,
        maxHeight,
        customAutoGridRenderTextList
    } = props;

    const [searchValue, setSearchValue] = useState<string>(defaultSearchValue);

    const handleCheck = (option: string) => () => {
        onValueChange(field, [...value, option]);
    };

    const handleUncheck = (option: string) => () => {
        onValueChange(
            field,
            value.filter(v => v !== option)
        );
    };

    const handleClear = () => {
        setSearchValue(defaultSearchValue);
        onClearValue(field);
    };

    const handleClearSearch = (event: React.MouseEvent<HTMLElement>) => {
        event.stopPropagation();
        setSearchValue(defaultSearchValue);
    };

    const orderedCheckedOptions = useMemo<string[]>(
        () => value.sort(),
        [value]
    );

    const renderText = useCallback(
        () =>
            customGridAutocompleteRenderText(
                customAutoGridRenderTextList || orderedCheckedOptions
            ),
        [orderedCheckedOptions, customAutoGridRenderTextList]
    );

    const orderedUncheckedOptions = useMemo<string[]>(() => {
        if (!options) return [];

        return options
            .filter(o => !value.includes(o))
            .filter(o =>
                o.toLocaleLowerCase().includes(searchValue.toLocaleLowerCase())
            )
            .sort();
    }, [options, value, searchValue]);

    const hasValue = !isEqual(value, defaultValue);
    const hasSearchValue = !isEmpty(searchValue);

    return (
        <CustomGridFilterBase
            {...props}
            hasValue={hasValue}
            renderText={renderText}
            onClearValue={handleClear}
            maxHeight={maxHeight}
        >
            <Stack m={2}>
                <TextField
                    value={searchValue}
                    placeholder='Search value...'
                    InputProps={{
                        startAdornment: <SearchIcon sx={{ mr: 1 }} />,
                        endAdornment: hasSearchValue && (
                            <IconButton onClick={handleClearSearch}>
                                <ClearIcon />
                            </IconButton>
                        )
                    }}
                    onChange={({ target }) => setSearchValue(target.value)}
                />
                {!!orderedCheckedOptions.length && <Divider sx={{ mt: 2 }} />}
                <List>
                    {orderedCheckedOptions.map(o => (
                        <ListItem key={o} disablePadding>
                            <ListItemButton onClick={handleUncheck(o)} dense>
                                <ListItemIcon>
                                    <Checkbox
                                        edge='start'
                                        checked={true}
                                        tabIndex={-1}
                                        disableRipple
                                    />
                                </ListItemIcon>
                                <ListItemText id={o} primary={o} />
                            </ListItemButton>
                        </ListItem>
                    ))}
                </List>
                <Divider />
                <List>
                    {!orderedUncheckedOptions.length ? (
                        <ListItem>
                            <ListItemText>No items</ListItemText>
                        </ListItem>
                    ) : (
                        orderedUncheckedOptions.map(o => (
                            <ListItem key={o} disablePadding>
                                <ListItemButton onClick={handleCheck(o)} dense>
                                    <ListItemIcon>
                                        <Checkbox
                                            edge='start'
                                            checked={false}
                                            tabIndex={-1}
                                            disableRipple
                                        />
                                    </ListItemIcon>
                                    <ListItemText id={o} primary={o} />
                                </ListItemButton>
                            </ListItem>
                        ))
                    )}
                </List>
            </Stack>
        </CustomGridFilterBase>
    );
};

export default memo(CustomGridAutocomplete);
