import { <PERSON>lide<PERSON>, Stack, TextField } from '@mui/material';
import { isEmpty, isEqual } from 'lodash';
import { memo } from 'react';
import { CustomGridFilterProps } from '../../interfaces/custom-grid-filter-props';
import CustomGridFilterBase from './CustomGridFilterBase';

const CustomGridNumericRange = (props: CustomGridFilterProps<number[]>) => {
    const {
        value,
        options,
        filterDefinition: {
            displayMultiplier = 1,
            step = 0.01,
            prefix = '',
            postfix = '',
            field,
            label
        },
        onValueChange
    } = props;

    const precisionDistance = (precision: number) => {
        if (!isFinite(precision)) return 0;

        let e = 1;
        let distance = 0;
        while (Math.round(precision * e) / e !== precision) {
            e *= 10;
            distance++;
        }

        return distance;
    };

    const round = precisionDistance(step);
    const min = options ? +options[0] : 0;
    const max = options ? +options[1] : 0;
    const defaultValue = [min, max];
    const range = value || defaultValue;

    const renderText = () => {
        if (!value?.length || (value[0] === min && value[1] === max)) {
            return 'Any';
        }

        const [currMin, currMax] = range;

        //prettier-ignore
        return `${prefix}${(currMin * displayMultiplier).toFixed(round)}${postfix} - ${prefix}${(currMax * displayMultiplier).toFixed(round)}${postfix}`;
    };

    const valueLabelFormat = (value: number) =>
        `${prefix}${(value * displayMultiplier).toFixed(round)}${postfix}`;

    const handleSliderChange = (
        _: Event,
        newValue: number | number[],
        activeThumb: number
    ) => {
        if (!Array.isArray(newValue)) return;

        const [currMin, currMax] = range;
        const [newMin, newMax] = newValue;

        const newRange =
            activeThumb === 0 ? [newMin, currMax] : [currMin, newMax];

        onValueChange(field, newRange);
    };

    const handleInputChange =
        (index: number) =>
        (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
            const newRange = [...range];

            newRange[index] = Number(e.target.value) / displayMultiplier;

            onValueChange(field, newRange);
        };

    const hasValue = !isEmpty(value);
    const hasNoValidRange = isEqual(min, max);
    const inputProps = {
        step: step * displayMultiplier,
        min: min * displayMultiplier,
        max: max * displayMultiplier
    };

    return (
        <CustomGridFilterBase
            {...props}
            hasValue={hasValue}
            renderText={renderText}
        >
            <Stack sx={{ m: 4 }}>
                <Slider
                    min={min}
                    max={max}
                    step={step}
                    value={range}
                    valueLabelDisplay='on'
                    getAriaLabel={() => label}
                    valueLabelFormat={valueLabelFormat}
                    onChange={handleSliderChange}
                    disableSwap
                />
                <Stack
                    mt={2}
                    direction='row'
                    alignItems='center'
                    justifyContent='space-between'
                    spacing={2}
                >
                    <TextField
                        type='number'
                        label='From'
                        inputProps={inputProps}
                        value={(range[0] * displayMultiplier).toFixed(round)}
                        onChange={handleInputChange(0)}
                        fullWidth
                    />
                    <TextField
                        type='number'
                        label='To'
                        disabled={hasNoValidRange}
                        inputProps={inputProps}
                        value={(range[1] * displayMultiplier).toFixed(round)}
                        onChange={handleInputChange(1)}
                        fullWidth
                    />
                </Stack>
            </Stack>
        </CustomGridFilterBase>
    );
};

export default memo(CustomGridNumericRange);
