import { Checkbox, ListItemText, MenuItem } from '@mui/material';
import { isEmpty } from 'lodash';
import { memo } from 'react';
import { CustomGridFilterProps } from '../../interfaces/custom-grid-filter-props';
import { CustomGridFilterValueType } from '../../interfaces/custom-grid-filter-value-type';
import CustomGridFilterBase from './CustomGridFilterBase';

const options = ['true', 'false'];

const CustomGridSelectBoolean = (props: CustomGridFilterProps<string>) => {
    const {
        value,
        filterDefinition: { field, valueToTextMap },
        onValueChange
    } = props;

    const handleSelect = (option: string) => () => {
        onValueChange(field, option);
    };

    const renderText = (value: CustomGridFilterValueType): string => {
        if (isEmpty(props.value)) return 'Any';

        value = value as string;

        return valueToTextMap?.get(value) || value;
    };

    const hasValue = !isEmpty(value);

    return (
        <CustomGridFilterBase
            {...props}
            hasValue={hasValue}
            renderText={renderText}
        >
            {options.map(o => (
                <MenuItem key={o} onClick={handleSelect(o)}>
                    <Checkbox
                        edge='start'
                        checked={value === o}
                        disableRipple
                    />
                    <ListItemText primary={valueToTextMap?.get(o)} />
                </MenuItem>
            ))}
        </CustomGridFilterBase>
    );
};

export default memo(CustomGridSelectBoolean);
