import {
    GridColumnHeaders,
    GridSlots,
    PropsFromSlot
} from '@mui/x-data-grid-premium';
import {
    CustomGridSummaryPanel,
    CustomGridSummaryPanelProps
} from './CustomGridSummaryPanel';

export type CustomGridColumnHeaderProps = CustomGridSummaryPanelProps &
    PropsFromSlot<GridSlots['columnHeaders']>;

export const CustomGridColumnHeader = ({
    gridData,
    selectedCreatives,
    ...props
}: CustomGridColumnHeaderProps) => (
    <span style={{ position: 'relative' }}>
        {gridData && (
            <CustomGridSummaryPanel
                gridData={gridData}
                selectedCreatives={selectedCreatives}
            />
        )}

        <GridColumnHeaders {...props} />
    </span>
);
