import FilterAltOffIcon from '@mui/icons-material/FilterAltOff';
import { Badge, Box, Button, Stack } from '@mui/material';
import { debounce, isEmpty, isEqual } from 'lodash';
import {
    forwardRef,
    useCallback,
    useEffect,
    useImperativeHandle,
    useMemo,
    useState
} from 'react';
import CustomGridNumericRange from './CustomGridNumericRange';
import CustomGridTextField from './CustomGridTextField';
import { CustomGridFilterDefinition } from '../../interfaces/custom-grid-filter-definition';
import { CustomGridFilterInput } from '../../interfaces/custom-grid-filter-input';
import { CustomGridFilterItemModel } from '../../interfaces/custom-grid-filter-item-model';
import { CustomGridFilterModel } from '../../interfaces/custom-grid-filter-model';
import { CustomGridFilterOperator } from '../../interfaces/custom-grid-filter-operator';
import { CustomGridFilterPanelReference } from '../../interfaces/custom-grid-filter-panel-reference';
import { CustomGridFilterProps } from '../../interfaces/custom-grid-filter-props';
import { CustomGridFilterValueType } from '../../interfaces/custom-grid-filter-value-type';
import { CustomGridRowModel } from '../../interfaces/custom-grid-row-model';
import CustomGridAddButton from './CustomGridAddButton';
import CustomGridAutocomplete from './CustomGridAutocomplete';
import CustomGridDatePicker from './CustomGridDatePicker';
import CustomGridMultiAutocomplete from './CustomGridMultiAutocomplete';
import CustomGridSelect from './CustomGridSelect';
import CustomGridSelectBoolean from './CustomGridSelectBoolean';
import SearchIcon from '@mui/icons-material/Search';

export interface CustomGridFilterPanelProps {
    gridData: CustomGridRowModel<unknown>;
    filterModel?: CustomGridFilterModel;
    filterDefinitions: CustomGridFilterDefinition<any>[];
    onFilterModelChange?: (filter: CustomGridFilterModel) => void;
}

//prettier-ignore
const operators = new Map<CustomGridFilterInput, CustomGridFilterOperator>([
    [CustomGridFilterInput.TextField, CustomGridFilterOperator.Contains],
    [CustomGridFilterInput.Select, CustomGridFilterOperator.Equals],
    [CustomGridFilterInput.Autocomplete, CustomGridFilterOperator.IsAnyOf],
    [CustomGridFilterInput.MultiAutocomplete, CustomGridFilterOperator.IsAnyOf],
    [CustomGridFilterInput.TextField, CustomGridFilterOperator.Contains],
    [CustomGridFilterInput.DatePickerDateTimeOffset, CustomGridFilterOperator.DateTimeOffset],
    [CustomGridFilterInput.DatePickerDateTime, CustomGridFilterOperator.DateTime],
    [CustomGridFilterInput.NumericRange, CustomGridFilterOperator.Range],
    [CustomGridFilterInput.SelectBoolean, CustomGridFilterOperator.Equals]
]);

const CustomGridFilterPanel = forwardRef<
    CustomGridFilterPanelReference,
    CustomGridFilterPanelProps
>(({ gridData, filterModel, filterDefinitions, onFilterModelChange }, ref) => {
    const defaultVisibleFilters = useMemo<Set<string>>(
        () =>
            new Set(
                filterDefinitions
                    .filter(fd => fd.defaultVisible)
                    .map(fd => fd.field)
            ),
        [filterDefinitions]
    );

    const [visibleFilters, setVisibleFilters] = useState(defaultVisibleFilters);

    const [pendingFilterValues, setPendingFilterValues] = useState<
        Map<string, CustomGridFilterValueType>
    >(new Map(filterModel?.filters.map(i => [i.columnField, i.value])));

    const [filterValues, setFilterValues] = useState(
        new Map(pendingFilterValues)
    );

    useImperativeHandle(ref, () => ({ clearFilters }));

    useEffect(() => {
        if (!filterModel || isEmpty(filterModel?.filters)) return;

        const newFilterValues = new Map(
            filterModel.filters.map(i => [i.columnField, i.value])
        );

        setFilterValues(prevFilterValues => {
            if (isEqual(prevFilterValues, newFilterValues))
                return prevFilterValues;

            setPendingFilterValues(newFilterValues);

            return new Map(newFilterValues);
        });
    }, [filterModel]);

    useEffect(() => {
        if (!onFilterModelChange) return;

        const filters: CustomGridFilterItemModel[] = Array.from(filterValues)
            .filter(([_, value]) => !!value)
            .map(([key, value]) => {
                const { filterType, orQueryGroupId } = filterDefinitions.find(
                    b => b.field === key
                )!;

                return {
                    value,
                    columnField: key,
                    orQueryGroupId,
                    operatorValue: operators.get(filterType)!
                };
            });

        const newFilterModel: CustomGridFilterModel = { filters };

        if (isEqual(filterModel, newFilterModel)) return;

        onFilterModelChange({ filters });
    }, [filterModel, filterValues, filterDefinitions, onFilterModelChange]);

    const updateFilterValue = useCallback(
        (field: string, newValue: CustomGridFilterValueType) => {
            setPendingFilterValues(prevState => {
                if (isEmpty(newValue)) prevState.delete(field);
                else prevState.set(field, newValue);

                return new Map(prevState);
            });
        },
        []
    );

    const clearFilters = useCallback(() => {
        setVisibleFilters(defaultVisibleFilters);
        setPendingFilterValues(new Map());
        setFilterValues(new Map());
    }, [defaultVisibleFilters]);

    const handleAddFilter = useCallback((field: string) => {
        setVisibleFilters(filters => new Set(filters).add(field));
    }, []);

    // eslint-disable-next-line
    const handleUpdateFilter = useCallback(
        debounce(() => {
            setPendingFilterValues(pendingFilterValues => {
                setFilterValues(filterValues => {
                    if (isEqual(pendingFilterValues, filterValues)) {
                        return filterValues;
                    }

                    return new Map(pendingFilterValues);
                });

                return pendingFilterValues;
            });
        }, 300),
        []
    );

    const handleUpdateValue = useCallback(
        (field: string, value: CustomGridFilterValueType) =>
            updateFilterValue(field, value),
        [updateFilterValue]
    );

    const handleClearValue = useCallback(
        (field: string) => {
            setPendingFilterValues(prevState => {
                prevState.delete(field);
                return new Map(prevState);
            });
            handleUpdateFilter();
        },
        [handleUpdateFilter]
    );

    const handleRemove = useCallback((field: string) => {
        setVisibleFilters(filters => {
            filters.delete(field);

            return new Set(filters);
        });
    }, []);

    const handleRemoveFilters = useCallback(
        () => clearFilters(),
        [clearFilters]
    );

    const renderFilter = (field: string) => {
        const filterDefinition = filterDefinitions.find(
            fd => fd.field === field
        )!;

        if (filterDefinition.noRender) return;

        let multiOptions;

        if (filterDefinition.orQueryGroupId) {
            multiOptions = filterDefinitions
                .filter(
                    f => f.orQueryGroupId === filterDefinition.orQueryGroupId
                )
                .map(({ field, label, optionsName }) => ({
                    field,
                    label,
                    options: gridData[optionsName!] || []
                }));
        }

        const options = filterDefinition.optionsName
            ? gridData[filterDefinition.optionsName]
            : [];

        const value = pendingFilterValues.get(field);

        const props: CustomGridFilterProps<any> = {
            value,
            options,
            multiOptions,
            filterDefinition,
            onUpdateFilter: handleUpdateFilter,
            onValueChange: handleUpdateValue,
            onClearValue: handleClearValue,
            onRemove: !filterDefinition.defaultVisible
                ? handleRemove
                : undefined
        };

        switch (filterDefinition.filterType) {
            case CustomGridFilterInput.TextField:
                return <CustomGridTextField key={field} {...props} />;
            case CustomGridFilterInput.Select:
                return <CustomGridSelect key={field} {...props} />;
            case CustomGridFilterInput.Autocomplete:
                return <CustomGridAutocomplete key={field} {...props} />;
            case CustomGridFilterInput.NumericRange:
                return <CustomGridNumericRange key={field} {...props} />;
            case CustomGridFilterInput.DatePickerDateTime:
            case CustomGridFilterInput.DatePickerDateTimeOffset:
                return <CustomGridDatePicker key={field} {...props} />;
            case CustomGridFilterInput.SelectBoolean:
                return <CustomGridSelectBoolean key={field} {...props} />;
            case CustomGridFilterInput.MultiAutocomplete:
                return <CustomGridMultiAutocomplete key={field} {...props} />;
            default:
                return null;
        }
    };

    const isDefaultVisibleFilters = useMemo<boolean>(
        () =>
            defaultVisibleFilters.size === visibleFilters.size &&
            [...visibleFilters].every(f => defaultVisibleFilters.has(f)),
        [defaultVisibleFilters, visibleFilters]
    );

    const hiddenFilterDefinitions = useMemo<
        CustomGridFilterDefinition<unknown>[]
    >(
        () =>
            filterDefinitions.filter(
                fd => !visibleFilters.has(fd.field) && !fd.noRender
            ),
        [visibleFilters, filterDefinitions]
    );

    return (
        <Stack direction='row' alignItems='start' gap={2} pt={3} pb={1}>
            <Box mt={2} mx={1}>
                <Badge badgeContent={filterValues.size} color='secondary'>
                    <SearchIcon />
                </Badge>
            </Box>
            <Stack direction='row' alignItems='center' flexWrap='wrap' gap={2}>
                {Array.from(visibleFilters).map(renderFilter)}
                <CustomGridAddButton
                    availableFilterDefinitions={hiddenFilterDefinitions}
                    onClick={handleAddFilter}
                />
                {!isDefaultVisibleFilters && (
                    <Box>
                        <Button
                            startIcon={<FilterAltOffIcon />}
                            onClick={handleRemoveFilters}
                        >
                            Remove filters
                        </Button>
                    </Box>
                )}
            </Stack>
        </Stack>
    );
});

export default CustomGridFilterPanel;
