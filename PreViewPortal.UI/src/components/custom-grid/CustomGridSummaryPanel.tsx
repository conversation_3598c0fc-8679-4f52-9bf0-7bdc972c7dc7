import { CreativeSelectionModel } from '@/interfaces/creative-selection-model';
import { CustomGridRowModel } from '@/interfaces/custom-grid-row-model';
import { Stack, Typography } from '@mui/material';
import { useEffect, useMemo, useState } from 'react';
import { useProduct } from '@/hooks/use-product';
import { ProductOptions } from '@/interfaces/product-options';

export interface CustomGridSummaryPanelProps {
    gridData: CustomGridRowModel<any>;
    selectedCreatives: CreativeSelectionModel[];
}

type SelectedCreativeWithSummaryData = CreativeSelectionModel & {
    brand: string;
    country: string;
    adformat?: string;
};

const fontColor = '#AFB9BB';
const fontSize = 13;

export const CustomGridSummaryPanel = ({
    gridData,
    selectedCreatives
}: CustomGridSummaryPanelProps) => {
    const [selectedRowsWithSummaryData, setSelectedRowsWithSummaryData] =
        useState<SelectedCreativeWithSummaryData[]>([]);

    const { selectedProduct } = useProduct();

    const isInContextProduct = selectedProduct === ProductOptions.InContext;

    useEffect(() => {
        if (!selectedCreatives.length) {
            setSelectedRowsWithSummaryData([]);
            return;
        }

        const newFilteredSelectedRows = selectedRowsWithSummaryData.filter(r =>
            selectedCreatives.find(
                c =>
                    c.sourceMediaID === r.sourceMediaID &&
                    c.testID === r.testID &&
                    c.orderAdSetID === r.orderAdSetID &&
                    c.taskID == r.taskID
            )
        );

        const newSelectedCreativesToAdd = selectedCreatives.filter(
            r =>
                !selectedRowsWithSummaryData.find(
                    c =>
                        c.sourceMediaID === r.sourceMediaID &&
                        c.testID === r.testID &&
                        c.orderAdSetID === r.orderAdSetID &&
                        c.taskID == r.taskID
                )
        );

        const selectedRowsToAdd =
            gridData?.rows
                .filter(r =>
                    newSelectedCreativesToAdd.find(
                        c =>
                            c.sourceMediaID === r.sourceMediaID &&
                            c.testID === r.testID &&
                            c.taskID == r.taskID &&
                            c.orderAdSetID == r.orderAdSetID
                    )
                )
                .map(
                    ({
                        sourceMediaID,
                        testID,
                        taskID,
                        orderAdSetID,
                        brand,
                        country,
                        adformat
                    }) => ({
                        sourceMediaID,
                        testID,
                        taskID,
                        orderAdSetID,
                        brand,
                        country,
                        adformat
                    })
                ) || [];

        const newSelectedRows = [
            ...newFilteredSelectedRows,
            ...selectedRowsToAdd
        ];

        setSelectedRowsWithSummaryData(newSelectedRows);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [selectedCreatives, gridData]);

    const { rowCount, brandCount, countryCount, adformatCount } =
        useMemo(() => {
            let { rowCount, brandCount, countryCount, adformatCount } =
                gridData;

            const selectedRowsCount = selectedRowsWithSummaryData.length;

            if (selectedRowsCount) {
                rowCount = selectedRowsCount;
                brandCount = new Set(
                    selectedRowsWithSummaryData.map(r => r.brand)
                ).size;
                countryCount = new Set(
                    selectedRowsWithSummaryData.map(r => r.country)
                ).size;

                if (isInContextProduct) {
                    adformatCount = new Set(
                        selectedRowsWithSummaryData.map(r => r.adformat)
                    ).size;
                }
            }

            return {
                rowCount,
                brandCount,
                countryCount,
                adformatCount
            };
            // eslint-disable-next-line react-hooks/exhaustive-deps
        }, [selectedRowsWithSummaryData]);

    return (
        <Stack
            position='absolute'
            direction='row'
            gap={1}
            top={10}
            left={65}
            zIndex={1000}
        >
            <Typography
                color={fontColor}
                fontSize={fontSize}
            >{`${rowCount} ${rowCount > 1 ? 'rows' : 'row'} ${selectedRowsWithSummaryData.length ? 'selected' : ''}:`}</Typography>
            <Typography
                color={fontColor}
                fontSize={fontSize}
            >{`${brandCount} ${brandCount > 1 ? 'brands' : 'brand'}`}</Typography>
            <Typography
                color={fontColor}
                fontSize={fontSize}
                borderLeft='1px solid #e0e0e0'
                pl={1}
            >{`${countryCount} ${countryCount > 1 ? 'countries' : 'country'}`}</Typography>
            {adformatCount !== undefined && (
                <Typography
                    color={fontColor}
                    fontSize={fontSize}
                    borderLeft='1px solid #e0e0e0'
                    pl={1}
                >{`${adformatCount} ${adformatCount > 1 ? 'ad formats' : 'ad format'}`}</Typography>
            )}
        </Stack>
    );
};
