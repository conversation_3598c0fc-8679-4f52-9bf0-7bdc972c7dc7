import { Divider } from '@mui/material';
import {
    GridColumnMenuContainer,
    GridColumnMenuProps,
    GridColumnMenuPinningItem,
    GridColumnMenuColumnsItem,
    GridColumnMenuFilterItem,
    GridColumnMenuHideItem,
    GridColumnMenuSortItem
} from '@mui/x-data-grid-premium';

export const CustomGridColumnMenu = ({
    hideMenu,
    colDef,
    ...menuProps
}: GridColumnMenuProps) => (
    <GridColumnMenuContainer hideMenu={hideMenu} colDef={colDef} {...menuProps}>
        <GridColumnMenuSortItem onClick={hideMenu} colDef={colDef} />
        <GridColumnMenuFilterItem onClick={hideMenu} colDef={colDef} />
        <GridColumnMenuHideItem onClick={hideMenu} colDef={colDef} />
        <GridColumnMenuColumnsItem onClick={hideMenu} colDef={colDef} />
        <Divider />
        <GridColumnMenuPinningItem onClick={hideMenu} colDef={colDef} />
    </GridColumnMenuContainer>
);
