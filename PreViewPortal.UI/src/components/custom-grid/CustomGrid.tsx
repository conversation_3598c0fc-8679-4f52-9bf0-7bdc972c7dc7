import { Box, Paper, Skeleton } from '@mui/material';
import {
    DataGridPremium,
    DataGridPremiumProps,
    GRID_CHECKBOX_SELECTION_COL_DEF,
    GridPaginationModel,
    GridCallbackDetails as MuiGridCallbackDetails,
    GridColumnVisibilityModel as MuiGridColumnVisibilityModel,
    GridRowParams as MuiGridRowParams,
    GridRowSelectionModel as MuiGridRowSelectionModel,
    GridSortItem as MuiGridSortItem,
    GridSortModel as MuiGridSortModel,
    GridState as MuiGridState,
    GridValidRowModel as MuiGridValidRowModel
} from '@mui/x-data-grid-premium';
import { debounce, isEqual, isEmpty } from 'lodash';
import { stringify } from 'qs';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useGridSetting } from '../../hooks/use-grid-setting';
import { useHttp } from '../../hooks/use-http';
import { CustomGridFilterModel } from '../../interfaces/custom-grid-filter-model';
import { CustomGridPagingModel } from '../../interfaces/custom-grid-page-model';
import { CustomGridRequestParams } from '../../interfaces/custom-grid-request-params';
import { CustomGridRowModel } from '../../interfaces/custom-grid-row-model';
import { CustomGridSortItemModel } from '../../interfaces/custom-grid-sort-item-model';
import { CustomGridSortModel } from '../../interfaces/custom-grid-sort-model';
import { GridColumnState } from '../../interfaces/grid-column-state';
import { GridSettingsModel } from '../../interfaces/grid-settings-model';
import { PreViewGrid } from '../../interfaces/preview-grid';
import { GRID_TOOLBAR_COLUMNS_NAME } from '../../constant/grid-toolbar-colums-name';
import { CustomToolbar, CustomToolbarProps } from './CustomGridToolbar';
import { GridToolbarExportButtonProps } from '../GridToolbarExportButton';
import { GridToolbarViewButtonProps } from '../GridToolbarViewButton';
import { CreativeSelectionModel } from '@/interfaces/creative-selection-model';
import { CustomGridFilterDefinition } from '@/interfaces/custom-grid-filter-definition';
import { getFilterModelWithJoinedValues } from '@/helpers/getFilterModelWithJoinedValues';

export interface CustomGridProps<TModel extends CustomGridRowModel<any>>
    extends Omit<
        DataGridPremiumProps<MuiGridValidRowModel>,
        'height' | 'rows' | 'filterModel' | 'onFilterModelChange'
    > {
    gridData: TModel | undefined;
    filterDefinitions: CustomGridFilterDefinition<any>[];
    gridId?: PreViewGrid;
    disableSelectAllCheckbox?: boolean;
    checkboxSelectionLimit?: number;
    endpointUrl: string;
    exportFileName?: string;
    customQueryParams?: any;
    customExportQueryParams?: any;
    onRowsChange?: (rows: TModel) => void;
    // delete this after sunsetting old cp page
    onFilterModelChange?: (model: CustomGridFilterModel) => void;
    onLoadingChange?: (isLoading: boolean) => void;
    selectedCreatives?: CreativeSelectionModel[];
}

export const CustomGrid = <TModel extends CustomGridRowModel<any>>({
    gridId,
    gridData,
    endpointUrl,
    exportFileName,
    filterDefinitions,
    customQueryParams,
    customExportQueryParams,
    disableSelectAllCheckbox,
    initialState: initialInitialState,
    sortingMode = 'server',
    onRowsChange,
    onFilterModelChange,
    onLoadingChange,
    selectedCreatives,
    ...gridProps
}: CustomGridProps<TModel>) => {
    const {
        gridSetting,
        isGridSettingsLoading,
        saveGridSetting,
        deleteGridSetting
    } = useGridSetting(gridId);

    const { http, handleError } = useHttp();

    const [filterModel, setFilterModel] = useState<CustomGridFilterModel>({
        filters: []
    });
    const [sortModel, setSortModel] = useState<MuiGridSortModel>([]);
    const [paginationModel, setPaginationModel] = useState<GridPaginationModel>(
        { page: 0, pageSize: 15 }
    );
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [rowCount, setRowCount] = useState<number>(0);

    const [rowSelectionModel, setRowSelectionModel] =
        useState<MuiGridRowSelectionModel>([]);
    const [resetTimestamp, setResetTimestamp] = useState(Date.now());

    const [initialState, setInitialState] = useState(initialInitialState);

    const defaultColumnVisibilityModel =
        initialInitialState?.columns?.columnVisibilityModel;

    const [columnVisibilityModel, setColumnVisibilityModel] = useState(
        defaultColumnVisibilityModel
    );

    const defaultOrderedFields: string[] = useMemo(() => {
        const defaultOrderedFieldsFromInitialState =
            initialState?.columns?.orderedFields;

        if (defaultOrderedFieldsFromInitialState)
            return defaultOrderedFieldsFromInitialState;

        const fields: string[] = [];
        if (gridProps.checkboxSelection) {
            fields.push(GRID_CHECKBOX_SELECTION_COL_DEF.field);
        }
        gridProps.columns.forEach(c => fields.push(c.field));
        return fields;
    }, [
        initialState?.columns?.orderedFields,
        gridProps.checkboxSelection,
        gridProps.columns
    ]);

    const [currentColumnOrder, setCurrentColumnOrder] =
        useState<string[]>(defaultOrderedFields);

    const customPagingModel = useMemo<CustomGridPagingModel | {}>(
        () => ({
            pageSize: paginationModel.pageSize,
            currentPage: paginationModel.page
        }),
        [paginationModel]
    );

    const customSortModel = useMemo<CustomGridSortModel | {}>(
        () => ({
            sorting: sortModel.map(({ field, sort }) => ({
                field,
                direction: sort
            }))
        }),
        [sortModel]
    );

    const customRequestParams = useMemo<CustomGridRequestParams>(() => {
        const customFilterModel = getFilterModelWithJoinedValues(filterModel);

        return {
            ...customSortModel,
            ...customPagingModel,
            ...customFilterModel,
            ...(customQueryParams || {})
        };
    }, [filterModel, customPagingModel, customSortModel, customQueryParams]);

    // eslint-disable-next-line
    const getGridData = useCallback(
        debounce(async (customRequestParams: CustomGridRequestParams) => {
            const queryString = stringify(customRequestParams, {
                allowDots: true,
                encode: true,
                addQueryPrefix: true
            });

            setIsLoading(true);
            try {
                const model = await http
                    .get(`${endpointUrl}/grid${queryString}`)
                    .json<TModel>();

                setRowCount(model.rowCount);

                if (onRowsChange) {
                    onRowsChange(model);
                }
            } catch (e) {
                handleError(e);
            }
            setIsLoading(false);
        }, 200),
        []
    );

    useEffect(() => {
        getGridData(customRequestParams);
    }, [customRequestParams, getGridData]);

    useEffect(() => {
        if (filterModel.filters.length) {
            setColumnVisibilityModel(prev => {
                const newVisibilityModel = { ...prev };

                filterModel.filters.forEach(({ columnField }) => {
                    newVisibilityModel[columnField] = true;
                });

                return newVisibilityModel;
            });
        }
    }, [filterModel]);

    useEffect(() => {
        const rowGroupingModel = initialState?.rowGrouping?.model;
        if (rowGroupingModel) {
            setColumnVisibilityModel(prev => {
                const newColumnVisibilityModel = { ...prev };

                if (
                    Object.values(newColumnVisibilityModel).find(
                        visible => visible
                    )
                ) {
                    for (const groupColumn of rowGroupingModel) {
                        newColumnVisibilityModel[groupColumn] =
                            gridProps.disableRowGrouping!;
                    }
                }

                return newColumnVisibilityModel;
            });
        }
        // eslint-disable-next-line
    }, [gridProps.disableRowGrouping]);

    useEffect(() => {
        if (gridId === undefined || !gridSetting || isGridSettingsLoading)
            return;

        const columnFields = gridProps.columns.map(c => c.field);
        const savedColumnFields = gridSetting.columns.map(c => c.field);

        const areConfiguredColumnsChanged = !savedColumnFields.every(scf =>
            columnFields.includes(scf)
        );
        if (areConfiguredColumnsChanged) {
            return;
        }

        if (gridProps.checkboxSelection) {
            savedColumnFields.unshift(GRID_CHECKBOX_SELECTION_COL_DEF.field);
        }

        const savedVisibilityModel: MuiGridColumnVisibilityModel = {};
        for (const column of gridSetting.columns) {
            savedVisibilityModel[column.field] = column.visible;
        }

        let savedSortModel: MuiGridSortModel | undefined;
        if (gridSetting.sortModel) {
            const sortItems: MuiGridSortItem[] = gridSetting.sortModel.map(
                ({ field, direction }) => ({ field, sort: direction })
            );

            savedSortModel = sortItems;
        }

        setInitialState(prev => ({
            ...prev,
            columns: {
                orderedFields: savedColumnFields
            },
            sorting: {
                sortModel: savedSortModel
            }
        }));

        setColumnVisibilityModel(savedVisibilityModel);

        setResetTimestamp(Date.now());
    }, [
        gridId,
        gridSetting,
        isGridSettingsLoading,
        gridProps.checkboxSelection,
        gridProps.columns
    ]);

    useEffect(() => {
        if (onLoadingChange) {
            onLoadingChange(isLoading);
        }
    }, [isLoading, onLoadingChange]);

    const handleSaveGridSetting = async () => {
        if (!columnVisibilityModel || !sortModel) return;

        const columnsToSave: GridColumnState[] = currentColumnOrder
            .filter(field => field !== GRID_CHECKBOX_SELECTION_COL_DEF.field)
            .map(field => ({
                field: field,
                visible: columnVisibilityModel[field]
            }));

        const newSettingsModel: GridSettingsModel = {
            columns: columnsToSave
        };

        if (sortModel.length) {
            const sortModelToSave: CustomGridSortItemModel[] = sortModel.map(
                ({ field, sort }) => ({ field, direction: sort })
            );

            newSettingsModel.sortModel = sortModelToSave;
        }

        await saveGridSetting(newSettingsModel);
    };

    const handleResetGridSetting = async () => {
        setInitialState(prev => ({
            ...prev,
            columns: {
                orderedFields: initialInitialState?.columns?.orderedFields
            },
            sorting: initialInitialState?.sorting
        }));

        setColumnVisibilityModel(defaultColumnVisibilityModel);

        setResetTimestamp(Date.now());

        await deleteGridSetting();
    };

    const handlePaginationModelChange = useCallback(
        (model: GridPaginationModel, details: MuiGridCallbackDetails) => {
            setPaginationModel(model);
            if (gridProps.onPaginationModelChange) {
                gridProps.onPaginationModelChange(model, details);
            }
        },
        [gridProps]
    );

    const handleColumnOrderChange = useCallback(
        (state: MuiGridState) => {
            if (state.columnReorder.dragCol) {
                const newColumnOrder = [...state.columns.orderedFields];

                if (gridProps.checkboxSelection) {
                    newColumnOrder.shift();
                }

                setCurrentColumnOrder(newColumnOrder);
            }
        },
        [gridProps.checkboxSelection]
    );

    const handleColumnVisibilityChange = useCallback(
        (
            model: MuiGridColumnVisibilityModel,
            details: MuiGridCallbackDetails
        ) => {
            //Note: after clicking "show all", MUI clears the columnVisibilityModel
            if (isEmpty(model)) {
                gridProps.columns.forEach(({ field }) => (model[field] = true));

                const rowGroupingModel = initialState?.rowGrouping?.model;
                if (rowGroupingModel && !gridProps.disableRowGrouping) {
                    for (const groupColumn of rowGroupingModel) {
                        model[groupColumn] = false;
                    }
                }
            }

            setColumnVisibilityModel(model);

            if (gridProps.onColumnVisibilityModelChange) {
                gridProps.onColumnVisibilityModelChange(model, details);
            }
        },
        // eslint-disable-next-line
        [gridProps]
    );

    const handleSortModelChange = useCallback(
        (model: MuiGridSortModel, details: MuiGridCallbackDetails) => {
            if (gridProps.onSortModelChange) {
                gridProps.onSortModelChange(model, details);
                return;
            }

            setSortModel(prevSortModel =>
                isEqual(model, prevSortModel) ? prevSortModel : model
            );
        },
        [gridProps]
    );

    // delete this after sunsetting old cp page
    const handleFilterModelChange = (model: CustomGridFilterModel) => {
        setFilterModel(model);

        if (onFilterModelChange) {
            onFilterModelChange(model);
        }
    };

    const handleStateChange = useCallback(
        (state: any, event: any, details: MuiGridCallbackDetails) => {
            if (gridId !== undefined) {
                handleSortModelChange(
                    state.sorting.sortModel,
                    {} as MuiGridCallbackDetails
                );
                handleColumnOrderChange(state);
            }

            if (gridProps.onStateChange) {
                gridProps.onStateChange(state, event, details);
            }
        },
        [gridId, gridProps, handleSortModelChange, handleColumnOrderChange]
    );

    const handleRowSelectionModelChange = useCallback(
        (
            selectionModel: MuiGridRowSelectionModel,
            details: MuiGridCallbackDetails
        ) => {
            if (gridProps.onRowSelectionModelChange) {
                gridProps.onRowSelectionModelChange(selectionModel, details);
            } else {
                setRowSelectionModel(selectionModel);
            }
        },
        [gridProps]
    );

    const isRowSelectable = useMemo(() => {
        if (gridProps.isRowSelectable) {
            return gridProps.isRowSelectable;
        }

        if (gridProps.checkboxSelectionLimit) {
            return (params: MuiGridRowParams<MuiGridValidRowModel>) => {
                const selection =
                    (gridProps.rowSelectionModel as MuiGridRowSelectionModel) ||
                    rowSelectionModel;

                return (
                    selection.includes(params.id) ||
                    selection.length < gridProps.checkboxSelectionLimit!
                );
            };
        }
    }, [gridProps, rowSelectionModel]);

    return (
        <Box width='inherit' height='inherit' position='relative'>
            {(gridProps.loading || isGridSettingsLoading) && (
                <Paper
                    sx={{
                        position: 'absolute',
                        top: 0,
                        bottom: 0,
                        left: 0,
                        right: 0,
                        zIndex: 2
                    }}
                >
                    <Skeleton
                        variant='rectangular'
                        width='100%'
                        height='100%'
                    />
                </Paper>
            )}
            <DataGridPremium
                key={resetTimestamp}
                {...gridProps}
                initialState={initialState}
                localeText={{
                    toolbarColumns: GRID_TOOLBAR_COLUMNS_NAME
                }}
                sx={{
                    ...gridProps.sx,
                    ...(disableSelectAllCheckbox && {
                        '& .MuiDataGrid-columnHeaderCheckbox .MuiDataGrid-columnHeaderTitleContainer':
                            {
                                display: 'none'
                            }
                    }),
                    ...(gridProps.onRowClick && {
                        '& .MuiDataGrid-row': {
                            cursor: 'pointer'
                        }
                    }),
                    ...(gridProps.columnGroupingModel && {
                        '& .MuiDataGrid-columnHeaders :first-of-type .MuiDataGrid-columnHeader':
                            {
                                height: '32px !important',

                                '& .MuiDataGrid-columnHeaderTitle': {
                                    fontSize: 13,
                                    color: '#AFB9BB',
                                    fontWeight: 'normal'
                                }
                            }
                    })
                }}
                slots={{
                    ...(gridProps.slots || {}),
                    toolbar: gridProps?.slots?.toolbar || CustomToolbar
                }}
                slotProps={{
                    ...(gridProps.slotProps || {}),
                    toolbar: {
                        ...(gridProps?.slotProps?.toolbar || {}),
                        ...(gridId !== undefined
                            ? ({
                                  onResetGridSetting: handleResetGridSetting,
                                  onSaveGridSetting: handleSaveGridSetting
                              } as GridToolbarViewButtonProps)
                            : {}),
                        ...({
                            filterModel,
                            onFilterModelChange: handleFilterModelChange,
                            filterDefinitions,
                            gridData
                        } as CustomToolbarProps),
                        ...(endpointUrl !== undefined &&
                        exportFileName !== undefined
                            ? ({
                                  exportFileName,
                                  selectedCreatives,
                                  endpointUrl,
                                  customQueryParams,
                                  customExportQueryParams,
                                  filterModel,
                                  sortModel:
                                      initialInitialState?.sorting?.sortModel ||
                                      [],
                                  columnVisibilityModel:
                                      defaultColumnVisibilityModel,
                                  rowCount
                              } as GridToolbarExportButtonProps)
                            : {})
                    }
                }}
                filterMode='server'
                sortingMode={sortingMode}
                columnVisibilityModel={columnVisibilityModel}
                paginationMode='server'
                rows={gridData?.rows ?? []}
                rowCount={rowCount}
                isRowSelectable={isRowSelectable}
                paginationModel={paginationModel}
                onPaginationModelChange={handlePaginationModelChange}
                onStateChange={handleStateChange}
                onSortModelChange={handleSortModelChange}
                onRowSelectionModelChange={handleRowSelectionModelChange}
                onColumnVisibilityModelChange={handleColumnVisibilityChange}
                pageSizeOptions={[15, 30, 50]}
                loading={isLoading}
                keepNonExistentRowsSelected
                disableVirtualization
                disableColumnFilter
                pagination={
                    gridProps.pagination !== undefined
                        ? gridProps.pagination
                        : true
                }
            />
        </Box>
    );
};
