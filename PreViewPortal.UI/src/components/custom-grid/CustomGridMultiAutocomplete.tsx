import ClearIcon from '@mui/icons-material/Clear';
import SearchIcon from '@mui/icons-material/Search';
import {
    Checkbox,
    Divider,
    IconButton,
    List,
    ListItem,
    ListItemButton,
    ListItemIcon,
    ListItemText,
    Stack,
    TextField
} from '@mui/material';
import { isEmpty } from 'lodash';
import { memo, useMemo, useState, useCallback } from 'react';
import { CustomGridFilterProps } from '../../interfaces/custom-grid-filter-props';
import { CustomGridMultiOptionType } from '../../interfaces/custom-grid-multi-option-type';
import { customGridAutocompleteRenderText } from '../../helpers/custom-grid-autocomplete-render-text';
import CustomGridFilterBase from './CustomGridFilterBase';

type CustomGridMultiFilterValueType = { field: string; name: string };

const defaultSearchValue = '';

const CustomGridMultiAutocomplete = (
    props: CustomGridFilterProps<Map<string, string[]>>
) => {
    const { multiOptions, onValueChange, onClearValue } = props;

    const [searchValue, setSearchValue] = useState<string>(defaultSearchValue);

    const [multiValues, setMultiValues] = useState<Map<string, string[]>>(
        new Map()
    );

    const handleCheck = (option: CustomGridMultiFilterValueType) => () => {
        const field = option.field;
        const newValue = [...(multiValues.get(field) || []), option.name];

        setMultiValues(new Map(multiValues.set(field, newValue)));
        onValueChange(field, newValue);
    };

    const handleUncheck = (option: CustomGridMultiFilterValueType) => () => {
        const field = option.field;
        const newValue = multiValues.get(field)!.filter(v => v !== option.name);

        const newMultiValues = new Map(multiValues);

        if (newValue.length) newMultiValues.set(field, newValue);
        else newMultiValues.delete(field);

        setMultiValues(newMultiValues);
        onValueChange(field, newValue);
    };

    const handleClear = () => {
        setSearchValue(defaultSearchValue);

        Array.from(multiValues).forEach(([field, _]) => onClearValue(field));
        setMultiValues(new Map());
    };

    const handleClearSearch = (event: React.MouseEvent<HTMLElement>) => {
        event.stopPropagation();
        setSearchValue(defaultSearchValue);
    };

    const orderedCheckedOptions = useMemo<CustomGridMultiFilterValueType[]>(
        () =>
            Array.from(multiValues)
                .flatMap(([field, names]) =>
                    names.map(name => ({ field, name }))
                )
                .sort((a, b) => a.name.localeCompare(b.name)),
        [multiValues]
    );

    const orderedUncheckedOptions = useMemo<CustomGridMultiOptionType[]>(() => {
        if (!multiOptions) return [];

        return multiOptions.map(mo => ({
            ...mo,
            options:
                !multiValues || !mo.options
                    ? []
                    : mo.options
                          .filter(o => !multiValues.get(mo.field)?.includes(o))
                          .filter(o =>
                              o
                                  .toLocaleLowerCase()
                                  .includes(searchValue.toLocaleLowerCase())
                          )
                          .sort()
        }));
    }, [multiOptions, multiValues, searchValue]);

    const renderText = useCallback(
        () =>
            customGridAutocompleteRenderText(
                orderedCheckedOptions.map(o => o.name)
            ),
        [orderedCheckedOptions]
    );

    const hasValue = !!multiValues.size;

    const hasSearchValue = !isEmpty(searchValue);

    return (
        <CustomGridFilterBase
            {...props}
            hasValue={hasValue}
            renderText={renderText}
            onClearValue={handleClear}
            onRemove={undefined}
        >
            <Stack m={2}>
                <TextField
                    value={searchValue}
                    placeholder='Search value...'
                    InputProps={{
                        startAdornment: <SearchIcon sx={{ mr: 1 }} />,
                        endAdornment: hasSearchValue && (
                            <IconButton onClick={handleClearSearch}>
                                <ClearIcon />
                            </IconButton>
                        )
                    }}
                    onChange={({ target }) => setSearchValue(target.value)}
                />
                {!!orderedCheckedOptions.length && <Divider sx={{ mt: 2 }} />}
                <List>
                    {orderedCheckedOptions.map(({ name, field }) => (
                        <ListItem key={`${field}-${name}`} disablePadding>
                            <ListItemButton
                                onClick={handleUncheck({ name, field })}
                                dense
                            >
                                <ListItemIcon>
                                    <Checkbox
                                        edge='start'
                                        checked={true}
                                        tabIndex={-1}
                                        disableRipple
                                    />
                                </ListItemIcon>
                                <ListItemText id={name} primary={name} />
                            </ListItemButton>
                        </ListItem>
                    ))}
                </List>
                <List>
                    {!multiOptions?.length ? (
                        <ListItem>
                            <ListItemText>No items</ListItemText>
                        </ListItem>
                    ) : (
                        orderedUncheckedOptions.map(
                            ({ field, label, options }) => (
                                <span key={`${field}-${label}`}>
                                    {!!options.length && (
                                        <Divider
                                            textAlign='left'
                                            sx={{ fontSize: 12 }}
                                        >
                                            {label}
                                        </Divider>
                                    )}
                                    {options.map(o => (
                                        <ListItem
                                            key={`${label}-${o}`}
                                            disablePadding
                                        >
                                            <ListItemButton
                                                onClick={handleCheck({
                                                    field,
                                                    name: o
                                                })}
                                                dense
                                            >
                                                <ListItemIcon>
                                                    <Checkbox
                                                        edge='start'
                                                        checked={false}
                                                        tabIndex={-1}
                                                        disableRipple
                                                    />
                                                </ListItemIcon>
                                                <ListItemText
                                                    id={o}
                                                    primary={o}
                                                />
                                            </ListItemButton>
                                        </ListItem>
                                    ))}
                                </span>
                            )
                        )
                    )}
                </List>
            </Stack>
        </CustomGridFilterBase>
    );
};

export default memo(CustomGridMultiAutocomplete);
