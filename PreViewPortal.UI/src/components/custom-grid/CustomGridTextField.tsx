import ClearIcon from '@mui/icons-material/Clear';
import { IconButton, TextField } from '@mui/material';
import { isEmpty } from 'lodash';
import { memo } from 'react';
import { CustomGridFilterProps } from '../../interfaces/custom-grid-filter-props';

const CustomGridTextField = ({
    value,
    filterDefinition: { field, label },
    onUpdateFilter,
    onValueChange,
    onClearValue
}: CustomGridFilterProps<string>) => {
    const handleChange = ({
        target: { value: newValue }
    }: React.ChangeEvent<HTMLInputElement>) => {
        onValueChange(field, newValue);
        onUpdateFilter();
    };

    const hasValue = !isEmpty(value);
    const showClearButton = hasValue ? 'visible ' : 'hidden';

    return (
        <TextField
            label={label}
            value={value || ''}
            placeholder='Any'
            variant='outlined'
            onChange={handleChange}
            InputLabelProps={{
                shrink: true
            }}
            InputProps={{
                endAdornment: (
                    <IconButton
                        onClick={() => onClearValue(field)}
                        sx={{ visibility: showClearButton }}
                    >
                        <ClearIcon />
                    </IconButton>
                )
            }}
        />
    );
};

export default memo(CustomGridTextField);
