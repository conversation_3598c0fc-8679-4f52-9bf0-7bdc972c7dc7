import {
    Grid<PERSON><PERSON>s,
    GridToolbarColumnsButton,
    GridToolbarContainer,
    GridToolbarExport,
    PropsFromSlot
} from '@mui/x-data-grid-premium';

import {
    GridToolbarExportButton,
    GridToolbarExportButtonProps
} from '../GridToolbarExportButton';
import {
    GridToolbarViewButton,
    GridToolbarViewButtonProps
} from '../GridToolbarViewButton';
import { GridToolbarNormSettingsButtonProps } from '../GridToolbarNormSettingsButton';
import { Button, SvgIcon, Tooltip } from '@mui/material';
import CompareTestsIcon from '../../assets/icons/compare-tests.svg?react';
import { useMemo } from 'react';
import { creativeViewerMediaLimit } from '@/constant/runtime-config';
import { InfoIcon } from '@/icons/InfoIcon';
import { useKnowledgeCenter } from '@/hooks/use-knowledge-center';
import CustomGridFilterPanel from './CustomGridFilterPanel';

interface Props {
    onClickCreativeViewer?: () => void;
    withDefinitions?: boolean;
}

export type CustomToolbarProps = React.PropsWithChildren<Props> &
    PropsFromSlot<GridSlots['toolbar']> &
    Partial<GridToolbarViewButtonProps> &
    Partial<GridToolbarExportButtonProps> &
    Partial<GridToolbarNormSettingsButtonProps>;

export const CustomToolbar = ({
    children,
    endpointUrl,
    exportFileName,
    filterModel,
    onFilterModelChange,
    filterDefinitions,
    gridData,
    sortModel,
    rowCount,
    customQueryParams,
    customExportQueryParams,
    columnVisibilityModel,
    onResetGridSetting,
    onSaveGridSetting,
    selectedCreatives,
    onClickCreativeViewer,
    withDefinitions
}: CustomToolbarProps) => {
    const { setIsKnowledgeCenterOpen, setCurrentAnchor } = useKnowledgeCenter();

    const cvTooltip = useMemo(() => {
        const countOfSelectedCreatives = selectedCreatives?.length;

        if (!countOfSelectedCreatives || countOfSelectedCreatives < 2)
            return 'Select multiple tests to compare.';

        if (countOfSelectedCreatives > creativeViewerMediaLimit)
            return `Maximum ${creativeViewerMediaLimit} test can be compared.`;

        return '';
    }, [selectedCreatives]);

    const handleOpenMetrics = () => {
        setIsKnowledgeCenterOpen(true);
        setCurrentAnchor('metrics');
    };

    return (
        <GridToolbarContainer>
            {onClickCreativeViewer && (
                <Tooltip title={cvTooltip}>
                    <span
                        style={{
                            borderRight: '1px solid #e0e0e0',
                            marginRight: 10
                        }}
                    >
                        <Button
                            sx={{
                                mr: 2,
                                '&.Mui-disabled': {
                                    color: '#AFB9BB'
                                }
                            }}
                            startIcon={
                                <SvgIcon
                                    component={CompareTestsIcon}
                                    inheritViewBox
                                    color='inherit'
                                />
                            }
                            disableRipple
                            disabled={!!cvTooltip}
                            onClick={onClickCreativeViewer}
                        >
                            {'Compare Tests'}
                        </Button>
                    </span>
                </Tooltip>
            )}

            <GridToolbarColumnsButton />

            {onResetGridSetting && onSaveGridSetting && (
                <GridToolbarViewButton
                    onResetGridSetting={onResetGridSetting}
                    onSaveGridSetting={onSaveGridSetting}
                />
            )}

            {endpointUrl && exportFileName && filterModel && sortModel ? (
                <GridToolbarExportButton
                    endpointUrl={endpointUrl}
                    exportFileName={exportFileName}
                    filterModel={filterModel}
                    sortModel={sortModel}
                    rowCount={rowCount}
                    selectedCreatives={selectedCreatives}
                    customQueryParams={customQueryParams}
                    customExportQueryParams={customExportQueryParams}
                    columnVisibilityModel={columnVisibilityModel}
                    isFromCreativeViewer={false}
                />
            ) : (
                <GridToolbarExport
                    printOptions={{ disableToolbarButton: true }}
                    csvOptions={{}}
                />
            )}

            {withDefinitions && (
                <Button
                    startIcon={<InfoIcon />}
                    disableRipple
                    onClick={handleOpenMetrics}
                >
                    {'Definitions'}
                </Button>
            )}

            {children}

            {gridData && (
                <CustomGridFilterPanel
                    gridData={gridData}
                    filterModel={filterModel}
                    onFilterModelChange={onFilterModelChange}
                    filterDefinitions={filterDefinitions}
                />
            )}
        </GridToolbarContainer>
    );
};
