import { Add } from '@mui/icons-material';
import { Box, Button, Menu, MenuItem } from '@mui/material';
import { useState } from 'react';
import { CustomGridFilterDefinition } from '../../interfaces/custom-grid-filter-definition';

interface Props {
    availableFilterDefinitions: CustomGridFilterDefinition<unknown>[];
    onClick: (field: string) => void;
}

const CustomGridAddButton = ({
    availableFilterDefinitions,
    onClick
}: Props) => {
    const [anchorEl, setAnchorEl] = useState(null);

    const handleClick = (event: any) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const handleAdd = (value: string) => () => {
        onClick(value);
        handleClose();
    };

    return (
        <Box>
            <Button startIcon={<Add />} onClick={handleClick}>
                Add new
            </Button>
            <Menu
                anchorEl={anchorEl}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'left'
                }}
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'left'
                }}
                open={Boolean(anchorEl)}
                onClose={handleClose}
                PaperProps={{
                    style: {
                        maxHeight: 300
                    }
                }}
            >
                {availableFilterDefinitions.map(
                    ({ field, label, groupLabel, exposureGroup, isDiff }) => {
                        const displayLabel =
                            groupLabel ||
                            `${
                                exposureGroup === 'inContext'
                                    ? 'In-Context Exposure - '
                                    : exposureGroup === 'focused'
                                      ? 'Focused Exposure - '
                                      : ''
                            }${label}${isDiff ? ' vs Norm' : ''}`;

                        return (
                            <MenuItem
                                key={displayLabel}
                                onClick={handleAdd(field)}
                            >
                                {displayLabel}
                            </MenuItem>
                        );
                    }
                )}
            </Menu>
        </Box>
    );
};

export default CustomGridAddButton;
