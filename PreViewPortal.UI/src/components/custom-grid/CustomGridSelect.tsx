import { Checkbox, ListItem, ListItemText, MenuItem } from '@mui/material';
import { isEmpty } from 'lodash';
import { memo } from 'react';
import { CustomGridFilterProps } from '../../interfaces/custom-grid-filter-props';
import { CustomGridFilterValueType } from '../../interfaces/custom-grid-filter-value-type';
import CustomGridFilterBase from './CustomGridFilterBase';

const CustomGridSelect = (props: CustomGridFilterProps<string>) => {
    const {
        value,
        options,
        filterDefinition: { field, valueToTextMap },
        onValueChange,
        onClearValue
    } = props;

    const handleSelect = (option: string) => () => {
        const isChecked = option !== value;

        if (isChecked) {
            onValueChange(field, option);
        } else {
            onClearValue(field);
        }
    };

    const mapOptionToText = (option: string) =>
        valueToTextMap?.get(option) ?? option;

    const renderText = (value: CustomGridFilterValueType) => {
        if (isEmpty(props.value)) return 'Any';

        return mapOptionToText(value as string);
    };

    const hasValue = !isEmpty(value);

    return (
        <CustomGridFilterBase
            {...props}
            hasValue={hasValue}
            renderText={renderText}
        >
            {options ? (
                options.map(o => (
                    <MenuItem key={o} onClick={handleSelect(o)}>
                        <Checkbox
                            edge='start'
                            checked={value === o}
                            disableRipple
                        />
                        <ListItemText id={o} primary={mapOptionToText(o)} />
                    </MenuItem>
                ))
            ) : (
                <ListItem>
                    <ListItemText>No items</ListItemText>
                </ListItem>
            )}
        </CustomGridFilterBase>
    );
};

export default memo(CustomGridSelect);
