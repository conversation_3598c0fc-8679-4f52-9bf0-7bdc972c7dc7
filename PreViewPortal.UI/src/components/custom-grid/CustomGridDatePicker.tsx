import { Link, Stack } from '@mui/material';
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers-pro';
import { AdapterMoment } from '@mui/x-date-pickers/AdapterMoment';
import { isEmpty, isEqual } from 'lodash';
import moment, { Moment } from 'moment';
import { memo } from 'react';
import { CustomGridFilterProps } from '../../interfaces/custom-grid-filter-props';
import CustomGridFilterBase from './CustomGridFilterBase';

const clientDateFormat = 'DD/MM/YYYY';
const serverDateFormat = 'YYYY-MM-DD';

const CustomGridDatePicker = (props: CustomGridFilterProps<string[]>) => {
    const {
        filterDefinition: { field },
        value,
        options,
        onValueChange
    } = props;

    const minDate = options
        ? moment(options[0], serverDateFormat)
        : moment().subtract(1, 'year');
    const currentDate = moment();
    const defaultRange = [minDate, currentDate];
    const range = value?.map(v => moment(v)) || defaultRange;
    const [from, to] = range;

    const handleChange = (index: number) => (newValue: Moment | null) => {
        const newRange = [...range];

        newRange[index] = moment(newValue);

        onValueChange(
            field,
            newRange.map(d => d.format(serverDateFormat))
        );
    };

    const handleClickOn30Days = (event: React.MouseEvent<HTMLElement>) => {
        event.preventDefault();

        const thirtyDaysAgo = moment().subtract(30, 'days');
        const newRange = [thirtyDaysAgo, currentDate];
        onValueChange(
            field,
            newRange.map(d => d.format(serverDateFormat))
        );
    };

    const handleClickOn3Months = (event: React.MouseEvent<HTMLElement>) => {
        event.preventDefault();

        const threeMonthsAgo = moment().subtract(3, 'months');
        const newRange = [threeMonthsAgo, currentDate];
        onValueChange(
            field,
            newRange.map(d => d.format(serverDateFormat))
        );
    };

    const renderText = () => {
        if (isEmpty(range) || isEqual(range, defaultRange)) return 'Any';

        //prettier-ignore
        if (minDate.diff(from, 'days') === 0 && currentDate.diff(to, 'days') === 0) return 'Any';

        //prettier-ignore
        return `${from.format(clientDateFormat)} - ${to.format(clientDateFormat)}`;
    };

    const hasValue = !isEmpty(value);

    const hasError = !from.isValid() || !to.isValid();

    return (
        <CustomGridFilterBase
            {...props}
            hasValue={hasValue}
            hasError={hasError}
            renderText={renderText}
        >
            <Stack m={4} spacing={2}>
                <LocalizationProvider dateAdapter={AdapterMoment}>
                    <DatePicker
                        label='From'
                        value={from}
                        minDate={minDate}
                        maxDate={to}
                        format={clientDateFormat}
                        slotProps={{ textField: { variant: 'outlined' } }}
                        onChange={handleChange(0)}
                    />
                </LocalizationProvider>
                <LocalizationProvider dateAdapter={AdapterMoment}>
                    <DatePicker
                        label='To'
                        value={to}
                        minDate={from}
                        maxDate={currentDate}
                        format={clientDateFormat}
                        slotProps={{ textField: { variant: 'outlined' } }}
                        onChange={handleChange(1)}
                    />
                </LocalizationProvider>
                <Stack direction='row' justifyContent='space-between'>
                    <Link href='' onClick={handleClickOn30Days}>
                        {'Last 30 days'}
                    </Link>
                    <Link href='' onClick={handleClickOn3Months}>
                        {'Last 3 months'}
                    </Link>
                </Stack>
            </Stack>
        </CustomGridFilterBase>
    );
};

export default memo(CustomGridDatePicker);
