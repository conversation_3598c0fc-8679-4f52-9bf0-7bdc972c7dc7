import ClearIcon from '@mui/icons-material/Clear';
import FilterAltOffIcon from '@mui/icons-material/FilterAltOff';
import { Box, Button, IconButton, Menu, Stack, TextField } from '@mui/material';
import { useState } from 'react';
import { CustomGridFilterProps } from '../../interfaces/custom-grid-filter-props';
import { CustomGridFilterValueType } from '../../interfaces/custom-grid-filter-value-type';

interface Props extends CustomGridFilterProps<CustomGridFilterValueType> {
    hasValue: boolean;
    hasError?: boolean;
    renderText?: (value: CustomGridFilterValueType) => string;
    maxHeight?: number;
}

const CustomGridFilterBase = ({
    value,
    filterDefinition: { field, label, groupLabel, exposureGroup, isDiff },
    hasValue,
    hasError,
    children,
    renderText = () => '',
    onUpdateFilter,
    onClearValue,
    onRemove,
    maxHeight = 300
}: React.PropsWithChildren<Props>) => {
    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);

    const handleClear = (event: React.MouseEvent<HTMLElement>) => {
        event.stopPropagation();
        onClearValue(field);
    };

    const handleClick = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = (): void => {
        setAnchorEl(null);

        if (!hasError) {
            onUpdateFilter();
        }
    };

    const isOpen = Boolean(anchorEl);
    const showClearButton = hasValue ? 'visible' : 'hidden';

    const fieldLabel =
        groupLabel ||
        `${label} ${
            exposureGroup === 'inContext'
                ? '- In-Context Exposure'
                : exposureGroup === 'focused'
                  ? '- Focused Exposure'
                  : ''
        }${isDiff ? ' vs Norm' : ''}`;

    return (
        <Box>
            <TextField
                sx={{
                    '& .MuiOutlinedInput-root': {
                        cursor: 'pointer'
                    },
                    '& .MuiOutlinedInput-input': {
                        cursor: 'pointer'
                    }
                }}
                InputProps={{
                    endAdornment: (
                        <IconButton
                            onClick={handleClear}
                            sx={{ visibility: showClearButton }}
                        >
                            <ClearIcon />
                        </IconButton>
                    )
                }}
                label={fieldLabel}
                focused={isOpen}
                error={hasError}
                value={renderText(value)}
                onClick={handleClick}
            />
            <Menu
                keepMounted
                anchorEl={anchorEl}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'left'
                }}
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'left'
                }}
                PaperProps={{
                    style: {
                        maxHeight: maxHeight,
                        width: 400
                    }
                }}
                open={isOpen}
                onClose={handleClose}
            >
                {children}
                {onRemove && (
                    <Stack m={2} alignItems='end'>
                        <Button
                            startIcon={<FilterAltOffIcon />}
                            onClick={() => onRemove(field)}
                        >
                            Remove filter
                        </Button>
                    </Stack>
                )}
            </Menu>
        </Box>
    );
};

export default CustomGridFilterBase;
