import { Checkbox, CheckboxProps, Radio, SvgIcon } from '@mui/material';
import CheckboxCheckedSvg from '../assets/icons/custom-checkbox-checked.svg?react';
import CheckboxUnCheckedSvg from '../assets/icons/custom-checkbox-unchecked.svg?react';

type Props = CheckboxProps & {
    singleSelection: boolean;
    filled: string;
};

export const CustomSelectionComponent = (props: Props) => {
    const { singleSelection, filled, sx, ...rest } = props;

    const size = props.size !== 'large' ? props.size : 'medium';

    if (singleSelection)
        return (
            <Radio
                {...rest}
                size={size}
                sx={{
                    ...sx,
                    color: filled,
                    height: 36,
                    width: 36,
                    '&.Mui-checked': {
                        color: filled
                    }
                }}
            />
        );

    const checkBoxColor = props.disabled ? undefined : filled;

    return (
        <Checkbox
            {...rest}
            sx={{ ...sx }}
            checkedIcon={
                <SvgIcon
                    component={CheckboxCheckedSvg}
                    sx={{ color: checkBoxColor, fontSize: 18 }}
                    inheritViewBox
                />
            }
            icon={
                <SvgIcon
                    component={CheckboxUnCheckedSvg}
                    sx={{
                        color: checkBoxColor,
                        stroke: filled,
                        fontSize: 18
                    }}
                    inheritViewBox
                />
            }
        />
    );
};
