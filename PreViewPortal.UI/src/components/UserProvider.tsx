import { useEffect, useState } from 'react';
import UserContext from '../contexts/user-context';
import { HubSpotTracking } from '../helpers/hubspot-tracking';
import { useHttp } from '../hooks/use-http';
import { GridSettingsCreateOrUpdateModel } from '../interfaces/grid-settings-create-or-update-model';
import { GridSettingsModel } from '../interfaces/grid-settings-model';
import { PreViewGrid } from '../interfaces/preview-grid';
import { UserInfoModel } from '../interfaces/user-info-model';

export interface UserGridSettingsModel {
    id: PreViewGrid;
    settings: GridSettingsModel;
}

export const UserProvider = ({ children }: React.PropsWithChildren<{}>) => {
    const [user, setUser] = useState<UserInfoModel | null>(null);
    const [gridSettings, setGridSettings] = useState<UserGridSettingsModel[]>(
        []
    );
    const [isUserLoading, setIsUserLoading] = useState(true);
    const [isGridSettingsLoading, setIsGridSettingsLoading] = useState(true);
    const { http } = useHttp();

    useEffect(() => {
        (async () => {
            try {
                const userInfo = await http
                    .get('user/current')
                    .json<UserInfoModel>();

                HubSpotTracking.sendEvent('identify', {
                    email: userInfo.email
                });

                setUser(userInfo);
            } catch  {}

            setIsUserLoading(false);
        })();
    }, [http]);

    useEffect(() => {
        if (!user) return;

        const parseGridSettings = (
            settings: GridSettingsCreateOrUpdateModel[]
        ) =>
            settings.map(s => ({
                id: s.gridId,
                settings: JSON.parse(s.gridSettings) as GridSettingsModel
            }));

        http.get('user/gridSettings')
            .json<GridSettingsCreateOrUpdateModel[]>()
            .then(parseGridSettings)
            .then(setGridSettings)
            .then(() => setIsGridSettingsLoading(false))
            .catch();
    }, [http, user]);

    if (isUserLoading) return null;

    return (
        <UserContext.Provider
            value={{
                user,
                setUser,
                gridSettings,
                setGridSettings,
                isGridSettingsLoading
            }}
        >
            {children}
        </UserContext.Provider>
    );
};
