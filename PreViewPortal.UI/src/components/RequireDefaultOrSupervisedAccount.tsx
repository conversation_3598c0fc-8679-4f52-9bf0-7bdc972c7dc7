import { useSupervisedAccount } from '@/hooks/use-supervised-account';
import { useUser } from '@/hooks/use-user';
import { Navigate } from 'react-router';

export const RequireDefaultOrSupervisedAccount = ({
    children
}: {
    children: React.JSX.Element;
}) => {
    const { supervisedAccount } = useSupervisedAccount();
    const { user } = useUser();

    if (!supervisedAccount && !user?.account) {
        return <Navigate to='/SwitchAccount' />;
    }

    return children;
};
