import { getLabelForProductOptions } from '@/helpers/get-label-for-product-options';
import { useProduct } from '@/hooks/use-product';
import {
    Autocomplete,
    Paper,
    Stack,
    TextField,
    Typography
} from '@mui/material';
import { useUser } from '@/hooks/use-user';
import { ProductOptions } from '@/interfaces/product-options';
import { useState } from 'react';
import { ConfirmPopover } from './ConfirmPopover';
import { GridPanelSelectorButton } from './GridPanelSelectorButton';

export const ProductTypeSelector = () => {
    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);

    const { accountProduct, selectedProduct, setSelectedProduct } =
        useProduct();

    const [selectedProductOnModal, setSelectedProductOnModal] = useState(
        selectedProduct!
    );

    const { isAdmin } = useUser();

    const handleClose = () => {
        setAnchorEl(null);
        setSelectedProductOnModal(selectedProduct!);
    };

    const handleAccept = () => {
        setAnchorEl(null);
        setSelectedProduct(selectedProductOnModal);
    };

    const disabledAccept = selectedProductOnModal === selectedProduct;

    const getProductDescription = (product: ProductOptions) =>
        product === ProductOptions.InContext
            ? 'Viewers engage in multiple ad exposure tasks accompanied by camera measurement complemented by survey questions.'
            : 'Viewers complete an ad exposure task in a neutral video player without options for interaction, no survey questions are asked.';

    return (
        <>
            <Paper sx={{ px: 3, py: 2, width: 450, borderRadius: '4px' }}>
                <Stack gap={2}>
                    <Typography variant='h6'>{'Product Type'}</Typography>

                    <GridPanelSelectorButton
                        label={getLabelForProductOptions(
                            selectedProduct,
                            isAdmin
                        )}
                        onClick={e => setAnchorEl(e.currentTarget)}
                    />

                    <Typography variant='body2'>
                        {getProductDescription(selectedProduct!)}
                    </Typography>
                </Stack>
            </Paper>

            <ConfirmPopover
                anchorEl={anchorEl}
                title='Product Type'
                onClose={handleClose}
                onAccept={handleAccept}
                disabledAccept={disabledAccept}
                width={480}
                body={
                    <>
                        <Autocomplete
                            sx={{ width: 320, pr: 1 }}
                            value={selectedProductOnModal}
                            options={accountProduct}
                            disableClearable
                            getOptionLabel={o =>
                                getLabelForProductOptions(o, isAdmin)
                            }
                            onChange={(_, o) => setSelectedProductOnModal(o!)}
                            renderInput={props => <TextField {...props} />}
                        />

                        <Typography variant='body2' mt={1}>
                            {getProductDescription(selectedProductOnModal)}
                        </Typography>
                    </>
                }
            />
        </>
    );
};
