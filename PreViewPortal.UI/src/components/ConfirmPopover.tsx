import { Box, Button, IconButton, Popover, Typography } from '@mui/material';
import { Close } from '@mui/icons-material';
import { ReactNode } from 'react';

interface Props {
    title: string;
    anchorEl: HTMLElement | null;
    onClose: () => void;
    onAccept: () => void;
    body: ReactNode;
    footer?: ReactNode;
    disabledAccept?: boolean;
    width?: number;
}

export const ConfirmPopover = ({
    title,
    body,
    footer,
    anchorEl,
    disabledAccept,
    width = 450,
    onClose,
    onAccept
}: Props) => (
    <Popover
        open={!!anchorEl}
        anchorEl={anchorEl}
        onClose={onClose}
        disableEscapeKeyDown
        anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'left'
        }}
    >
        <Box width={width} p={2}>
            <Box
                sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                }}
                mb={1}
            >
                <Typography variant='h6'>{title}</Typography>
                <IconButton onClick={onClose} sx={{ p: 0 }} color='primary'>
                    <Close />
                </IconButton>
            </Box>

            <Box>{body}</Box>

            <Box display='flex' alignItems='center' mt={2}>
                {footer && <Box>{footer}</Box>}

                <Box display='flex' justifyContent='end' gap={1} flex={1}>
                    <Button onClick={onClose}>{'Cancel'}</Button>
                    <Button onClick={onAccept} disabled={disabledAccept}>
                        {'Accept'}
                    </Button>
                </Box>
            </Box>
        </Box>
    </Popover>
);
