import { Paper, Stack, Typography } from '@mui/material';
import { ScoringSelector } from './ScoringSelector';
import { useSupervisedAccount } from '@/hooks/use-supervised-account';
import { GridPanelSelectorButton } from './GridPanelSelectorButton';

export const ScoringAndNormsPanel = () => {
    const { supervisedAccount } = useSupervisedAccount();

    //TODO: Only temporary
    const areCustomNorms = supervisedAccount?.id === 65;

    const normType = areCustomNorms ? 'Personalized Norms' : 'Default Norms';

    const description = areCustomNorms
        ? "‘Food & Confectionery' and 'Pet Care' category-based country norms applied by default. Norms fall back to regional general level when <50 ads are available."
        : 'Default automated norms ensure that each ad receives norms for meaningful result analysis. Review norm details when comparing a variety of tests.';

    return (
        <Paper
            sx={{
                px: 3,
                py: 2,
                borderRadius: '4px',
                display: 'flex',
                flex: 1,
                gap: 2
            }}
        >
            <ScoringSelector />

            <Stack maxWidth={380}>
                <Typography variant='body2' fontStyle='italic' pl={3} mb={2}>
                    {'Norms Preferences'}
                </Typography>

                <Stack gap={2} borderLeft='1.5px dashed #AFB9BB' py={1} pl={3}>
                    <GridPanelSelectorButton
                        label={normType}
                        withoutIcon
                        disabled
                    />

                    <Typography variant='body2'>{description}</Typography>
                </Stack>
            </Stack>
        </Paper>
    );
};
