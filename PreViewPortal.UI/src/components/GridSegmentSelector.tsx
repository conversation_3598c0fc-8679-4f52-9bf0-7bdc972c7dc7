import { Autocomplete, Paper, TextField, Typography } from '@mui/material';
import { useEffect, useMemo, useState } from 'react';
import { useHttp } from '@/hooks/use-http';
import { SegmentKeyLabel } from '@/interfaces/segment-key-label';
import { fullSegment } from '@/constant/full-segment';
import { ConfirmPopover } from './ConfirmPopover';
import { GridPanelSelectorButton } from './GridPanelSelectorButton';

interface Props {
    endpointUrl: string;
    isLoading?: boolean;
    selectedSegment: SegmentKeyLabel;
    onSelectedSegmentChange: (option: SegmentKeyLabel) => void;
}

export const GridSegmentSelector = ({
    endpointUrl,
    isLoading,
    selectedSegment,
    onSelectedSegmentChange
}: Props) => {
    const [segmentsData, setSegmentsData] = useState<SegmentKeyLabel[]>();

    const [selectedSegmentOnModal, setSelectedSegmentOnModal] =
        useState(selectedSegment);

    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);

    const segmentOptions = useMemo(
        () => [fullSegment, ...(segmentsData || [])],
        [segmentsData]
    );

    const { http, abort } = useHttp();

    const { question, answer } = selectedSegment;

    const selectedSegmentLabel = question ? `${question}: ${answer}` : answer;

    useEffect(() => {
        http.get(`${endpointUrl}/mediaSegments`)
            .json<SegmentKeyLabel[]>()
            .then(setSegmentsData);

        return () => abort();
    }, [abort, endpointUrl, http]);

    const handleClose = () => {
        setAnchorEl(null);
        setSelectedSegmentOnModal(selectedSegment);
    };

    const handleAccept = () => {
        setAnchorEl(null);
        onSelectedSegmentChange(selectedSegmentOnModal);
    };

    const disabledAccept =
        selectedSegmentOnModal.segmentKey === selectedSegment.segmentKey;

    const disabledSelect = !segmentsData || isLoading;

    return (
        <>
            <Paper
                sx={{
                    px: 3,
                    py: 2,
                    borderRadius: '4px',
                    display: 'flex',
                    alignItems: 'center'
                }}
            >
                <Typography variant='h6' pr={4}>
                    {'Audience'}
                </Typography>

                <GridPanelSelectorButton
                    label={selectedSegmentLabel}
                    disabled={disabledSelect}
                    onClick={e => setAnchorEl(e.currentTarget)}
                />
            </Paper>

            <ConfirmPopover
                anchorEl={anchorEl}
                title='Audience'
                onClose={handleClose}
                onAccept={handleAccept}
                disabledAccept={disabledAccept}
                body={
                    <>
                        <Autocomplete
                            sx={{
                                width: 380,
                                pr: 1,
                                '& .MuiInputBase-root': {
                                    paddingRight: '0px !important'
                                }
                            }}
                            value={[selectedSegmentOnModal]}
                            disabled={disabledSelect}
                            options={segmentOptions}
                            disableClearable
                            multiple={true}
                            getOptionLabel={o => o.answer}
                            groupBy={o => o.question}
                            onChange={(_, o) =>
                                o.length && setSelectedSegmentOnModal(o.at(-1)!)
                            }
                            renderTags={() => {
                                const { question, answer } =
                                    selectedSegmentOnModal;
                                return question
                                    ? `${question}: ${answer}`
                                    : answer;
                            }}
                            renderInput={props => {
                                return (
                                    <TextField
                                        {...props}
                                        sx={{ caretColor: 'transparent' }}
                                    />
                                );
                            }}
                        />

                        <Typography variant='body2' mt={2}>
                            After segmentation:
                        </Typography>
                        <Typography component='ul' sx={{ pl: 2, mt: 0.5 }}>
                            <li style={{ marginLeft: '1rem' }}>
                                <Typography variant='body2'>
                                    Metrics (and norms) will be updated.
                                </Typography>
                            </li>
                            <li style={{ marginLeft: '1rem' }}>
                                <Typography variant='body2'>
                                    Only tests with sufficient view counts will
                                    be shown.
                                </Typography>
                            </li>
                        </Typography>
                    </>
                }
            />
        </>
    );
};
