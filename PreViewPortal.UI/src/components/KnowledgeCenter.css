.KnowledgeCenter h5 {
    visibility: hidden;
    position: fixed;
    scroll-margin-top: 15em;
}

.KnowledgeCenter summary:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.KnowledgeCenter details > details {
  margin: 10px 0 10px 40px;
}

.KnowledgeCenter details > p {
  text-align: justify;
  margin-left: 40px;
  margin-right: 40px;
}

.KnowledgeCenter details > summary {
  cursor: pointer;
  padding: 12px;
  list-style: none;
  display: flex;
  font-weight: bold;
  user-select: none;
}

.KnowledgeCenter summary::-webkit-details-marker {
  display: none;
}

.KnowledgeCenter a {
  color:#2DC0A2;
  text-decoration-color: #2DC0A2;
}

.KnowledgeCenter details[open] > summary {
  background-color:#2DC0A2;
  color: white;
}

.KnowledgeCenter summary::before {
  content: url("data:image/svg+xml,<svg width='13' height='8' viewBox='0 0 13 8' fill='none' xmlns='http://www.w3.org/2000/svg'><path d='M6.5 8L12.5 2L11.09 0.59L6.5 5.17L1.91 0.59L0.5 2L6.5 8Z' fill='black'/></svg>");
  padding-right: 15px;
}

.KnowledgeCenter details[open] > summary::before {
  content: url("data:image/svg+xml, <svg width='12' height='8' viewBox='0 0 12 8' fill='none' xmlns='http://www.w3.org/2000/svg'><path d='M1.41 7.41016L6 2.83016L10.59 7.41016L12 6.00016L6 0.000156403L0 6.00016L1.41 7.41016Z' fill='white'/></svg>");
}