import { useEffect } from 'react';
import {
    Navigate,
    Route,
    Routes as ReactRouterRoutes,
    useLocation
} from 'react-router';
import { HubSpotTracking } from '../helpers/hubspot-tracking';
import CreativePerformancePage from '../pages/CreativePerformancePage';
import CreativeSharePage from '../pages/CreativeSharePage';
import DesignPage from '../pages/DesignPage';
import ExpiredPage from '../pages/ExpiredPage';
import NotfoundPage from '../pages/NotfoundPage';
import SwitchAccountPage from '../pages/SwitchAccountPage';
import UnauthorizedPage from '../pages/UnauthorizedPage';
import Layout from './Layout';
import { RequireAdminAuth } from './RequireAdminAuth';
import { RequireAuth } from './RequireAuth';
import { RequireDefaultOrSupervisedAccount } from './RequireDefaultOrSupervisedAccount';
import { RequireSelectableAccount } from './RequireSelectableAccount';

const Routes = () => {
    const location = useLocation();

    useEffect(() => {
        HubSpotTracking.sendEvent('setPath', location.pathname);
        HubSpotTracking.sendEvent('trackPageView');
    }, [location]);

    return (
        <ReactRouterRoutes>
            <Route
                path='/Design'
                element={
                    <RequireAdminAuth>
                        <Layout>
                            <DesignPage />
                        </Layout>
                    </RequireAdminAuth>
                }
            />
            <Route
                path='/SwitchAccount'
                element={
                    <RequireAuth>
                        <RequireSelectableAccount>
                            <Layout>
                                <SwitchAccountPage />
                            </Layout>
                        </RequireSelectableAccount>
                    </RequireAuth>
                }
            />

            <Route
                path='/CreativePerformance'
                element={
                    <RequireAuth>
                        <RequireDefaultOrSupervisedAccount>
                            <Layout pt={2}>
                                <CreativePerformancePage />
                            </Layout>
                        </RequireDefaultOrSupervisedAccount>
                    </RequireAuth>
                }
            />
            <Route path='/Creative/Share' element={<CreativeSharePage />} />
            <Route path='/Unauthorized' element={<UnauthorizedPage />} />
            <Route path='/ExpiredPage' element={<ExpiredPage />} />
            <Route path='/Notfound' element={<NotfoundPage />} />
            <Route
                path='/Home'
                element={<Navigate to='/CreativePerformance' />}
            />
            <Route path='/' element={<Navigate to='/CreativePerformance' />} />
            <Route path='*' element={<Navigate to='/Notfound' />} />
        </ReactRouterRoutes>
    );
};

export default Routes;
