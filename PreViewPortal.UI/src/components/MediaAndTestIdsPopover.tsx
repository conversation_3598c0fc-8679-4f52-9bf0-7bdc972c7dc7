import { ExposureLabel } from '@/interfaces/exposure-label';
import { Popover, PopoverProps, Stack, Typography } from '@mui/material';
import { CopyRow } from './CopyRow';

interface Props {
    sourceMediaID: number;
    sourceMedia?: string;
    isAdmin?: boolean;
    inFocusTestID: number;
    inContextTestID?: number;
    surveyKeyAlias: string;
    anchorEl: PopoverProps['anchorEl'];
    onClose: () => void;
}

export const MediaAndTestIdsPopover = ({
    anchorEl,
    isAdmin = true,
    onClose,
    surveyKeyAlias,
    sourceMedia,
    sourceMediaID,
    inFocusTestID,
    inContextTestID
}: Props) => (
    <Popover
        open={!!anchorEl}
        onClose={onClose}
        anchorEl={anchorEl}
        anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'center'
        }}
    >
        <Stack
            bgcolor='#273235'
            borderRadius='5px'
            p={2}
            gap={1}
            sx={{
                color: 'white'
            }}
        >
            {sourceMedia && (
                <Typography variant='body2' maxWidth={400} color='inherit'>
                    {sourceMedia}
                </Typography>
            )}

            {isAdmin && (
                <>
                    <CopyRow
                        label='Source Media ID'
                        value={sourceMediaID}
                        onCopy={onClose}
                    />
                    {inContextTestID && (
                        <CopyRow
                            label={`Test ID (${ExposureLabel.InContext})`}
                            value={inContextTestID}
                            onCopy={onClose}
                        />
                    )}
                    <CopyRow
                        label={`Test ID (${ExposureLabel.Focused})`}
                        value={inFocusTestID}
                        onCopy={onClose}
                    />
                    <CopyRow
                        label={`Project reference`}
                        value={surveyKeyAlias}
                        onCopy={onClose}
                    />
                </>
            )}
        </Stack>
    </Popover>
);
