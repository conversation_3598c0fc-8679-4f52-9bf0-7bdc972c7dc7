import React, { useState } from 'react';
import { NormTooltipContext } from '@/contexts/norm-tooltip-context';

export const NormTooltipProvider = ({
    children
}: React.PropsWithChildren<{}>) => {
    const [showDetails, setShowDetails] = useState<boolean>(false);
    const [showFullNormData, setShowFullNormData] = useState<boolean>(false);

    return (
        <NormTooltipContext.Provider
            value={{
                showDetails,
                setShowDetails,
                showFullNormData,
                setShowFullNormData
            }}
        >
            {children}
        </NormTooltipContext.Provider>
    );
};
