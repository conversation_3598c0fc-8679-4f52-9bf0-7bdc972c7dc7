import { useState } from 'react';
import { NormSettingsMode } from '../contexts/user-context';
import { defaultCurveTypeYAxisGroups } from '@/constant/default-curve-type-y-axis-groups';
import { defaultExposureGroupStlye } from '@/constant/default-exposure-group-style';
import ChartSettingsContext from '@/contexts/chart-settings-context';

export const ChartSettingsProvider = ({
    children
}: React.PropsWithChildren<{}>) => {
    const [curveTypeYAxisGroups, setCurveTypeYAxisGroups] = useState(
        defaultCurveTypeYAxisGroups
    );
    const [exposureGroupStyles, setExposureGroupStyles] = useState(
        defaultExposureGroupStlye
    );
    const [showLegendBar, setShowLegendBar] = useState(true);
    const [showAxisMarkers, setShowAxisMarkers] = useState(true);
    const [normSettingsMode, setNormSettingsMode] =
        useState<NormSettingsMode>('hovering');
    const [showNormDetails, setShowNormDetails] = useState(true);
    const [smoothingLevel, setSmoothingLevel] = useState(0);

    return (
        <ChartSettingsContext.Provider
            value={{
                curveTypeYAxisGroups,
                setCurveTypeYAxisGroups,
                exposureGroupStyles,
                setExposureGroupStyles,
                showLegendBar,
                setShowLegendBar,
                showAxisMarkers,
                setShowAxisMarkers,
                normSettingsMode,
                setNormSettingsMode,
                showNormDetails,
                setShowNormDetails,
                smoothingLevel,
                setSmoothingLevel
            }}
        >
            {children}
        </ChartSettingsContext.Provider>
    );
};
