import ViewComfyIcon from '@mui/icons-material/ViewComfy';
import {
    Box,
    Button,
    Divider,
    Popover,
    Stack,
    Typography
} from '@mui/material';
import React, { useState } from 'react';

export interface GridToolbarViewButtonProps {
    onResetGridSetting: () => void;
    onSaveGridSetting: () => void;
}

export const GridToolbarViewButton = ({
    onResetGridSetting,
    onSaveGridSetting
}: GridToolbarViewButtonProps) => {
    const [viewAnchorEl, setViewAnchorEl] = useState<HTMLButtonElement | null>(
        null
    );

    return (
        <>
            <Button
                onClick={event => setViewAnchorEl(event.currentTarget)}
                startIcon={<ViewComfyIcon />}
            >
                View
            </Button>
            <Popover
                open={!!viewAnchorEl}
                onClose={() => setViewAnchorEl(null)}
                anchorEl={viewAnchorEl}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'left'
                }}
            >
                <Box width={450}>
                    <Typography my={4} ml={2} variant='body2'>
                        Sorting, order and visibility of columns are going to be
                        saved.
                    </Typography>
                    <Divider />
                    <Stack
                        m={2}
                        flexDirection='row'
                        justifyContent='end'
                        gap={3}
                    >
                        <Button
                            onClick={() => {
                                setViewAnchorEl(null);
                                onResetGridSetting();
                            }}
                        >
                            Reset view
                        </Button>
                        <Button
                            variant='contained'
                            onClick={() => {
                                setViewAnchorEl(null);
                                onSaveGridSetting();
                            }}
                        >
                            Save current view
                        </Button>
                    </Stack>
                </Box>
            </Popover>
        </>
    );
};
