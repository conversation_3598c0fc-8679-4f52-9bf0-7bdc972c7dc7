import {
    <PERSON>,
    Button,
    Divider,
    Pop<PERSON>,
    Stack,
    SvgIcon,
    Switch,
    Typography
} from '@mui/material';
import { useState } from 'react';
import EditNormSvg from '@/assets/icons/edit-norm-icon.svg?react';

export interface GridToolbarNormSettingsButtonProps {
    showNormTooltip: boolean;
    onShowNormTooltipChange: (value: boolean) => void;
}

export const GridToolbarNormSettingsButton = ({
    showNormTooltip,
    onShowNormTooltipChange
}: GridToolbarNormSettingsButtonProps) => {
    const [viewAnchorEl, setViewAnchorEl] = useState<HTMLButtonElement | null>(
        null
    );

    return (
        <>
            <Button
                onClick={e => setViewAnchorEl(e.currentTarget)}
                startIcon={
                    <SvgIcon
                        component={EditNormSvg}
                        inheritViewBox
                        color='inherit'
                    />
                }
                disableRipple
            >
                {'Norm Settings'}
            </Button>

            <Popover
                open={!!viewAnchorEl}
                onClose={() => setViewAnchorEl(null)}
                anchorEl={viewAnchorEl}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'left'
                }}
            >
                <Box width={450}>
                    <Typography variant='h6' m={2}>
                        {'Norm Settings'}
                    </Typography>
                    <Divider />
                    <Stack
                        m={2}
                        flexDirection='row'
                        gap={4}
                        alignItems='center'
                    >
                        <Typography>
                            {'Show norm details when hovering on metrics'}
                        </Typography>
                        <Switch
                            checked={showNormTooltip}
                            onChange={(_, checked) =>
                                onShowNormTooltipChange(checked)
                            }
                        />
                    </Stack>
                </Box>
            </Popover>
        </>
    );
};
