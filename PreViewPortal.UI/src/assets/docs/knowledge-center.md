<details id='read-me'>
<summary>Read Me</summary>

##### Read Me

<details id='realeyes-and-preview-creative-testing'>
<summary>Realeyes & PreView Creative Testing</summary>

##### Realeyes & PreView Creative Testing

Realeyes leverages advanced computer vision and AI to analyze human attention and emotional responses to video content. Our solutions empower advertisers to optimize creative effectiveness and maximize audience engagement.

PreView is Realeyes’ innovative testing platform, designed to assess the performance of video ads. It combines AI-powered attention and reaction analysis with surveys and benchmarks to deliver actionable insights for boosting ad impact.

</details>

<details id='realeyes-knowledge-center'>
<summary>Realeyes Knowledge Center</summary>

##### Realeyes Knowledge Center

This documentation hub is your go-to resource for understanding and leveraging Realeyes’ PreView creative testing platform. It includes detailed guides on using Realeyes' tools, interpreting data, and optimizing creative effectiveness through metrics, benchmarks, and insights.

</details>

<details id='support'>
<summary>Support</summary>

##### Support

If you find this knowledge base insufficient for your needs, please feel free to contact our support team via email for personalized support and further assistance.

</details>

<details id='newsletter'>
<summary>Newsletter</summary>

##### Newsletter

Subscribe to our periodic product newsletter on our <a href="https://www.realeyesit.com/" target="_blank">website</a> to stay informed about the latest updates in our dashboard capabilities, ongoing product developments, and upcoming features.

</details>

</details>

<details id='preview-creative-tests'>
<summary>PreView Creative Tests</summary>

##### PreView Creative Tests

<details id='preview-introduction'>
<summary>PreView Introduction</summary>

##### PreView Introduction

PreView is Realeyes’ advanced creative testing platform designed to assess the effectiveness of video ads. It provides pre-market evaluations of ad performance using AI-driven attention and reaction analysis, combined with survey insights and benchmarks, to predict brand and sales outcomes. Tailored for CPG advertisers with offline sales, PreView enables optimization where direct sales funnel data may be limited. The platform offers flexibility with multiple product options, including panel-based testing, self-service integrations, and predictive AI tools for evaluating ad performance without human respondents.

</details>

<details id='preview-testing-capabilities'>
<summary>PreView Testing Capabilities
</summary>

##### PreView Testing Capabilities

1. **Measurement Integration**: Embed Realeyes’ measurement services directly into your platform for seamless, self-serve programmatic ad testing. This option allows you to control the respondent experience entirely, ensuring privacy for tested assets if required.  
   
2. **URL Redirects**: Incorporate simple redirect links into your workflows to direct respondents to Realeyes’ testing platform. Camera-based exposures are completed there, and respondents are then returned to your system, enabling data matching and integrated reporting.  
   
3. **Managed Panel Testing**: Leverage Realeyes’ managed service to collect respondent data from third-party panel sources across 90+ countries. This option provides end-to-end human panel testing with minimal client-side effort, with advanced audience targeting and survey data collection advantages.  
   
4. **Synthetic Testing**: Conduct synthetic creative evaluations without human participants. Using AI models trained on Realeyes' historic dataset, this method predicts attention and reaction outcomes, offering quick and scalable insights.

</details>

<details id='preview-camera-exposure-types'>
<summary>PreView Camera Exposure Types
</summary>

##### PreView Camera Exposure Types

<details id='focused-exposures'>
<summary>Focused Exposures</summary>

##### Focused Exposures

Focused (or forced) exposure places respondents in a controlled environment, ensuring they are fully aware they are part of a test and directly exposed to the creative outside its natural context. This method excels in generating rich reactional data, as it eliminates contextual distractions. However, the absence of real-world environmental factors can make attention curve analyses less reflective of organic viewing behaviors. Focused exposure testing is often used as a TV testing proxy where user interactions are limited.

[PreView Focused Exposure Tests](#focused-exposure-product-type), [PreView In-Context Tests](#in-context-product-type)   

</details>

<details id='in-context-exposures'>
<summary>In-Context Exposures</summary>

##### In-Context Exposures

PreView In-Context product type offers a combined testing methodology in which respondents are exposed to the target advertisement twice. The first exposure occurs within a simulated media platform context provided by our tech partner, Eye Square, allowing respondents to freely explore the platform without being forced to any content. However, this freedom may result in respondents spending little to no time with the creatives configured for testing. To ensure sufficient facial data yield for actionable conclusions, a second focused exposure is conducted where respondents are asked to watch the target media. The privacy-safe attention and facial expression data collected from these two tasks are then reported alongside various survey metrics.

[PreView In-Context Tests](#in-context-product-type), [Supported Ad Formats](#supported-ad-formats)   

</details>

</details>

<details id='preview-panel-testing-product-types'>
<summary>PreView Panel Testing Product Types</summary>

##### PreView Panel Testing Product Types

<details id='focused-exposure-product-type'>
<summary>Focused Exposure Product Type</summary>

##### Focused Exposure Product Type

PreView Focused Exposure tests evaluate creative performance in a controlled environment. Tests can be configured to capture facial engagement metrics without the need for survey data using a Realeyes branded video player. However, the product also offers survey integration possibilities, allowing advertisers to explore brand outcomes, recall, or purchase intent. The advantage of this human panel test type is that it is quick and more cost effective than in-context tests.

[Surveys: Focused Exposure Product Type](#surveys-focused-exposure-product-type)

</details>

<details id='in-context-product-type'>
<summary>In-Context Product Type</summary>

##### In-Context Product Type

PreView In-Context tests simulate real-world media platform experiences and assess ad effectiveness both in-context and in controlled settings. First, the ad is displayed in a simulated media platform context. Attention and distraction data measure its ability to break through distractions. This phase concludes with recognition surveys evaluating whether the ad stood out in the context. Second, the ad is reintroduced in a distraction-free setting to capture a complete dataset. Perception-related survey questions assess how reactions translated into key brand or sales outcomes. Customization options include additional survey questions and audience qualifiers to align tests with specific advertiser objectives.

[Supported Ad Formats](#supported-ad-formats), [Surveys: In-Context Product Type](#surveys-in-context-product-type)   

</details>

</details>

<details id='supported-ad-formats'>
<summary>Supported Ad Formats</summary>

##### Supported Ad Formats

PreView In-Context supports ad testing using the most popular ad formats available on YouTube, Facebook, Instagram, TikTok, Snapchat and X. New product capabilities are added on a continuous basis, visit our <a href="https://www.realeyesit.com/" target="_blank">website</a> or reach out to our representatives to learn about the most up-to-date ad format list.

</details>

</details>


<details id='frequently-asked-questions'>
<summary>Frequently Asked Questions</summary>

##### Frequently Asked Questions

<details id='who-can-access-the-dashboard'>
<summary>Who can access the dashboard?</summary>

##### Who can access the dashboard?

New user accounts can be created on our <a href="https://www.realeyesit.com/" target="_blank">website</a>. By default, new users are granted access to the Demo Account, showcasing example results from public tests. To access specific customer accounts, reach out to the Realeyes team. Users may need to log out and back in to see updates after their roles are adjusted.

</details>

<details id='how-to-switch-between-dashboard-accounts'>
<summary>How to switch between dashboard accounts?</summary>

##### How to switch between dashboard accounts?

The dropdown menu in the top-right corner of the dashboard allows you to select from the accounts linked to your profile. If the desired account is not listed, contact the account administrator or the Realeyes team for assistance.

</details>

<details id='how-to-order-new-creative-tests'>
<summary>How to order new creative tests?</summary>

##### How to order new creative tests?

The dashboard does not currently offer an ordering feature. New tests can be requested via email or dedicated order forms. For tests without survey questions (camera exposure only), self-service options are available through our ordering API. Read more in our <a href="https://preview-api.realeyesit.com" target="_blank">API documentation</a>.

</details>

<details id='when-will-my-test-results-appear-on-the-dashboard'>
<summary>When will my test results appear on the dashboard?</summary>

##### When will my test results appear on the dashboard?

To ensure data accuracy, the Realeyes operations team reviews and verifies collected data before it is displayed. Once approved, the team approves the data for live dashboard access, typically making it available shortly after verification.

</details>

<details id='getting-started-with-your-data-analysis'>
<summary>Getting started with your data analysis</summary>

##### Getting started with your data analysis

Once your test data has been approved, it will be ready for analysis on the [Creative Performance](#creative-performance-page) page. This central hub is designed to help you explore your ad-level insights effectively and tailor the view to your needs. Simply locate your tests in the table, select one or multiple rows, and open [Creative Viewer](#creative-viewer-page) for a detailed diagnostic analysis or comparison.

<details id='creative-performance-page'>
<summary>Creative Performance Page</summary>

##### Creative Performance Page

<details id='customer-accounts'>
<summary>Customer Accounts</summary>

##### Customer Accounts

Your completed tests are displayed by Customer Account, ensuring you only see relevant data. Verify you’re in the correct account by checking the account name in the top-right corner. Tests are grouped by [Product Type](#preview-panel-testing-product-types), making it easier to compare creatives tested under similar conditions.

</details>

<details id='audience-segmentation'>
<summary>Audience Segmentation</summary>

##### Audience Segmentation

By default, the table reflects results for the Total Audience, which includes all respondents in your project. Audience segments are customizable, allowing you to filter data based on specific subgroups. However, tests with a very low number of respondents in a chosen segment will be automatically excluded to maintain meaningful analysis. Please note that this segmentation only applies to the dataset collected during the test and does not account for panel-side recruitment preferences or filtering criteria.

</details>

<details id='optimized-table-interactions'>
<summary>Optimized Table Interactions</summary>

##### Optimized Table Interactions

The test history table offers rich, Excel-like functionality for sorting and customizing data:

>* **Sort and Pin Columns**: Use column headers to organize data. Hold ‘CTRL’ while clicking headers to apply multi-level sorting. Quickly access metrics descriptions.  
>* **Column Visibility**: Adjust which metrics appear in the table to focus on relevant data.  
>* **Views**: Store your column preferences for a consistent setup (one view can be saved specifically for the user account).  
>* **Norms Settings**: Toggle benchmark sources on or off for comparative analysis.  
>* **Export Data**: Export up to 500 tests at once in XLS format, with options to include total audience or all segments. Progress for larger exports is displayed at the top of the page.  
>* **Quality Score Review**: Understand how the overall composite score is calculated for your selected tests.

</details>

<details id='in-depth-creative-insights'>
<summary>In-Depth Creative Insights</summary>

##### In-Depth Creative Insights

For a deeper dive into audience reactions, access the [Creative Viewer](#creative-viewer-page) mode. You can enable this mode directly or open it by clicking on one or multiple specific test rows. This view allows you to explore specific creative reactions in granular detail with second-by-second charting.

</details>

</details>

<details id='creative-viewer-page'>
<summary>Creative Viewer Page</summary>

##### Creative Viewer Page

The Creative Viewer allows advertisers to perform detailed diagnostics of their ads, identifying both successful and underperforming moments. Up to five creatives can be compared simultaneously, enabling clear identification of performance trends.

<details id='video-players'>
<summary>Video Players</summary>

##### Video Players

Review your creatives in full or minimized screen mode alongside relevant meta-data.

</details>

<details id='chart-area'>
<summary>Chart Area</summary>

##### Chart Area

The interactive Chart Area provides second-by-second diagnostics synchronized with the video player.

>* Data interaction: Click on a specific data point to jump to that moment in the video or hover to highlight relevant metrics.  
>* Data plotting: Use the time-series data selector to chart attention or reaction metrics from any test exposure. Use the primary or secondary axes to plot the data following your preferences (right or left axes). Multiple metrics can be selected for one creative, or one metric for multiple creatives.

</details>

<details id='chart-settings'>
<summary>Chart Settings</summary>

##### Chart Settings

Configure chart visuals and benchmarks through intuitive tools:

>* Add benchmark time-series data and view norm source explanations.  
>* Customize axes, apply line smoothing, or display chart legends.  
>* Export data to XLS or generate a public sharing link for stakeholders without dashboard access. Links can be revoked at any time by pressing the same button again.

</details>

<details id='chart-segmentation'>
<summary>Chart Segmentation</summary>

##### Chart Segmentation

Segment data to uncover nuanced insights:

>* Break down results by demographics (age, gender, generations).  
>* Break down results by survey responses (for instance to explore whether attention or reactions resulted in better recognition or perception outcomes).  
>* Custom segments can be arranged with operational support.

Only one segmentation dimension is active at a time to maintain sufficient sample size for meaningful analysis. When analyzing multiple creatives, only one group in one segmentation dimension (e.g. Female under Gender) can be actively plotted \- export the data if you require data visualizations with multiple ads and segments.  

</details>

</details>

</details>

</details>

<details id='metrics'>
<summary>Metrics</summary>

##### Metrics

<details id='metrics-types'>
<summary>Metrics Types</summary>

##### Metrics Types

<details id='survey-responses'>
<summary>Survey Responses</summary>

##### Survey Responses

Survey questions assess how well an advertisement impacts respondents, providing insights into outcomes like emotional response, brand trust, or persuasion. Standardized questions are benchmarked for performance comparison, but surveys can be customized to meet specific campaign needs. Results are best interpreted alongside attention and reaction metrics to explain how outcomes were achieved.

Read more in [Survey Metrics](#survey-metrics).

</details>

<details id='attention-reactions'>
<summary>Attention & Reactions</summary>

##### Attention & Reactions

These metrics derive from computer vision classifiers processing frame-level data from facial recordings. They are foundational for deep-dive analysis, enabling advanced statistical studies, clustering, correlations, or custom norm development. Delivered as granular signals, they can also be aggregated into actionable metrics or indexed for tailored performance scoring. When it comes to reactions, some composite reaction scores are available to see aggregated effect of individual reactions.

Attention Signals: [Attention](#attention), [Distraction](#distraction)

Reaction Signals: [Happiness](#happiness), [Surprise](#surprise), [Confusion](#confusion), [Contempt](#contempt), [Disgust](#disgust)

Read about [Aggregated Reaction Scores](#aggregated-reaction-scores) to combine multiple signals into one score.

</details>

<details id='aggregated-reaction-scores'>
<summary>Aggregated Reaction Scores</summary>

##### Aggregated Reaction Scores

Aggregated reaction scores summarize the collective emotional responses from viewers, providing a comprehensive view of how an audience interacts with a creative. Metrics like [Reactions](#reactions) combine all facial reactions into a single metric to indicate overall engagement. [Negativity](#negativity) captures the peak combined levels of negative emotions such as confusion, contempt, and disgust during ad exposure.

</details>

<details id='benchmarks-norms'>
<summary>Benchmarks (Norms)</summary>

##### Benchmarks (Norms)

Realeyes’ benchmarks are based on a robust historical dataset of creative tests. They offer performance references using six dimensions: device type, ad duration, creative type, exposure type, ad format, and region. Flexible configurations are available to align benchmarks with specific country or industry needs, ensuring precise performance evaluations \- please reach out to Realeyes representatives to explore the options in detail.

Read more in [Benchmarks](#benchmarks).

</details>

<details id='ranks-indexes'>
<summary>Ranks (Indexes)</summary>

##### Ranks (Indexes)

Ranks enable straightforward comparisons for normalized metrics, such as attention, reactions, or survey scores. Using a simple 1-10 scale, ranks highlight how a metric performs against norms. They are ideal for creating custom composite scores or quick comparative insights across multiple campaigns, for instance when including creatives of different duration.

</details>

<details id='metric-types-composite-scores'>
<summary>Composite Scores</summary>

##### Composite Scores

Composite scores are aggregated metrics designed to provide a single, interpretable measure of creative performance or audience response. These scores combine various metrics using specific weighting or selection methods tailored to the intended purpose.

For example: [Quality Score](#quality-score) evaluates overall creative effectiveness by weighing multiple metrics like attention, reactions and survey outcomes.

Composite scores also support full customization, allowing advertisers to configure their own scores by using [Ranks](#ranks-indexes) to prioritize and weigh preferred metrics.

</details>

</details>

<details id='composite-scores'>
<summary>Composite Scores</summary>

##### Composite Scores

<details id='quality-score'>
<summary>Quality Score</summary>

##### Quality Score

Quality Score is a composite metric (1–100 scale) designed to evaluate creative effectiveness. PreView includes default formulas tailored for different product types, which can be accessed via the "Quality Score" button on the Creative Performance page for transparency.

Recognizing the unique nature of each advertisement \- shaped by brand objectives, audience, platform, and campaign goals \- the system supports the customization of composite scores to better align with individual needs.

For advanced analysis, export your data and use the [Ranks](#ranks-indexes) from your chosen metrics to develop a tailored composite score. In 2025, dashboard enhancements will enable direct customization of Quality Scores.

[Ranks](#ranks-indexes), [Creative Performance](#creative-performance-page) 

</details>

</details>

<details id='survey-metrics'>
<summary>Survey Metrics</summary>

##### Survey Metrics

Review the [Product Types](#preview-panel-testing-product-types) section to read about which creative testing methodologies are supported by PreView. Depending on your selected product type, survey questions may be asked during the respondent experience before or after the camera exposure(s). Some survey questions are asked on a standardized basis to ensure benchmark availability, while other questions can be customized considering your individual test needs.

<details id='surveys-in-context-product-type'>
<summary>Surveys: In-Context Product Type</summary>

##### Surveys: In-Context Product Type

This section summarizes survey capabilities when using [PreView In-Context Product Type](#in-context-product-type).

<details id='in-context-survey-platform-usage'>
<summary>Platform Usage</summary>

##### Platform Usage

Before in-context camera exposures, qualifies respondents based on how often they use the given media platform:

>*“How often do you use \[media platform name\]?*

>* *Never*  
>* *I have an account but rarely use it*  
>* *Occasionally*  
>* *A few times a week*  
>* *Nearly every day or more”*

Secondly, except for those respondents who never use the given platform, an additional question is asked to assess their underlying platform usage motivation:

>*“I use [media platform name] to:*

>* *Express myself*  
>* *Check on friends and relatives*  
>* *Communicate*  
>* *Advance my career*  
>* *Kill time*  
>* *Escape worries*  
>* *Be entertained with low effort*  
>* *Learn things*  
>* *See attractive humans*  
>* *See cute animals*  
>* *See things I’ve never seen*  
>* *Check on the latest controversies*  
>* *Get a laugh*  
>* *Relax*  
>* *Keep up on things*  
>* *Not miss out on anything*  
>* *Other”*

</details>

<details id='in-context-survey-brand-recognition'>
<summary>Brand Recognition</summary>

##### Brand Recognition

Measures the ad’s ability to break through and leave a lasting impression on viewers, assessed by linking attention to short-term recall. After viewing the ad in a realistic context, respondents are shown a randomized list of 12 brand logos and asked:

>*“Have you seen an ad for any of the following brands during this test? Please click any brand logo you remember viewing an ad from in the last few minutes.” (Respondents can also choose “Don’t remember” option.)*

Brand Recognition % shows the share of viewers who selected the correct brand logo.

</details>

<details id='in-context-survey-ad-recognition'>
<summary>Ad Recognition</summary>

##### Ad Recognition

Quantifies the ad's effectiveness in driving brand recall and recognition, validating the connection between attention and memory. After in-context exposure, respondents select from a randomized list of 12 ad thumbnail images, responding to:

>*“Have you seen any of these ads during this test? Please click any ad you remember viewing in the last few minutes.” (Respondents can also choose “Don’t remember” option.)*

Ad Recognition % shows the share of viewers who selected the correct ad thumbnail.

</details>

<details id='in-context-survey-ad-likeability'>
<summary>Ad Likeability</summary>

##### Ad Likeability

Measures respondents' subjective evaluation of their emotional reaction and favorability toward the ad creative. Data is collected via a post-exposure survey after the second (focused) exposure, utilizing a 1-5 scale.

>*“How much did you enjoy watching this ad?” (1 \= Not at all, 5 \= Very much)*

Ad Likeability % shows the average response excluding respondents who had previously failed to recognize the brand logo or the ad thumbnail.

</details>

<details id='in-context-survey-brand-trust'>
<summary>Brand Trust</summary>

##### Brand Trust

Assesses the degree of trust respondents feel toward the brand after exposure to the ad. It reflects how credible, reliable, and authentic the audience perceives the brand to be. Respondents are asked the following question after the second (focused) exposure, rated on a 1-5 scale:

>*“How much would you rate your trust for this brand?” (1 \= Not at all, 5 \= Very much)*

Brand Trust % shows the average response from all respondents.

</details>

<details id='in-context-survey-ad-persuasion'>
<summary>Ad Persuasion</summary>

##### Ad Persuasion

Addresses whether the ad successfully lifted consumer perception in a positive direction. After the second (focused) exposure, the following question is asked with three answer options. Persuasion % metric reports the share of respondents feedbacking positive perception changes. Data from respondents who had previously fail to recognize the brand or the ad are excluded when analyzing answers for this survey question:

>*“Did this ad change the way you feel about this brand in any way?"*

>* *Yes, positively*  
>* *Yes, negatively,*  
>* *No”*

Ad Persuasion % shows the share of viewers whose feelings were changed into a positive direction thanks to watching the ad.

</details>

<details id='in-context-survey-custom-surveys'>
<summary>Custom Surveys</summary>

##### Custom Surveys 

As the custom survey options are similar for both In-Context and Focused Exposure product type, please read more in the [Surveys: Custom Surveys](#survey-custom-surveys) section.

</details>

</details>

<details id='surveys-focused-exposure-product-type'>
<summary>Surveys: Focused Exposure Product Type</summary>

##### Surveys: Focused Exposure Product Type

This section summarizes survey capabilities when using [PreView Focused Exposure Product Type](#focused-exposure-product-type).

There are 3 different formats used in case of the Focused Exposure Product Type:

**1. No Surveys  \-  "Core", "Redirect"**  
>>Our partners have the possibility to execute focused exposure camera tests without asking survey questions. This product type is supported when bringing your own panel (“Redirect tests”) or when requesting panels from Realeyes (“Core”).  
(However, Redirect tests are a different setup with different limitations, due to RE technology integration into client's own surveys)

**2. Sentiment Surveys \- “Standard”**  
>* Sentiment Analysis
>* Custom Surveys (all focused exposure test types)

**3. Various Surveys  \- “Pro”**
>* Favorability (Pre exposure)
>* Purchase Intent (Pre exposure)
>* Sentiment Analysis
>* Brand Recognition
>* Favorability (Post exposure)
>* Purchase Intent (Post exposure)
>* Campaign Objective
>* Custom Surveys (all focused exposure test types)

The Rating Scale used by many PRO questions is weighted as follows (Top Positive Answer: 10, Somewhat Positive Answer: 7.75, Neutral Answer: 5.5, Somewhat Negative Answer: 3.25, Very Negative Answer: 1, Unfamiliar: Excluded from calculations)

<details id='focused-exposure-survey-favorability-pre-exposure'>
<summary>Favorability (Pre exposure)</summary>

##### Favorability (Pre exposure)

Before in-focus camera exposure, qualifies respondents based on how favorably they perceive the given brand when presented alongside 3 or more other competitor brands, all represented with brand logos:

>*“How would you describe your overall attitude toward the following?” (5 \= Very positive, 1 \= Very negative, 6 \= Not familiar)*

Brand Favorability (Pre) score indicates the average 1-10 brand favorability rating BEFORE viewing the ad.

</details>

<details id='focused-exposure-survey-purchase-intent-pre-exposure'>
<summary>Purchase Intent (Pre exposure)</summary>

##### Purchase Intent (Pre exposure)

Before in-focus camera exposure, qualifies respondents based on how likely they are to purchase the given brand when presented alongside 3 or more other competitor brands, all represented with brand logos:

>*“How likely are you to purchase the following?” (5 \= Very Likely, 1 \= Very Unlikely, 6 \= Not Familiar)*

Purchase Intent (Pre) score indicates the average 1-10 purchase intent rating BEFORE viewing the ad.

</details>

<details id='focused-exposure-survey-sentiment-analysis'>
<summary>Sentiment Analysis</summary>

##### Sentiment Analysis

Our automated text analysis algorithm assesses the open-ended answers for Positive, Negative, Neutral, or Ambiguous (containing both positive and negative) sentiments respondents feel toward the ad after exposure to the ad.

>*“Please share your thoughts and feelings about the video you have just seen. Everything is acceptable. There are no right or wrong answers.”*

Sentiment Scale score indicates the overall emotional sentiment of the open-ended responses on a positive to negative scale of \+10 to \-10.

Sentiment Analysis % shows the percentage of audience that gave a Positive, Negative, Neutral, or Ambiguous response according to the algorithm.

</details>

<details id='focused-exposure-survey-brand-recognition'>
<summary>Brand Recognition</summary>

##### Brand Recognition

Our automated text analysis tool assesses the open-ended answers for correct brand recognition from the ad after exposure to the ad.

>*“Which brand was advertised in this video?”*

Brand Recall % shows the percentage of audience that correctly recalled the brand according to the algorithm.

</details>

<details id='focused-exposure-survey-favorability-post-exposure'>
<summary>Favorability (Post exposure)</summary>

##### Favorability (Post exposure)

After in-focus camera exposure, qualifies respondents based on how favorably they perceive the given brand when presented alongside 3 or more other competitive brands, all represented with brand logos:

>*“After seeing this video, how would you describe your overall attitude toward the following?” (5 \= Very positive, 1 \= Very negative, 6= Not familiar)*

Brand Favorability (Post) score indicates the average 1-10 brand favorability rating AFTER viewing the ad.

Brand Favorability Lift % calculates the percent difference between the Pre and Post Brand Favorability scores.

</details>

<details id='focused-exposure-survey-purchase-intent-post-exposure'>
<summary>Purchase Intent (Post exposure)</summary>

##### Purchase Intent (Post exposure)

After in-focus camera exposure, qualifies respondents based on how likely they are to purchase the given brand when presented alongside 3 or more other competitive brands, all represented with brand logos:

>*“After seeing this video, how likely are you to purchase the following?” (5 \= Very Likely, 1 \= Very Unlikely, 6 \= Not familiar)*

Purchase Intent (Post) score indicates the average 1-10 purchase intent rating AFTER viewing the ad.

Purchase Intent Lift % calculates the percent difference between the Pre and Post Purchase Intent scores.

</details>

<details id='focused-exposure-survey-campaign-objective'>
<summary>Campaign Objective</summary>

##### Campaign Objective

After the in-focus camera exposure, measures respondents’ reported feelings toward the video in areas of: Likeability, Informative, Memorability, Intrigue, Shareability, and Viewability.

>*“How strongly do you agree with the following statements?” (5 \= Strongly Agree, 1 \= Strongly Disagree)”*

>* *I like the video*  
>* *The video has interesting information*  
>* *I would remember the video*  
>* *I want to know more about what is advertised in the video*  
>* *I would share the video on social media*  
>* *I would watch the video again*  
>* *\[Optional Additional Custom Statements\]*

Campaign Objective score indicates the average 1-10 rating for each statement.

Campaign Objective % score indicates the percentage of top-2-box answers (Strongly Agree/Somewhat Agree) for each statement.

The 1-10 Rating Scale used by many PRO questions is as follows (Top Positive Answer: 10, Somewhat Positive Answer: 7.75, Neutral Answer: 5.5, Somewhat Negative Answer: 3.25, Very Negative Answer: 1, Unfamiliar: Excluded from calculations)

>* *I would remember the video*  
>* *I want to know more about what is advertised in the video*

</details>

<details id='focused-exposure-survey-custom-surveys'>
<summary>Custom Surveys</summary>

##### Custom Surveys 

As the custom survey options are similar for both In-Context and Focused Exposure product type, please read more in the [Surveys: Custom Surveys](#survey-custom-surveys) section.

</details>

</details>

<details id='survey-custom-surveys'>
<summary>Surveys: Custom Surveys </summary>

##### Surveys: Custom Surveys 

When running PreView tests, the respondent experience can optionally be extended with custom survey questions. Single Select, Multi-Select, Open End, and Grid questions may be used. Custom survey questions (excluding Open End, and with minor adaptation for Grid questions) are supported in reporting when setting up segments to drill deeper into attention or reaction curves, but benchmarks are unavailable.

Custom questions that relate to the brand/product are discouraged from being asked as pre-contextual as they could bias the natural behavior of a respondent in the environment. Post-survey custom questions of this nature are suggested instead.

</details>

</details>

<details id='attention-signals'>
<summary>Attention Signals</summary>

##### Attention Signals

<details id='attention'>
<summary>Attention</summary>

##### Attention

Attention reflects the ability of an ad to capture and maintain visual engagement. Realeyes’ patented deep-learning approach evaluates head pose, face angles, and gaze direction and accounts for behavioral cues like yawning and speaking to measure true visual focus \- an advanced version of eyes on screen. Attention metrics reveal how effectively an ad draws and sustains viewer interest, critical for driving mental availability and brand outcomes.

>* **Attention Average**: Average % of viewers attentive across the entire creative.  
>* **Attention Seconds**: Average duration (in seconds) for which viewers maintain attention during the ad impression.  
>* **Attentive VTR**: Average % of viewers who maintained attention for at least 50% of the advertisement duration and during the final 2 seconds.

We recommend prioritizing **Attention Average** for most analyses, as it provides a balanced view of engagement. However, other metrics may streamline specific evaluations:

>* **Attentive VTR** enhances traditional VTR metrics by integrating attention duration with ad playback effectiveness.  
>* **Attention Seconds** offers more stable benchmarks for longer creatives, where attention VTR values may already drop to negligible levels, providing critical data in these scenarios.

Higher attention levels in our experience correlates with brand and ad recognition levels, ad likeability and ad interest scores. Viewers with high attention also reported to be open to watch the ad again or would more likely interact with the ad on social media.

When running [PreView In-Context](#in-context-product-type), we recommend analyzing Attention from the first in-context natural exposure to have a more precise understanding of how respondents tend to get distracted by the context.

To analyze when creatives are inattentive, check [Distraction](#distraction).

</details>

<details id='distraction'>
<summary>Distraction</summary>

##### Distraction

**Distraction** is the counterpart to [Attention](#attention), capturing when viewers disengage from the content. It assesses indicators like head movements away from the screen, facial occlusions, and gaze shifts to detect moments when attention is lost. In other words, distraction explains when viewers are inattentive.

>* **Distraction Average**: Average % of viewers inattentive across the entire creative.

Realeyes products are frequently used to reduce media waste by identifying and optimizing inattentive creatives. This approach is often simpler to implement compared to analyzing the positive impacts of attention. However, attention is a prerequisite for any opportunity to create impact \- without it, no meaningful engagement or influence can occur.

When running [PreView In-Context](#in-context-product-type), we recommend analyzing Distraction from the first in-context natural exposure to have a more precise understanding of how respondents tend to get distracted by the context.

To analyze when creatives are attentive, check [Attention](#attention).

</details>

</details>

<details id='aggregated-reaction-metrics'>
<summary>Aggregated Reaction Metrics</summary>

##### Aggregated Reaction Metrics

<details id='reactions'>
<summary>Reactions</summary>

##### Reactions

Reactions is a composite metric representing the average proportion of viewers who displayed a recognizable facial expression from the ones supported by Realeyes: [Happiness](#happiness), [Surprise](#surprise), [Confusion](#confusion), [Contempt](#contempt), [Disgust](#disgust). In other words, Reactions aggregates the separate facial expression signals and indicates how expressive the audience was during the viewing experience.

>* **Reactions Average**: Average % of viewers expressing any facial expressions across the entire creative.

Stronger reactions correlate with brand and ad memorability, ad likeability and interests scores and purchase intent. When running [PreView In-Context](#in-context-product-type), we recommend analyzing Reactions from the focused exposure for the better data yield.

[Happiness](#happiness), [Surprise](#surprise), [Confusion](#confusion), [Contempt](#contempt), [Disgust](#disgust)

</details>

<details id='negativity'>
<summary>Negativity</summary>

##### Negativity

Negativity is a composite metric representing the percentage of viewers expressing any of the following emotions: [Confusion](#confusion), [Contempt](#contempt), or [Disgust](#disgust). The presence of negative emotions is not inherently bad \- it depends on the creative's intent. Negative emotions may be purposeful (for instance, provoking concern in a public service announcement) or unintended feedback.

>* **Negativity Peak**: Maximum % of viewers simultaneously expressing negativity across the entire creative.

Viewers with high negativity levels tend to find ads less interesting, less likeable and would prefer not to watch the ad again. When running [PreView In-Context](#in-context-product-type), we recommend analyzing Negativity from the focused exposure for the better data yield.

[Confusion](#confusion), [Contempt](#contempt), [Disgust](#disgust)

</details>

</details>

<details id='reaction-signals'>
<summary>Reaction Signals</summary>

##### Reaction Signals

<details id='happiness'>
<summary>Happiness</summary>

##### Happiness

Happiness is a basic emotion shown through smiles or laughter, indicating pleasant feelings.

>* **Happiness Average**: Average % of viewers expressing happiness across the entire creative.

>* **Happiness Peak**: Maximum % of viewers simultaneously expressing happiness during any second of the creative.

High happiness often correlates with better ad recall, likability, and purchase intent. When running [PreView In-Context](#in-context-product-type), we recommend analyzing Attention from the first in-context natural exposure to have a more precise understanding of how respondents tend to get distracted by the context.

</details>

<details id='surprise'>
<summary>Surprise</summary>

##### Surprise

Surprise is a shocked reaction, shown through raised eyebrows, wide eyes, and dropped jaw. Surprise reflects novelty or unexpectedness, which can evoke either positive or negative reactions depending on the context. Reviewing preceding scenes and co-occurring emotions helps interpret this.

>* **Surprise Average**: Average % of viewers expressing surprise across the entire creative.

>* **Surprise Peak**: Maximum % of viewers simultaneously expressing surprise during any second of the creative.

High surprise levels reduce the likeliness of viewers liking the ad or interacting with the ad. When running [PreView In-Context](#in-context-product-type), we recommend analyzing Attention from the first in-context natural exposure to have a more precise understanding of how respondents tend to get distracted by the context.

</details>

<details id='confusion'>
<summary>Confusion</summary>

##### Confusion

Confusion is represented by a frown, suggesting concern, worry, or concentration. It can indicate uncertainty or complexity within the ad's messaging.

>* **Confusion Average**: Average % of viewers expressing confusion across the entire creative.

>* **Confusion Peak**: Maximum % of viewers simultaneously expressing confusion during any second of the creative.

Viewers with high confusion levels show less likeliness to like the ad or find the ad interesting. When running [PreView In-Context](#in-context-product-type), we recommend analyzing Attention from the first in-context natural exposure to have a more precise understanding of how respondents tend to get distracted by the context.

Check [Negativity](#negativity) to combine multiple negative signals into one metric.

</details>

<details id='contempt'>
<summary>Contempt</summary>

##### Contempt

Contempt is shown by a raised lip corner on one side of the face, contempt conveys superiority, judgment, or skepticism. Often linked to perceptions of competence or trust.

>* **Contempt Average**: Average % of viewers expressing contempt across the entire creative.  

>* **Contempt Peak**: Maximum % of viewers simultaneously expressing contempt during any second of the creative.

Check [Negativity](#negativity) to combine multiple negative signals into one metric. When running [PreView In-Context](#in-context-product-type), we recommend analyzing Attention from the first in-context natural exposure to have a more precise understanding of how respondents tend to get distracted by the context.

</details>

<details id='disgust'>
<summary>Disgust</summary>

##### Disgust

Disgust is indicated by nose wrinkling and a downturn of the mouth; disgust reflects distaste or aversion.

>* **Disgust Average**: Average % of viewers expressing disgust across the entire creative.  

>* **Disgust Peak**: Maximum % of viewers simultaneously expressing disgust during any second of the creative.

Check [Negativity](#negativity) to combine multiple negative signals into one metric. When running [PreView In-Context](#in-context-product-type), we recommend analyzing Attention from the first in-context natural exposure to have a more precise understanding of how respondents tend to get distracted by the context.

</details>

</details>

<details id='other-data'>
<summary>Other Data</summary>

##### Other Data

<details id='ad-format'>
<summary>Ad Format</summary>

##### Ad Format

An ad format is the specific style or design of an advertisement tailored to suit specific goals, audience, and context of the advertising campaign, specific to media platforms. PreView In-Context ad tests offer a wide spectrum of ad formats for test configuration \- provided by our partner Eye Square.

</details>

<details id='brand'>
<summary>Brand</summary>

##### Brand

Each creative is assigned to a single unique brand. Tests from multiple brands can be executed under the same customer account.

</details>

<details id='country'>
<summary>Country</summary>

##### Country

Country in which participants were recruited.

</details>

<details id='date-tested'>
<summary>Date Tested</summary>

##### Date Tested

Date when the test was conducted.

</details>

<details id='device'>
<summary>Device</summary>

##### Device

The device on which the respondents were exposed to the advertisements.

</details>

<details id='duration'>
<summary>Duration</summary>

##### Duration

Duration of the video creative in seconds.

</details>

<details id='industry'>
<summary>Industry</summary>

##### Industry

We use a customized version of the IAB Taxonomy to classify brands into industry categories, with 3 hierarchical levels (top, mid, sub).

The column Industry reports the industry vertical (mid-level) if it is defined for the brand, otherwise it rolls up to the top-level hierarchical level. This ensure that every brand always has an industry reported.

>* Industry (top-level): example \- Automotive  
>* Industry (mid-level): example \- Car  
>* Industry (sub-level): example \- Budget

</details>

<details id='generation'>
<summary>Generation</summary>

##### Generation

Audience segmentation by Generations offers a new way to analyze data. Generational definitions follow USA standards, categorizing respondents into groups like Baby Boomers (1946–1964), Generation X (1965–1980), Millennials (1981–1996), Generation Z (1997–2012), and Generation Alpha (2013 onward). Please note that this segmentation is available only for data collected after 01 July 2024\. Generation segmentation can be combined with gender segmentation for enhanced insights (e.g. Millenial Females).

</details>

<details id='playback-seconds'>
<summary>Playback Seconds</summary>

##### Playback Seconds

Explains how long a video is played. Should not be considered as a visibility indicator, because in some cases the ad might be invisible but playing (for instance respondents reading YouTube comments while playing the video). When analyzing creatives on second-by-second level in Creative Viewer, Attention and Distraction metrics shall add up to Playback %.

</details>

<details id='region'>
<summary>Region</summary>

##### Region

Geographic region of the country in which participants were recruited. It would represent one or multiple countries that are widely recognized as individual markets. See more in [Geographic Regions](#benchmarks-geographic-regions).

</details>

<details id='views'>
<summary>Views</summary>

##### Views

The number of respondents that completed our tests, passed our quality checks and resulted in usable data for reports. View counts may be different for camera data from exposures and for survey responses when running in-context tests with multiple exposures.

</details>

<details id='view-through-rate-vtr'>
<summary>View Through Rate (VTR)</summary>

##### View Through Rate (VTR)

% of viewers who played the video until the last second of and ad. VTR% results for focused exposure tests may not always add up to 100% due to technical reasons (for instance unstable network connection, ad duration rounding and more).  

</details>

</details>

</details>


<details id='benchmarks'>
<summary>Benchmarks</summary>

##### Benchmarks

<details id='benchmarks-overview'>
<summary>Benchmarks Overview</summary>

##### Benchmarks Overview

To ensure accurate performance references, our product offers benchmarks built on a robust historical dataset.

>* **Dataset Scale:** Our benchmark dataset is built from over 10 years of creative testing across 65 countries, with each test using a sample size of 150-300.  
>* **Data Scope:** We analyzed over 66,000 tested videos, including more than 1,000 ads tested on authentic social media platform mocks with Eye Square. The dataset is updated weekly with tests initiated by our creative testing partners.  
>* **Benchmarking Focus:** A high-quality subset of data informs benchmarks, emphasizing attention and reaction consistency across markets, contexts, and media formats.

This section details these benchmarks, which support median metrics for direct comparisons and provide benchmark vs. measured metrics for quick % performance insights. Additionally, 1-10 rank scales highlight whether each metric surpasses norms, making it easy to see how each ad stands out against relevant standards. Ranks can also be used to build composite scores from any available set of metrics to meet any custom reporting needs.

</details>

<details id='benchmark-dimensions-default'>
<summary>Benchmark Dimensions (Default)</summary>

##### Benchmark Dimensions (Default)

Our benchmarks are categorized by significant variability patterns to ensure accurate norms:

>* **Device Type:** Segmented by mobile, desktop, or mixed.  
>* **Region:** Based on UN Economic regions.  
>* **Ad Format:** Benchmarks differ between Eye Square's media contexts and Realeyes Video Player for focused exposure settings.  
>* **Exposure Type:** Focused versus free-browsing exposure, accounting for user interaction (for example: skip, scroll or other actions).  
>* **Ad Duration:** The duration of video creatives.

Default automated benchmarks ensure that each ad receives norms for meaningful result comparison.

</details>

<details id='benchmark-dimensions-via-operations-support'>
<summary>Benchmark Dimensions (via Operations Support)</summary>

##### Benchmark Dimensions (via Operations Support)

For reports needing more granular dimensions, additional norms are available:

>* **Country:** Specific country norms (for example: Germany in Western Europe).  
>* **Industry:** Further classification by industry vertical (example top-level: Home Supplies or mid-level Pet Care).

Custom data requirements may require manual support from Realeyes operations team, depending on the complexity of requests.

</details>

<details id='benchmarks-automated-fallback-rules'>
<summary>Automated Fallback Rules</summary>

##### Automated Fallback Rules

If standard benchmarks are unavailable due to a small sample, various fallback levels are in place to help provide norms:

>1. **First Fallback:** Ad format is simplified to an environment category (e.g., Feed/Stream/Shorts).  
>2. **Second Fallback:** Region-specific norms are replaced with global benchmarks.  
>3. **Final Fallback:** For long-form creatives (common in social ads), scores can be trimmed to represent only the initial X seconds.

**Note:** Tests without benchmarks are rare and typically involve non-standard ad durations/configurations.

</details>

<details id='benchmarks-ad-formats'>
<summary>Ad Formats</summary>

##### Ad Formats

Our format categories ensure fallback consistency:

>* **Stream:** Realeyes Video Player, YouTube pre-roll ads, and YouTube pods.  
>* **Shorts:** Includes video ad formats across Instagram, TikTok, Snapchat, and YouTube Shorts.  
>* **Feed:** Facebook and Instagram feed ads, video ads, and mid-rolls.

New product capabilities are added on a continuous basis, visit our website or reach out to our representatives to learn about the most up-to-date ad format list.

</details>

<details id='benchmarks-geographic-regions'>
<summary>Geographic Regions</summary>

##### Geographic Regions

Benchmarks use the UN Geographic Scheme for consistent norms:

**Africa**

>* Southern Africa: Botswana, Lesotho, Namibia, South Africa, Swaziland.  
>* Eastern Africa: British Indian Ocean Territory, Burundi, Comoros, Djibouti, Eritrea, Ethiopia, French Southern Territories, Kenya, Madagascar, Malawi, Mauritius, Mayotte, Mozambique, Reunion, Rwanda, Seychelles, Somalia, Tanzania, Uganda, Zambia, Zimbabwe.  
>* Middle Africa: Angola, Cameroon, Central African Republic, Chad, Congo, Equatorial Guinea, Gabon, Sao Tome and Principe.  
>* Northern Africa: Algeria, Egypt, Libya, Morocco, Tunisia, Western Sahara.  
>* Western Africa: Benin, Burkina Faso, Cape Verde, Cote d’Ivoire, Gambia, Ghana, Guinea, Guinea-Bissau, Liberia, Mali, Mauritania, Niger, Nigeria, Saint Helena, Senegal, Sierra Leone, Togo.

**Americas**

>* South America: Argentina, Bolivia, Bouvet Island, Brazil, Chile, Colombia, Ecuador, Falkland Islands, French Guiana, Guyana, Paraguay, Peru, South Georgia and South Sandwich Islands, Suriname, Uruguay, Venezuela.  
>* Caribbean: Anguilla, Antigua and Barbuda, Aruba, Bahamas, Barbados, British Virgin Islands, Cayman Islands, Cuba, Dominica, Dominican Republic, Grenada, Guadeloupe, Haiti, Jamaica, Martinique, Montserrat, Puerto Rico, Saint Barthelemy, Saint Kitts and Nevis, Saint Lucia, Saint Martin, Saint Vincent and the Grenadines, Trinidad and Tobago, Turks and Caicos Islands, US Virgin Islands.  
>* Central America: Belize, Costa Rica, El Salvador, Guatemala, Honduras, Mexico, Nicaragua, Panama.  
>* Northern America: Bermuda, Canada, Greenland, Saint Pierre and Miquelon, United States of America.

**Asia**

>* Southern Asia: Afghanistan, Bangladesh, Bhutan, India, Iran, Maldives, Nepal, Pakistan, Sri Lanka.  
>* South-Eastern Asia: Brunei, Cambodia, Indonesia, Laos, Malaysia, Myanmar, Philippines, Singapore, Thailand, Timor-Leste, Vietnam.  
>* Eastern Asia: China, Hong Kong, Japan, Macao, Mongolia, North Korea, South Korea.  
>* Central Asia: Kazakhstan, Kyrgyzstan, Tajikistan, Turkmenistan, Uzbekistan.  
>* Western Asia: Armenia, Azerbaijan, Bahrain, Cyprus, Georgia, Iraq, Israel, Jordan, Kuwait, Lebanon, Oman, Palestinian Territory, Qatar, Saudi Arabia, Syria, Turkey, United Arab Emirates, Yemen.

**Europe**

>* Southern Europe: Albania, Andorra, Bosnia and Herzegovina, Croatia, Gibraltar, Greece, Holy See (Vatican City State), Italy, Macedonia, Malta, Montenegro, Portugal, San Marino, Serbia, Slovenia, Spain.  
>* Eastern Europe: Belarus, Bulgaria, Czech Republic, Hungary, Moldova, Poland, Romania, Russia, Slovakia, Ukraine.  
>* Northern Europe: Åland Islands, Denmark, Estonia, Feroe Islands, Finland, Guernsey, Iceland, Ireland, Isle of Man, Jersey, Latvia, Lithuania, Norway, Svalbard & Jan Mayen Islands, Sweden, United Kingdom.  
>* Western Europe: Austria, Belgium, France, Germany, Liechtenstein, Luxembourg, Monaco, Netherlands, Switzerland.

**Oceania**

>* Australia/New Zealand: Australia, Christmas Island, Cocos Islands, Heard and McDonald Islands, New Zealand, Norfolk Island.  
>* Melanesia: Fiji, New Caledonia, Papua New Guinea, Solomon Islands, Vanuatu.  
>* Micronesia: Guam, Kiribati, Marshall Islands, Micronesia, Nauru, Northern Mariana Islands, Palau, United States Minor Outlying Islands.  
>* Polynesia: American Samoa, Cook Islands, French Polynesia, Niue, Pitcairn Islands, Samoa, Tokelau, Tonga, Tuvalu, Wallis and Futuna.

</details>

</details>