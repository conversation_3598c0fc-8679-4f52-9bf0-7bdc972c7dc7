body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    '<PERSON>bunt<PERSON>', '<PERSON><PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
  height: 100vh;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

.MuiDataGrid-root {
  --DataGrid-pinnedBackground: white !important;
}

.MuiDataGrid-cell {
  display: flex;
  border-right-color: transparent !important; 
} 

.MuiDataGrid-columnHeader {
  border-right-color: transparent !important;
}

.MuiDataGrid-pinnedRows .MuiDataGrid-cell {
  background-color:  #F4F6FD;
}

.MuiDataGrid-pinnedRows,
.MuiDataGrid-pinnedRows p {
  font-weight: bold;
}

.MuiDataGrid-toolbarContainer {
  padding: 10px !important;
  gap: unset;
  column-gap: 8px;
}

.MuiDataGrid-columnHeaderTitleContainerContent {
  white-space: break-spaces;
  font-weight: 500;
}


