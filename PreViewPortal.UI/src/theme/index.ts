import { ThemeOptions } from '@mui/material';
import { createTheme } from '@mui/material/styles';
import { deepmerge } from '@mui/utils';
import { ThemeType } from '../constant/themes';
import { darkShadows, lightShadows } from './shadows';

//ToDo: Refactor later palette usage into light & dark and use that in configs..

const baseOptions: ThemeOptions = {
    direction: 'ltr',
    shape: {
        borderRadius: 0
    },
    components: {
        MuiAvatar: {
            styleOverrides: {
                fallback: {
                    height: '75%',
                    width: '75%'
                }
            }
        },
        MuiButton: {
            styleOverrides: {
                root: {
                    textTransform: 'uppercase'
                },
                text: {
                    '&:hover': {
                        backgroundColor: 'unset'
                    }
                }
            }
        },
        MuiCssBaseline: {
            styleOverrides: {
                '*': {
                    boxSizing: 'border-box'
                },
                html: {
                    MozOsxFontSmoothing: 'grayscale',
                    WebkitFontSmoothing: 'antialiased',
                    height: '100%',
                    width: '100%'
                },
                body: {
                    height: '100%'
                },
                '#root': {
                    height: '100%'
                },
                '#nprogress .bar': {
                    zIndex: '2000 !important'
                }
            }
        },
        MuiCardHeader: {
            defaultProps: {
                titleTypographyProps: {
                    variant: 'h6'
                }
            }
        },
        MuiLinearProgress: {
            styleOverrides: {
                root: {
                    overflow: 'hidden'
                }
            }
        },
        MuiListItemIcon: {
            styleOverrides: {
                root: {
                    minWidth: 'auto',
                    marginRight: '16px'
                }
            }
        },
        MuiPaper: {
            styleOverrides: {
                root: {
                    backgroundImage: 'none'
                }
            }
        },
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        MuiDataGrid: {
            styleOverrides: {
                root: {
                    backgroundColor: '#ffffff',
                    '& .MuiDataGrid-toolbarContainer': {
                        padding: '15px',

                        '& .MuiButton-root': {
                            padding: '5px 15px',
                            fontSize: '14px'
                        }
                    }
                },
                cell: {
                    ':focus-within': {
                        outline: 'none'
                    }
                },
                columnHeader: {
                    backgroundColor: '#ffffff'
                }
            }
        },
        MuiTooltip: {
            styleOverrides: {
                tooltip: {
                    backgroundColor: '#4a595e'
                }
            }
        },
        MuiAutocomplete: {
            styleOverrides: {
                inputRoot: {}
            }
        },
        MuiInput: {
            styleOverrides: {
                root: {
                    fontSize: '14px',
                    '&&&:before': {
                        borderBottom: 'none'
                    },
                    '&&:after': {
                        borderBottom: 'none'
                    }
                }
            }
        },
        MuiInputLabel: {
            styleOverrides: {
                root: {
                    fontSize: '16px'
                }
            }
        },
        MuiInputBase: {
            styleOverrides: {
                multiline: {
                    '& textarea': {
                        overflow: 'hidden'
                    }
                }
            }
        },
        MuiCheckbox: {
            defaultProps: {
                color: 'secondary'
            }
        },
        MuiRadio: {
            defaultProps: {
                color: 'secondary'
            }
        },
        MuiSwitch: {
            defaultProps: {
                color: 'secondary'
            }
        },
        MuiFormHelperText: {
            styleOverrides: {
                root: {
                    marginLeft: 0,
                    marginRight: 0
                }
            }
        },
        MuiTabs: {
            styleOverrides: {
                indicator: {
                    height: '3px'
                }
            }
        }
    },
    typography: {
        button: {
            fontWeight: 600
        },
        fontFamily:
            'Roboto, -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji"',
        h1: {
            fontWeight: 700,
            fontSize: '56px',
            letterSpacing: '-1.5px'
        },
        h2: {
            fontWeight: 700,
            fontSize: '48px',
            letterSpacing: '-0.5px'
        },
        h3: {
            fontWeight: 700,
            fontSize: '36px',
            letterSpacing: '-0px'
        },
        h4: {
            fontWeight: 700,
            fontSize: '24px',
            letterSpacing: '0.25px'
        },
        h5: {
            fontWeight: 700,
            fontSize: '20px',
            letterSpacing: '0px'
        },
        h6: {
            fontWeight: 700,
            fontSize: '18px',
            letterSpacing: '0.15px'
        },
        subtitle1: {
            fontSize: '16px',
            letterSpacing: '0.15px'
        },
        subtitle2: {
            fontWeight: 500,
            fontSize: '14px',
            letterSpacing: '0.1px'
        },
        body1: {
            fontSize: '16px',
            letterSpacing: '0.15px'
        },
        body2: {
            fontSize: '14px',
            letterSpacing: '0.15px'
        },
        caption: {
            fontWeight: 400,
            fontSize: '12px',
            color: '#505B5F',
            letterSpacing: '0.4px'
        },
        overline: {
            fontWeight: 500,
            fontSize: '11px',
            textTransform: 'uppercase',
            letterSpacing: '0.5px'
        }
    },
    palette: {
        primary: {
            main: '#273235',
            dark: '#061A24',
            light: '#505B5F',
            contrastText: '#FFFFFF'
        },
        secondary: {
            main: '#2DC0A2',
            dark: '#16A086',
            light: '#27EABF',
            contrastText: '#FFFFFF'
        },
        info: {
            main: '#DAE0F7',
            dark: '#A5ABBF',
            light: '#FAFAFA',
            contrastText: '#273235'
        },
        error: {
            main: '#FB5C63',
            dark: '#C12A3D',
            light: '#FC9092',
            contrastText: '#FFFFFF'
        },
        warning: {
            main: '#F1B44B',
            dark: '#BB852B',
            light: '#FEE484',
            contrastText: '#273235'
        },
        success: {
            main: '#5AC57D',
            dark: '#238C50',
            light: '#8CF0AB',
            contrastText: '#273235'
        },
        background: {
            default: '#F2F2F2'
        }
    }
};

const lightTheme = createTheme(
    deepmerge(baseOptions, {
        typography: {
            allVariants: {
                color: '#273235'
            }
        },
        components: {
            MuiInputBase: {
                styleOverrides: {
                    input: {
                        '&::placeholder': {
                            opacity: 0.86,
                            color: '#42526e'
                        }
                    }
                }
            },
            MuiButton: {
                styleOverrides: {
                    containedPrimary: {
                        '&:hover': {
                            backgroundColor: '#2DC0A2'
                        },
                        '&.Mui-focusVisible': {
                            backgroundColor: '#16A086'
                        }
                    },
                    outlinedPrimary: {
                        '&:hover': {
                            color: '#2DC0A2',
                            borderColor: '#2DC0A2',
                            backgroundColor: 'unset'
                        }
                    },
                    textPrimary: {
                        '&:hover': {
                            color: '#2DC0A2',
                            borderColor: '#2DC0A2'
                        }
                    }
                }
            },
            MuiInput: {
                styleOverrides: {
                    root: {
                        root: {
                            color: '#505B5F'
                        }
                    }
                }
            },
            MuiOutlinedInput: {
                styleOverrides: {
                    root: {
                        '&.Mui-focused:not(.Mui-error) .MuiOutlinedInput-notchedOutline':
                            {
                                borderColor: '#2DC0A2'
                            }
                    }
                }
            },
            MuiInputLabel: {
                styleOverrides: {
                    root: {
                        color: '#273235'
                    }
                }
            },
            MuiTabs: {
                styleOverrides: {
                    indicator: {
                        backgroundColor: '#2DC0A2'
                    }
                }
            },
            MuiLink: {
                styleOverrides: {
                    root: {
                        '&:hover': {
                            color: '#2DC0A2'
                        }
                    }
                }
            }
        },
        shadows: lightShadows
    } as ThemeOptions)
);

const darkTheme = createTheme(
    deepmerge(baseOptions, {
        typography: {
            allVariants: {
                color: '#ffffff'
            },
            caption: {
                color: '#ffffff'
            }
        },
        components: {
            MuiTableCell: {
                styleOverrides: {
                    root: {
                        borderBottom: '1px solid rgba(145, 158, 171, 0.24)'
                    }
                }
            },
            MuiButton: {
                styleOverrides: {
                    containedPrimary: {
                        backgroundColor: '#ffffff',
                        color: '#273235',
                        '&.Mui-disabled': {
                            color: 'rgba(255, 255, 255, 0.5)',
                            backgroundColor: 'rgba(255, 255, 255, 0.12)'
                        },
                        '&:hover': {
                            backgroundColor: '#27EABF'
                        }
                    },
                    outlinedPrimary: {
                        backgroundColor: 'transparent',
                        border: '1px solid #ffffff',
                        color: '#ffffff',
                        '&.Mui-disabled': {
                            color: 'rgba(255, 255, 255, 0.5)',
                            borderColor: 'rgba(255, 255, 255, 0.5)'
                        },
                        '&:hover': {
                            color: '#27EABF',
                            borderColor: '#27EABF',
                            backgroundColor: 'unset'
                        }
                    },
                    textPrimary: {
                        '&.Mui-disabled': {
                            color: 'rgba(255, 255, 255, 0.5)'
                        },
                        '&:hover': {
                            color: '#27EABF',
                            borderColor: '#27EABF'
                        }
                    }
                }
            },
            MuiInput: {
                styleOverrides: {
                    root: {
                        color: '#ffffff'
                    }
                }
            },
            MuiInputLabel: {
                styleOverrides: {
                    root: {
                        color: 'rgba(255, 255, 255, 0.75)',
                        fontSize: '16px',
                        '&:not(.Mui-error) .Mui-focused': {
                            color: '#ffffff'
                        },
                        '&:not(.Mui-error) .MuiOutlinedInput-notchedOutline': {
                            color: '#ffffff'
                        },
                        '&.Mui-disabled': {
                            color: 'rgba(255, 255, 255, 0.5)'
                        }
                    }
                }
            },
            MuiOutlinedInput: {
                styleOverrides: {
                    root: {
                        color: '#ffffff',
                        '&:hover:not(.Mui-disabled):not(.Mui-focused) .MuiOutlinedInput-notchedOutline':
                            {
                                borderColor: '#ffffff'
                            },
                        '&.Mui-disabled .MuiOutlinedInput-notchedOutline': {
                            borderColor: 'rgba(255, 255, 255, 0.5)'
                        }
                    },
                    notchedOutline: {
                        borderColor: '#ffffff'
                    }
                }
            },
            MuiLinearProgress: {
                styleOverrides: {
                    colorPrimary: {
                        backgroundColor: '#2DC0A2'
                    },
                    barColorPrimary: {
                        backgroundColor: '#273235'
                    }
                }
            },
            MuiCheckbox: {
                styleOverrides: {
                    root: {
                        color: '#FFFFFF',
                        '&.Mui-disabled': {
                            color: 'rgba(255, 255, 255, 0.5)'
                        }
                    }
                }
            },
            MuiRadio: {
                styleOverrides: {
                    root: {
                        color: '#FFFFFF',
                        '&.Mui-disabled': {
                            color: 'rgba(255, 255, 255, 0.5)'
                        }
                    }
                }
            },
            MuiFormControlLabel: {
                styleOverrides: {
                    root: {
                        '.MuiTypography-root.Mui-disabled': {
                            color: 'rgba(255, 255, 255, 0.5)'
                        }
                    }
                }
            },
            MuiTab: {
                styleOverrides: {
                    root: {
                        '&:not(.Mui-selected)': {
                            color: 'rgba(255, 255, 255, 0.75)'
                        },
                        '&.Mui-disabled': {
                            color: 'rgba(255, 255, 255, 0.5)'
                        }
                    }
                }
            },
            MuiChip: {
                styleOverrides: {
                    root: {
                        color: '#FFFFFF',
                        backgroundColor: '#505B5F'
                    }
                }
            }
        },
        palette: {
            background: {
                default: '#273235'
            },
            divider: '#7D8486',
            primary: {
                main: '#FFFFFF',
                dark: '#D5DDE4',
                light: '#919FAE',
                contrastText: '#BEC2C2'
            }
        },
        shadows: darkShadows
    } as ThemeOptions)
);

export const Themes = {
    [ThemeType.Light]: lightTheme,
    [ThemeType.Dark]: darkTheme
};
