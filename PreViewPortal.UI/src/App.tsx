import {
    CssBaseline,
    styled,
    StyledEngineProvider,
    ThemeProvider
} from '@mui/material';
import { MaterialDesignContent, SnackbarProvider } from 'notistack';
import { ErrorBoundary } from 'react-error-boundary';
import { BrowserRouter as Router } from 'react-router';
import { ConfirmDialogProvider } from './components/ConfirmDialogProvider';
import { KnowledgeCenterProvider } from './components/KnowledgeCenterProvider';
import Routes from './components/Routes';
import { SupervisedAccountProvider } from './components/SupervisedAccountProvider';
import { UnhandledErrorFallback } from './components/UnhandledErrorFallback';
import { UserProvider } from './components/UserProvider';
import { Themes } from './theme';
import { ProductProvider } from './components/ProductProvider';
import { ChartSettingsProvider } from './components/ChartSettingsProvider';
import { ExportStatusProvider } from './components/ExportStatusProvider';
import { NormTooltipProvider } from '@/components/NormTooltipProvider';

const StyledMaterialDesignContent = styled(MaterialDesignContent)(() => ({
    '&.notistack-MuiContent-info': {
        backgroundColor: '#000000'
    }
}));

const App = () => (
    <StyledEngineProvider injectFirst>
        <ThemeProvider theme={Themes.light}>
            <CssBaseline />
            <SnackbarProvider
                Components={{
                    info: StyledMaterialDesignContent
                }}
            >
                <ErrorBoundary FallbackComponent={UnhandledErrorFallback}>
                    <UserProvider>
                        <SupervisedAccountProvider>
                            <ProductProvider>
                                <ConfirmDialogProvider>
                                    <KnowledgeCenterProvider>
                                        <ChartSettingsProvider>
                                            <ExportStatusProvider>
                                                <NormTooltipProvider>
                                                    <Router>
                                                        <Routes />
                                                    </Router>
                                                </NormTooltipProvider>
                                            </ExportStatusProvider>
                                        </ChartSettingsProvider>
                                    </KnowledgeCenterProvider>
                                </ConfirmDialogProvider>
                            </ProductProvider>
                        </SupervisedAccountProvider>
                    </UserProvider>
                </ErrorBoundary>
            </SnackbarProvider>
        </ThemeProvider>
    </StyledEngineProvider>
);

export default App;
