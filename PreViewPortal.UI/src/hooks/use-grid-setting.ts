import { useSnackbar } from 'notistack';
import { useCallback, useContext, useMemo } from 'react';
import UserContext from '../contexts/user-context';
import { GridSettingsCreateOrUpdateModel } from '../interfaces/grid-settings-create-or-update-model';
import { GridSettingsModel } from '../interfaces/grid-settings-model';
import { PreViewGrid } from '../interfaces/preview-grid';
import { useHttp } from './use-http';

export const useGridSetting = (id?: PreViewGrid) => {
    const { gridSettings, setGridSettings, isGridSettingsLoading } =
        useContext(UserContext);
    const { http } = useHttp();
    const { enqueueSnackbar } = useSnackbar();

    const gridSetting = useMemo(
        () => gridSettings.find(s => s.id === id)?.settings,
        [id, gridSettings]
    );

    const saveGridSetting = useCallback(
        async (settings: GridSettingsModel) => {
            if (id === undefined) return;

            try {
                const createOrUpdateModel: GridSettingsCreateOrUpdateModel = {
                    gridId: id,
                    gridSettings: JSON.stringify(settings)
                };
                await http.put('user/gridSettings', {
                    json: createOrUpdateModel
                });

                const newSettings = [
                    ...gridSettings.filter(s => s.id !== id),
                    { id, settings }
                ];
                setGridSettings(newSettings);

                enqueueSnackbar('Current view has been successfully saved.', {
                    variant: 'success'
                });
            } catch {
                enqueueSnackbar('Saving current view has failed.', {
                    variant: 'error'
                });
            }
        },
        [id, gridSettings, setGridSettings, enqueueSnackbar, http]
    );

    const deleteGridSetting = useCallback(async () => {
        if (id === undefined || !gridSetting) return;

        try {
            await http.delete(`user/gridSettings/${id}`);

            enqueueSnackbar('Default view has been successfully restored.', {
                variant: 'success'
            });

            const newSettings = gridSettings.filter(s => s.id !== id);
            setGridSettings(newSettings);
        } catch {
            enqueueSnackbar('Restoring default view has failed.', {
                variant: 'error'
            });
        }
    }, [id, gridSetting, gridSettings, setGridSettings, enqueueSnackbar, http]);

    return {
        gridSetting,
        isGridSettingsLoading,
        saveGridSetting,
        deleteGridSetting
    };
};
