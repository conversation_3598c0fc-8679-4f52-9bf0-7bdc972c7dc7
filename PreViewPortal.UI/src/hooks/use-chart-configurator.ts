import ChartSettingsContext from '@/contexts/chart-settings-context';
import { CreativeViewerContext } from '@/contexts/creative-viewer-context';
import { useContext } from 'react';

export const useChartConfigurator = () => {
    const {
        exposureGroupStyles,
        setExposureGroupStyles,
        curveTypeYAxisGroups,
        setCurveTypeYAxisGroups,
        showLegendBar,
        setShowLegendBar,
        showAxisMarkers,
        setShowAxisMarkers,
        normSettingsMode,
        setNormSettingsMode,
        showNormDetails,
        setShowNormDetails,
        smoothingLevel,
        setSmoothingLevel
    } = useContext(ChartSettingsContext);

    const {
        customLeftAxisRange,
        setCustomLeftAxisRange,
        customRightAxisRange,
        setCustomRightAxisRange
    } = useContext(CreativeViewerContext);

    return {
        exposureGroupStyles,
        setExposureGroupStyles,
        curveTypeYAxisGroups,
        setCurveTypeYAxisGroups,
        customLeftAxisRange,
        setCustomLeftAxisRange,
        customRightAxisRange,
        setCustomRightAxisRange,
        showLegendBar,
        setShowLegendBar,
        showAxisMarkers,
        setShowAxisMarkers,
        normSettingsMode,
        setNormSettingsMode,
        showNormDetails,
        setShowNormDetails,
        smoothingLevel,
        setSmoothingLevel
    };
};
