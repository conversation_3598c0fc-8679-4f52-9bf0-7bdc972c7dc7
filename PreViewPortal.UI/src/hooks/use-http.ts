import ky, { BeforeRequestHook, Options } from 'ky';
import { KyInstance } from 'ky/distribution/types/ky';
import { useCallback, useMemo, useState } from 'react';
import { useErrorBoundary } from 'react-error-boundary';
import { SupervisedAccountInfo } from '../helpers/supervised-account-info';

const setSupervisedAccountHeaderHook: BeforeRequestHook = request => {
    const account = SupervisedAccountInfo.persistedAccount;
    if (account?.id) {
        request.headers.set(
            SupervisedAccountInfo.headerName,
            account.id.toString()
        );
    }

    return request;
};

const defaultHttpOptions: Options = {
    prefixUrl: '/api',
    credentials: 'include',
    mode: 'cors',
    timeout: 30000,
    hooks: {
        beforeRequest: [setSupervisedAccountHeaderHook]
    }
};

const http = ky.create(defaultHttpOptions);

export const useHttp = () => {
    const [abortController, setAbortController] = useState<AbortController>(
        new AbortController()
    );
    const { showBoundary } = useErrorBoundary();

    const isDevMode = import.meta.env.DEV;

    const abort = useCallback(() => {
        //in react 18 strictmode useffect and its cleanup are fired twice causing error at abort
        if (!isDevMode) {
            abortController.abort();
            setAbortController(new AbortController());
        }
    }, [abortController, isDevMode]);

    const httpWithAbort = useMemo<KyInstance>(
        () => http.extend({ signal: abortController.signal }),
        [abortController]
    );

    return { http: httpWithAbort, abort, handleError: showBoundary };
};

export const useHttpWithoutAbort = () => {
    const { showBoundary } = useErrorBoundary();

    return { http, handleError: showBoundary };
};
