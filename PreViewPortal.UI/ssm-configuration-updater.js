import { SSMClient, GetParametersCommand } from '@aws-sdk/client-ssm';
import { readFileSync, writeFile } from 'fs';

const selectedFileType = process.argv[2] || 'env';
const stage = process.argv[3] || process?.env?.STAGE || 'stage';
const awsRegion = process.argv[4] || process?.env?.AWS_REGION || 'eu-west-1';

// Configuration for different file types
const configurationFileHandlers = [
    {
        type: 'config',
        sourceFilePath: `config.${stage}.js`,
        destinationFilePath: `completed-config.${stage}.js`,
        ssmParametersRegistry: [{ key: 'CLIENT_ID_PLACEHOLDER', path: `/preview/iam/${awsRegion}/${stage}/backend/audience` }],
        handle: handleConfigFile
    },
    {
        type: 'env',
        sourceFilePath: null,
        destinationFilePath: '.env',
        ssmParametersRegistry: [{ key: 'REACT_APP_MUI_LICENSE', path: '/live/materialui/datagridpremium' }],
        handle: handleEnvironmentFile
    }
];

function handleConfigFile(ssmParametersResult) {
    let fileContent = readFileSync(this.sourceFilePath, 'utf8');

    ssmParametersResult.Parameters.forEach(ssmParameter => {
        const configEntry = this.ssmParametersRegistry.find(config => config.path === ssmParameter.Name);
        fileContent = fileContent.replace(configEntry.key, ssmParameter.Value);
    });

    return fileContent;
}

function handleEnvironmentFile(ssmParametersResult) {
    const fileContent = ssmParametersResult.Parameters.reduce((accumulator, ssmParameter) => {
        const configEntry = this.ssmParametersRegistry.find(config => config.path === ssmParameter.Name);
        return accumulator + `VITE_${configEntry.key}=${ssmParameter.Value}\n`;
    }, '');

    return fileContent;
};

function updateConfigurationFile(configurationFileHandler) {
    const { destinationFilePath, ssmParametersRegistry } = configurationFileHandler;

    const ssmClient = new SSMClient();
    const params = {
        Names: ssmParametersRegistry.map(config => config.path),
        WithDecryption: false
    };

    const command = new GetParametersCommand(params);

    ssmClient
        .send(command)
        .then(ssmParametersResult => {
            const fileContent =
                configurationFileHandler.handle(ssmParametersResult);

            writeFile(destinationFilePath, fileContent, error => {
                if (error) {
                    console.error(error);
                } else {
                    console.log(
                        `Created ${destinationFilePath} file${
                            stage === 'live'
                                ? '.'
                                : ` file with content:\n${fileContent}`
                        }`
                    );
                }
            });
        })
        .catch(console.error);
}

// Entry point
function update() {
    const configurationFileHandler = configurationFileHandlers.find(configFile => configFile.type === selectedFileType);

    if (!configurationFileHandler) {
        console.error('Invalid file type. Please select either "config" or "env".');
        return;
    }

    updateConfigurationFile(configurationFileHandler);
}

update();
