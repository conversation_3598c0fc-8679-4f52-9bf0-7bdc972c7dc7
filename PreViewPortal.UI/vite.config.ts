import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import checker from 'vite-plugin-checker';
import svgr from 'vite-plugin-svgr';
import path from 'path';
import fs from 'fs';

// https://vitejs.dev/config/
export default defineConfig({
    plugins: [
        react(),
        checker({
            typescript: true,
            overlay: true,
            eslint: {
                useFlatConfig: true,
                lintCommand: 'eslint "./src/**/*.{ts,tsx}"'
            }
        }),
        svgr()
    ],
    assetsInclude: ['**/*.md'],
    build: {
        outDir: './build'
    },
    resolve: {
        alias: {
            '@': path.resolve(__dirname, './src')
        }
    },
    server: {
        host: 'local.realeyesit.com',
        port: 3000,
        https: {
            key: fs.readFileSync('../.cert/local.realeyesit.com.key', 'utf-8'),
            cert: fs.readFileSync('../.cert/local.realeyesit.com.crt', 'utf-8')
        },
        proxy: {
            '/api': {
                target: 'https://local.realeyesit.com:5003',
                changeOrigin: true,
                secure: false,
                ws: true
            }
        }
    }
});
