{"name": "preview-portal", "version": "0.1.0", "license": "UNLICENSED", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "node ssm-configuration-updater.js env && vite build", "preview": "vite preview", "update-env": "node ssm-configuration-updater.js env", "update-config": "node ssm-configuration-updater.js config", "start:dev": "(node ssm-configuration-updater.js config dev && move completed-config.dev.js public/config.js) && vite", "start:stage": "(node ssm-configuration-updater.js config stage && move completed-config.stage.js public/config.js) && vite", "start:live": "(node ssm-configuration-updater.js config live && move completed-config.live.js public/config.js) && vite"}, "dependencies": {"@aws-sdk/client-ssm": "^3.653.0", "@date-io/moment": "2.17.0", "@emotion/cache": "11.11.0", "@emotion/react": "11.11.1", "@emotion/styled": "11.11.0", "@mui/icons-material": "^6.3.1", "@mui/material": "^6.3.1", "@mui/styles": "^6.3.1", "@mui/x-data-grid-premium": "^7.23.5", "@mui/x-date-pickers-pro": "^7.23.3", "d3": "7.8.5", "flag-icons": "6.11.1", "ky": "1.0.1", "lodash": "^4.17.21", "moment": "2.29.4", "moment-duration-format": "^2.3.2", "notistack": "3.0.1", "qs": "6.11.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-error-boundary": "4.0.11", "react-markdown": "^9.0.3", "react-rnd": "^10.4.13", "react-router": "^7.1.2", "react-use": "17.5.0", "rehype-raw": "7.0.0"}, "devDependencies": {"@types/d3": "^7.4.0", "@types/lodash": "^4.14.199", "@types/moment-duration-format": "^2.2.4", "@types/node": "^20.8.2", "@types/qs": "^6.9.8", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@types/react-router": "^5.1.20", "@vitejs/plugin-react": "^4.3.1", "eslint": "^9.18.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.1.0", "prettier": "^3.0.3", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0", "vite": "^6.2.0", "vite-plugin-checker": "^0.8.0", "vite-plugin-svgr": "^4.2.0"}, "packageManager": "yarn@4.9.1"}