{"compilerOptions": {"target": "ESNext", "lib": ["DOM", "DOM.Iterable", "ESNext"], "allowJs": true, "types": ["vite/client", "vite-plugin-svgr/client", "node"], "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "downlevelIteration": true, "baseUrl": ".", "paths": {"*": ["node_modules/*"], "@/*": ["src/*"]}}, "include": ["src", "vite.config.ts"]}